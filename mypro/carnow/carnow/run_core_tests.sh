#!/bin/bash

# تشغيل الاختبارات الأساسية - Core Tests Runner
# Core Tests Runner for CarNow Project

echo "🚀 بدء تشغيل الاختبارات الأساسية..."
echo "🚀 Starting Core Tests..."

# الألوان للتنسيق
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# متغيرات العداد
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

echo -e "${BLUE}📊 تشغيل اختبارات النماذج الأساسية...${NC}"
echo -e "${BLUE}📊 Running Core Model Tests...${NC}"

# اختبار ProductModel
echo -e "${YELLOW}🔍 اختبار ProductModel...${NC}"
if flutter test test/unit/product_model_test.dart --reporter=compact; then
    echo -e "${GREEN}✅ ProductModel: نجح${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ ProductModel: فشل${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# اختبار CategoryModel
echo -e "${YELLOW}🔍 اختبار CategoryModel...${NC}"
if flutter test test/unit/features/categories/category_model_test.dart --reporter=compact; then
    echo -e "${GREEN}✅ CategoryModel: نجح${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ CategoryModel: فشل${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# اختبار UserModel
echo -e "${YELLOW}🔍 اختبار UserModel...${NC}"
if flutter test test/unit/features/auth/user_model_test.dart --reporter=compact; then
    echo -e "${GREEN}✅ UserModel: نجح${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ UserModel: فشل${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo -e "${BLUE}🔧 تشغيل اختبارات الأدوات المساعدة...${NC}"
echo -e "${BLUE}🔧 Running Utility Tests...${NC}"

# اختبار Validators
echo -e "${YELLOW}🔍 اختبار Validators...${NC}"
if flutter test test/unit/core/utils/validators_test.dart --reporter=compact; then
    echo -e "${GREEN}✅ Validators: نجح${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Validators: فشل${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# اختبار Formatters
echo -e "${YELLOW}🔍 اختبار Formatters...${NC}"
if flutter test test/unit/core/utils/formatters_test.dart --reporter=compact; then
    echo -e "${GREEN}✅ Formatters: نجح${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ Formatters: فشل${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo -e "${BLUE}🎨 تشغيل اختبارات الواجهة...${NC}"
echo -e "${BLUE}🎨 Running Widget Tests...${NC}"

# اختبار CustomTextField
echo -e "${YELLOW}🔍 اختبار CustomTextField...${NC}"
if flutter test test/widget/core/widgets/custom_text_field_test.dart --reporter=compact; then
    echo -e "${GREEN}✅ CustomTextField: نجح${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ CustomTextField: فشل${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# اختبار UnifiedButton
echo -e "${YELLOW}🔍 اختبار UnifiedButton...${NC}"
if flutter test test/widget/core/widgets/unified_button_test.dart --reporter=compact; then
    echo -e "${GREEN}✅ UnifiedButton: نجح${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ UnifiedButton: فشل${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo -e "${BLUE}🔐 تشغيل اختبارات الأمان...${NC}"
echo -e "${BLUE}🔐 Running Security Tests...${NC}"

# اختبار CSRF Provider
echo -e "${YELLOW}🔍 اختبار CSRF Provider...${NC}"
if flutter test test/unit/core/security/basic_csrf_provider_test.dart --reporter=compact; then
    echo -e "${GREEN}✅ CSRF Provider: نجح${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ CSRF Provider: فشل${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# اختبار MFA Provider
echo -e "${YELLOW}🔍 اختبار MFA Provider...${NC}"
if flutter test test/unit/features/auth/simple_mfa_provider_test.dart --reporter=compact; then
    echo -e "${GREEN}✅ MFA Provider: نجح${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ MFA Provider: فشل${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# حساب النسبة المئوية
if [ $TOTAL_TESTS -gt 0 ]; then
    SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
else
    SUCCESS_RATE=0
fi

echo ""
echo "📊 ملخص النتائج | Results Summary"
echo "=================================="
echo -e "إجمالي الاختبارات | Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "الاختبارات الناجحة | Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "الاختبارات الفاشلة | Failed: ${RED}$FAILED_TESTS${NC}"
echo -e "معدل النجاح | Success Rate: ${YELLOW}$SUCCESS_RATE%${NC}"

if [ $SUCCESS_RATE -ge 90 ]; then
    echo -e "${GREEN}🎉 ممتاز! معدل نجاح عالي${NC}"
    echo -e "${GREEN}🎉 Excellent! High success rate${NC}"
elif [ $SUCCESS_RATE -ge 75 ]; then
    echo -e "${YELLOW}👍 جيد، يحتاج تحسين طفيف${NC}"
    echo -e "${YELLOW}👍 Good, needs minor improvements${NC}"
else
    echo -e "${RED}⚠️ يحتاج تحسين كبير${NC}"
    echo -e "${RED}⚠️ Needs significant improvements${NC}"
fi

echo ""
echo "🔗 للمزيد من التفاصيل، راجع:"
echo "🔗 For more details, check:"
echo "   - WEEK_1_TESTING_QUALITY_REPORT.md"
echo "   - test/comprehensive_test_report.md"

# إنهاء البرنامج بحالة النجاح أو الفشل
if [ $FAILED_TESTS -eq 0 ]; then
    exit 0
else
    exit 1
fi

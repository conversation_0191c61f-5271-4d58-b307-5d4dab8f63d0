#!/bin/bash

# CarNow Backend Restart Script with Session Fix
# إعادة تشغيل الخادم الخلفي مع إصلاح الجلسة

echo "🔄 CarNow Backend Restart with Session Persistence Fix"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "backend-go/go.mod" ]; then
    echo "❌ Error: Please run this script from the carnow root directory"
    exit 1
fi

# Navigate to backend directory
cd backend-go

echo "📦 Building backend with new session configuration..."

# Build the backend
go build -o carnow-backend ./cmd/server

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"

# Set environment variables for extended session
export JWT_EXPIRES_IN="168h"
export JWT_REFRESH_EXPIRES_IN="720h"
export SESSION_TIMEOUT="604800"

echo "🚀 Starting backend with extended session configuration..."
echo "   - JWT Expires In: 168h (7 days)"
echo "   - JWT Refresh Expires In: 720h (30 days)"
echo "   - Session Timeout: 604800s (7 days)"

# Start the backend
./carnow-backend

echo "🎉 Backend started with session persistence fix!"

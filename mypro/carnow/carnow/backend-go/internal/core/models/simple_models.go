package models

import "time"

// Product represents a simple product model
type Product struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description *string   `json:"description" db:"description"`
	Price       float64   `json:"price" db:"price"`
	UserID      string    `json:"user_id" db:"user_id"`
	CategoryID  *string   `json:"category_id" db:"category_id"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	IsDeleted   bool      `json:"is_deleted" db:"is_deleted"`
}

// Category represents a simple category model
type Category struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description *string   `json:"description" db:"description"`
	ParentID    *string   `json:"parent_id" db:"parent_id"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	IsDeleted   bool      `json:"is_deleted" db:"is_deleted"`
}

// User represents a simple user profile model
type User struct {
	ID        string    `json:"id" db:"id"`
	Email     string    `json:"email" db:"email"`
	Name      *string   `json:"name" db:"name"`
	AvatarURL *string   `json:"avatar_url" db:"avatar_url"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// UpdateUserProfileRequest represents the request body for updating user profile
type UpdateUserProfileRequest struct {
	Name      *string `json:"name" binding:"omitempty,max=255"`
	AvatarURL *string `json:"avatar_url" binding:"omitempty,url"`
}

// FileUploadRequest represents a file upload request
type FileUploadRequest struct {
	File     []byte `json:"file" binding:"required"`
	FileName string `json:"file_name" binding:"required,max=255"`
	FileType string `json:"file_type" binding:"required"`
	Bucket   string `json:"bucket" binding:"required"`
}

// FileUploadResponse represents a file upload response
type FileUploadResponse struct {
	URL      string `json:"url"`
	FileName string `json:"file_name"`
	FileSize int64  `json:"file_size"`
	MimeType string `json:"mime_type"`
}

// FileDeleteRequest represents a file deletion request
type FileDeleteRequest struct {
	URL    string `json:"url" binding:"required,url"`
	Bucket string `json:"bucket" binding:"required"`
}

// StorageURLRequest represents a request for generating storage URL
type StorageURLRequest struct {
	FileName string `json:"file_name" binding:"required"`
	Bucket   string `json:"bucket" binding:"required"`
}

// Wallet represents a user wallet
type Wallet struct {
	ID        string    `json:"id" db:"id"`
	UserID    string    `json:"user_id" db:"user_id"`
	Balance   float64   `json:"balance" db:"balance"`
	Currency  string    `json:"currency" db:"currency"`
	Status    string    `json:"status" db:"status"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
	IsDeleted bool      `json:"is_deleted" db:"is_deleted"`
}

// WalletTransaction represents a wallet transaction
type WalletTransaction struct {
	ID          string    `json:"id" db:"id"`
	WalletID    string    `json:"wallet_id" db:"wallet_id"`
	Amount      float64   `json:"amount" db:"amount"`
	Type        string    `json:"type" db:"type"`     // deposit, withdraw, transfer
	Status      string    `json:"status" db:"status"` // pending, completed, failed
	Description *string   `json:"description" db:"description"`
	ReferenceID *string   `json:"reference_id" db:"reference_id"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	IsDeleted   bool      `json:"is_deleted" db:"is_deleted"`
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   *string     `json:"error,omitempty"`
	Message *string     `json:"message,omitempty"`
}

// PaginatedResponse represents a paginated API response
type PaginatedResponse struct {
	Success    bool        `json:"success"`
	Data       interface{} `json:"data"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	Total      int64       `json:"total"`
	TotalPages int         `json:"total_pages"`
	Error      *string     `json:"error,omitempty"`
}

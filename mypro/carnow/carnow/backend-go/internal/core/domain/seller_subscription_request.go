package domain

import (
	"time"
)

// SellerSubscriptionRequest represents a seller's subscription request
type SellerSubscriptionRequest struct {
	ID                        string    `json:"id" gorm:"primaryKey;type:uuid;default:uuid_generate_v4()"`
	SellerID                  string    `json:"seller_id" gorm:"type:uuid;not null;index"`
	PlanID                    string    `json:"plan_id" gorm:"type:uuid;not null"`
	RequestedTier             string    `json:"requested_tier" gorm:"type:varchar(20);not null"` // basic, premium, enterprise
	BillingCycle              string    `json:"billing_cycle" gorm:"type:varchar(10);not null"`  // monthly, yearly
	Status                    string    `json:"status" gorm:"type:varchar(20);not null;default:'pending'"` // pending, under_review, approved, rejected, cancelled, expired
	RequestDate               time.Time `json:"request_date" gorm:"type:timestamp with time zone;not null;default:now()"`
	ApprovedDate              *time.Time `json:"approved_date" gorm:"type:timestamp with time zone"`
	RejectedDate              *time.Time `json:"rejected_date" gorm:"type:timestamp with time zone"`
	AdminID                   *string    `json:"admin_id" gorm:"type:uuid"` // ID of admin who approved/rejected
	AdminNotes                *string    `json:"admin_notes" gorm:"type:text"` // Notes from admin
	RejectionReason           *string    `json:"rejection_reason" gorm:"type:text"` // Reason for rejection
	RequestedPriceLD          float64    `json:"requested_price_ld" gorm:"type:decimal(10,2);not null"` // Price in Libyan Dinar
	PaymentMethodID           *string    `json:"payment_method_id" gorm:"type:uuid"` // Payment method used
	SellerInfo                *string    `json:"seller_info" gorm:"type:jsonb"` // Additional seller information
	BusinessDocuments         *string    `json:"business_documents" gorm:"type:jsonb"` // Attached business documents
	RequiresDocumentVerification bool     `json:"requires_document_verification" gorm:"type:boolean;default:false"` // Whether document verification is required
	Priority                  *int       `json:"priority" gorm:"type:integer"` // Review priority (1-5)
	CreatedAt                 time.Time `json:"created_at" gorm:"type:timestamp with time zone;not null;default:now()"`
	UpdatedAt                 time.Time `json:"updated_at" gorm:"type:timestamp with time zone;not null;default:now()"`
}

// TableName specifies the table name for GORM
func (SellerSubscriptionRequest) TableName() string {
	return "seller_subscription_requests"
}

// SellerSubscriptionRequestReview represents a review of a seller subscription request
type SellerSubscriptionRequestReview struct {
	ID          string    `json:"id" gorm:"primaryKey;type:uuid;default:uuid_generate_v4()"`
	RequestID   string    `json:"request_id" gorm:"type:uuid;not null;index"`
	AdminID     string    `json:"admin_id" gorm:"type:uuid;not null"`
	ReviewDate  time.Time `json:"review_date" gorm:"type:timestamp with time zone;not null;default:now()"`
	Decision    string    `json:"decision" gorm:"type:varchar(20);not null"` // approved, rejected
	Notes       *string   `json:"notes" gorm:"type:text"` // Review notes
	RejectionReason *string `json:"rejection_reason" gorm:"type:text"` // Reason for rejection if rejected
	ReviewData  *string   `json:"review_data" gorm:"type:jsonb"` // Additional review data
	CreatedAt   time.Time `json:"created_at" gorm:"type:timestamp with time zone;not null;default:now()"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"type:timestamp with time zone;not null;default:now()"`
}

// TableName specifies the table name for GORM
func (SellerSubscriptionRequestReview) TableName() string {
	return "seller_subscription_request_reviews"
}

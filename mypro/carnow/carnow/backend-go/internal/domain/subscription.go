package domain

import (
	"time"
)

// Subscription represents a user's subscription
type Subscription struct {
	ID             uint             `json:"id" gorm:"primaryKey"`
	PlanID         uint             `json:"plan_id" gorm:"not null"`
	Plan           SubscriptionPlan `json:"plan" gorm:"foreignKey:PlanID"`
	UserAuthID     string           `json:"user_auth_id" gorm:"not null"`
	StartDate      time.Time        `json:"start_date" gorm:"not null"`
	EndDate        time.Time        `json:"end_date" gorm:"not null"`
	Status         string           `json:"status" gorm:"default:'active'"` // active, cancelled, expired
	BillingCycle   string           `json:"billing_cycle" gorm:"not null"`  // monthly, yearly
	Amount         float64          `json:"amount" gorm:"not null"`
	OriginalAmount float64          `json:"original_amount" gorm:"not null"`
	DiscountAmount float64          `json:"discount_amount" gorm:"default:0"`
	MaxListings    int              `json:"max_listings" gorm:"not null"`
	UsedListings   int              `json:"used_listings" gorm:"default:0"`
	Features       []string         `json:"features" gorm:"type:text[]"`
	CreatedAt      time.Time        `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt      time.Time        `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName specifies the table name for Subscription
func (Subscription) TableName() string {
	return "subscriptions"
}

// IsActive checks if the subscription is currently active
func (s *Subscription) IsActive() bool {
	return s.Status == "active" && time.Now().Before(s.EndDate)
}

// IsExpired checks if the subscription has expired
func (s *Subscription) IsExpired() bool {
	return time.Now().After(s.EndDate)
}

// DaysUntilExpiry returns the number of days until the subscription expires
func (s *Subscription) DaysUntilExpiry() int {
	if s.IsExpired() {
		return 0
	}

	duration := s.EndDate.Sub(time.Now())
	return int(duration.Hours() / 24)
}

// RemainingListings returns the number of remaining listings
func (s *Subscription) RemainingListings() int {
	remaining := s.MaxListings - s.UsedListings
	if remaining < 0 {
		return 0
	}
	return remaining
}

// CanAddListing checks if the user can add a new listing
func (s *Subscription) CanAddListing() bool {
	return s.IsActive() && s.RemainingListings() > 0
}

// GetDiscountPercentage returns the discount percentage applied
func (s *Subscription) GetDiscountPercentage() float64 {
	if s.OriginalAmount <= 0 {
		return 0
	}
	return (s.DiscountAmount / s.OriginalAmount) * 100
}

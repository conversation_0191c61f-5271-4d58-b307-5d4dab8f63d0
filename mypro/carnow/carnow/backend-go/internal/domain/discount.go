package domain

import (
	"time"
)

// Discount represents a discount configuration
type Discount struct {
	ID            uint       `json:"id" gorm:"primaryKey"`
	Name          string     `json:"name" gorm:"not null"`
	NameAr        string     `json:"name_ar" gorm:"column:name_ar;not null"`
	Description   string     `json:"description"`
	DescriptionAr string     `json:"description_ar" gorm:"column:description_ar"`
	DiscountType  string     `json:"discount_type" gorm:"not null"` // percentage, fixed_amount
	DiscountValue float64    `json:"discount_value" gorm:"not null"`
	MinAmount     float64    `json:"min_amount" gorm:"default:0"`
	MaxDiscount   *float64   `json:"max_discount"`
	ApplicableTo  []string   `json:"applicable_to" gorm:"type:text[]"`
	StartDate     time.Time  `json:"start_date" gorm:"default:CURRENT_DATE"`
	EndDate       *time.Time `json:"end_date"`
	IsActive      bool       `json:"is_active" gorm:"default:true"`
	UsageLimit    *int       `json:"usage_limit"`
	UsedCount     int        `json:"used_count" gorm:"default:0"`
	CreatedAt     time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}

// DiscountCode represents a discount code
type DiscountCode struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	Code       string    `json:"code" gorm:"unique;not null"`
	DiscountID uint      `json:"discount_id" gorm:"not null"`
	Discount   Discount  `json:"discount" gorm:"foreignKey:DiscountID"`
	IsActive   bool      `json:"is_active" gorm:"default:true"`
	UsageLimit *int      `json:"usage_limit"`
	UsedCount  int       `json:"used_count" gorm:"default:0"`
	CreatedAt  time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// DiscountUsage represents discount usage tracking
type DiscountUsage struct {
	ID                   uint      `json:"id" gorm:"primaryKey"`
	DiscountID           uint      `json:"discount_id" gorm:"not null"`
	DiscountCodeID       *uint     `json:"discount_code_id"`
	UserID               string    `json:"user_id" gorm:"not null"`
	SubscriptionID       *uint     `json:"subscription_id"`
	AmountBeforeDiscount float64   `json:"amount_before_discount" gorm:"not null"`
	DiscountAmount       float64   `json:"discount_amount" gorm:"not null"`
	AmountAfterDiscount  float64   `json:"amount_after_discount" gorm:"not null"`
	UsedAt               time.Time `json:"used_at" gorm:"autoCreateTime"`
}

// DiscountCalculation represents discount calculation result
type DiscountCalculation struct {
	OriginalAmount     float64       `json:"original_amount"`
	DiscountAmount     float64       `json:"discount_amount"`
	FinalAmount        float64       `json:"final_amount"`
	DiscountPercentage float64       `json:"discount_percentage"`
	AppliedDiscount    *Discount     `json:"applied_discount,omitempty"`
	AppliedCode        *DiscountCode `json:"applied_code,omitempty"`
}

// TableName specifies the table name for Discount
func (Discount) TableName() string {
	return "discounts"
}

// TableName specifies the table name for DiscountCode
func (DiscountCode) TableName() string {
	return "discount_codes"
}

// TableName specifies the table name for DiscountUsage
func (DiscountUsage) TableName() string {
	return "discount_usage"
}

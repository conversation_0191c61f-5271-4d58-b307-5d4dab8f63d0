package repositories

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"carnow-backend/internal/modules/admin/models"
)

// AdminRepository handles database operations for admin functionality
type AdminRepository struct {
	db *sql.DB
}

// NewAdminRepository creates a new admin repository
func NewAdminRepository(db *sql.DB) *AdminRepository {
	return &AdminRepository{
		db: db,
	}
}

// GetFinancialSummary retrieves financial summary data
func (r *AdminRepository) GetFinancialSummary(ctx context.Context, period string) (*models.FinancialSummary, error) {
	var summary models.FinancialSummary

	// Get total revenue
	revenueQuery := `SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE is_deleted = false`
	err := r.db.QueryRowContext(ctx, revenueQuery).Scan(&summary.TotalRevenue)
	if err != nil {
		return nil, fmt.Errorf("failed to get total revenue: %w", err)
	}

	// Get monthly revenue
	monthlyQuery := `
		SELECT COALESCE(SUM(total_amount), 0) 
		FROM orders 
		WHERE is_deleted = false 
		AND created_at >= date_trunc('month', CURRENT_DATE)
	`
	err = r.db.QueryRowContext(ctx, monthlyQuery).Scan(&summary.MonthlyRevenue)
	if err != nil {
		return nil, fmt.Errorf("failed to get monthly revenue: %w", err)
	}

	// Get order counts
	orderQuery := `
		SELECT 
			COUNT(*) as total_orders,
			COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders
		FROM orders 
		WHERE is_deleted = false
	`
	err = r.db.QueryRowContext(ctx, orderQuery).Scan(&summary.TotalOrders, &summary.PendingOrders)
	if err != nil {
		return nil, fmt.Errorf("failed to get order counts: %w", err)
	}

	// Get user count
	userQuery := `SELECT COUNT(*) FROM users WHERE is_deleted = false`
	err = r.db.QueryRowContext(ctx, userQuery).Scan(&summary.ActiveUsers)
	if err != nil {
		return nil, fmt.Errorf("failed to get user count: %w", err)
	}

	// Get product count
	productQuery := `SELECT COUNT(*) FROM products WHERE is_deleted = false`
	err = r.db.QueryRowContext(ctx, productQuery).Scan(&summary.TotalProducts)
	if err != nil {
		return nil, fmt.Errorf("failed to get product count: %w", err)
	}

	// Calculate growth rates (simplified - in production, compare with previous period)
	summary.RevenueGrowth = 0.0 // Calculate based on previous period
	summary.OrderGrowth = 0.0   // Calculate based on previous period
	summary.UserGrowth = 0.0    // Calculate based on previous period

	return &summary, nil
}

// GetAlerts retrieves system alerts
func (r *AdminRepository) GetAlerts(ctx context.Context, limit int) ([]models.Alert, error) {
	query := `
		SELECT id, type, level, title, message, status, created_at, resolved_at, resolved_by
		FROM alerts 
		WHERE status = 'active'
		ORDER BY created_at DESC
		LIMIT $1
	`

	rows, err := r.db.QueryContext(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query alerts: %w", err)
	}
	defer rows.Close()

	var alerts []models.Alert
	for rows.Next() {
		var alert models.Alert
		var resolvedAt sql.NullTime
		var resolvedBy sql.NullString

		err := rows.Scan(
			&alert.ID, &alert.Type, &alert.Level, &alert.Title, &alert.Message,
			&alert.Status, &alert.CreatedAt, &resolvedAt, &resolvedBy,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan alert: %w", err)
		}

		if resolvedAt.Valid {
			alert.ResolvedAt = &resolvedAt.Time
		}
		if resolvedBy.Valid {
			alert.ResolvedBy = &resolvedBy.String
		}

		alerts = append(alerts, alert)
	}

	return alerts, nil
}

// GetRecentActions retrieves recent system actions
func (r *AdminRepository) GetRecentActions(ctx context.Context, limit int) ([]models.RecentAction, error) {
	query := `
		SELECT id, user_id, action, resource, details, ip_address, timestamp
		FROM admin_actions 
		ORDER BY timestamp DESC
		LIMIT $1
	`

	rows, err := r.db.QueryContext(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query recent actions: %w", err)
	}
	defer rows.Close()

	var actions []models.RecentAction
	for rows.Next() {
		var action models.RecentAction

		err := rows.Scan(
			&action.ID, &action.UserID, &action.Action, &action.Resource,
			&action.Details, &action.IPAddress, &action.Timestamp,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan action: %w", err)
		}

		actions = append(actions, action)
	}

	return actions, nil
}

// GetSystemHealth retrieves system health metrics
func (r *AdminRepository) GetSystemHealth(ctx context.Context) (*models.SystemHealth, error) {
	health := &models.SystemHealth{
		Status: "healthy",
	}

	// Get database status
	err := r.db.PingContext(ctx)
	if err != nil {
		health.DatabaseStatus = "unhealthy"
		health.Status = "degraded"
	} else {
		health.DatabaseStatus = "healthy"
	}

	// Get basic metrics (simplified - in production, use proper monitoring)
	health.Uptime = 99.9
	health.ResponseTime = 150.0 // ms
	health.ErrorRate = 0.1      // percentage
	health.CacheStatus = "healthy"
	health.MemoryUsage = 65.0 // percentage
	health.CPUUsage = 45.0    // percentage
	health.DiskUsage = 55.0   // percentage
	health.ActiveConnections = 25

	// Determine overall status
	if health.ErrorRate > 5.0 || health.MemoryUsage > 90.0 || health.CPUUsage > 90.0 {
		health.Status = "critical"
	} else if health.ErrorRate > 1.0 || health.MemoryUsage > 80.0 || health.CPUUsage > 80.0 {
		health.Status = "degraded"
	}

	return health, nil
}

// GetWalletManagement retrieves wallet management data
func (r *AdminRepository) GetWalletManagement(ctx context.Context, page, limit int) (*models.WalletManagementResponse, error) {
	// Get wallet summary
	summaryQuery := `
		SELECT 
			COUNT(*) as total_wallets,
			COUNT(CASE WHEN balance > 0 THEN 1 END) as active_wallets,
			COALESCE(SUM(balance), 0) as total_balance,
			COALESCE(AVG(balance), 0) as average_balance
		FROM wallets 
		WHERE is_deleted = false
	`

	var summary models.WalletManagement
	err := r.db.QueryRowContext(ctx, summaryQuery).Scan(
		&summary.TotalWallets, &summary.ActiveWallets,
		&summary.TotalBalance, &summary.AverageBalance,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get wallet summary: %w", err)
	}

	// Get transaction counts
	transactionQuery := `
		SELECT 
			COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) as transactions_today,
			COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions
		FROM wallet_transactions 
		WHERE is_deleted = false
	`

	err = r.db.QueryRowContext(ctx, transactionQuery).Scan(
		&summary.TransactionsToday, &summary.PendingTransactions, &summary.FailedTransactions,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction counts: %w", err)
	}

	// Get wallet list with pagination
	walletQuery := `
		SELECT w.id, w.user_id, w.balance, w.status, w.created_at,
		       MAX(wt.created_at) as last_transaction,
		       COUNT(wt.id) as transaction_count
		FROM wallets w
		LEFT JOIN wallet_transactions wt ON w.id = wt.wallet_id
		WHERE w.is_deleted = false
		GROUP BY w.id, w.user_id, w.balance, w.status, w.created_at
		ORDER BY w.created_at DESC
		LIMIT $1 OFFSET $2
	`

	offset := (page - 1) * limit
	rows, err := r.db.QueryContext(ctx, walletQuery, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query wallets: %w", err)
	}
	defer rows.Close()

	var wallets []models.WalletSummary
	for rows.Next() {
		var wallet models.WalletSummary
		var lastTransaction sql.NullTime

		err := rows.Scan(
			&wallet.ID, &wallet.UserID, &wallet.Balance, &wallet.Status, &wallet.CreatedAt,
			&lastTransaction, &wallet.TransactionCount,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan wallet: %w", err)
		}

		if lastTransaction.Valid {
			wallet.LastTransaction = lastTransaction.Time
		} else {
			wallet.LastTransaction = wallet.CreatedAt
		}

		wallets = append(wallets, wallet)
	}

	// Get total count for pagination
	countQuery := `SELECT COUNT(*) FROM wallets WHERE is_deleted = false`
	var total int
	err = r.db.QueryRowContext(ctx, countQuery).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("failed to count wallets: %w", err)
	}

	pagination := models.AdminPagination{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + limit - 1) / limit,
		HasNext:    page*limit < total,
		HasPrev:    page > 1,
	}

	return &models.WalletManagementResponse{
		Wallets:    wallets,
		Pagination: pagination,
		Summary:    summary,
	}, nil
}

// UpdateAlertStatus updates the status of an alert
func (r *AdminRepository) UpdateAlertStatus(ctx context.Context, alertID, status, resolvedBy string) error {
	query := `
		UPDATE alerts 
		SET status = $1, resolved_at = $2, resolved_by = $3
		WHERE id = $4
	`

	var resolvedAt *time.Time
	if status == "resolved" {
		now := time.Now()
		resolvedAt = &now
	}

	result, err := r.db.ExecContext(ctx, query, status, resolvedAt, resolvedBy, alertID)
	if err != nil {
		return fmt.Errorf("failed to update alert status: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("alert not found")
	}

	return nil
}

// GetFinancialReports retrieves financial reports
func (r *AdminRepository) GetFinancialReports(ctx context.Context, reportType string, page, limit int) (*models.FinancialReportsResponse, error) {
	query := `
		SELECT id, type, period, revenue, orders, users, products, generated_at, data
		FROM financial_reports 
		WHERE type = $1
		ORDER BY generated_at DESC
		LIMIT $2 OFFSET $3
	`

	offset := (page - 1) * limit
	rows, err := r.db.QueryContext(ctx, query, reportType, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query financial reports: %w", err)
	}
	defer rows.Close()

	var reports []models.FinancialReport
	for rows.Next() {
		var report models.FinancialReport
		var dataStr sql.NullString

		err := rows.Scan(
			&report.ID, &report.Type, &report.Period, &report.Revenue,
			&report.Orders, &report.Users, &report.Products,
			&report.GeneratedAt, &dataStr,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan report: %w", err)
		}

		// Parse data JSON (simplified)
		report.Data = make(map[string]interface{})
		if dataStr.Valid {
			// In production, use proper JSON parsing
			report.Data["raw"] = dataStr.String
		}

		reports = append(reports, report)
	}

	// Get total count for pagination
	countQuery := `SELECT COUNT(*) FROM financial_reports WHERE type = $1`
	var total int
	err = r.db.QueryRowContext(ctx, countQuery, reportType).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("failed to count reports: %w", err)
	}

	pagination := models.AdminPagination{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + limit - 1) / limit,
		HasNext:    page*limit < total,
		HasPrev:    page > 1,
	}

	return &models.FinancialReportsResponse{
		Reports:    reports,
		Pagination: pagination,
	}, nil
}

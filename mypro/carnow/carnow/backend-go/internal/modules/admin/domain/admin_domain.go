package domain

import (
	"time"
)

type AdminActionType string

const (
	WalletBalanceAdjustment AdminActionType = "wallet_balance_adjustment"
	TransactionReversal     AdminActionType = "transaction_reversal"
	AccountFreeze           AdminActionType = "account_freeze"
	AccountUnfreeze         AdminActionType = "account_unfreeze"
	ManualRefund            AdminActionType = "manual_refund"
	CommissionAdjustment    AdminActionType = "commission_adjustment"
	LimitModification       AdminActionType = "limit_modification"
	VerificationOverride    AdminActionType = "verification_override"
	SuspiciousActivityFlag  AdminActionType = "suspicious_activity_flag"
	ReportGeneration        AdminActionType = "report_generation"
	Other                   AdminActionType = "other"
)

type AdminActionLog struct {
	ID                string           `json:"id"`
	AdminUserID       string           `json:"admin_user_id"`
	AdminEmail        string           `json:"admin_email"`
	ActionType        AdminActionType  `json:"action_type"`
	TargetUserID      *string          `json:"target_user_id,omitempty"`
	TargetWalletID    *string          `json:"target_wallet_id,omitempty"`
	TargetTransactionID *string        `json:"target_transaction_id,omitempty"`
	Description       string           `json:"description"`
	Reason            string           `json:"reason"`
	PreviousValue     *string          `json:"previous_value,omitempty"`
	NewValue          *string          `json:"new_value,omitempty"`
	Metadata          map[string]any   `json:"metadata,omitempty"`
	IPAddress         *string          `json:"ip_address,omitempty"`
	UserAgent         *string          `json:"user_agent,omitempty"`
	CreatedAt         time.Time        `json:"created_at"`
}
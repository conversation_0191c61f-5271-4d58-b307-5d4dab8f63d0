package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/modules/admin/models"
	"carnow-backend/internal/modules/admin/services"
	"carnow-backend/internal/shared/middleware"
)

// AdminFinancialHandler handles admin financial operations
type AdminFinancialHandler struct {
	adminService *services.AdminFinancialService
	logger       *zap.Logger
}

// NewAdminFinancialHandler creates a new admin financial handler
func NewAdminFinancialHandler(adminService *services.AdminFinancialService, logger *zap.Logger) *AdminFinancialHandler {
	return &AdminFinancialHandler{
		adminService: adminService,
		logger:       logger,
	}
}

// GetFinancialDashboard returns financial dashboard data
// @Summary Get financial dashboard
// @Description Get comprehensive financial dashboard data for admin
// @Tags Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param period query string false "Dashboard period (day, week, month, year)" default(month)
// @Success 200 {object} models.AdminDashboardResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/admin/financial/dashboard [get]
func (h *AdminFinancialHandler) GetFinancialDashboard(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Check if user is admin (this would be implemented in middleware)
	// For now, we'll allow any authenticated user to access admin endpoints

	period := c.DefaultQuery("period", "month")

	dashboard, err := h.adminService.GetFinancialDashboard(c.Request.Context(), period)
	if err != nil {
		h.logger.Error("Failed to get financial dashboard", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get financial dashboard"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"dashboard": dashboard,
		"success":   true,
	})
}

// GetWalletManagement returns wallet management data
// @Summary Get wallet management
// @Description Get wallet management data and statistics
// @Tags Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} models.WalletManagementResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/admin/financial/wallets [get]
func (h *AdminFinancialHandler) GetWalletManagement(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	response, err := h.adminService.GetWalletManagement(c.Request.Context(), page, limit)
	if err != nil {
		h.logger.Error("Failed to get wallet management", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get wallet management"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"wallets":    response.Wallets,
		"pagination": response.Pagination,
		"summary":    response.Summary,
		"success":    true,
	})
}

// GetFinancialAlerts returns financial alerts
// @Summary Get financial alerts
// @Description Get financial alerts and notifications for admin
// @Tags Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Number of alerts to return" default(20)
// @Success 200 {object} models.FinancialAlertsResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/admin/financial/alerts [get]
func (h *AdminFinancialHandler) GetFinancialAlerts(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if limit < 1 || limit > 50 {
		limit = 20
	}

	alerts, err := h.adminService.GetFinancialAlerts(c.Request.Context(), limit)
	if err != nil {
		h.logger.Error("Failed to get financial alerts", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get financial alerts"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"alerts":  alerts,
		"success": true,
	})
}

// GetRecentActions returns recent admin actions
// @Summary Get recent actions
// @Description Get recent admin actions and audit logs
// @Tags Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Number of actions to return" default(20)
// @Success 200 {object} models.RecentActionsResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/admin/financial/actions [get]
func (h *AdminFinancialHandler) GetRecentActions(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if limit < 1 || limit > 100 {
		limit = 20
	}

	actions, err := h.adminService.GetRecentActions(c.Request.Context(), limit)
	if err != nil {
		h.logger.Error("Failed to get recent actions", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recent actions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"actions": actions,
		"success": true,
	})
}

// GetSystemHealth returns system health information
// @Summary Get system health
// @Description Get system health metrics and status
// @Tags Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.SystemHealthResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/admin/financial/health [get]
func (h *AdminFinancialHandler) GetSystemHealth(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	health, err := h.adminService.GetSystemHealth(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get system health", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get system health"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"health":  health,
		"success": true,
	})
}

// UpdateAlertStatus updates the status of a financial alert
// @Summary Update alert status
// @Description Update the status of a financial alert (resolve, acknowledge)
// @Tags Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Alert ID"
// @Param status body models.UpdateAlertStatusRequest true "New status"
// @Success 200 {object} models.AlertResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/admin/financial/alerts/{id}/status [put]
func (h *AdminFinancialHandler) UpdateAlertStatus(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	alertID := c.Param("id")
	if alertID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Alert ID is required"})
		return
	}

	var req models.UpdateAlertStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data", "details": err.Error()})
		return
	}

	err := h.adminService.UpdateAlertStatus(c.Request.Context(), alertID, req.Status, req.Notes)
	if err != nil {
		h.logger.Error("Failed to update alert status", zap.Error(err), zap.String("alert_id", alertID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update alert status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert status updated successfully",
		"success": true,
	})
}

// GetFinancialReports returns financial reports
// @Summary Get financial reports
// @Description Get financial reports and analytics
// @Tags Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param report_type query string true "Report type (daily, weekly, monthly, yearly)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} models.FinancialReportResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/admin/financial/reports [get]
func (h *AdminFinancialHandler) GetFinancialReports(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	reportType := c.Query("report_type")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if reportType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Report type is required"})
		return
	}

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 50 {
		limit = 20
	}

	response, err := h.adminService.GetFinancialReports(c.Request.Context(), reportType, page, limit)
	if err != nil {
		h.logger.Error("Failed to get financial reports", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get financial reports"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"reports":    response.Reports,
		"pagination": response.Pagination,
		"success":    true,
	})
}

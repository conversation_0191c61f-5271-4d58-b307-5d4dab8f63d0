package services

import (
	"context"
	"fmt"
	"time"

	"carnow-backend/internal/modules/admin/models"
	"carnow-backend/internal/modules/admin/repositories"

	"go.uber.org/zap"
)

// AdminFinancialService handles admin financial business logic
type AdminFinancialService struct {
	adminRepo *repositories.AdminRepository
	logger    *zap.Logger
}

// NewAdminFinancialService creates a new admin financial service
func NewAdminFinancialService(adminRepo *repositories.AdminRepository, logger *zap.Logger) *AdminFinancialService {
	return &AdminFinancialService{
		adminRepo: adminRepo,
		logger:    logger,
	}
}

// GetFinancialDashboard retrieves comprehensive financial dashboard data
func (s *AdminFinancialService) GetFinancialDashboard(ctx context.Context, period string) (*models.AdminDashboard, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetFinancialDashboard completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate period
	if period == "" {
		period = "month"
	}
	validPeriods := map[string]bool{
		"day":   true,
		"week":  true,
		"month": true,
		"year":  true,
	}
	if !validPeriods[period] {
		return nil, fmt.Errorf("invalid period: %s", period)
	}

	// Get financial summary
	financialSummary, err := s.adminRepo.GetFinancialSummary(ctx, period)
	if err != nil {
		s.logger.Error("Failed to get financial summary", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve financial summary: %w", err)
	}

	// Get alerts
	alerts, err := s.adminRepo.GetAlerts(ctx, 10)
	if err != nil {
		s.logger.Error("Failed to get alerts", zap.Error(err))
		// Don't fail the entire request for alerts
		alerts = []models.Alert{}
	}

	// Get recent actions
	recentActions, err := s.adminRepo.GetRecentActions(ctx, 20)
	if err != nil {
		s.logger.Error("Failed to get recent actions", zap.Error(err))
		// Don't fail the entire request for actions
		recentActions = []models.RecentAction{}
	}

	// Get system health
	systemHealth, err := s.adminRepo.GetSystemHealth(ctx)
	if err != nil {
		s.logger.Error("Failed to get system health", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve system health: %w", err)
	}

	// Get wallet management summary
	walletManagement, err := s.adminRepo.GetWalletManagement(ctx, 1, 1)
	if err != nil {
		s.logger.Error("Failed to get wallet management", zap.Error(err))
		// Don't fail the entire request for wallet management
		walletManagement = &models.WalletManagementResponse{
			Summary: models.WalletManagement{},
		}
	}

	dashboard := &models.AdminDashboard{
		FinancialSummary: *financialSummary,
		Alerts:           alerts,
		RecentActions:    recentActions,
		SystemHealth:     *systemHealth,
		WalletManagement: walletManagement.Summary,
	}

	s.logger.Info("Successfully retrieved financial dashboard",
		zap.String("period", period),
		zap.Int("alerts_count", len(alerts)),
		zap.Int("actions_count", len(recentActions)))

	return dashboard, nil
}

// GetWalletManagement retrieves wallet management data
func (s *AdminFinancialService) GetWalletManagement(ctx context.Context, page, limit int) (*models.WalletManagementResponse, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetWalletManagement completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Get wallet management data from repository
	response, err := s.adminRepo.GetWalletManagement(ctx, page, limit)
	if err != nil {
		s.logger.Error("Failed to get wallet management from repository", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve wallet management: %w", err)
	}

	s.logger.Info("Successfully retrieved wallet management",
		zap.Int("wallets_count", len(response.Wallets)),
		zap.Int("total", response.Pagination.Total),
		zap.Int("page", page),
		zap.Int("limit", limit))

	return response, nil
}

// GetFinancialAlerts retrieves financial alerts
func (s *AdminFinancialService) GetFinancialAlerts(ctx context.Context, limit int) ([]models.Alert, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetFinancialAlerts completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate limit
	if limit < 1 || limit > 50 {
		limit = 10
	}

	// Get all alerts and filter for financial ones
	allAlerts, err := s.adminRepo.GetAlerts(ctx, limit*2) // Get more to filter
	if err != nil {
		s.logger.Error("Failed to get alerts from repository", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve alerts: %w", err)
	}

	// Filter for financial alerts
	var financialAlerts []models.Alert
	for _, alert := range allAlerts {
		if alert.Type == "financial" && len(financialAlerts) < limit {
			financialAlerts = append(financialAlerts, alert)
		}
	}

	s.logger.Info("Successfully retrieved financial alerts", zap.Int("count", len(financialAlerts)))
	return financialAlerts, nil
}

// GetRecentActions retrieves recent system actions
func (s *AdminFinancialService) GetRecentActions(ctx context.Context, limit int) ([]models.RecentAction, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetRecentActions completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate limit
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Get recent actions from repository
	actions, err := s.adminRepo.GetRecentActions(ctx, limit)
	if err != nil {
		s.logger.Error("Failed to get recent actions from repository", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve recent actions: %w", err)
	}

	s.logger.Info("Successfully retrieved recent actions", zap.Int("count", len(actions)))
	return actions, nil
}

// GetSystemHealth retrieves system health metrics
func (s *AdminFinancialService) GetSystemHealth(ctx context.Context) (*models.SystemHealth, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetSystemHealth completed", zap.Duration("duration", time.Since(start)))
	}()

	// Get system health from repository
	health, err := s.adminRepo.GetSystemHealth(ctx)
	if err != nil {
		s.logger.Error("Failed to get system health from repository", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve system health: %w", err)
	}

	s.logger.Info("Successfully retrieved system health",
		zap.String("status", health.Status),
		zap.Float64("uptime", health.Uptime),
		zap.Float64("error_rate", health.ErrorRate))

	return health, nil
}

// UpdateAlertStatus updates the status of an alert
func (s *AdminFinancialService) UpdateAlertStatus(ctx context.Context, alertID, status, resolvedBy string) error {
	start := time.Now()
	defer func() {
		s.logger.Info("UpdateAlertStatus completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if alertID == "" {
		return fmt.Errorf("alert ID is required")
	}
	if status == "" {
		return fmt.Errorf("status is required")
	}

	// Validate status
	validStatuses := map[string]bool{
		"active":    true,
		"resolved":  true,
		"dismissed": true,
	}
	if !validStatuses[status] {
		return fmt.Errorf("invalid status: %s", status)
	}

	// Update alert status in repository
	err := s.adminRepo.UpdateAlertStatus(ctx, alertID, status, resolvedBy)
	if err != nil {
		s.logger.Error("Failed to update alert status in repository", zap.Error(err), zap.String("alert_id", alertID))
		return fmt.Errorf("failed to update alert status: %w", err)
	}

	s.logger.Info("Successfully updated alert status",
		zap.String("alert_id", alertID),
		zap.String("status", status),
		zap.String("resolved_by", resolvedBy))

	return nil
}

// GetFinancialReports retrieves financial reports
func (s *AdminFinancialService) GetFinancialReports(ctx context.Context, reportType string, page, limit int) (*models.FinancialReportsResponse, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetFinancialReports completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if reportType == "" {
		reportType = "monthly"
	}
	validTypes := map[string]bool{
		"daily":   true,
		"weekly":  true,
		"monthly": true,
		"yearly":  true,
	}
	if !validTypes[reportType] {
		return nil, fmt.Errorf("invalid report type: %s", reportType)
	}

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 50 {
		limit = 20
	}

	// Get financial reports from repository
	response, err := s.adminRepo.GetFinancialReports(ctx, reportType, page, limit)
	if err != nil {
		s.logger.Error("Failed to get financial reports from repository", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve financial reports: %w", err)
	}

	s.logger.Info("Successfully retrieved financial reports",
		zap.String("report_type", reportType),
		zap.Int("reports_count", len(response.Reports)),
		zap.Int("total", response.Pagination.Total))

	return response, nil
}

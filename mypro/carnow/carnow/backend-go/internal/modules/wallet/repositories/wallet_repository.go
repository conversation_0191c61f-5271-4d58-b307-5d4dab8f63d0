package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"

	"carnow-backend/internal/modules/wallet/models"
	"carnow-backend/internal/shared/errors"
)

// WalletRepository handles wallet data access
type WalletRepository struct {
	db     *pgx.Conn
	logger *zap.Logger
}

// NewWalletRepository creates a new wallet repository
func NewWalletRepository(db *pgx.Conn, logger *zap.Logger) *WalletRepository {
	return &WalletRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new wallet
func (r *WalletRepository) Create(ctx context.Context, wallet *models.Wallet) (*models.Wallet, error) {
	wallet.ID = uuid.New()
	wallet.CreatedAt = time.Now()
	wallet.UpdatedAt = time.Now()

	query := `
		INSERT INTO wallets (id, user_id, balance, currency, status, is_verified, created_at, updated_at, is_deleted)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id, user_id, balance, currency, status, is_verified, created_at, updated_at, is_deleted
	`

	err := r.db.QueryRow(ctx, query,
		wallet.ID,
		wallet.UserID,
		wallet.Balance,
		wallet.Currency,
		wallet.Status,
		wallet.IsVerified,
		wallet.CreatedAt,
		wallet.UpdatedAt,
		wallet.IsDeleted,
	).Scan(
		&wallet.ID,
		&wallet.UserID,
		&wallet.Balance,
		&wallet.Currency,
		&wallet.Status,
		&wallet.IsVerified,
		&wallet.CreatedAt,
		&wallet.UpdatedAt,
		&wallet.IsDeleted,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create wallet: %w", err)
	}

	return wallet, nil
}

// GetByUserID retrieves a wallet by user ID
func (r *WalletRepository) GetByUserID(ctx context.Context, userID string) (*models.Wallet, error) {
	query := `
		SELECT id, user_id, balance, currency, status, is_verified, created_at, updated_at, is_deleted
		FROM wallets
		WHERE user_id = $1 AND is_deleted = false
	`

	var wallet models.Wallet
	err := r.db.QueryRow(ctx, query, userID).Scan(
		&wallet.ID,
		&wallet.UserID,
		&wallet.Balance,
		&wallet.Currency,
		&wallet.Status,
		&wallet.IsVerified,
		&wallet.CreatedAt,
		&wallet.UpdatedAt,
		&wallet.IsDeleted,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("wallet not found")
		}
		return nil, fmt.Errorf("failed to get wallet: %w", err)
	}

	return &wallet, nil
}

// Update updates a wallet
func (r *WalletRepository) Update(ctx context.Context, wallet *models.Wallet) error {
	wallet.UpdatedAt = time.Now()

	query := `
		UPDATE wallets
		SET balance = $2, currency = $3, status = $4, is_verified = $5, updated_at = $6, is_deleted = $7
		WHERE id = $1
	`

	result, err := r.db.Exec(ctx, query,
		wallet.ID,
		wallet.Balance,
		wallet.Currency,
		wallet.Status,
		wallet.IsVerified,
		wallet.UpdatedAt,
		wallet.IsDeleted,
	)

	if err != nil {
		return fmt.Errorf("failed to update wallet: %w", err)
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("wallet not found")
	}

	return nil
}

// GetLimits retrieves wallet limits
func (r *WalletRepository) GetLimits(ctx context.Context, walletID uuid.UUID) (*models.WalletLimits, error) {
	query := `
		SELECT id, wallet_id, daily_deposit_limit, daily_withdrawal_limit, monthly_deposit_limit, 
		       monthly_withdrawal_limit, max_balance, min_transaction_amount, max_transaction_amount, 
		       created_at, updated_at
		FROM wallet_limits
		WHERE wallet_id = $1
	`

	var limits models.WalletLimits
	err := r.db.QueryRow(ctx, query, walletID).Scan(
		&limits.ID,
		&limits.WalletID,
		&limits.DailyDepositLimit,
		&limits.DailyWithdrawalLimit,
		&limits.MonthlyDepositLimit,
		&limits.MonthlyWithdrawalLimit,
		&limits.MaxBalance,
		&limits.MinTransactionAmount,
		&limits.MaxTransactionAmount,
		&limits.CreatedAt,
		&limits.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("wallet limits not found")
		}
		return nil, fmt.Errorf("failed to get wallet limits: %w", err)
	}

	return &limits, nil
}

// CreateLimits creates wallet limits
func (r *WalletRepository) CreateLimits(ctx context.Context, limits *models.WalletLimits) (*models.WalletLimits, error) {
	limits.ID = uuid.New()
	limits.CreatedAt = time.Now()
	limits.UpdatedAt = time.Now()

	query := `
		INSERT INTO wallet_limits (id, wallet_id, daily_deposit_limit, daily_withdrawal_limit, 
		                          monthly_deposit_limit, monthly_withdrawal_limit, max_balance, 
		                          min_transaction_amount, max_transaction_amount, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		RETURNING id, wallet_id, daily_deposit_limit, daily_withdrawal_limit, monthly_deposit_limit, 
		          monthly_withdrawal_limit, max_balance, min_transaction_amount, max_transaction_amount, 
		          created_at, updated_at
	`

	err := r.db.QueryRow(ctx, query,
		limits.ID,
		limits.WalletID,
		limits.DailyDepositLimit,
		limits.DailyWithdrawalLimit,
		limits.MonthlyDepositLimit,
		limits.MonthlyWithdrawalLimit,
		limits.MaxBalance,
		limits.MinTransactionAmount,
		limits.MaxTransactionAmount,
		limits.CreatedAt,
		limits.UpdatedAt,
	).Scan(
		&limits.ID,
		&limits.WalletID,
		&limits.DailyDepositLimit,
		&limits.DailyWithdrawalLimit,
		&limits.MonthlyDepositLimit,
		&limits.MonthlyWithdrawalLimit,
		&limits.MaxBalance,
		&limits.MinTransactionAmount,
		&limits.MaxTransactionAmount,
		&limits.CreatedAt,
		&limits.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create wallet limits: %w", err)
	}

	return limits, nil
}

// GetSettings retrieves wallet settings
func (r *WalletRepository) GetSettings(ctx context.Context, walletID uuid.UUID) (*models.WalletSettings, error) {
	query := `
		SELECT id, wallet_id, email_notifications, push_notifications, sms_notifications,
		       transaction_alerts, balance_alerts, security_alerts, auto_recharge,
		       auto_recharge_amount, auto_recharge_threshold, created_at, updated_at
		FROM wallet_settings
		WHERE wallet_id = $1
	`

	var settings models.WalletSettings
	err := r.db.QueryRow(ctx, query, walletID).Scan(
		&settings.ID,
		&settings.WalletID,
		&settings.EmailNotifications,
		&settings.PushNotifications,
		&settings.SMSNotifications,
		&settings.TransactionAlerts,
		&settings.BalanceAlerts,
		&settings.SecurityAlerts,
		&settings.AutoRecharge,
		&settings.AutoRechargeAmount,
		&settings.AutoRechargeThreshold,
		&settings.CreatedAt,
		&settings.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("wallet settings not found")
		}
		return nil, fmt.Errorf("failed to get wallet settings: %w", err)
	}

	return &settings, nil
}

// CreateSettings creates wallet settings
func (r *WalletRepository) CreateSettings(ctx context.Context, settings *models.WalletSettings) (*models.WalletSettings, error) {
	settings.ID = uuid.New()
	settings.CreatedAt = time.Now()
	settings.UpdatedAt = time.Now()

	query := `
		INSERT INTO wallet_settings (id, wallet_id, email_notifications, push_notifications, sms_notifications,
		                            transaction_alerts, balance_alerts, security_alerts, auto_recharge,
		                            auto_recharge_amount, auto_recharge_threshold, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
		RETURNING id, wallet_id, email_notifications, push_notifications, sms_notifications,
		          transaction_alerts, balance_alerts, security_alerts, auto_recharge,
		          auto_recharge_amount, auto_recharge_threshold, created_at, updated_at
	`

	err := r.db.QueryRow(ctx, query,
		settings.ID,
		settings.WalletID,
		settings.EmailNotifications,
		settings.PushNotifications,
		settings.SMSNotifications,
		settings.TransactionAlerts,
		settings.BalanceAlerts,
		settings.SecurityAlerts,
		settings.AutoRecharge,
		settings.AutoRechargeAmount,
		settings.AutoRechargeThreshold,
		settings.CreatedAt,
		settings.UpdatedAt,
	).Scan(
		&settings.ID,
		&settings.WalletID,
		&settings.EmailNotifications,
		&settings.PushNotifications,
		&settings.SMSNotifications,
		&settings.TransactionAlerts,
		&settings.BalanceAlerts,
		&settings.SecurityAlerts,
		&settings.AutoRecharge,
		&settings.AutoRechargeAmount,
		&settings.AutoRechargeThreshold,
		&settings.CreatedAt,
		&settings.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create wallet settings: %w", err)
	}

	return settings, nil
}

// UpdateSettings updates wallet settings
func (r *WalletRepository) UpdateSettings(ctx context.Context, settings *models.WalletSettings) (*models.WalletSettings, error) {
	settings.UpdatedAt = time.Now()

	query := `
		UPDATE wallet_settings
		SET email_notifications = $2, push_notifications = $3, sms_notifications = $4,
		    transaction_alerts = $5, balance_alerts = $6, security_alerts = $7, auto_recharge = $8,
		    auto_recharge_amount = $9, auto_recharge_threshold = $10, updated_at = $11
		WHERE id = $1
		RETURNING id, wallet_id, email_notifications, push_notifications, sms_notifications,
		          transaction_alerts, balance_alerts, security_alerts, auto_recharge,
		          auto_recharge_amount, auto_recharge_threshold, created_at, updated_at
	`

	err := r.db.QueryRow(ctx, query,
		settings.ID,
		settings.EmailNotifications,
		settings.PushNotifications,
		settings.SMSNotifications,
		settings.TransactionAlerts,
		settings.BalanceAlerts,
		settings.SecurityAlerts,
		settings.AutoRecharge,
		settings.AutoRechargeAmount,
		settings.AutoRechargeThreshold,
		settings.UpdatedAt,
	).Scan(
		&settings.ID,
		&settings.WalletID,
		&settings.EmailNotifications,
		&settings.PushNotifications,
		&settings.SMSNotifications,
		&settings.TransactionAlerts,
		&settings.BalanceAlerts,
		&settings.SecurityAlerts,
		&settings.AutoRecharge,
		&settings.AutoRechargeAmount,
		&settings.AutoRechargeThreshold,
		&settings.CreatedAt,
		&settings.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("wallet settings not found")
		}
		return nil, fmt.Errorf("failed to update wallet settings: %w", err)
	}

	return settings, nil
}

package repositories

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"carnow-backend/internal/modules/orders/models"
)

// OrderRepository handles database operations for orders
type OrderRepository struct {
	db *sql.DB
}

// NewOrderRepository creates a new order repository
func NewOrderRepository(db *sql.DB) *OrderRepository {
	return &OrderRepository{
		db: db,
	}
}

// GetAllOrders retrieves all orders for a user with filtering and pagination
func (r *OrderRepository) GetAllOrders(ctx context.Context, userID string, page, limit int, filter models.OrderFilter) ([]models.Order, int, error) {
	// Build the base query
	baseQuery := `
		SELECT id, user_id, product_id, quantity, total_amount, status, payment_status, 
		       shipping_address, billing_address, notes, is_deleted, created_at, updated_at, version
		FROM orders 
		WHERE user_id = $1 AND is_deleted = false
	`

	// Build WHERE clause for filters
	var conditions []string
	var args []interface{}
	args = append(args, userID)
	argIndex := 2

	if filter.Status != "" {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, filter.Status)
		argIndex++
	}

	if filter.PaymentStatus != "" {
		conditions = append(conditions, fmt.Sprintf("payment_status = $%d", argIndex))
		args = append(args, filter.PaymentStatus)
		argIndex++
	}

	if filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(notes ILIKE $%d OR shipping_address ILIKE $%d)",
			argIndex, argIndex))
		args = append(args, "%"+filter.Search+"%")
		argIndex++
	}

	if filter.StartDate != "" {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, filter.StartDate)
		argIndex++
	}

	if filter.EndDate != "" {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, filter.EndDate)
		argIndex++
	}

	// Add conditions to base query
	if len(conditions) > 0 {
		baseQuery += " AND " + strings.Join(conditions, " AND ")
	}

	// Get total count
	countQuery := strings.Replace(baseQuery, "SELECT id, user_id, product_id, quantity, total_amount, status, payment_status, shipping_address, billing_address, notes, is_deleted, created_at, updated_at, version", "SELECT COUNT(*)", 1)

	var total int
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count orders: %w", err)
	}

	// Add sorting and pagination
	query := baseQuery + fmt.Sprintf(" ORDER BY created_at DESC LIMIT $%d OFFSET $%d",
		argIndex, argIndex+1)

	offset := (page - 1) * limit
	args = append(args, limit, offset)

	// Execute query
	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query orders: %w", err)
	}
	defer rows.Close()

	var orders []models.Order
	for rows.Next() {
		var order models.Order

		err := rows.Scan(
			&order.ID, &order.UserID, &order.ProductID, &order.Quantity, &order.TotalAmount,
			&order.Status, &order.PaymentStatus, &order.ShippingAddress, &order.BillingAddress,
			&order.Notes, &order.IsDeleted, &order.CreatedAt, &order.UpdatedAt, &order.Version,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan order: %w", err)
		}

		orders = append(orders, order)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating orders: %w", err)
	}

	return orders, total, nil
}

// GetActiveOrders retrieves active orders (pending, confirmed, shipped) for a user
func (r *OrderRepository) GetActiveOrders(ctx context.Context, userID string, page, limit int) ([]models.Order, int, error) {
	query := `
		SELECT id, user_id, product_id, quantity, total_amount, status, payment_status, 
		       shipping_address, billing_address, notes, is_deleted, created_at, updated_at, version
		FROM orders 
		WHERE user_id = $1 AND is_deleted = false AND status IN ('pending', 'confirmed', 'shipped')
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	offset := (page - 1) * limit
	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query active orders: %w", err)
	}
	defer rows.Close()

	var orders []models.Order
	for rows.Next() {
		var order models.Order

		err := rows.Scan(
			&order.ID, &order.UserID, &order.ProductID, &order.Quantity, &order.TotalAmount,
			&order.Status, &order.PaymentStatus, &order.ShippingAddress, &order.BillingAddress,
			&order.Notes, &order.IsDeleted, &order.CreatedAt, &order.UpdatedAt, &order.Version,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan order: %w", err)
		}

		orders = append(orders, order)
	}

	// Get total count for active orders
	countQuery := `
		SELECT COUNT(*) 
		FROM orders 
		WHERE user_id = $1 AND is_deleted = false AND status IN ('pending', 'confirmed', 'shipped')
	`
	var total int
	err = r.db.QueryRowContext(ctx, countQuery, userID).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count active orders: %w", err)
	}

	return orders, total, nil
}

// GetOrderByID retrieves an order by ID
func (r *OrderRepository) GetOrderByID(ctx context.Context, orderID string) (*models.Order, error) {
	query := `
		SELECT id, user_id, product_id, quantity, total_amount, status, payment_status, 
		       shipping_address, billing_address, notes, is_deleted, created_at, updated_at, version
		FROM orders 
		WHERE id = $1 AND is_deleted = false
	`

	var order models.Order
	err := r.db.QueryRowContext(ctx, query, orderID).Scan(
		&order.ID, &order.UserID, &order.ProductID, &order.Quantity, &order.TotalAmount,
		&order.Status, &order.PaymentStatus, &order.ShippingAddress, &order.BillingAddress,
		&order.Notes, &order.IsDeleted, &order.CreatedAt, &order.UpdatedAt, &order.Version,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	return &order, nil
}

// CreateOrder creates a new order
func (r *OrderRepository) CreateOrder(ctx context.Context, order *models.Order) error {
	query := `
		INSERT INTO orders (id, user_id, product_id, quantity, total_amount, status, payment_status, 
		                   shipping_address, billing_address, notes, is_deleted, created_at, updated_at, version)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
	`

	_, err := r.db.ExecContext(ctx, query,
		order.ID, order.UserID, order.ProductID, order.Quantity, order.TotalAmount,
		order.Status, order.PaymentStatus, order.ShippingAddress, order.BillingAddress,
		order.Notes, order.IsDeleted, order.CreatedAt, order.UpdatedAt, order.Version,
	)
	if err != nil {
		return fmt.Errorf("failed to create order: %w", err)
	}

	return nil
}

// UpdateOrderStatus updates the status of an order
func (r *OrderRepository) UpdateOrderStatus(ctx context.Context, orderID, status string) error {
	query := `
		UPDATE orders 
		SET status = $1, updated_at = $2, version = version + 1
		WHERE id = $3 AND is_deleted = false
	`

	result, err := r.db.ExecContext(ctx, query, status, time.Now(), orderID)
	if err != nil {
		return fmt.Errorf("failed to update order status: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("order not found or already deleted")
	}

	return nil
}

// CancelOrder cancels an order
func (r *OrderRepository) CancelOrder(ctx context.Context, orderID string) error {
	query := `
		UPDATE orders 
		SET status = 'cancelled', updated_at = $1, version = version + 1
		WHERE id = $2 AND is_deleted = false AND status IN ('pending', 'confirmed')
	`

	result, err := r.db.ExecContext(ctx, query, time.Now(), orderID)
	if err != nil {
		return fmt.Errorf("failed to cancel order: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("order not found or cannot be cancelled")
	}

	return nil
}

// GetOrderStatistics retrieves order statistics for a user
func (r *OrderRepository) GetOrderStatistics(ctx context.Context, userID string) (*models.OrderStatistics, error) {
	// Get basic counts
	countQuery := `
		SELECT 
			COUNT(*) as total_orders,
			COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
			COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_orders,
			COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped_orders,
			COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
			COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
			COALESCE(SUM(total_amount), 0) as total_revenue,
			COALESCE(AVG(total_amount), 0) as average_order_value
		FROM orders 
		WHERE user_id = $1 AND is_deleted = false
	`

	var stats models.OrderStatistics
	err := r.db.QueryRowContext(ctx, countQuery, userID).Scan(
		&stats.TotalOrders, &stats.PendingOrders, &stats.ConfirmedOrders,
		&stats.ShippedOrders, &stats.DeliveredOrders, &stats.CancelledOrders,
		&stats.TotalRevenue, &stats.AverageOrderValue,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get order statistics: %w", err)
	}

	// Get this month's statistics
	monthQuery := `
		SELECT 
			COUNT(*) as orders_this_month,
			COALESCE(SUM(total_amount), 0) as revenue_this_month
		FROM orders 
		WHERE user_id = $1 AND is_deleted = false 
		AND created_at >= date_trunc('month', CURRENT_DATE)
	`

	err = r.db.QueryRowContext(ctx, monthQuery, userID).Scan(
		&stats.OrdersThisMonth, &stats.RevenueThisMonth,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get monthly statistics: %w", err)
	}

	return &stats, nil
}

// GetOrderTracking retrieves tracking information for an order
func (r *OrderRepository) GetOrderTracking(ctx context.Context, orderID string) ([]models.OrderTracking, error) {
	query := `
		SELECT id, order_id, status, location, description, timestamp
		FROM order_tracking 
		WHERE order_id = $1
		ORDER BY timestamp DESC
	`

	rows, err := r.db.QueryContext(ctx, query, orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to query order tracking: %w", err)
	}
	defer rows.Close()

	var tracking []models.OrderTracking
	for rows.Next() {
		var track models.OrderTracking

		err := rows.Scan(
			&track.ID, &track.OrderID, &track.Status, &track.Location,
			&track.Description, &track.Timestamp,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan tracking: %w", err)
		}

		tracking = append(tracking, track)
	}

	return tracking, nil
}

package services

import (
	"context"
	"fmt"
	"time"

	"carnow-backend/internal/modules/orders/models"
	"carnow-backend/internal/modules/orders/repositories"

	"go.uber.org/zap"
)

// OrderService handles order business logic
type OrderService struct {
	orderRepo *repositories.OrderRepository
	logger    *zap.Logger
}

// NewOrderService creates a new order service
func NewOrderService(orderRepo *repositories.OrderRepository, logger *zap.Logger) *OrderService {
	return &OrderService{
		orderRepo: orderRepo,
		logger:    logger,
	}
}

// GetAllOrders retrieves all orders for a user with filtering and pagination
func (s *OrderService) GetAllOrders(ctx context.Context, userID string, page, limit int, filter models.OrderFilter) ([]models.Order, int, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetAllOrders completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if userID == "" {
		return nil, 0, fmt.Errorf("user ID is required")
	}
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Validate filter parameters
	if err := s.validateOrderFilter(filter); err != nil {
		s.logger.Error("Invalid order filter", zap.Error(err))
		return nil, 0, fmt.Errorf("invalid filter parameters: %w", err)
	}

	// Get orders from repository
	orders, total, err := s.orderRepo.GetAllOrders(ctx, userID, page, limit, filter)
	if err != nil {
		s.logger.Error("Failed to get orders from repository", zap.Error(err), zap.String("user_id", userID))
		return nil, 0, fmt.Errorf("failed to retrieve orders: %w", err)
	}

	s.logger.Info("Successfully retrieved orders",
		zap.String("user_id", userID),
		zap.Int("count", len(orders)),
		zap.Int("total", total),
		zap.Int("page", page),
		zap.Int("limit", limit))

	return orders, total, nil
}

// GetActiveOrders retrieves active orders for a user
func (s *OrderService) GetActiveOrders(ctx context.Context, userID string, page, limit int) ([]models.Order, int, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetActiveOrders completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if userID == "" {
		return nil, 0, fmt.Errorf("user ID is required")
	}
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Get active orders from repository
	orders, total, err := s.orderRepo.GetActiveOrders(ctx, userID, page, limit)
	if err != nil {
		s.logger.Error("Failed to get active orders from repository", zap.Error(err), zap.String("user_id", userID))
		return nil, 0, fmt.Errorf("failed to retrieve active orders: %w", err)
	}

	s.logger.Info("Successfully retrieved active orders",
		zap.String("user_id", userID),
		zap.Int("count", len(orders)),
		zap.Int("total", total))

	return orders, total, nil
}

// GetOrderByID retrieves an order by ID
func (s *OrderService) GetOrderByID(ctx context.Context, orderID string) (*models.Order, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetOrderByID completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate order ID
	if orderID == "" {
		return nil, fmt.Errorf("order ID is required")
	}

	// Get order from repository
	order, err := s.orderRepo.GetOrderByID(ctx, orderID)
	if err != nil {
		s.logger.Error("Failed to get order by ID", zap.Error(err), zap.String("order_id", orderID))
		return nil, fmt.Errorf("failed to retrieve order: %w", err)
	}

	if order == nil {
		s.logger.Warn("Order not found", zap.String("order_id", orderID))
		return nil, fmt.Errorf("order not found")
	}

	s.logger.Info("Successfully retrieved order", zap.String("order_id", orderID))
	return order, nil
}

// CreateOrder creates a new order
func (s *OrderService) CreateOrder(ctx context.Context, userID, productID string, quantity int, totalAmount float64, shippingAddress, billingAddress, notes string) (*models.Order, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("CreateOrder completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if userID == "" {
		return nil, fmt.Errorf("user ID is required")
	}
	if productID == "" {
		return nil, fmt.Errorf("product ID is required")
	}
	if quantity <= 0 {
		return nil, fmt.Errorf("quantity must be greater than 0")
	}
	if totalAmount <= 0 {
		return nil, fmt.Errorf("total amount must be greater than 0")
	}

	// Create new order
	order := models.NewOrder(userID, productID, quantity, totalAmount)
	order.ShippingAddress = shippingAddress
	order.BillingAddress = billingAddress
	order.Notes = notes

	// Save order to repository
	err := s.orderRepo.CreateOrder(ctx, order)
	if err != nil {
		s.logger.Error("Failed to create order in repository", zap.Error(err), zap.String("user_id", userID))
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	s.logger.Info("Successfully created order",
		zap.String("order_id", order.ID),
		zap.String("user_id", userID),
		zap.String("product_id", productID),
		zap.Float64("total_amount", totalAmount))

	return order, nil
}

// UpdateOrderStatus updates the status of an order
func (s *OrderService) UpdateOrderStatus(ctx context.Context, orderID, status string) error {
	start := time.Now()
	defer func() {
		s.logger.Info("UpdateOrderStatus completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if orderID == "" {
		return fmt.Errorf("order ID is required")
	}
	if status == "" {
		return fmt.Errorf("status is required")
	}

	// Validate status
	validStatuses := map[string]bool{
		"pending":   true,
		"confirmed": true,
		"shipped":   true,
		"delivered": true,
		"cancelled": true,
	}
	if !validStatuses[status] {
		return fmt.Errorf("invalid status: %s", status)
	}

	// Update order status in repository
	err := s.orderRepo.UpdateOrderStatus(ctx, orderID, status)
	if err != nil {
		s.logger.Error("Failed to update order status in repository", zap.Error(err), zap.String("order_id", orderID))
		return fmt.Errorf("failed to update order status: %w", err)
	}

	s.logger.Info("Successfully updated order status",
		zap.String("order_id", orderID),
		zap.String("status", status))

	return nil
}

// CancelOrder cancels an order
func (s *OrderService) CancelOrder(ctx context.Context, orderID string) error {
	start := time.Now()
	defer func() {
		s.logger.Info("CancelOrder completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate order ID
	if orderID == "" {
		return fmt.Errorf("order ID is required")
	}

	// Cancel order in repository
	err := s.orderRepo.CancelOrder(ctx, orderID)
	if err != nil {
		s.logger.Error("Failed to cancel order in repository", zap.Error(err), zap.String("order_id", orderID))
		return fmt.Errorf("failed to cancel order: %w", err)
	}

	s.logger.Info("Successfully cancelled order", zap.String("order_id", orderID))
	return nil
}

// GetOrderStatistics retrieves order statistics for a user
func (s *OrderService) GetOrderStatistics(ctx context.Context, userID string) (*models.OrderStatistics, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetOrderStatistics completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate user ID
	if userID == "" {
		return nil, fmt.Errorf("user ID is required")
	}

	// Get statistics from repository
	stats, err := s.orderRepo.GetOrderStatistics(ctx, userID)
	if err != nil {
		s.logger.Error("Failed to get order statistics from repository", zap.Error(err), zap.String("user_id", userID))
		return nil, fmt.Errorf("failed to retrieve order statistics: %w", err)
	}

	s.logger.Info("Successfully retrieved order statistics",
		zap.String("user_id", userID),
		zap.Int("total_orders", stats.TotalOrders),
		zap.Float64("total_revenue", stats.TotalRevenue))

	return stats, nil
}

// TrackOrder retrieves tracking information for an order
func (s *OrderService) TrackOrder(ctx context.Context, orderID string) ([]models.OrderTracking, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("TrackOrder completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate order ID
	if orderID == "" {
		return nil, fmt.Errorf("order ID is required")
	}

	// Get tracking information from repository
	tracking, err := s.orderRepo.GetOrderTracking(ctx, orderID)
	if err != nil {
		s.logger.Error("Failed to get order tracking from repository", zap.Error(err), zap.String("order_id", orderID))
		return nil, fmt.Errorf("failed to retrieve order tracking: %w", err)
	}

	s.logger.Info("Successfully retrieved order tracking",
		zap.String("order_id", orderID),
		zap.Int("tracking_entries", len(tracking)))

	return tracking, nil
}

// validateOrderFilter validates order filter parameters
func (s *OrderService) validateOrderFilter(filter models.OrderFilter) error {
	// Validate status
	if filter.Status != "" {
		validStatuses := map[string]bool{
			"pending":   true,
			"confirmed": true,
			"shipped":   true,
			"delivered": true,
			"cancelled": true,
		}
		if !validStatuses[filter.Status] {
			return fmt.Errorf("invalid status: %s", filter.Status)
		}
	}

	// Validate payment status
	if filter.PaymentStatus != "" {
		validPaymentStatuses := map[string]bool{
			"pending":  true,
			"paid":     true,
			"failed":   true,
			"refunded": true,
		}
		if !validPaymentStatuses[filter.PaymentStatus] {
			return fmt.Errorf("invalid payment status: %s", filter.PaymentStatus)
		}
	}

	// Validate date format if provided
	if filter.StartDate != "" {
		if _, err := time.Parse("2006-01-02", filter.StartDate); err != nil {
			return fmt.Errorf("invalid start date format: %s", filter.StartDate)
		}
	}

	if filter.EndDate != "" {
		if _, err := time.Parse("2006-01-02", filter.EndDate); err != nil {
			return fmt.Errorf("invalid end date format: %s", filter.EndDate)
		}
	}

	return nil
}

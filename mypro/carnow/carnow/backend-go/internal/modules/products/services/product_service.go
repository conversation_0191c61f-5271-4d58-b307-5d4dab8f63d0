package services

import (
	"context"
	"fmt"
	"time"

	"carnow-backend/internal/modules/products/models"
	"carnow-backend/internal/modules/products/repositories"

	"go.uber.org/zap"
)

// ProductService handles product business logic
type ProductService struct {
	productRepo *repositories.ProductRepository
	logger      *zap.Logger
}

// NewProductService creates a new product service
func NewProductService(productRepo *repositories.ProductRepository, logger *zap.Logger) *ProductService {
	return &ProductService{
		productRepo: productRepo,
		logger:      logger,
	}
}

// GetAllProducts retrieves all products with filtering and pagination
func (s *ProductService) GetAllProducts(ctx context.Context, page, limit int, filter models.ProductFilter) ([]models.Product, int, error) {
	// Start performance monitoring
	start := time.Now()
	defer func() {
		s.logger.Info("GetAllProducts completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Validate filter parameters
	if err := s.validateProductFilter(filter); err != nil {
		s.logger.Error("Invalid product filter", zap.Error(err))
		return nil, 0, fmt.Errorf("invalid filter parameters: %w", err)
	}

	// Get products from repository
	products, total, err := s.productRepo.GetAllProducts(ctx, page, limit, filter)
	if err != nil {
		s.logger.Error("Failed to get products from repository", zap.Error(err))
		return nil, 0, fmt.Errorf("failed to retrieve products: %w", err)
	}

	// Log successful operation
	s.logger.Info("Successfully retrieved products",
		zap.Int("count", len(products)),
		zap.Int("total", total),
		zap.Int("page", page),
		zap.Int("limit", limit))

	return products, total, nil
}

// GetProductByID retrieves a product by ID
func (s *ProductService) GetProductByID(ctx context.Context, id string) (*models.Product, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetProductByID completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate product ID
	if id == "" {
		return nil, fmt.Errorf("product ID is required")
	}

	// Get product from repository
	product, err := s.productRepo.GetProductByID(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get product by ID", zap.Error(err), zap.String("product_id", id))
		return nil, fmt.Errorf("failed to retrieve product: %w", err)
	}

	if product == nil {
		s.logger.Warn("Product not found", zap.String("product_id", id))
		return nil, fmt.Errorf("product not found")
	}

	s.logger.Info("Successfully retrieved product", zap.String("product_id", id))
	return product, nil
}

// GetProductsByCategory retrieves products by category ID
func (s *ProductService) GetProductsByCategory(ctx context.Context, categoryID string, page, limit int) ([]models.Product, int, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetProductsByCategory completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if categoryID == "" {
		return nil, 0, fmt.Errorf("category ID is required")
	}
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Get products from repository
	products, total, err := s.productRepo.GetProductsByCategory(ctx, categoryID, page, limit)
	if err != nil {
		s.logger.Error("Failed to get products by category", zap.Error(err), zap.String("category_id", categoryID))
		return nil, 0, fmt.Errorf("failed to retrieve products by category: %w", err)
	}

	s.logger.Info("Successfully retrieved products by category",
		zap.String("category_id", categoryID),
		zap.Int("count", len(products)),
		zap.Int("total", total))

	return products, total, nil
}

// SearchProducts searches products by term
func (s *ProductService) SearchProducts(ctx context.Context, searchTerm string, page, limit int) ([]models.Product, int, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("SearchProducts completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if searchTerm == "" {
		return nil, 0, fmt.Errorf("search term is required")
	}
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Create filter for search
	filter := models.ProductFilter{
		Search: searchTerm,
	}

	// Get products from repository
	products, total, err := s.productRepo.GetAllProducts(ctx, page, limit, filter)
	if err != nil {
		s.logger.Error("Failed to search products", zap.Error(err), zap.String("search_term", searchTerm))
		return nil, 0, fmt.Errorf("failed to search products: %w", err)
	}

	s.logger.Info("Successfully searched products",
		zap.String("search_term", searchTerm),
		zap.Int("count", len(products)),
		zap.Int("total", total))

	return products, total, nil
}

// GetFeaturedProducts retrieves featured products
func (s *ProductService) GetFeaturedProducts(ctx context.Context, limit int) ([]models.Product, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetFeaturedProducts completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate limit
	if limit < 1 || limit > 50 {
		limit = 10
	}

	// Get featured products from repository
	products, err := s.productRepo.GetFeaturedProducts(ctx, limit)
	if err != nil {
		s.logger.Error("Failed to get featured products", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve featured products: %w", err)
	}

	s.logger.Info("Successfully retrieved featured products", zap.Int("count", len(products)))
	return products, nil
}

// GetProductCategories retrieves all product categories
func (s *ProductService) GetProductCategories(ctx context.Context) ([]models.Category, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetProductCategories completed", zap.Duration("duration", time.Since(start)))
	}()

	// Get categories from repository
	categories, err := s.productRepo.GetAllCategories(ctx)
	if err != nil {
		s.logger.Error("Failed to get product categories", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve categories: %w", err)
	}

	s.logger.Info("Successfully retrieved product categories", zap.Int("count", len(categories)))
	return categories, nil
}

// GetProductRecommendations retrieves product recommendations
func (s *ProductService) GetProductRecommendations(ctx context.Context, productID string, limit int) ([]models.Product, error) {
	start := time.Now()
	defer func() {
		s.logger.Info("GetProductRecommendations completed", zap.Duration("duration", time.Since(start)))
	}()

	// Validate input parameters
	if productID == "" {
		return nil, fmt.Errorf("product ID is required")
	}
	if limit < 1 || limit > 20 {
		limit = 5
	}

	// Get the base product to understand its category
	baseProduct, err := s.productRepo.GetProductByID(ctx, productID)
	if err != nil {
		s.logger.Error("Failed to get base product for recommendations", zap.Error(err), zap.String("product_id", productID))
		return nil, fmt.Errorf("failed to get base product: %w", err)
	}

	if baseProduct == nil {
		return nil, fmt.Errorf("base product not found")
	}

	// Get products from the same category (simple recommendation logic)
	filter := models.ProductFilter{
		CategoryID: baseProduct.CategoryID,
	}

	products, _, err := s.productRepo.GetAllProducts(ctx, 1, limit, filter)
	if err != nil {
		s.logger.Error("Failed to get product recommendations", zap.Error(err), zap.String("product_id", productID))
		return nil, fmt.Errorf("failed to retrieve recommendations: %w", err)
	}

	// Filter out the base product from recommendations
	var recommendations []models.Product
	for _, product := range products {
		if product.ID != productID {
			recommendations = append(recommendations, product)
		}
	}

	s.logger.Info("Successfully retrieved product recommendations",
		zap.String("product_id", productID),
		zap.Int("count", len(recommendations)))

	return recommendations, nil
}

// validateProductFilter validates product filter parameters
func (s *ProductService) validateProductFilter(filter models.ProductFilter) error {
	// Validate sort by field
	if filter.SortBy != "" {
		validSortFields := map[string]bool{
			"name":       true,
			"price":      true,
			"created_at": true,
			"brand":      true,
			"year":       true,
		}
		if !validSortFields[filter.SortBy] {
			return fmt.Errorf("invalid sort field: %s", filter.SortBy)
		}
	}

	// Validate sort order
	if filter.SortOrder != "" {
		if filter.SortOrder != "asc" && filter.SortOrder != "desc" {
			return fmt.Errorf("invalid sort order: %s", filter.SortOrder)
		}
	}

	// Validate condition
	if filter.Condition != "" {
		validConditions := map[string]bool{
			"new":         true,
			"used":        true,
			"refurbished": true,
		}
		if !validConditions[filter.Condition] {
			return fmt.Errorf("invalid condition: %s", filter.Condition)
		}
	}

	// Validate price range
	if filter.MinPrice > 0 && filter.MaxPrice > 0 {
		if filter.MinPrice > filter.MaxPrice {
			return fmt.Errorf("minimum price cannot be greater than maximum price")
		}
	}

	return nil
}

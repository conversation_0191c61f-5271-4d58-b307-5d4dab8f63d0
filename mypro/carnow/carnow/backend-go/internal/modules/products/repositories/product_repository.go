package repositories

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"carnow-backend/internal/modules/products/models"
)

// ProductRepository handles database operations for products
type ProductRepository struct {
	db *sql.DB
}

// NewProductRepository creates a new product repository
func NewProductRepository(db *sql.DB) *ProductRepository {
	return &ProductRepository{
		db: db,
	}
}

// GetAllProducts retrieves all products with filtering and pagination
func (r *ProductRepository) GetAllProducts(ctx context.Context, page, limit int, filter models.ProductFilter) ([]models.Product, int, error) {
	// Build the base query
	baseQuery := `
		SELECT id, name_en, name_ar, description_en, price, main_category_id, brand, condition_type, 
		       stock_quantity, is_active, created_at, updated_at
		FROM "Products" 
		WHERE is_active = true
	`

	// Build WHERE clause for filters
	var conditions []string
	var args []interface{}
	argIndex := 1

	if filter.CategoryID != "" {
		conditions = append(conditions, fmt.Sprintf("main_category_id = $%d", argIndex))
		args = append(args, filter.CategoryID)
		argIndex++
	}

	if filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(name_en ILIKE $%d OR name_ar ILIKE $%d OR description_en ILIKE $%d OR brand ILIKE $%d)",
			argIndex, argIndex+1, argIndex+2, argIndex+3))
		searchTerm := "%" + filter.Search + "%"
		args = append(args, searchTerm, searchTerm, searchTerm, searchTerm)
		argIndex += 4
	}

	if filter.MinPrice > 0 {
		conditions = append(conditions, fmt.Sprintf("price >= $%d", argIndex))
		args = append(args, filter.MinPrice)
		argIndex++
	}

	if filter.MaxPrice > 0 {
		conditions = append(conditions, fmt.Sprintf("price <= $%d", argIndex))
		args = append(args, filter.MaxPrice)
		argIndex++
	}

	if filter.Condition != "" {
		conditions = append(conditions, fmt.Sprintf("condition_type = $%d", argIndex))
		args = append(args, filter.Condition)
		argIndex++
	}

	if filter.Brand != "" {
		conditions = append(conditions, fmt.Sprintf("brand = $%d", argIndex))
		args = append(args, filter.Brand)
		argIndex++
	}

	// Note: year field doesn't exist in Products table, removing this filter
	// if filter.Year > 0 {
	// 	conditions = append(conditions, fmt.Sprintf("year = $%d", argIndex))
	// 	args = append(args, filter.Year)
	// 	argIndex++
	// }

	// Add conditions to base query
	if len(conditions) > 0 {
		baseQuery += " AND " + strings.Join(conditions, " AND ")
	}

	// Get total count
	countQuery := strings.Replace(baseQuery, "SELECT id, name_en, name_ar, description_en, price, main_category_id, brand, condition_type, stock_quantity, is_active, created_at, updated_at", "SELECT COUNT(*)", 1)

	var total int
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count products: %w", err)
	}

	// Add sorting and pagination
	orderBy := "created_at"
	if filter.SortBy != "" {
		orderBy = filter.SortBy
	}

	sortOrder := "DESC"
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}

	query := baseQuery + fmt.Sprintf(" ORDER BY %s %s LIMIT $%d OFFSET $%d",
		orderBy, sortOrder, argIndex, argIndex+1)

	offset := (page - 1) * limit
	args = append(args, limit, offset)

	// Execute query
	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query products: %w", err)
	}
	defer rows.Close()

	var products []models.Product
	for rows.Next() {
		var product models.Product
		var nameEn, nameAr, descriptionEn *string
		var categoryID *string

		err := rows.Scan(
			&product.ID, &nameEn, &nameAr, &descriptionEn, &product.Price,
			&categoryID, &product.Brand, &product.Condition, &product.Stock,
			&product.IsActive, &product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan product: %w", err)
		}

		// Handle null values
		if nameEn != nil {
			product.Name = *nameEn
		} else if nameAr != nil {
			product.Name = *nameAr
		} else {
			product.Name = "Unnamed Product"
		}

		if descriptionEn != nil {
			product.Description = *descriptionEn
		} else {
			product.Description = ""
		}

		if categoryID != nil {
			product.CategoryID = *categoryID
		} else {
			product.CategoryID = ""
		}

		product.IsDeleted = false // Always false for Products table

		products = append(products, product)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating products: %w", err)
	}

	return products, total, nil
}

// GetProductByID retrieves a product by ID
func (r *ProductRepository) GetProductByID(ctx context.Context, id string) (*models.Product, error) {
	query := `
		SELECT id, name_en, name_ar, description_en, price, main_category_id, brand, condition_type, 
		       stock_quantity, is_active, created_at, updated_at
		FROM "Products" 
		WHERE id = $1 AND is_active = true
	`

	var product models.Product
	var nameEn, nameAr, descriptionEn *string
	var categoryID *string

	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&product.ID, &nameEn, &nameAr, &descriptionEn, &product.Price,
		&categoryID, &product.Brand, &product.Condition, &product.Stock,
		&product.IsActive, &product.CreatedAt, &product.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get product: %w", err)
	}

	// Handle null values
	if nameEn != nil {
		product.Name = *nameEn
	} else if nameAr != nil {
		product.Name = *nameAr
	} else {
		product.Name = "Unnamed Product"
	}

	if descriptionEn != nil {
		product.Description = *descriptionEn
	} else {
		product.Description = ""
	}

	if categoryID != nil {
		product.CategoryID = *categoryID
	} else {
		product.CategoryID = ""
	}

	product.IsDeleted = false // Always false for Products table

	return &product, nil
}

// GetProductsByCategory retrieves products by category ID
func (r *ProductRepository) GetProductsByCategory(ctx context.Context, categoryID string, page, limit int) ([]models.Product, int, error) {
	query := `
		SELECT id, name_en, name_ar, description_en, price, main_category_id, brand, condition_type, 
		       stock_quantity, is_active, created_at, updated_at
		FROM "Products" 
		WHERE main_category_id = $1 AND is_active = true
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	offset := (page - 1) * limit
	rows, err := r.db.QueryContext(ctx, query, categoryID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query products by category: %w", err)
	}
	defer rows.Close()

	var products []models.Product
	for rows.Next() {
		var product models.Product
		var nameEn, nameAr, descriptionEn *string
		var categoryID *string

		err := rows.Scan(
			&product.ID, &nameEn, &nameAr, &descriptionEn, &product.Price,
			&categoryID, &product.Brand, &product.Condition, &product.Stock,
			&product.IsActive, &product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan product: %w", err)
		}

		// Handle null values
		if nameEn != nil {
			product.Name = *nameEn
		} else if nameAr != nil {
			product.Name = *nameAr
		} else {
			product.Name = "Unnamed Product"
		}

		if descriptionEn != nil {
			product.Description = *descriptionEn
		} else {
			product.Description = ""
		}

		if categoryID != nil {
			product.CategoryID = *categoryID
		} else {
			product.CategoryID = ""
		}

		product.IsDeleted = false // Always false for Products table

		products = append(products, product)
	}

	// Get total count for category
	countQuery := `SELECT COUNT(*) FROM "Products" WHERE main_category_id = $1 AND is_active = true`
	var total int
	err = r.db.QueryRowContext(ctx, countQuery, categoryID).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count products by category: %w", err)
	}

	return products, total, nil
}

// GetAllCategories retrieves all active categories
func (r *ProductRepository) GetAllCategories(ctx context.Context) ([]models.Category, error) {
	query := `
		SELECT id, name, description, image_url, parent_id, is_active, is_deleted, 
		       created_at, updated_at, version
		FROM categories 
		WHERE is_deleted = false AND is_active = true
		ORDER BY name ASC
	`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query categories: %w", err)
	}
	defer rows.Close()

	var categories []models.Category
	for rows.Next() {
		var category models.Category
		var parentID sql.NullString

		err := rows.Scan(
			&category.ID, &category.Name, &category.Description, &category.ImageURL,
			&parentID, &category.IsActive, &category.IsDeleted,
			&category.CreatedAt, &category.UpdatedAt, &category.Version,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan category: %w", err)
		}

		if parentID.Valid {
			category.ParentID = &parentID.String
		}

		categories = append(categories, category)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating categories: %w", err)
	}

	return categories, nil
}

// GetFeaturedProducts retrieves featured products
func (r *ProductRepository) GetFeaturedProducts(ctx context.Context, limit int) ([]models.Product, error) {
	query := `
		SELECT id, name_en, name_ar, description_en, price, main_category_id, brand, condition_type, 
		       stock_quantity, is_active, created_at, updated_at
		FROM "Products" 
		WHERE is_active = true
		ORDER BY created_at DESC
		LIMIT $1
	`

	rows, err := r.db.QueryContext(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query featured products: %w", err)
	}
	defer rows.Close()

	var products []models.Product
	for rows.Next() {
		var product models.Product
		var nameEn, nameAr, descriptionEn *string
		var categoryID *string

		err := rows.Scan(
			&product.ID, &nameEn, &nameAr, &descriptionEn, &product.Price,
			&categoryID, &product.Brand, &product.Condition, &product.Stock,
			&product.IsActive, &product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan product: %w", err)
		}

		// Handle null values
		if nameEn != nil {
			product.Name = *nameEn
		} else if nameAr != nil {
			product.Name = *nameAr
		} else {
			product.Name = "Unnamed Product"
		}

		if descriptionEn != nil {
			product.Description = *descriptionEn
		} else {
			product.Description = ""
		}

		if categoryID != nil {
			product.CategoryID = *categoryID
		} else {
			product.CategoryID = ""
		}

		product.IsDeleted = false // Always false for Products table

		products = append(products, product)
	}

	return products, nil
}

// parseImagesArray parses a JSON array string into a string slice
func parseImagesArray(imagesStr string) []string {
	// Simple implementation - in production, use proper JSON parsing
	if imagesStr == "" || imagesStr == "[]" {
		return []string{}
	}

	// Remove brackets and split by comma
	imagesStr = strings.Trim(imagesStr, "[]")
	if imagesStr == "" {
		return []string{}
	}

	// Split by comma and clean up quotes
	parts := strings.Split(imagesStr, ",")
	var images []string
	for _, part := range parts {
		part = strings.Trim(part, `" `)
		if part != "" {
			images = append(images, part)
		}
	}

	return images
}

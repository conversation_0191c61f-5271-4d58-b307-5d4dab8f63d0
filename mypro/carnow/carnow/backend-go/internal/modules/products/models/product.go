package models

import (
	"time"

	"github.com/google/uuid"
)

// Product represents a product in the system
type Product struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	Price       float64   `json:"price" db:"price"`
	CategoryID  string    `json:"category_id" db:"category_id"`
	Condition   string    `json:"condition" db:"condition"` // new, used, refurbished
	Brand       string    `json:"brand" db:"brand"`
	Model       string    `json:"model" db:"model"`
	Year        int       `json:"year" db:"year"`
	Images      []string  `json:"images" db:"images"`
	Stock       int       `json:"stock" db:"stock"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	IsDeleted   bool      `json:"is_deleted" db:"is_deleted"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	Version     int       `json:"version" db:"version"`
}

// ProductFilter represents filtering options for products
type ProductFilter struct {
	CategoryID string  `json:"category_id"`
	Search     string  `json:"search"`
	SortBy     string  `json:"sort_by"`
	SortOrder  string  `json:"sort_order"`
	MinPrice   float64 `json:"min_price"`
	MaxPrice   float64 `json:"max_price"`
	Condition  string  `json:"condition"`
	Brand      string  `json:"brand"`
	Year       int     `json:"year"`
}

// ProductsResponse represents the response for product queries
type ProductsResponse struct {
	Products   []Product  `json:"products"`
	Pagination Pagination `json:"pagination"`
}

// Pagination represents pagination information
type Pagination struct {
	Page       int  `json:"page"`
	Limit      int  `json:"limit"`
	Total      int  `json:"total"`
	TotalPages int  `json:"total_pages"`
	HasNext    bool `json:"has_next"`
	HasPrev    bool `json:"has_prev"`
}

// Category represents a product category
type Category struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	ImageURL    string    `json:"image_url" db:"image_url"`
	ParentID    *string   `json:"parent_id" db:"parent_id"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	IsDeleted   bool      `json:"is_deleted" db:"is_deleted"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	Version     int       `json:"version" db:"version"`
}

// CategoriesResponse represents the response for category queries
type CategoriesResponse struct {
	Categories []Category `json:"categories"`
}

// NewProduct creates a new product with default values
func NewProduct(name, description string, price float64, categoryID string) *Product {
	now := time.Now()
	return &Product{
		ID:          uuid.New().String(),
		Name:        name,
		Description: description,
		Price:       price,
		CategoryID:  categoryID,
		Condition:   "new",
		Stock:       0,
		IsActive:    true,
		IsDeleted:   false,
		CreatedAt:   now,
		UpdatedAt:   now,
		Version:     1,
	}
}

// NewCategory creates a new category with default values
func NewCategory(name, description string) *Category {
	now := time.Now()
	return &Category{
		ID:          uuid.New().String(),
		Name:        name,
		Description: description,
		IsActive:    true,
		IsDeleted:   false,
		CreatedAt:   now,
		UpdatedAt:   now,
		Version:     1,
	}
}

// CreateProductRequest represents the request for creating a new product
type CreateProductRequest struct {
	Name        string   `json:"name" binding:"required"`
	Description string   `json:"description" binding:"required"`
	Price       float64  `json:"price" binding:"required,min=0"`
	CategoryID  string   `json:"category_id" binding:"required"`
	Condition   string   `json:"condition" binding:"required,oneof=new used refurbished"`
	Brand       string   `json:"brand" binding:"required"`
	Model       string   `json:"model" binding:"required"`
	Year        int      `json:"year" binding:"required,min=1900,max=2030"`
	Images      []string `json:"images"`
	Stock       int      `json:"stock" binding:"min=0"`
}

// UpdateProductRequest represents the request for updating an existing product
type UpdateProductRequest struct {
	Name        *string  `json:"name"`
	Description *string  `json:"description"`
	Price       *float64 `json:"price" binding:"omitempty,min=0"`
	CategoryID  *string  `json:"category_id"`
	Condition   *string  `json:"condition" binding:"omitempty,oneof=new used refurbished"`
	Brand       *string  `json:"brand"`
	Model       *string  `json:"model"`
	Year        *int     `json:"year" binding:"omitempty,min=1900,max=2030"`
	Images      []string `json:"images"`
	Stock       *int     `json:"stock" binding:"omitempty,min=0"`
	IsActive    *bool    `json:"is_active"`
}

// ProductResponse represents the response for a single product
type ProductResponse struct {
	Product *Product `json:"product"`
}

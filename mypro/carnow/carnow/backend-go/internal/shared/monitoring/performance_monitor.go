package monitoring

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PerformanceMonitor provides performance monitoring for authentication flows
type PerformanceMonitor struct {
	logger   *zap.Logger
	config   *PerformanceConfig
	metrics  *PerformanceMetrics
	mu       sync.RWMutex
}

// PerformanceConfig contains configuration for performance monitoring
type PerformanceConfig struct {
	// Thresholds
	SlowRequestThreshold    time.Duration `json:"slow_request_threshold"`
	VerySlowRequestThreshold time.Duration `json:"very_slow_request_threshold"`
	
	// Monitoring settings
	EnableDetailedMetrics   bool `json:"enable_detailed_metrics"`
	EnableRequestTracing    bool `json:"enable_request_tracing"`
	MetricsRetentionPeriod  time.Duration `json:"metrics_retention_period"`
	
	// Sampling
	SampleRate              float64 `json:"sample_rate"`
	MaxTrackedRequests      int     `json:"max_tracked_requests"`
}

// DefaultPerformanceConfig returns default configuration
func DefaultPerformanceConfig() *PerformanceConfig {
	return &PerformanceConfig{
		SlowRequestThreshold:     2 * time.Second,
		VerySlowRequestThreshold: 5 * time.Second,
		EnableDetailedMetrics:    true,
		EnableRequestTracing:     true,
		MetricsRetentionPeriod:   24 * time.Hour,
		SampleRate:               1.0, // Sample all requests
		MaxTrackedRequests:       10000,
	}
}

// PerformanceMetrics tracks performance-related metrics
type PerformanceMetrics struct {
	// Request timing
	TotalRequests           int64                    `json:"total_requests"`
	AverageResponseTime     time.Duration            `json:"average_response_time"`
	MedianResponseTime      time.Duration            `json:"median_response_time"`
	P95ResponseTime         time.Duration            `json:"p95_response_time"`
	P99ResponseTime         time.Duration            `json:"p99_response_time"`
	
	// Request categories
	FastRequests            int64                    `json:"fast_requests"`
	SlowRequests            int64                    `json:"slow_requests"`
	VerySlowRequests        int64                    `json:"very_slow_requests"`
	
	// Endpoint performance
	EndpointMetrics         map[string]*EndpointMetrics `json:"endpoint_metrics"`
	
	// Time-based metrics
	RequestsPerSecond       float64                  `json:"requests_per_second"`
	LastRequestTime         time.Time                `json:"last_request_time"`
	
	// Historical data
	ResponseTimeHistory     []time.Duration          `json:"response_time_history"`
	RequestTimestamps       []time.Time              `json:"request_timestamps"`
	
	mu sync.RWMutex
}

// EndpointMetrics tracks metrics for specific endpoints
type EndpointMetrics struct {
	Endpoint            string        `json:"endpoint"`
	RequestCount        int64         `json:"request_count"`
	AverageResponseTime time.Duration `json:"average_response_time"`
	MinResponseTime     time.Duration `json:"min_response_time"`
	MaxResponseTime     time.Duration `json:"max_response_time"`
	SlowRequestCount    int64         `json:"slow_request_count"`
	ErrorCount          int64         `json:"error_count"`
	LastRequestTime     time.Time     `json:"last_request_time"`
}

// PerformanceEvent represents a performance measurement event
type PerformanceEvent struct {
	ID              string                 `json:"id"`
	Timestamp       time.Time              `json:"timestamp"`
	Endpoint        string                 `json:"endpoint"`
	Method          string                 `json:"method"`
	ResponseTime    time.Duration          `json:"response_time"`
	StatusCode      int                    `json:"status_code"`
	UserID          string                 `json:"user_id,omitempty"`
	RequestID       string                 `json:"request_id,omitempty"`
	Context         map[string]interface{} `json:"context,omitempty"`
	DatabaseTime    time.Duration          `json:"database_time,omitempty"`
	ExternalAPITime time.Duration          `json:"external_api_time,omitempty"`
}

// NewPerformanceMonitor creates a new performance monitor
func NewPerformanceMonitor(config *PerformanceConfig, logger *zap.Logger) *PerformanceMonitor {
	if config == nil {
		config = DefaultPerformanceConfig()
	}

	if logger == nil {
		logger, _ = zap.NewProduction()
	}

	return &PerformanceMonitor{
		logger: logger,
		config: config,
		metrics: &PerformanceMetrics{
			EndpointMetrics:     make(map[string]*EndpointMetrics),
			ResponseTimeHistory: make([]time.Duration, 0, config.MaxTrackedRequests),
			RequestTimestamps:   make([]time.Time, 0, config.MaxTrackedRequests),
		},
	}
}

// RecordPerformanceEvent records a performance measurement event
func (pm *PerformanceMonitor) RecordPerformanceEvent(ctx context.Context, event PerformanceEvent) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	// Update overall metrics
	pm.metrics.TotalRequests++
	pm.metrics.LastRequestTime = event.Timestamp

	// Add to response time history
	pm.metrics.ResponseTimeHistory = append(pm.metrics.ResponseTimeHistory, event.ResponseTime)
	pm.metrics.RequestTimestamps = append(pm.metrics.RequestTimestamps, event.Timestamp)

	// Maintain history size limit
	if len(pm.metrics.ResponseTimeHistory) > pm.config.MaxTrackedRequests {
		pm.metrics.ResponseTimeHistory = pm.metrics.ResponseTimeHistory[1:]
		pm.metrics.RequestTimestamps = pm.metrics.RequestTimestamps[1:]
	}

	// Categorize request speed
	if event.ResponseTime <= pm.config.SlowRequestThreshold {
		pm.metrics.FastRequests++
	} else if event.ResponseTime <= pm.config.VerySlowRequestThreshold {
		pm.metrics.SlowRequests++
	} else {
		pm.metrics.VerySlowRequests++
	}

	// Update endpoint-specific metrics
	pm.updateEndpointMetrics(event)

	// Calculate aggregate metrics
	pm.calculateAggregateMetrics()

	// Log performance event
	pm.logPerformanceEvent(event)
}

// updateEndpointMetrics updates metrics for a specific endpoint
func (pm *PerformanceMonitor) updateEndpointMetrics(event PerformanceEvent) {
	endpoint := event.Endpoint
	
	if pm.metrics.EndpointMetrics[endpoint] == nil {
		pm.metrics.EndpointMetrics[endpoint] = &EndpointMetrics{
			Endpoint:        endpoint,
			MinResponseTime: event.ResponseTime,
			MaxResponseTime: event.ResponseTime,
		}
	}

	endpointMetrics := pm.metrics.EndpointMetrics[endpoint]
	endpointMetrics.RequestCount++
	endpointMetrics.LastRequestTime = event.Timestamp

	// Update min/max response times
	if event.ResponseTime < endpointMetrics.MinResponseTime {
		endpointMetrics.MinResponseTime = event.ResponseTime
	}
	if event.ResponseTime > endpointMetrics.MaxResponseTime {
		endpointMetrics.MaxResponseTime = event.ResponseTime
	}

	// Count slow requests
	if event.ResponseTime > pm.config.SlowRequestThreshold {
		endpointMetrics.SlowRequestCount++
	}

	// Count errors
	if event.StatusCode >= 400 {
		endpointMetrics.ErrorCount++
	}

	// Calculate average response time for endpoint
	// This is a simplified calculation - in production, you might want to use a moving average
	totalTime := time.Duration(endpointMetrics.RequestCount) * endpointMetrics.AverageResponseTime
	totalTime += event.ResponseTime
	endpointMetrics.AverageResponseTime = totalTime / time.Duration(endpointMetrics.RequestCount)
}

// calculateAggregateMetrics calculates aggregate performance metrics
func (pm *PerformanceMonitor) calculateAggregateMetrics() {
	if len(pm.metrics.ResponseTimeHistory) == 0 {
		return
	}

	// Calculate average response time
	var total time.Duration
	for _, rt := range pm.metrics.ResponseTimeHistory {
		total += rt
	}
	pm.metrics.AverageResponseTime = total / time.Duration(len(pm.metrics.ResponseTimeHistory))

	// Calculate percentiles
	sortedTimes := make([]time.Duration, len(pm.metrics.ResponseTimeHistory))
	copy(sortedTimes, pm.metrics.ResponseTimeHistory)
	
	// Simple sort for percentile calculation
	for i := 0; i < len(sortedTimes); i++ {
		for j := i + 1; j < len(sortedTimes); j++ {
			if sortedTimes[i] > sortedTimes[j] {
				sortedTimes[i], sortedTimes[j] = sortedTimes[j], sortedTimes[i]
			}
		}
	}

	// Calculate median (P50)
	mid := len(sortedTimes) / 2
	if len(sortedTimes)%2 == 0 {
		pm.metrics.MedianResponseTime = (sortedTimes[mid-1] + sortedTimes[mid]) / 2
	} else {
		pm.metrics.MedianResponseTime = sortedTimes[mid]
	}

	// Calculate P95
	p95Index := int(float64(len(sortedTimes)) * 0.95)
	if p95Index >= len(sortedTimes) {
		p95Index = len(sortedTimes) - 1
	}
	pm.metrics.P95ResponseTime = sortedTimes[p95Index]

	// Calculate P99
	p99Index := int(float64(len(sortedTimes)) * 0.99)
	if p99Index >= len(sortedTimes) {
		p99Index = len(sortedTimes) - 1
	}
	pm.metrics.P99ResponseTime = sortedTimes[p99Index]

	// Calculate requests per second (based on last minute)
	pm.calculateRequestsPerSecond()
}

// calculateRequestsPerSecond calculates current requests per second
func (pm *PerformanceMonitor) calculateRequestsPerSecond() {
	now := time.Now()
	oneMinuteAgo := now.Add(-time.Minute)
	
	recentRequests := 0
	for _, timestamp := range pm.metrics.RequestTimestamps {
		if timestamp.After(oneMinuteAgo) {
			recentRequests++
		}
	}
	
	pm.metrics.RequestsPerSecond = float64(recentRequests) / 60.0
}

// logPerformanceEvent logs the performance event
func (pm *PerformanceMonitor) logPerformanceEvent(event PerformanceEvent) {
	fields := []zap.Field{
		zap.String("event_id", event.ID),
		zap.String("endpoint", event.Endpoint),
		zap.String("method", event.Method),
		zap.Duration("response_time", event.ResponseTime),
		zap.Int("status_code", event.StatusCode),
		zap.Time("timestamp", event.Timestamp),
	}

	if event.UserID != "" {
		fields = append(fields, zap.String("user_id", event.UserID))
	}
	if event.RequestID != "" {
		fields = append(fields, zap.String("request_id", event.RequestID))
	}
	if event.DatabaseTime > 0 {
		fields = append(fields, zap.Duration("database_time", event.DatabaseTime))
	}
	if event.ExternalAPITime > 0 {
		fields = append(fields, zap.Duration("external_api_time", event.ExternalAPITime))
	}

	// Log based on performance
	if event.ResponseTime > pm.config.VerySlowRequestThreshold {
		pm.logger.Warn("🐌 Very Slow Request", fields...)
	} else if event.ResponseTime > pm.config.SlowRequestThreshold {
		pm.logger.Info("⏱️ Slow Request", fields...)
	} else if pm.config.EnableDetailedMetrics {
		pm.logger.Debug("⚡ Fast Request", fields...)
	}
}

// GetMetrics returns current performance metrics
func (pm *PerformanceMonitor) GetMetrics() *PerformanceMetrics {
	pm.metrics.mu.RLock()
	defer pm.metrics.mu.RUnlock()

	// Create a copy to avoid race conditions
	metrics := &PerformanceMetrics{
		TotalRequests:       pm.metrics.TotalRequests,
		AverageResponseTime: pm.metrics.AverageResponseTime,
		MedianResponseTime:  pm.metrics.MedianResponseTime,
		P95ResponseTime:     pm.metrics.P95ResponseTime,
		P99ResponseTime:     pm.metrics.P99ResponseTime,
		FastRequests:        pm.metrics.FastRequests,
		SlowRequests:        pm.metrics.SlowRequests,
		VerySlowRequests:    pm.metrics.VerySlowRequests,
		RequestsPerSecond:   pm.metrics.RequestsPerSecond,
		LastRequestTime:     pm.metrics.LastRequestTime,
		EndpointMetrics:     make(map[string]*EndpointMetrics),
		ResponseTimeHistory: make([]time.Duration, len(pm.metrics.ResponseTimeHistory)),
		RequestTimestamps:   make([]time.Time, len(pm.metrics.RequestTimestamps)),
	}

	// Copy endpoint metrics
	for k, v := range pm.metrics.EndpointMetrics {
		metrics.EndpointMetrics[k] = &EndpointMetrics{
			Endpoint:            v.Endpoint,
			RequestCount:        v.RequestCount,
			AverageResponseTime: v.AverageResponseTime,
			MinResponseTime:     v.MinResponseTime,
			MaxResponseTime:     v.MaxResponseTime,
			SlowRequestCount:    v.SlowRequestCount,
			ErrorCount:          v.ErrorCount,
			LastRequestTime:     v.LastRequestTime,
		}
	}

	// Copy slices
	copy(metrics.ResponseTimeHistory, pm.metrics.ResponseTimeHistory)
	copy(metrics.RequestTimestamps, pm.metrics.RequestTimestamps)

	return metrics
}

// GetEndpointMetrics returns metrics for a specific endpoint
func (pm *PerformanceMonitor) GetEndpointMetrics(endpoint string) *EndpointMetrics {
	pm.metrics.mu.RLock()
	defer pm.metrics.mu.RUnlock()

	if metrics, exists := pm.metrics.EndpointMetrics[endpoint]; exists {
		// Return a copy
		return &EndpointMetrics{
			Endpoint:            metrics.Endpoint,
			RequestCount:        metrics.RequestCount,
			AverageResponseTime: metrics.AverageResponseTime,
			MinResponseTime:     metrics.MinResponseTime,
			MaxResponseTime:     metrics.MaxResponseTime,
			SlowRequestCount:    metrics.SlowRequestCount,
			ErrorCount:          metrics.ErrorCount,
			LastRequestTime:     metrics.LastRequestTime,
		}
	}

	return nil
}

// GetPerformanceSummary returns a summary of performance metrics
func (pm *PerformanceMonitor) GetPerformanceSummary() map[string]interface{} {
	metrics := pm.GetMetrics()

	summary := map[string]interface{}{
		"total_requests":        metrics.TotalRequests,
		"requests_per_second":   metrics.RequestsPerSecond,
		"average_response_time": metrics.AverageResponseTime.Milliseconds(),
		"median_response_time":  metrics.MedianResponseTime.Milliseconds(),
		"p95_response_time":     metrics.P95ResponseTime.Milliseconds(),
		"p99_response_time":     metrics.P99ResponseTime.Milliseconds(),
		"fast_requests":         metrics.FastRequests,
		"slow_requests":         metrics.SlowRequests,
		"very_slow_requests":    metrics.VerySlowRequests,
		"last_request_time":     metrics.LastRequestTime,
		"endpoint_count":        len(metrics.EndpointMetrics),
	}

	// Add performance health status
	if metrics.TotalRequests > 0 {
		slowPercentage := float64(metrics.SlowRequests+metrics.VerySlowRequests) / float64(metrics.TotalRequests)
		if slowPercentage > 0.2 { // More than 20% slow requests
			summary["performance_status"] = "degraded"
		} else if slowPercentage > 0.1 { // More than 10% slow requests
			summary["performance_status"] = "warning"
		} else {
			summary["performance_status"] = "healthy"
		}
	} else {
		summary["performance_status"] = "unknown"
	}

	return summary
}

// CleanupOldMetrics removes old metrics data
func (pm *PerformanceMonitor) CleanupOldMetrics() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	cutoff := time.Now().Add(-pm.config.MetricsRetentionPeriod)
	
	// Remove old timestamps and corresponding response times
	validIndices := make([]int, 0)
	for i, timestamp := range pm.metrics.RequestTimestamps {
		if timestamp.After(cutoff) {
			validIndices = append(validIndices, i)
		}
	}

	// Create new slices with only valid data
	newTimestamps := make([]time.Time, len(validIndices))
	newResponseTimes := make([]time.Duration, len(validIndices))
	
	for i, validIndex := range validIndices {
		newTimestamps[i] = pm.metrics.RequestTimestamps[validIndex]
		newResponseTimes[i] = pm.metrics.ResponseTimeHistory[validIndex]
	}

	pm.metrics.RequestTimestamps = newTimestamps
	pm.metrics.ResponseTimeHistory = newResponseTimes

	pm.logger.Info("🧹 Cleaned up old performance metrics",
		zap.Int("removed_entries", len(pm.metrics.RequestTimestamps)-len(validIndices)),
		zap.Int("remaining_entries", len(validIndices)),
	)
}

// Helper function to generate performance event ID
func generatePerformanceEventID() string {
	return fmt.Sprintf("perf_%d_%d", time.Now().Unix(), time.Now().Nanosecond())
}

// Helper function to create performance event from request context
func CreatePerformanceEventFromRequest(endpoint, method string, responseTime time.Duration, statusCode int) PerformanceEvent {
	return PerformanceEvent{
		ID:           generatePerformanceEventID(),
		Timestamp:    time.Now(),
		Endpoint:     endpoint,
		Method:       method,
		ResponseTime: responseTime,
		StatusCode:   statusCode,
	}
}

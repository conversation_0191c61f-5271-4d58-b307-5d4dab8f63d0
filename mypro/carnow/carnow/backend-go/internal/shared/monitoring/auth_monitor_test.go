package monitoring

import (
	"context"
	"testing"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"
)

func TestNewAuthMonitor(t *testing.T) {
	tests := []struct {
		name   string
		config *AuthMonitorConfig
		logger *zap.Logger
		want   bool
	}{
		{
			name:   "with nil config",
			config: nil,
			logger: zaptest.NewLogger(t),
			want:   true,
		},
		{
			name:   "with valid config",
			config: DefaultAuthMonitorConfig(),
			logger: zaptest.NewLogger(t),
			want:   true,
		},
		{
			name:   "with nil logger",
			config: DefaultAuthMonitorConfig(),
			logger: nil,
			want:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			monitor := NewAuthMonitor(tt.config, tt.logger, nil)
			if (monitor != nil) != tt.want {
				t.<PERSON>("NewAuthMonitor() = %v, want %v", monitor != nil, tt.want)
			}
			if monitor != nil {
				if monitor.metrics == nil {
					t.Error("Expected metrics to be initialized")
				}
				if monitor.config == nil {
					t.Error("Expected config to be initialized")
				}
			}
		})
	}
}

func TestAuthMonitor_RecordAuthEvent(t *testing.T) {
	logger := zaptest.NewLogger(t)
	monitor := NewAuthMonitor(DefaultAuthMonitorConfig(), logger, nil)

	tests := []struct {
		name  string
		event AuthEvent
	}{
		{
			name: "successful login event",
			event: AuthEvent{
				ID:           "test-1",
				Timestamp:    time.Now(),
				EventType:    AuthEventLogin,
				Endpoint:     "/auth/login",
				UserID:       "user-123",
				Email:        "<EMAIL>",
				Provider:     "email",
				Success:      true,
				ResponseTime: 100 * time.Millisecond,
			},
		},
		{
			name: "failed login event",
			event: AuthEvent{
				ID:           "test-2",
				Timestamp:    time.Now(),
				EventType:    AuthEventLogin,
				Endpoint:     "/auth/login",
				Email:        "<EMAIL>",
				Success:      false,
				ErrorMessage: "invalid credentials",
				ResponseTime: 50 * time.Millisecond,
				IPAddress:    "***********",
			},
		},
		{
			name: "google oauth event",
			event: AuthEvent{
				ID:           "test-3",
				Timestamp:    time.Now(),
				EventType:    AuthEventGoogleOAuth,
				Endpoint:     "/auth/google",
				UserID:       "user-456",
				Provider:     "google",
				Success:      true,
				ResponseTime: 200 * time.Millisecond,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			initialRequests := monitor.metrics.TotalRequests
			initialSuccessful := monitor.metrics.SuccessfulRequests
			initialFailed := monitor.metrics.FailedRequests

			monitor.RecordAuthEvent(context.Background(), tt.event)

			// Check total requests increased
			if monitor.metrics.TotalRequests != initialRequests+1 {
				t.Errorf("Expected TotalRequests to increase by 1, got %d", monitor.metrics.TotalRequests)
			}

			// Check success/failure counts
			if tt.event.Success {
				if monitor.metrics.SuccessfulRequests != initialSuccessful+1 {
					t.Errorf("Expected SuccessfulRequests to increase by 1, got %d", monitor.metrics.SuccessfulRequests)
				}
			} else {
				if monitor.metrics.FailedRequests != initialFailed+1 {
					t.Errorf("Expected FailedRequests to increase by 1, got %d", monitor.metrics.FailedRequests)
				}
			}

			// Check endpoint tracking
			if count, exists := monitor.metrics.RequestsByEndpoint[tt.event.Endpoint]; !exists || count == 0 {
				t.Errorf("Expected endpoint %s to be tracked", tt.event.Endpoint)
			}
		})
	}
}

func TestAuthMonitor_PerformanceMetrics(t *testing.T) {
	logger := zaptest.NewLogger(t)
	config := DefaultAuthMonitorConfig()
	config.SlowRequestThreshold = 150 * time.Millisecond
	monitor := NewAuthMonitor(config, logger, nil)

	// Record fast request
	fastEvent := AuthEvent{
		ID:           "fast-1",
		Timestamp:    time.Now(),
		EventType:    AuthEventLogin,
		Endpoint:     "/auth/login",
		Success:      true,
		ResponseTime: 50 * time.Millisecond,
	}

	// Record slow request
	slowEvent := AuthEvent{
		ID:           "slow-1",
		Timestamp:    time.Now(),
		EventType:    AuthEventLogin,
		Endpoint:     "/auth/login",
		Success:      true,
		ResponseTime: 200 * time.Millisecond,
	}

	monitor.RecordAuthEvent(context.Background(), fastEvent)
	monitor.RecordAuthEvent(context.Background(), slowEvent)

	// Check slow requests count
	if monitor.metrics.SlowRequests != 1 {
		t.Errorf("Expected 1 slow request, got %d", monitor.metrics.SlowRequests)
	}

	// Check response times tracking
	if len(monitor.metrics.ResponseTimes) != 2 {
		t.Errorf("Expected 2 response times recorded, got %d", len(monitor.metrics.ResponseTimes))
	}

	// Check average response time calculation
	expectedAvg := (50*time.Millisecond + 200*time.Millisecond) / 2
	if monitor.metrics.AverageResponseTime != expectedAvg {
		t.Errorf("Expected average response time %v, got %v", expectedAvg, monitor.metrics.AverageResponseTime)
	}
}

func TestAuthMonitor_SecurityMetrics(t *testing.T) {
	logger := zaptest.NewLogger(t)
	config := DefaultAuthMonitorConfig()
	config.SuspiciousActivityLimit = 3
	monitor := NewAuthMonitor(config, logger, nil)

	ipAddress := "***********00"

	// Record multiple failed login attempts from same IP
	for i := 0; i < 5; i++ {
		event := AuthEvent{
			ID:        generateEventID(),
			Timestamp: time.Now(),
			EventType: AuthEventLogin,
			Endpoint:  "/auth/login",
			Success:   false,
			IPAddress: ipAddress,
		}
		monitor.RecordAuthEvent(context.Background(), event)
	}

	// Check failed login attempts
	if monitor.metrics.FailedLoginAttempts != 5 {
		t.Errorf("Expected 5 failed login attempts, got %d", monitor.metrics.FailedLoginAttempts)
	}

	// Check blocked IPs tracking
	if count, exists := monitor.metrics.BlockedIPs[ipAddress]; !exists || count != 5 {
		t.Errorf("Expected IP %s to have 5 failed attempts, got %d", ipAddress, count)
	}

	// Check suspicious activities (should be triggered after 3rd attempt)
	if monitor.metrics.SuspiciousActivities == 0 {
		t.Error("Expected suspicious activities to be detected")
	}
}

func TestAuthMonitor_GetMetrics(t *testing.T) {
	logger := zaptest.NewLogger(t)
	monitor := NewAuthMonitor(DefaultAuthMonitorConfig(), logger, nil)

	// Record some events
	events := []AuthEvent{
		{
			ID:        "test-1",
			Timestamp: time.Now(),
			EventType: AuthEventLogin,
			Endpoint:  "/auth/login",
			Success:   true,
			Provider:  "email",
		},
		{
			ID:        "test-2",
			Timestamp: time.Now(),
			EventType: AuthEventRegister,
			Endpoint:  "/auth/register",
			Success:   true,
			Provider:  "email",
		},
	}

	for _, event := range events {
		monitor.RecordAuthEvent(context.Background(), event)
	}

	metrics := monitor.GetMetrics()

	// Check that we get a copy, not the original
	if metrics == monitor.metrics {
		t.Error("GetMetrics should return a copy, not the original")
	}

	// Check metrics values
	if metrics.TotalRequests != 2 {
		t.Errorf("Expected 2 total requests, got %d", metrics.TotalRequests)
	}

	if metrics.SuccessfulRequests != 2 {
		t.Errorf("Expected 2 successful requests, got %d", metrics.SuccessfulRequests)
	}

	if len(metrics.RequestsByEndpoint) != 2 {
		t.Errorf("Expected 2 endpoints tracked, got %d", len(metrics.RequestsByEndpoint))
	}
}

func TestAuthMonitor_GetHealthStatus(t *testing.T) {
	logger := zaptest.NewLogger(t)
	monitor := NewAuthMonitor(DefaultAuthMonitorConfig(), logger, nil)

	// Test with no requests
	status := monitor.GetHealthStatus()
	if status["status"] != "healthy" {
		t.Errorf("Expected healthy status with no requests, got %s", status["status"])
	}

	// Record some successful requests
	for i := 0; i < 10; i++ {
		event := AuthEvent{
			ID:        generateEventID(),
			Timestamp: time.Now(),
			EventType: AuthEventLogin,
			Success:   true,
		}
		monitor.RecordAuthEvent(context.Background(), event)
	}

	status = monitor.GetHealthStatus()
	if status["status"] != "healthy" {
		t.Errorf("Expected healthy status with all successful requests, got %s", status["status"])
	}

	// Record some failed requests to degrade health
	for i := 0; i < 5; i++ {
		event := AuthEvent{
			ID:        generateEventID(),
			Timestamp: time.Now(),
			EventType: AuthEventLogin,
			Success:   false,
		}
		monitor.RecordAuthEvent(context.Background(), event)
	}

	status = monitor.GetHealthStatus()
	// Success rate should be 10/15 = 0.67, which is < 0.9, so should be degraded
	if status["status"] != "degraded" {
		t.Errorf("Expected degraded status with low success rate, got %s", status["status"])
	}
}

func TestDefaultAuthMonitorConfig(t *testing.T) {
	config := DefaultAuthMonitorConfig()

	if config == nil {
		t.Fatal("Expected non-nil config")
	}

	// Check default values
	if config.SlowRequestThreshold != 2*time.Second {
		t.Errorf("Expected SlowRequestThreshold to be 2s, got %v", config.SlowRequestThreshold)
	}

	if config.FailureRateThreshold != 0.1 {
		t.Errorf("Expected FailureRateThreshold to be 0.1, got %f", config.FailureRateThreshold)
	}

	if config.SuspiciousActivityLimit != 5 {
		t.Errorf("Expected SuspiciousActivityLimit to be 5, got %d", config.SuspiciousActivityLimit)
	}

	if !config.EnablePerformanceTracking {
		t.Error("Expected EnablePerformanceTracking to be true")
	}

	if !config.EnableSecurityMonitoring {
		t.Error("Expected EnableSecurityMonitoring to be true")
	}
}

func TestGenerateEventID(t *testing.T) {
	id1 := generateEventID()
	id2 := generateEventID()

	if id1 == "" {
		t.Error("Expected non-empty event ID")
	}

	if id1 == id2 {
		t.Error("Expected unique event IDs")
	}

	// Check format
	if len(id1) < 10 {
		t.Error("Expected event ID to have reasonable length")
	}
}

// Benchmark tests
func BenchmarkAuthMonitor_RecordAuthEvent(b *testing.B) {
	logger := zaptest.NewLogger(b)
	monitor := NewAuthMonitor(DefaultAuthMonitorConfig(), logger, nil)

	event := AuthEvent{
		ID:           "bench-test",
		Timestamp:    time.Now(),
		EventType:    AuthEventLogin,
		Endpoint:     "/auth/login",
		Success:      true,
		ResponseTime: 100 * time.Millisecond,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		monitor.RecordAuthEvent(context.Background(), event)
	}
}

func BenchmarkAuthMonitor_GetMetrics(b *testing.B) {
	logger := zaptest.NewLogger(b)
	monitor := NewAuthMonitor(DefaultAuthMonitorConfig(), logger, nil)

	// Pre-populate with some data
	for i := 0; i < 1000; i++ {
		event := AuthEvent{
			ID:        generateEventID(),
			Timestamp: time.Now(),
			EventType: AuthEventLogin,
			Success:   i%2 == 0,
		}
		monitor.RecordAuthEvent(context.Background(), event)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = monitor.GetMetrics()
	}
}

package errors

import (
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"time"

	"carnow-backend/internal/shared/monitoring"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CentralizedErrorHandler provides unified error handling across the application
type CentralizedErrorHandler struct {
	logger       *zap.Logger
	errorMonitor *monitoring.ErrorMonitor
	config       *ErrorHandlerConfig
}

// ErrorHandlerConfig contains configuration for error handling
type ErrorHandlerConfig struct {
	IncludeStackTrace bool          `json:"include_stack_trace"`
	LogLevel          string        `json:"log_level"`
	EnableMonitoring  bool          `json:"enable_monitoring"`
	SanitizeErrors    bool          `json:"sanitize_errors"`
	MaxErrorLength    int           `json:"max_error_length"`
	RetryableErrors   []string      `json:"retryable_errors"`
	Timeout           time.Duration `json:"timeout"`
}

// DefaultErrorHandlerConfig returns default configuration
func DefaultErrorHandlerConfig() *ErrorHandlerConfig {
	return &ErrorHandlerConfig{
		IncludeStackTrace: false, // Don't expose stack traces in production
		LogLevel:          "error",
		EnableMonitoring:  true,
		SanitizeErrors:    true,
		MaxErrorLength:    500,
		RetryableErrors: []string{
			"connection refused",
			"timeout",
			"temporary failure",
			"service unavailable",
		},
		Timeout: 30 * time.Second,
	}
}

// AppError represents a structured application error
type AppError struct {
	Code       string                 `json:"code"`
	Message    string                 `json:"message"`
	Details    string                 `json:"details,omitempty"`
	StatusCode int                    `json:"-"`
	Internal   error                  `json:"-"`
	Context    map[string]interface{} `json:"context,omitempty"`
	Retryable  bool                   `json:"retryable"`
	Timestamp  time.Time              `json:"timestamp"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// NewCentralizedErrorHandler creates a new centralized error handler
func NewCentralizedErrorHandler(logger *zap.Logger, errorMonitor *monitoring.ErrorMonitor, config *ErrorHandlerConfig) *CentralizedErrorHandler {
	if config == nil {
		config = DefaultErrorHandlerConfig()
	}

	return &CentralizedErrorHandler{
		logger:       logger,
		errorMonitor: errorMonitor,
		config:       config,
	}
}

// HandleError processes and responds to errors in a centralized manner
func (h *CentralizedErrorHandler) HandleError(c *gin.Context, err error) {
	if err == nil {
		return
	}

	// Convert to AppError if not already
	appErr := h.convertToAppError(err)

	// Add request context
	appErr.Context = h.extractRequestContext(c)

	// Log the error
	h.logError(c, appErr)

	// Monitor the error if monitoring is enabled
	if h.config.EnableMonitoring && h.errorMonitor != nil {
		level := h.getMonitoringLevel(appErr)
		endpoint := c.FullPath()
		h.errorMonitor.RecordError(c.Request.Context(), appErr, level, endpoint)
	}

	// Respond to client
	h.respondWithError(c, appErr)
}

// convertToAppError converts any error to AppError
func (h *CentralizedErrorHandler) convertToAppError(err error) *AppError {
	// If already an AppError, return as is
	if appErr, ok := err.(*AppError); ok {
		return appErr
	}

	// Convert based on error type/message
	message := err.Error()

	// Sanitize error message if needed
	if h.config.SanitizeErrors {
		message = h.sanitizeErrorMessage(message)
	}

	// Determine error code and status
	code, statusCode := h.categorizeError(err)

	// Check if error is retryable
	retryable := h.isRetryableError(err)

	return &AppError{
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
		Internal:   err,
		Retryable:  retryable,
		Timestamp:  time.Now(),
	}
}

// categorizeError determines the error code and HTTP status code
func (h *CentralizedErrorHandler) categorizeError(err error) (string, int) {
	message := strings.ToLower(err.Error())

	switch {
	case strings.Contains(message, "not found"):
		return "NOT_FOUND", http.StatusNotFound
	case strings.Contains(message, "unauthorized") || strings.Contains(message, "authentication"):
		return "UNAUTHORIZED", http.StatusUnauthorized
	case strings.Contains(message, "forbidden") || strings.Contains(message, "permission"):
		return "FORBIDDEN", http.StatusForbidden
	case strings.Contains(message, "validation") || strings.Contains(message, "invalid"):
		return "VALIDATION_ERROR", http.StatusBadRequest
	case strings.Contains(message, "conflict") || strings.Contains(message, "duplicate"):
		return "CONFLICT", http.StatusConflict
	case strings.Contains(message, "timeout"):
		return "TIMEOUT", http.StatusRequestTimeout
	case strings.Contains(message, "rate limit"):
		return "RATE_LIMITED", http.StatusTooManyRequests
	case strings.Contains(message, "service unavailable"):
		return "SERVICE_UNAVAILABLE", http.StatusServiceUnavailable
	case strings.Contains(message, "database") || strings.Contains(message, "connection"):
		return "DATABASE_ERROR", http.StatusInternalServerError
	default:
		return "INTERNAL_ERROR", http.StatusInternalServerError
	}
}

// isRetryableError determines if an error is retryable
func (h *CentralizedErrorHandler) isRetryableError(err error) bool {
	message := strings.ToLower(err.Error())

	for _, retryablePattern := range h.config.RetryableErrors {
		if strings.Contains(message, strings.ToLower(retryablePattern)) {
			return true
		}
	}

	return false
}

// sanitizeErrorMessage removes sensitive information from error messages
func (h *CentralizedErrorHandler) sanitizeErrorMessage(message string) string {
	// Limit message length
	if len(message) > h.config.MaxErrorLength {
		message = message[:h.config.MaxErrorLength] + "..."
	}

	// Remove sensitive patterns
	sensitivePatterns := []string{
		"password",
		"token",
		"secret",
		"key",
		"credential",
	}

	lowerMessage := strings.ToLower(message)
	for _, pattern := range sensitivePatterns {
		if strings.Contains(lowerMessage, pattern) {
			return "An error occurred while processing your request"
		}
	}

	return message
}

// extractRequestContext extracts relevant context from the request
func (h *CentralizedErrorHandler) extractRequestContext(c *gin.Context) map[string]interface{} {
	context := make(map[string]interface{})

	// Basic request info
	context["method"] = c.Request.Method
	context["path"] = c.Request.URL.Path
	context["user_agent"] = c.GetHeader("User-Agent")
	context["ip"] = c.ClientIP()

	// User context if available
	if userID, exists := c.Get("user_id"); exists {
		context["user_id"] = userID
	}

	// Request ID if available
	if requestID, exists := c.Get("request_id"); exists {
		context["request_id"] = requestID
	}

	// Timing information
	if startTime, exists := c.Get("start_time"); exists {
		if start, ok := startTime.(time.Time); ok {
			context["duration_ms"] = time.Since(start).Milliseconds()
		}
	}

	return context
}

// logError logs the error with appropriate level and context
func (h *CentralizedErrorHandler) logError(c *gin.Context, appErr *AppError) {
	fields := []zap.Field{
		zap.String("error_code", appErr.Code),
		zap.Int("status_code", appErr.StatusCode),
		zap.String("method", c.Request.Method),
		zap.String("path", c.Request.URL.Path),
		zap.String("ip", c.ClientIP()),
		zap.Bool("retryable", appErr.Retryable),
	}

	// Add user context if available
	if userID, exists := c.Get("user_id"); exists {
		fields = append(fields, zap.Any("user_id", userID))
	}

	// Add stack trace in development
	if h.config.IncludeStackTrace {
		fields = append(fields, zap.String("stack_trace", h.getStackTrace()))
	}

	// Add internal error details
	if appErr.Internal != nil {
		fields = append(fields, zap.Error(appErr.Internal))
	}

	// Log based on severity
	switch appErr.StatusCode {
	case http.StatusInternalServerError, http.StatusServiceUnavailable:
		h.logger.Error("🔴 Server Error", fields...)
	case http.StatusUnauthorized, http.StatusForbidden:
		h.logger.Warn("🟡 Authorization Error", fields...)
	case http.StatusBadRequest, http.StatusNotFound:
		h.logger.Info("🔵 Client Error", fields...)
	default:
		h.logger.Info("ℹ️ Request Error", fields...)
	}
}

// getMonitoringLevel converts HTTP status to monitoring level
func (h *CentralizedErrorHandler) getMonitoringLevel(appErr *AppError) monitoring.ErrorLevel {
	switch appErr.StatusCode {
	case http.StatusInternalServerError, http.StatusServiceUnavailable:
		return monitoring.ErrorLevelCritical
	case http.StatusUnauthorized, http.StatusForbidden, http.StatusRequestTimeout:
		return monitoring.ErrorLevelError
	case http.StatusBadRequest, http.StatusNotFound, http.StatusConflict:
		return monitoring.ErrorLevelWarning
	default:
		return monitoring.ErrorLevelInfo
	}
}

// respondWithError sends error response to client
func (h *CentralizedErrorHandler) respondWithError(c *gin.Context, appErr *AppError) {
	response := gin.H{
		"success":   false,
		"error":     appErr.Message,
		"code":      appErr.Code,
		"timestamp": appErr.Timestamp.Format(time.RFC3339),
		"retryable": appErr.Retryable,
	}

	// Add details in development mode
	if appErr.Details != "" && !h.config.SanitizeErrors {
		response["details"] = appErr.Details
	}

	// Add request ID for tracking
	if requestID, exists := c.Get("request_id"); exists {
		response["request_id"] = requestID
	}

	c.JSON(appErr.StatusCode, response)
}

// getStackTrace returns current stack trace
func (h *CentralizedErrorHandler) getStackTrace() string {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	return string(buf[:n])
}

// Middleware returns a Gin middleware for centralized error handling
func (h *CentralizedErrorHandler) Middleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		var err error

		switch v := recovered.(type) {
		case error:
			err = v
		case string:
			err = fmt.Errorf("%s", v)
		default:
			err = fmt.Errorf("unknown panic: %v", v)
		}

		// Handle the panic as an error
		h.HandleError(c, &AppError{
			Code:       "PANIC_RECOVERED",
			Message:    "Internal server error",
			Details:    err.Error(),
			StatusCode: http.StatusInternalServerError,
			Internal:   err,
			Retryable:  false,
			Timestamp:  time.Now(),
		})
	})
}

// Predefined error constructors

func NewValidationError(message string, details ...string) *AppError {
	detail := ""
	if len(details) > 0 {
		detail = details[0]
	}

	return &AppError{
		Code:       "VALIDATION_ERROR",
		Message:    message,
		Details:    detail,
		StatusCode: http.StatusBadRequest,
		Retryable:  false,
		Timestamp:  time.Now(),
	}
}

func NewNotFoundError(resource string) *AppError {
	return &AppError{
		Code:       "NOT_FOUND",
		Message:    fmt.Sprintf("%s not found", resource),
		StatusCode: http.StatusNotFound,
		Retryable:  false,
		Timestamp:  time.Now(),
	}
}

func NewUnauthorizedError(message string) *AppError {
	return &AppError{
		Code:       "UNAUTHORIZED",
		Message:    message,
		StatusCode: http.StatusUnauthorized,
		Retryable:  false,
		Timestamp:  time.Now(),
	}
}

func NewInternalError(message string, internal error) *AppError {
	return &AppError{
		Code:       "INTERNAL_ERROR",
		Message:    message,
		StatusCode: http.StatusInternalServerError,
		Internal:   internal,
		Retryable:  true,
		Timestamp:  time.Now(),
	}
}

func NewServiceUnavailableError(service string) *AppError {
	return &AppError{
		Code:       "SERVICE_UNAVAILABLE",
		Message:    fmt.Sprintf("Service %s is currently unavailable", service),
		StatusCode: http.StatusServiceUnavailable,
		Retryable:  true,
		Timestamp:  time.Now(),
	}
}

// IsNotFound checks if an error is a not found error
func IsNotFound(err error) bool {
	if err == nil {
		return false
	}

	// Check if it's our AppError
	if appErr, ok := err.(*AppError); ok {
		return appErr.Code == "NOT_FOUND"
	}

	// Check for common not found error patterns
	errStr := err.Error()
	return strings.Contains(strings.ToLower(errStr), "not found") ||
		strings.Contains(strings.ToLower(errStr), "no rows") ||
		strings.Contains(strings.ToLower(errStr), "record not found")
}

package resilience

import (
	"context"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"time"

	"go.uber.org/zap"
)

// RetryConfig contains configuration for retry mechanism
type RetryConfig struct {
	MaxAttempts     int           `json:"max_attempts"`
	InitialDelay    time.Duration `json:"initial_delay"`
	MaxDelay        time.Duration `json:"max_delay"`
	BackoffFactor   float64       `json:"backoff_factor"`
	Jitter          bool          `json:"jitter"`
	RetryableErrors []string      `json:"retryable_errors"`
	IsRetryable     func(error) bool
	OnRetry         func(attempt int, err error, delay time.Duration)
	BeforeRetry     func(attempt int) error
	Context         context.Context
}

// DefaultRetryConfig returns default retry configuration
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxAttempts:   3,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        true,
		RetryableErrors: []string{
			"connection refused",
			"timeout",
			"temporary failure",
			"service unavailable",
			"network error",
		},
		IsRetryable: func(err error) bool {
			if err == nil {
				return false
			}
			// Default retryable errors
			errMsg := err.Error()
			retryablePatterns := []string{
				"connection refused",
				"timeout",
				"temporary",
				"unavailable",
				"network",
			}
			for _, pattern := range retryablePatterns {
				if contains(errMsg, pattern) {
					return true
				}
			}
			return false
		},
	}
}

// RetryManager manages retry operations
type RetryManager struct {
	config *RetryConfig
	logger *zap.Logger
}

// NewRetryManager creates a new retry manager
func NewRetryManager(config *RetryConfig, logger *zap.Logger) *RetryManager {
	if config == nil {
		config = DefaultRetryConfig()
	}

	if logger == nil {
		logger, _ = zap.NewProduction()
	}

	return &RetryManager{
		config: config,
		logger: logger,
	}
}

// Execute executes a function with retry logic
func (rm *RetryManager) Execute(ctx context.Context, operation func() error) error {
	return rm.ExecuteWithConfig(ctx, operation, rm.config)
}

// ExecuteWithConfig executes a function with custom retry configuration
func (rm *RetryManager) ExecuteWithConfig(ctx context.Context, operation func() error, config *RetryConfig) error {
	if config == nil {
		config = rm.config
	}

	var lastErr error

	for attempt := 1; attempt <= config.MaxAttempts; attempt++ {
		// Check context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Execute before retry hook
		if config.BeforeRetry != nil {
			if err := config.BeforeRetry(attempt); err != nil {
				return fmt.Errorf("before retry hook failed: %w", err)
			}
		}

		// Execute the operation
		err := operation()
		if err == nil {
			if attempt > 1 {
				rm.logger.Info("Operation succeeded after retry",
					zap.Int("attempt", attempt),
					zap.Int("total_attempts", config.MaxAttempts),
				)
			}
			return nil
		}

		lastErr = err

		// Check if error is retryable
		if !config.IsRetryable(err) {
			rm.logger.Debug("Error is not retryable, stopping",
				zap.Error(err),
				zap.Int("attempt", attempt),
			)
			return err
		}

		// Don't retry on last attempt
		if attempt == config.MaxAttempts {
			rm.logger.Error("All retry attempts exhausted",
				zap.Error(err),
				zap.Int("max_attempts", config.MaxAttempts),
			)
			break
		}

		// Calculate delay
		delay := rm.calculateDelay(attempt, config)

		rm.logger.Warn("Operation failed, retrying",
			zap.Error(err),
			zap.Int("attempt", attempt),
			zap.Int("max_attempts", config.MaxAttempts),
			zap.Duration("delay", delay),
		)

		// Execute retry callback
		if config.OnRetry != nil {
			config.OnRetry(attempt, err, delay)
		}

		// Wait before retry
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
		}
	}

	return fmt.Errorf("operation failed after %d attempts: %w", config.MaxAttempts, lastErr)
}

// calculateDelay calculates the delay for the next retry
func (rm *RetryManager) calculateDelay(attempt int, config *RetryConfig) time.Duration {
	// Exponential backoff
	delay := float64(config.InitialDelay) * math.Pow(config.BackoffFactor, float64(attempt-1))

	// Apply jitter if enabled
	if config.Jitter {
		jitter := rand.Float64() * 0.1 // 10% jitter
		delay = delay * (1 + jitter)
	}

	// Cap at max delay
	if time.Duration(delay) > config.MaxDelay {
		delay = float64(config.MaxDelay)
	}

	return time.Duration(delay)
}

// RetryWithCircuitBreaker combines retry with circuit breaker
func (rm *RetryManager) RetryWithCircuitBreaker(
	ctx context.Context,
	operation func() error,
	circuitBreaker *CircuitBreaker,
	config *RetryConfig,
) error {
	if config == nil {
		config = rm.config
	}

	return rm.ExecuteWithConfig(ctx, func() error {
		return circuitBreaker.Execute(ctx, operation)
	}, config)
}

// RetryableError wraps an error to indicate it's retryable
type RetryableError struct {
	Err       error
	Retryable bool
}

func (re *RetryableError) Error() string {
	return re.Err.Error()
}

func (re *RetryableError) Unwrap() error {
	return re.Err
}

// NewRetryableError creates a new retryable error
func NewRetryableError(err error, retryable bool) *RetryableError {
	return &RetryableError{
		Err:       err,
		Retryable: retryable,
	}
}

// IsRetryableError checks if an error is retryable
func IsRetryableError(err error) bool {
	var retryableErr *RetryableError
	if errors.As(err, &retryableErr) {
		return retryableErr.Retryable
	}
	return false
}

// RetryStats tracks retry statistics
type RetryStats struct {
	TotalOperations   int64         `json:"total_operations"`
	SuccessfulRetries int64         `json:"successful_retries"`
	FailedRetries     int64         `json:"failed_retries"`
	AverageAttempts   float64       `json:"average_attempts"`
	AverageDelay      time.Duration `json:"average_delay"`
	LastOperationTime time.Time     `json:"last_operation_time"`
}

// RetryStatsCollector collects retry statistics
type RetryStatsCollector struct {
	stats  *RetryStats
	logger *zap.Logger
}

// NewRetryStatsCollector creates a new retry stats collector
func NewRetryStatsCollector(logger *zap.Logger) *RetryStatsCollector {
	return &RetryStatsCollector{
		stats:  &RetryStats{},
		logger: logger,
	}
}

// RecordOperation records a retry operation
func (rsc *RetryStatsCollector) RecordOperation(attempts int, success bool, totalDelay time.Duration) {
	rsc.stats.TotalOperations++
	rsc.stats.LastOperationTime = time.Now()

	if success && attempts > 1 {
		rsc.stats.SuccessfulRetries++
	} else if !success {
		rsc.stats.FailedRetries++
	}

	// Update averages
	rsc.stats.AverageAttempts = (rsc.stats.AverageAttempts*float64(rsc.stats.TotalOperations-1) + float64(attempts)) / float64(rsc.stats.TotalOperations)
	rsc.stats.AverageDelay = time.Duration((int64(rsc.stats.AverageDelay)*int64(rsc.stats.TotalOperations-1) + int64(totalDelay)) / int64(rsc.stats.TotalOperations))
}

// GetStats returns current retry statistics
func (rsc *RetryStatsCollector) GetStats() *RetryStats {
	return rsc.stats
}

// Helper functions

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && s[:len(substr)] == substr) ||
		(len(s) > len(substr) && s[len(s)-len(substr):] == substr) ||
		containsSubstring(s, substr))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// Predefined retry configurations

// QuickRetryConfig for fast operations
func QuickRetryConfig() *RetryConfig {
	config := DefaultRetryConfig()
	config.MaxAttempts = 3
	config.InitialDelay = 50 * time.Millisecond
	config.MaxDelay = 1 * time.Second
	return config
}

// SlowRetryConfig for slow operations
func SlowRetryConfig() *RetryConfig {
	config := DefaultRetryConfig()
	config.MaxAttempts = 5
	config.InitialDelay = 1 * time.Second
	config.MaxDelay = 60 * time.Second
	return config
}

// DatabaseRetryConfig for database operations
func DatabaseRetryConfig() *RetryConfig {
	config := DefaultRetryConfig()
	config.MaxAttempts = 3
	config.InitialDelay = 100 * time.Millisecond
	config.MaxDelay = 5 * time.Second
	config.RetryableErrors = append(config.RetryableErrors,
		"connection reset",
		"connection lost",
		"deadlock",
		"lock timeout",
	)
	return config
}

// NetworkRetryConfig for network operations
func NetworkRetryConfig() *RetryConfig {
	config := DefaultRetryConfig()
	config.MaxAttempts = 5
	config.InitialDelay = 200 * time.Millisecond
	config.MaxDelay = 10 * time.Second
	config.RetryableErrors = append(config.RetryableErrors,
		"connection reset",
		"connection timeout",
		"dns lookup failed",
		"no route to host",
	)
	return config
}

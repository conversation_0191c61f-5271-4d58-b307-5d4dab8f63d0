package services

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// Performance tests for Google OAuth service
// These tests measure performance characteristics under various conditions

func TestGoogleOAuthService_PerformanceCharacteristics(t *testing.T) {
	service, err := NewGoogleOAuthService("test-client-id", "test-client-secret")
	if err != nil {
		t.Fatalf("Failed to create Google OAuth service: %v", err)
	}
	ctx := context.Background()

	t.Run("Single Request Performance", func(t *testing.T) {
		// Measure single request performance
		start := time.Now()
		_, err := service.VerifyIDToken(ctx, "invalid-token")
		duration := time.Since(start)

		assert.Error(t, err) // Should fail with invalid token
		assert.Less(t, duration, 5*time.Second, "Single request should complete quickly")

		t.Logf("Single request took: %v", duration)
	})

	t.Run("Batch Request Performance", func(t *testing.T) {
		const batchSize = 100
		tokens := make([]string, batchSize)
		for i := 0; i < batchSize; i++ {
			tokens[i] = fmt.Sprintf("invalid-token-%d", i)
		}

		start := time.Now()
		for _, token := range tokens {
			_, _ = service.VerifyIDToken(ctx, token)
		}
		totalDuration := time.Since(start)

		avgDuration := totalDuration / time.Duration(batchSize)
		assert.Less(t, avgDuration, 1*time.Second, "Average request time should be reasonable")

		t.Logf("Batch of %d requests took: %v (avg: %v per request)",
			batchSize, totalDuration, avgDuration)
	})

	t.Run("Memory Usage Under Load", func(t *testing.T) {
		// Force garbage collection before test
		runtime.GC()

		var m1, m2 runtime.MemStats
		runtime.ReadMemStats(&m1)

		// Perform many operations
		const numOperations = 1000
		for i := 0; i < numOperations; i++ {
			_, _ = service.VerifyIDToken(ctx, fmt.Sprintf("token-%d", i))
		}

		runtime.GC()
		runtime.ReadMemStats(&m2)

		memoryIncrease := m2.Alloc - m1.Alloc
		t.Logf("Memory increase after %d operations: %d bytes", numOperations, memoryIncrease)

		// Memory increase should be reasonable (less than 10MB for 1000 operations)
		assert.Less(t, memoryIncrease, uint64(10*1024*1024),
			"Memory usage should not grow excessively")
	})
}

func TestGoogleOAuthService_ConcurrencyPerformance(t *testing.T) {
	service, err := NewGoogleOAuthService("test-client-id", "test-client-secret")
	if err != nil {
		t.Fatalf("Failed to create GoogleOAuthService: %v", err)
	}
	ctx := context.Background()

	t.Run("Concurrent Request Handling", func(t *testing.T) {
		const numGoroutines = 50
		const requestsPerGoroutine = 20

		var wg sync.WaitGroup
		results := make(chan time.Duration, numGoroutines*requestsPerGoroutine)

		start := time.Now()

		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(goroutineID int) {
				defer wg.Done()

				for j := 0; j < requestsPerGoroutine; j++ {
					requestStart := time.Now()
					_, _ = service.VerifyIDToken(ctx, fmt.Sprintf("token-%d-%d", goroutineID, j))
					requestDuration := time.Since(requestStart)
					results <- requestDuration
				}
			}(i)
		}

		wg.Wait()
		close(results)

		totalTime := time.Since(start)

		// Analyze results
		var totalRequestTime time.Duration
		var maxRequestTime time.Duration
		var minRequestTime time.Duration = time.Hour // Initialize to large value
		requestCount := 0

		for duration := range results {
			totalRequestTime += duration
			requestCount++

			if duration > maxRequestTime {
				maxRequestTime = duration
			}
			if duration < minRequestTime {
				minRequestTime = duration
			}
		}

		avgRequestTime := totalRequestTime / time.Duration(requestCount)

		t.Logf("Concurrent performance stats:")
		t.Logf("  Total time: %v", totalTime)
		t.Logf("  Total requests: %d", requestCount)
		t.Logf("  Average request time: %v", avgRequestTime)
		t.Logf("  Min request time: %v", minRequestTime)
		t.Logf("  Max request time: %v", maxRequestTime)
		t.Logf("  Requests per second: %.2f", float64(requestCount)/totalTime.Seconds())

		// Performance assertions
		assert.Less(t, avgRequestTime, 2*time.Second, "Average request time should be reasonable")
		assert.Less(t, maxRequestTime, 10*time.Second, "No request should take too long")
		assert.Greater(t, float64(requestCount)/totalTime.Seconds(), 10.0,
			"Should handle at least 10 requests per second")
	})

	t.Run("Goroutine Leak Detection", func(t *testing.T) {
		initialGoroutines := runtime.NumGoroutine()

		// Perform concurrent operations
		const numOperations = 100
		var wg sync.WaitGroup

		for i := 0; i < numOperations; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				_, _ = service.VerifyIDToken(ctx, fmt.Sprintf("token-%d", id))
			}(i)
		}

		wg.Wait()

		// Allow some time for cleanup
		time.Sleep(100 * time.Millisecond)
		runtime.GC()

		finalGoroutines := runtime.NumGoroutine()
		goroutineIncrease := finalGoroutines - initialGoroutines

		t.Logf("Goroutines before: %d, after: %d, increase: %d",
			initialGoroutines, finalGoroutines, goroutineIncrease)

		// Should not leak significant number of goroutines
		assert.Less(t, goroutineIncrease, 10, "Should not leak goroutines")
	})
}

func TestGoogleOAuthService_ScalabilityLimits(t *testing.T) {
	service, err := NewGoogleOAuthService("test-client-id", "test-client-secret")
	if err != nil {
		t.Fatalf("Failed to create GoogleOAuthService: %v", err)
	}
	ctx := context.Background()

	t.Run("High Load Simulation", func(t *testing.T) {
		// Simulate high load scenario
		const highLoadGoroutines = 100
		const requestsPerGoroutine = 50

		var wg sync.WaitGroup
		errorCount := int64(0)
		successCount := int64(0)
		var mu sync.Mutex

		start := time.Now()

		for i := 0; i < highLoadGoroutines; i++ {
			wg.Add(1)
			go func(goroutineID int) {
				defer wg.Done()

				localErrors := 0
				localSuccesses := 0

				for j := 0; j < requestsPerGoroutine; j++ {
					_, err := service.VerifyIDToken(ctx, fmt.Sprintf("token-%d-%d", goroutineID, j))
					if err != nil {
						localErrors++
					} else {
						localSuccesses++
					}
				}

				mu.Lock()
				errorCount += int64(localErrors)
				successCount += int64(localSuccesses)
				mu.Unlock()
			}(i)
		}

		wg.Wait()
		totalTime := time.Since(start)
		totalRequests := highLoadGoroutines * requestsPerGoroutine

		t.Logf("High load test results:")
		t.Logf("  Total requests: %d", totalRequests)
		t.Logf("  Total time: %v", totalTime)
		t.Logf("  Requests per second: %.2f", float64(totalRequests)/totalTime.Seconds())
		t.Logf("  Error count: %d", errorCount)
		t.Logf("  Success count: %d", successCount)

		// System should remain responsive under high load
		assert.Less(t, totalTime, 2*time.Minute, "High load test should complete in reasonable time")
		assert.Greater(t, float64(totalRequests)/totalTime.Seconds(), 50.0,
			"Should maintain reasonable throughput under load")
	})

	t.Run("Resource Exhaustion Protection", func(t *testing.T) {
		// Test behavior when system resources are stressed
		const extremeLoad = 200

		var wg sync.WaitGroup
		completed := int64(0)
		var mu sync.Mutex

		// Set a reasonable timeout for the entire test
		testCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
		defer cancel()

		start := time.Now()

		for i := 0; i < extremeLoad; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				// Each goroutine makes multiple requests
				for j := 0; j < 10; j++ {
					select {
					case <-testCtx.Done():
						return
					default:
						_, _ = service.VerifyIDToken(testCtx, fmt.Sprintf("extreme-token-%d-%d", id, j))

						mu.Lock()
						completed++
						mu.Unlock()
					}
				}
			}(i)
		}

		wg.Wait()
		totalTime := time.Since(start)

		t.Logf("Resource exhaustion test:")
		t.Logf("  Completed requests: %d", completed)
		t.Logf("  Total time: %v", totalTime)
		t.Logf("  System remained responsive: %v", totalTime < 30*time.Second)

		// System should not crash or hang under extreme load
		assert.Greater(t, completed, int64(0), "Should complete some requests even under extreme load")
		assert.Less(t, totalTime, 35*time.Second, "Should not hang indefinitely")
	})
}

func TestGoogleOAuthService_LatencyCharacteristics(t *testing.T) {
	service, err := NewGoogleOAuthService("test-client-id", "test-client-secret")
	if err != nil {
		t.Fatalf("Failed to create GoogleOAuthService: %v", err)
	}
	ctx := context.Background()

	t.Run("Latency Distribution Analysis", func(t *testing.T) {
		const numSamples = 200
		latencies := make([]time.Duration, numSamples)

		// Collect latency samples
		for i := 0; i < numSamples; i++ {
			start := time.Now()
			_, _ = service.VerifyIDToken(ctx, fmt.Sprintf("latency-token-%d", i))
			latencies[i] = time.Since(start)
		}

		// Calculate statistics
		var total time.Duration
		min := latencies[0]
		max := latencies[0]

		for _, latency := range latencies {
			total += latency
			if latency < min {
				min = latency
			}
			if latency > max {
				max = latency
			}
		}

		avg := total / time.Duration(numSamples)

		// Calculate percentiles (simple approximation)
		// Sort latencies for percentile calculation
		for i := 0; i < len(latencies)-1; i++ {
			for j := i + 1; j < len(latencies); j++ {
				if latencies[i] > latencies[j] {
					latencies[i], latencies[j] = latencies[j], latencies[i]
				}
			}
		}

		p50 := latencies[numSamples/2]
		p95 := latencies[int(float64(numSamples)*0.95)]
		p99 := latencies[int(float64(numSamples)*0.99)]

		t.Logf("Latency distribution:")
		t.Logf("  Min: %v", min)
		t.Logf("  Max: %v", max)
		t.Logf("  Average: %v", avg)
		t.Logf("  P50 (median): %v", p50)
		t.Logf("  P95: %v", p95)
		t.Logf("  P99: %v", p99)

		// Performance assertions
		assert.Less(t, avg, 2*time.Second, "Average latency should be reasonable")
		assert.Less(t, p95, 5*time.Second, "95th percentile should be acceptable")
		assert.Less(t, p99, 10*time.Second, "99th percentile should not be excessive")
	})

	t.Run("Warm-up Performance", func(t *testing.T) {
		// Test cold start vs warm performance

		// Cold start
		coldStart := time.Now()
		_, _ = service.VerifyIDToken(ctx, "cold-start-token")
		coldDuration := time.Since(coldStart)

		// Warm up with a few requests
		for i := 0; i < 5; i++ {
			_, _ = service.VerifyIDToken(ctx, fmt.Sprintf("warmup-token-%d", i))
		}

		// Warm performance
		warmStart := time.Now()
		_, _ = service.VerifyIDToken(ctx, "warm-token")
		warmDuration := time.Since(warmStart)

		t.Logf("Cold start duration: %v", coldDuration)
		t.Logf("Warm duration: %v", warmDuration)
		t.Logf("Performance improvement: %.2fx", float64(coldDuration)/float64(warmDuration))

		// Warm requests should generally be faster (though not guaranteed due to network variability)
		assert.Less(t, warmDuration, 10*time.Second, "Warm requests should complete quickly")
	})
}

// Benchmark tests for detailed performance measurement
func BenchmarkGoogleOAuthService_DetailedPerformance(b *testing.B) {
	service, err := NewGoogleOAuthService("test-client-id", "test-client-secret")
	if err != nil {
		b.Fatalf("Failed to create GoogleOAuthService: %v", err)
	}
	ctx := context.Background()

	b.Run("VerifyIDToken", func(b *testing.B) {
		b.ResetTimer()
		for range b.N {
			_, _ = service.VerifyIDToken(ctx, "benchmark-token")
		}
	})

	b.Run("VerifyIDTokenWithFallback", func(b *testing.B) {
		b.ResetTimer()
		for range b.N {
			_, _ = service.VerifyIDTokenWithFallback(ctx, "benchmark-token")
		}
	})

	b.Run("VerifyAccessToken", func(b *testing.B) {
		b.ResetTimer()
		for range b.N {
			_, _ = service.VerifyAccessToken(ctx, "benchmark-access-token")
		}
	})
}

func BenchmarkGoogleOAuthService_ConcurrentPerformance(b *testing.B) {
	service, err := NewGoogleOAuthService("test-client-id", "test-client-secret")
	if err != nil {
		b.Fatalf("Failed to create GoogleOAuthService: %v", err)
	}
	ctx := context.Background()

	b.Run("ConcurrentVerifyIDToken", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				_, _ = service.VerifyIDToken(ctx, "concurrent-benchmark-token")
			}
		})
	})

	b.Run("ConcurrentWithDifferentTokens", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			counter := 0
			for pb.Next() {
				token := fmt.Sprintf("concurrent-token-%d", counter)
				_, _ = service.VerifyIDToken(ctx, token)
				counter++
			}
		})
	})
}

func BenchmarkGoogleOAuthService_MemoryAllocation(b *testing.B) {
	service, err := NewGoogleOAuthService("test-client-id", "test-client-secret")
	if err != nil {
		b.Fatalf("Failed to create GoogleOAuthService: %v", err)
	}
	ctx := context.Background()

	b.Run("MemoryUsage", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()

		for range b.N {
			_, _ = service.VerifyIDToken(ctx, "memory-benchmark-token")
		}
	})
}

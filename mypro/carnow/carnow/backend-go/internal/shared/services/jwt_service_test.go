package services

import (
	"strings"
	"testing"
	"time"

	"carnow-backend/internal/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestJWTService_NewJWTService(t *testing.T) {
	tests := []struct {
		name    string
		config  *config.Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: &config.Config{
				JWT: config.JWTConfig{
					Secret:           "test-secret-key-32-characters-long",
					ExpiresIn:        15 * time.Minute,
					RefreshExpiresIn: 7 * 24 * time.Hour,
					Issuer:           "carnow-test",
					Audience:         "carnow-app",
					Algorithm:        "RS256",
				},
				Supabase: config.SupabaseConfig{
					JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
				},
			},
			wantErr: false,
		},
		{
			name: "empty secret key",
			config: &config.Config{
				JWT: config.JWTConfig{
					Secret:           "",
					ExpiresIn:        15 * time.Minute,
					RefreshExpiresIn: 7 * 24 * time.Hour,
					Issuer:           "carnow-test",
					Audience:         "carnow-app",
					Algorithm:        "RS256",
				},
				Supabase: config.SupabaseConfig{
					JWTSecret: "", // Empty to trigger error
				},
			},
			wantErr: true,
		},
		{
			name: "short secret key",
			config: &config.Config{
				JWT: config.JWTConfig{
					Secret:           "short",
					ExpiresIn:        15 * time.Minute,
					RefreshExpiresIn: 7 * 24 * time.Hour,
					Issuer:           "carnow-test",
					Audience:         "carnow-app",
					Algorithm:        "RS256",
				},
				Supabase: config.SupabaseConfig{
					JWTSecret: "short", // Short to potentially trigger error
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := NewJWTService(tt.config)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, service)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
			}
		})
	}
}

func TestJWTService_GenerateTokenPair(t *testing.T) {
	// Setup
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	testCases := []struct {
		name    string
		userID  string
		email   string
		role    string
		wantErr bool
	}{
		{
			name:    "valid user data",
			userID:  "550e8400-e29b-41d4-a716-446655440000",
			email:   "<EMAIL>",
			role:    "authenticated",
			wantErr: false,
		},
		{
			name:    "admin user",
			userID:  "550e8400-e29b-41d4-a716-446655440001",
			email:   "<EMAIL>",
			role:    "admin",
			wantErr: false,
		},
		{
			name:    "empty user ID",
			userID:  "",
			email:   "<EMAIL>",
			role:    "authenticated",
			wantErr: true,
		},
		{
			name:    "empty email",
			userID:  "550e8400-e29b-41d4-a716-446655440000",
			email:   "",
			role:    "authenticated",
			wantErr: true,
		},
		{
			name:    "empty role",
			userID:  "550e8400-e29b-41d4-a716-446655440000",
			email:   "<EMAIL>",
			role:    "",
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tokenPair, err := service.GenerateTokenPair(tc.userID, tc.email, tc.role)

			if tc.wantErr {
				assert.Error(t, err)
				assert.Nil(t, tokenPair)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, tokenPair)
				assert.NotEmpty(t, tokenPair.AccessToken)
				assert.NotEmpty(t, tokenPair.RefreshToken)
				assert.Equal(t, "Bearer", tokenPair.TokenType)
				assert.True(t, tokenPair.ExpiresAt.After(time.Now()))
			}
		})
	}
}

func TestJWTService_ValidateToken(t *testing.T) {
	// Setup
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	// Generate valid token for testing
	userID := "550e8400-e29b-41d4-a716-446655440000"
	email := "<EMAIL>"
	role := "authenticated"

	tokenPair, err := service.GenerateTokenPair(userID, email, role)
	require.NoError(t, err)

	testCases := []struct {
		name    string
		token   string
		wantErr bool
	}{
		{
			name:    "valid access token",
			token:   tokenPair.AccessToken,
			wantErr: false,
		},
		{
			name:    "valid refresh token",
			token:   tokenPair.RefreshToken,
			wantErr: false,
		},
		{
			name:    "empty token",
			token:   "",
			wantErr: true,
		},
		{
			name:    "invalid token format",
			token:   "invalid.token.format",
			wantErr: true,
		},
		{
			name:    "malformed token",
			token:   "not-a-jwt-token",
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			claims, err := service.ValidateToken(tc.token)

			if tc.wantErr {
				assert.Error(t, err)
				assert.Nil(t, claims)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, claims)
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, email, claims.Email)
				assert.Equal(t, role, claims.Role)
			}
		})
	}
}

func TestJWTService_RefreshToken(t *testing.T) {
	// Setup
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	// Generate tokens for testing
	userID := "550e8400-e29b-41d4-a716-446655440000"
	email := "<EMAIL>"
	role := "authenticated"

	tokenPair, err := service.GenerateTokenPair(userID, email, role)
	require.NoError(t, err)

	testCases := []struct {
		name         string
		refreshToken string
		wantErr      bool
	}{
		{
			name:         "valid refresh token",
			refreshToken: tokenPair.RefreshToken,
			wantErr:      false,
		},
		{
			name:         "access token instead of refresh token",
			refreshToken: tokenPair.AccessToken,
			wantErr:      true,
		},
		{
			name:         "empty refresh token",
			refreshToken: "",
			wantErr:      true,
		},
		{
			name:         "invalid refresh token",
			refreshToken: "invalid.token.format",
			wantErr:      true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			newTokenPair, err := service.RefreshToken(tc.refreshToken)

			if tc.wantErr {
				assert.Error(t, err)
				assert.Nil(t, newTokenPair)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, newTokenPair)
				assert.NotEmpty(t, newTokenPair.AccessToken)
				assert.NotEmpty(t, newTokenPair.RefreshToken)
				assert.NotEqual(t, tokenPair.AccessToken, newTokenPair.AccessToken)
				assert.NotEqual(t, tokenPair.RefreshToken, newTokenPair.RefreshToken)
			}
		})
	}
}

func TestJWTService_GetPublicKeyPEM(t *testing.T) {
	// Setup
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	publicKeyPEM, err := service.GetPublicKeyPEM()
	assert.NoError(t, err)
	assert.NotEmpty(t, publicKeyPEM)
	assert.Contains(t, publicKeyPEM, "BEGIN PUBLIC KEY")
	assert.Contains(t, publicKeyPEM, "END PUBLIC KEY")
}

func TestJWTService_TokenExpiration(t *testing.T) {
	// Setup with short expiration for testing
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        1 * time.Millisecond, // Very short for testing
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	// Generate token
	tokenPair, err := service.GenerateTokenPair(
		"550e8400-e29b-41d4-a716-446655440000",
		"<EMAIL>",
		"authenticated",
	)
	require.NoError(t, err)

	// Wait for token to expire
	time.Sleep(10 * time.Millisecond)

	// Try to validate expired token
	_, err = service.ValidateToken(tokenPair.AccessToken)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "token is expired")
}

func TestJWTService_ClaimsStructure(t *testing.T) {
	// Setup
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	userID := "550e8400-e29b-41d4-a716-446655440000"
	email := "<EMAIL>"
	role := "authenticated"

	tokenPair, err := service.GenerateTokenPair(userID, email, role)
	require.NoError(t, err)

	// Validate and check claims structure
	claims, err := service.ValidateToken(tokenPair.AccessToken)
	require.NoError(t, err)

	// Check all required fields
	assert.Equal(t, userID, claims.UserID)
	assert.Equal(t, email, claims.Email)
	assert.Equal(t, role, claims.Role)
	assert.Equal(t, "access", claims.TokenType)
	assert.Equal(t, "carnow-test", claims.Issuer)
	assert.True(t, claims.ExpiresAt.Time.After(time.Now()))
	assert.True(t, claims.IssuedAt.Time.Before(time.Now()))
}

func TestJWTService_ThreadSafety(t *testing.T) {
	// Setup
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	// Test concurrent token generation
	const numGoroutines = 10
	done := make(chan bool, numGoroutines)
	errors := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()

			userID := "550e8400-e29b-41d4-a716-44665544000" + string(rune('0'+id))
			email := "test" + string(rune('0'+id)) + "@carnow.com"
			role := "authenticated"

			tokenPair, err := service.GenerateTokenPair(userID, email, role)
			if err != nil {
				errors <- err
				return
			}

			_, err = service.ValidateToken(tokenPair.AccessToken)
			if err != nil {
				errors <- err
				return
			}
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// Check for any errors
	select {
	case err := <-errors:
		t.Errorf("Concurrent operation failed: %v", err)
	default:
		// No errors, test passed
	}
}

// Benchmark tests
func BenchmarkJWTService_GenerateTokenPair(b *testing.B) {
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(b, err)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.GenerateTokenPair(
			"550e8400-e29b-41d4-a716-446655440000",
			"<EMAIL>",
			"authenticated",
		)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkJWTService_ValidateToken(b *testing.B) {
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(b, err)

	tokenPair, err := service.GenerateTokenPair(
		"550e8400-e29b-41d4-a716-446655440000",
		"<EMAIL>",
		"authenticated",
	)
	require.NoError(b, err)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.ValidateToken(tokenPair.AccessToken)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Enhanced Tests for Production Features

// TestJWTService_TokenBlacklisting tests the token revocation functionality
func TestJWTService_TokenBlacklisting(t *testing.T) {
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	// Generate token pair
	tokenPair, err := service.GenerateTokenPair("user123", "<EMAIL>", "authenticated")
	require.NoError(t, err)

	// Token should be valid initially
	_, err = service.ValidateToken(tokenPair.AccessToken)
	assert.NoError(t, err)

	// Check token is not revoked initially
	assert.False(t, service.IsTokenRevoked(tokenPair.AccessToken))

	// Revoke the token
	err = service.RevokeToken(tokenPair.AccessToken)
	assert.NoError(t, err)

	// Check token is now revoked
	assert.True(t, service.IsTokenRevoked(tokenPair.AccessToken))

	// Test edge cases
	err = service.RevokeToken("") // Empty token ID
	assert.Error(t, err)

	assert.False(t, service.IsTokenRevoked("")) // Empty token ID
}

// TestJWTService_EnhancedRefreshToken tests the enhanced refresh token mechanism
func TestJWTService_EnhancedRefreshToken(t *testing.T) {
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	// Generate initial token pair
	tokenPair, err := service.GenerateTokenPair("user123", "<EMAIL>", "authenticated")
	require.NoError(t, err)

	// Test successful refresh
	newTokenPair, err := service.RefreshToken(tokenPair.RefreshToken)
	assert.NoError(t, err)
	assert.NotNil(t, newTokenPair)
	assert.NotEmpty(t, newTokenPair.AccessToken)
	assert.NotEmpty(t, newTokenPair.RefreshToken)
	assert.NotEqual(t, tokenPair.AccessToken, newTokenPair.AccessToken)
	assert.NotEqual(t, tokenPair.RefreshToken, newTokenPair.RefreshToken)

	// Test that old refresh token is revoked after use
	_, err = service.RefreshToken(tokenPair.RefreshToken)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "revoked")

	// Test refresh with invalid tokens
	testCases := []struct {
		name        string
		token       string
		expectedErr string
	}{
		{
			name:        "empty token",
			token:       "",
			expectedErr: "token is empty",
		},
		{
			name:        "invalid format",
			token:       "invalid.token",
			expectedErr: "invalid JWT format",
		},
		{
			name:        "malformed token",
			token:       "header.payload.signature.extra",
			expectedErr: "invalid JWT format",
		},
		{
			name:        "access token instead of refresh",
			token:       newTokenPair.AccessToken,
			expectedErr: "not a refresh token",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			_, err := service.RefreshToken(tc.token)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), tc.expectedErr)
		})
	}
}

// TestJWTService_TokenFormatValidation tests the token format validation
func TestJWTService_TokenFormatValidation(t *testing.T) {
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	// Test valid token format
	tokenPair, err := service.GenerateTokenPair("user123", "<EMAIL>", "authenticated")
	require.NoError(t, err)

	// Use reflection to access private method for testing
	// Note: In production, this would be tested through public methods
	validToken := tokenPair.AccessToken
	parts := strings.Split(validToken, ".")
	assert.Len(t, parts, 3, "Valid JWT should have 3 parts")

	// Test invalid formats through refresh token (which calls validateTokenFormat)
	invalidTokens := []struct {
		name  string
		token string
	}{
		{"empty", ""},
		{"single part", "onlyonepart"},
		{"two parts", "header.payload"},
		{"four parts", "header.payload.signature.extra"},
		{"empty part", "header..signature"},
		{"invalid base64", "<EMAIL>"},
	}

	for _, tc := range invalidTokens {
		t.Run(tc.name, func(t *testing.T) {
			_, err := service.RefreshToken(tc.token)
			assert.Error(t, err)
			if tc.token == "" {
				assert.Contains(t, err.Error(), "token is empty")
			} else {
				assert.Contains(t, err.Error(), "invalid")
			}
		})
	}
}

// TestJWTService_KeyRotationSupport tests key rotation functionality
func TestJWTService_KeyRotationSupport(t *testing.T) {
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	// Generate token with current key
	tokenPair1, err := service.GenerateTokenPair("user123", "<EMAIL>", "authenticated")
	require.NoError(t, err)

	// Validate token works
	claims1, err := service.ValidateToken(tokenPair1.AccessToken)
	assert.NoError(t, err)
	assert.Equal(t, "user123", claims1.UserID)

	// Test that tokens include key ID for rotation support
	// This is verified by the fact that tokens can be validated
	// In a real key rotation scenario, multiple keys would be supported
	assert.NotEmpty(t, tokenPair1.AccessToken)
	assert.NotEmpty(t, tokenPair1.RefreshToken)
}

// TestJWTService_SecurityValidation tests security-related validations
func TestJWTService_SecurityValidation(t *testing.T) {
	config := &config.Config{
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
			Audience:         "carnow-app",
			Algorithm:        "RS256",
		},
		Supabase: config.SupabaseConfig{
			JWTSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNjA5NDU5MjAwLCJleHAiOjE5MjUwMzkyMDB9.6J-2UYW1aBP5CY5K7nKrw0SJjK7gK1xM8FJ3K-gKdXM",
		},
	}

	service, err := NewJWTService(config)
	require.NoError(t, err)

	// Test input validation for token generation
	invalidInputs := []struct {
		name   string
		userID string
		email  string
		role   string
	}{
		{"empty userID", "", "<EMAIL>", "authenticated"},
		{"empty email", "user123", "", "authenticated"},
		{"empty role", "user123", "<EMAIL>", ""},
		{"invalid email format", "user123", "invalid-email", "authenticated"},
	}

	for _, tc := range invalidInputs {
		t.Run(tc.name, func(t *testing.T) {
			_, err := service.GenerateTokenPair(tc.userID, tc.email, tc.role)
			assert.Error(t, err)
		})
	}

	// Test RSA-256 algorithm enforcement
	tokenPair, err := service.GenerateTokenPair("user123", "<EMAIL>", "authenticated")
	require.NoError(t, err)

	// Verify token uses RSA-256 by successful validation
	claims, err := service.ValidateToken(tokenPair.AccessToken)
	assert.NoError(t, err)
	assert.Equal(t, "carnow-test", claims.Issuer)
	assert.Equal(t, "carnow-app", claims.Audience[0])
}

package services

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewGoogleOAuthService(t *testing.T) {
	t.Run("should create service with valid client ID", func(t *testing.T) {
		clientID := "test-client-id.apps.googleusercontent.com"
		clientSecret := "test-client-secret"

		service, err := NewGoogleOAuthService(clientID, clientSecret)

		assert.NoError(t, err)
		assert.NotNil(t, service)
		assert.NotNil(t, service.client)
	})

	t.Run("should fail with empty client ID", func(t *testing.T) {
		service, err := NewGoogleOAuthService("", "secret")

		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "client ID is required")
	})

	t.Run("should fail with invalid client ID format", func(t *testing.T) {
		service, err := NewGoogleOAuthService("invalid-client-id", "secret")

		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.<PERSON>r(), "invalid Google OAuth client ID format")
	})
}

func TestGoogleOAuthService_ValidateTokenFormat(t *testing.T) {
	service, err := NewGoogleOAuthService("test-client-id.apps.googleusercontent.com", "test-secret")
	require.NoError(t, err)

	tests := []struct {
		name        string
		token       string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "empty token",
			token:       "",
			expectError: true,
			errorMsg:    "cannot be empty",
		},
		{
			name:        "too short token",
			token:       "short",
			expectError: true,
			errorMsg:    "too short",
		},
		{
			name:        "too long token",
			token:       string(make([]byte, 5000)),
			expectError: true,
			errorMsg:    "too long",
		},
		{
			name:        "valid JWT format",
			token:       "**********************************************.eyJpc3MiOiJhY2NvdW50cy5nb29nbGUuY29tIn0.signature",
			expectError: false,
		},
		{
			name:        "valid access token format",
			token:       "ya29.a0AfH6SMBxyz123456789abcdefghijklmnopqrstuvwxyz",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateTokenFormat(tt.token)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGoogleOAuthService_VerifyIDToken(t *testing.T) {
	service, err := NewGoogleOAuthService("test-client-id.apps.googleusercontent.com", "test-secret")
	require.NoError(t, err)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	t.Run("should handle empty token", func(t *testing.T) {
		userInfo, err := service.VerifyIDToken(ctx, "")
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "cannot be empty")
	})

	t.Run("should handle invalid token format", func(t *testing.T) {
		userInfo, err := service.VerifyIDToken(ctx, "invalid-token")
		assert.Error(t, err)
		assert.Nil(t, userInfo)
	})
}

func TestGoogleOAuthService_VerifyIDTokenWithFallback(t *testing.T) {
	service, err := NewGoogleOAuthService("test-client-id.apps.googleusercontent.com", "test-secret")
	require.NoError(t, err)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	t.Run("should handle empty token", func(t *testing.T) {
		userInfo, err := service.VerifyIDTokenWithFallback(ctx, "")
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "cannot be empty")
	})

	t.Run("should handle invalid token", func(t *testing.T) {
		userInfo, err := service.VerifyIDTokenWithFallback(ctx, "invalid-token")
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "verification failed")
	})
}

func TestGoogleOAuthService_VerifyAccessToken(t *testing.T) {
	service, err := NewGoogleOAuthService("test-client-id.apps.googleusercontent.com", "test-secret")
	require.NoError(t, err)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	t.Run("should handle empty access token", func(t *testing.T) {
		userInfo, err := service.VerifyAccessToken(ctx, "")
		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "cannot be empty")
	})

	t.Run("should handle invalid access token", func(t *testing.T) {
		userInfo, err := service.VerifyAccessToken(ctx, "invalid-access-token")
		assert.Error(t, err)
		assert.Nil(t, userInfo)
	})
}

func TestMaskClientID(t *testing.T) {
	tests := []struct {
		name     string
		clientID string
		expected string
	}{
		{
			name:     "short client ID",
			clientID: "short",
			expected: "***",
		},
		{
			name:     "normal client ID",
			clientID: "630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com",
			expected: "6309803784***ontent.com",
		},
		{
			name:     "medium client ID",
			clientID: "1234567890abcdefghij",
			expected: "***",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := maskClientID(tt.clientID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGoogleOAuthService_GetClientID(t *testing.T) {
	clientID := "630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com"
	service, err := NewGoogleOAuthService(clientID, "secret")
	require.NoError(t, err)

	maskedID := service.GetClientID()
	assert.Contains(t, maskedID, "***")
	assert.NotEqual(t, clientID, maskedID)
}

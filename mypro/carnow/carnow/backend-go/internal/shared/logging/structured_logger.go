package logging

import (
	"context"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// StructuredLogger provides structured logging with correlation IDs
type StructuredLogger struct {
	logger *zap.Logger
	config *LoggerConfig
}

// LoggerConfig contains configuration for structured logging
type LoggerConfig struct {
	Level            string `json:"level"`
	Environment      string `json:"environment"`
	EnableStackTrace bool   `json:"enable_stack_trace"`
	EnableCaller     bool   `json:"enable_caller"`
	OutputFormat     string `json:"output_format"` // json, console
	OutputPaths      []string `json:"output_paths"`
	ErrorOutputPaths []string `json:"error_output_paths"`
}

// DefaultLoggerConfig returns default configuration
func DefaultLoggerConfig() *LoggerConfig {
	return &LoggerConfig{
		Level:            "info",
		Environment:      "development",
		EnableStackTrace: true,
		EnableCaller:     true,
		OutputFormat:     "json",
		OutputPaths:      []string{"stdout"},
		ErrorOutputPaths: []string{"stderr"},
	}
}

// LogContext contains contextual information for logging
type LogContext struct {
	CorrelationID string                 `json:"correlation_id"`
	RequestID     string                 `json:"request_id,omitempty"`
	UserID        string                 `json:"user_id,omitempty"`
	SessionID     string                 `json:"session_id,omitempty"`
	Operation     string                 `json:"operation,omitempty"`
	Component     string                 `json:"component,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// NewStructuredLogger creates a new structured logger
func NewStructuredLogger(config *LoggerConfig) (*StructuredLogger, error) {
	if config == nil {
		config = DefaultLoggerConfig()
	}

	// Parse log level
	level, err := zapcore.ParseLevel(config.Level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}

	// Create encoder config
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.LevelKey = "level"
	encoderConfig.MessageKey = "message"
	encoderConfig.CallerKey = "caller"
	encoderConfig.StacktraceKey = "stacktrace"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.LowercaseLevelEncoder
	encoderConfig.EncodeCaller = zapcore.ShortCallerEncoder

	// Create encoder
	var encoder zapcore.Encoder
	if config.OutputFormat == "console" {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	}

	// Create core
	core := zapcore.NewCore(
		encoder,
		zapcore.NewMultiWriteSyncer(zapcore.AddSync(zapcore.Lock(zapcore.AddSync(zapcore.NewMultiWriteSyncer())))),
		level,
	)

	// Create logger options
	options := []zap.Option{
		zap.AddCaller(),
		zap.Fields(
			zap.String("service", "carnow-auth"),
			zap.String("environment", config.Environment),
			zap.String("version", "1.0.0"),
		),
	}

	if config.EnableStackTrace {
		options = append(options, zap.AddStacktrace(zapcore.ErrorLevel))
	}

	logger := zap.New(core, options...)

	return &StructuredLogger{
		logger: logger,
		config: config,
	}, nil
}

// WithContext creates a logger with contextual information
func (sl *StructuredLogger) WithContext(ctx context.Context) *ContextualLogger {
	logCtx := ExtractLogContext(ctx)
	if logCtx.CorrelationID == "" {
		logCtx.CorrelationID = GenerateCorrelationID()
	}

	return &ContextualLogger{
		logger:  sl.logger,
		context: logCtx,
	}
}

// ContextualLogger provides logging with context
type ContextualLogger struct {
	logger  *zap.Logger
	context LogContext
}

// Info logs an info message with context
func (cl *ContextualLogger) Info(message string, fields ...zap.Field) {
	fields = cl.addContextFields(fields)
	cl.logger.Info(message, fields...)
}

// Warn logs a warning message with context
func (cl *ContextualLogger) Warn(message string, fields ...zap.Field) {
	fields = cl.addContextFields(fields)
	cl.logger.Warn(message, fields...)
}

// Error logs an error message with context
func (cl *ContextualLogger) Error(message string, fields ...zap.Field) {
	fields = cl.addContextFields(fields)
	cl.logger.Error(message, fields...)
}

// Debug logs a debug message with context
func (cl *ContextualLogger) Debug(message string, fields ...zap.Field) {
	fields = cl.addContextFields(fields)
	cl.logger.Debug(message, fields...)
}

// Fatal logs a fatal message with context and exits
func (cl *ContextualLogger) Fatal(message string, fields ...zap.Field) {
	fields = cl.addContextFields(fields)
	cl.logger.Fatal(message, fields...)
}

// AuthInfo logs authentication-specific info
func (cl *ContextualLogger) AuthInfo(operation string, message string, fields ...zap.Field) {
	fields = append(fields,
		zap.String("auth_operation", operation),
		zap.String("component", "authentication"),
	)
	cl.Info(message, fields...)
}

// AuthError logs authentication-specific error
func (cl *ContextualLogger) AuthError(operation string, message string, err error, fields ...zap.Field) {
	fields = append(fields,
		zap.String("auth_operation", operation),
		zap.String("component", "authentication"),
		zap.Error(err),
	)
	cl.Error(message, fields...)
}

// AuthWarn logs authentication-specific warning
func (cl *ContextualLogger) AuthWarn(operation string, message string, fields ...zap.Field) {
	fields = append(fields,
		zap.String("auth_operation", operation),
		zap.String("component", "authentication"),
	)
	cl.Warn(message, fields...)
}

// SecurityAlert logs security-related alerts
func (cl *ContextualLogger) SecurityAlert(alertType string, message string, fields ...zap.Field) {
	fields = append(fields,
		zap.String("alert_type", alertType),
		zap.String("component", "security"),
		zap.String("severity", "high"),
		zap.Time("alert_time", time.Now()),
	)
	cl.Error("🚨 SECURITY ALERT: "+message, fields...)
}

// PerformanceLog logs performance metrics
func (cl *ContextualLogger) PerformanceLog(operation string, duration time.Duration, fields ...zap.Field) {
	fields = append(fields,
		zap.String("operation", operation),
		zap.Duration("duration", duration),
		zap.String("component", "performance"),
	)
	
	// Log as warning if slow
	if duration > 2*time.Second {
		cl.Warn("⚠️ Slow Operation Detected", fields...)
	} else {
		cl.Info("📊 Performance Metric", fields...)
	}
}

// addContextFields adds contextual fields to log entries
func (cl *ContextualLogger) addContextFields(fields []zap.Field) []zap.Field {
	contextFields := []zap.Field{
		zap.String("correlation_id", cl.context.CorrelationID),
	}

	if cl.context.RequestID != "" {
		contextFields = append(contextFields, zap.String("request_id", cl.context.RequestID))
	}
	if cl.context.UserID != "" {
		contextFields = append(contextFields, zap.String("user_id", cl.context.UserID))
	}
	if cl.context.SessionID != "" {
		contextFields = append(contextFields, zap.String("session_id", cl.context.SessionID))
	}
	if cl.context.Operation != "" {
		contextFields = append(contextFields, zap.String("operation", cl.context.Operation))
	}
	if cl.context.Component != "" {
		contextFields = append(contextFields, zap.String("component", cl.context.Component))
	}

	// Add metadata fields
	for key, value := range cl.context.Metadata {
		contextFields = append(contextFields, zap.Any(fmt.Sprintf("meta_%s", key), value))
	}

	return append(contextFields, fields...)
}

// Context management functions

// GenerateCorrelationID generates a new correlation ID
func GenerateCorrelationID() string {
	return uuid.New().String()
}

// ExtractLogContext extracts log context from Go context
func ExtractLogContext(ctx context.Context) LogContext {
	logCtx := LogContext{}

	if correlationID := ctx.Value("correlation_id"); correlationID != nil {
		if id, ok := correlationID.(string); ok {
			logCtx.CorrelationID = id
		}
	}

	if requestID := ctx.Value("request_id"); requestID != nil {
		if id, ok := requestID.(string); ok {
			logCtx.RequestID = id
		}
	}

	if userID := ctx.Value("user_id"); userID != nil {
		if id, ok := userID.(string); ok {
			logCtx.UserID = id
		}
	}

	if sessionID := ctx.Value("session_id"); sessionID != nil {
		if id, ok := sessionID.(string); ok {
			logCtx.SessionID = id
		}
	}

	if operation := ctx.Value("operation"); operation != nil {
		if op, ok := operation.(string); ok {
			logCtx.Operation = op
		}
	}

	if component := ctx.Value("component"); component != nil {
		if comp, ok := component.(string); ok {
			logCtx.Component = comp
		}
	}

	return logCtx
}

// WithLogContext adds log context to Go context
func WithLogContext(ctx context.Context, logCtx LogContext) context.Context {
	ctx = context.WithValue(ctx, "correlation_id", logCtx.CorrelationID)
	
	if logCtx.RequestID != "" {
		ctx = context.WithValue(ctx, "request_id", logCtx.RequestID)
	}
	if logCtx.UserID != "" {
		ctx = context.WithValue(ctx, "user_id", logCtx.UserID)
	}
	if logCtx.SessionID != "" {
		ctx = context.WithValue(ctx, "session_id", logCtx.SessionID)
	}
	if logCtx.Operation != "" {
		ctx = context.WithValue(ctx, "operation", logCtx.Operation)
	}
	if logCtx.Component != "" {
		ctx = context.WithValue(ctx, "component", logCtx.Component)
	}

	return ctx
}

// WithCorrelationID adds correlation ID to context
func WithCorrelationID(ctx context.Context, correlationID string) context.Context {
	return context.WithValue(ctx, "correlation_id", correlationID)
}

// WithRequestID adds request ID to context
func WithRequestID(ctx context.Context, requestID string) context.Context {
	return context.WithValue(ctx, "request_id", requestID)
}

// WithUserID adds user ID to context
func WithUserID(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, "user_id", userID)
}

// WithOperation adds operation name to context
func WithOperation(ctx context.Context, operation string) context.Context {
	return context.WithValue(ctx, "operation", operation)
}

// Middleware for Gin to add correlation ID
func CorrelationIDMiddleware() func(*gin.Context) {
	return func(c *gin.Context) {
		correlationID := c.GetHeader("X-Correlation-ID")
		if correlationID == "" {
			correlationID = GenerateCorrelationID()
		}

		// Add to response header
		c.Header("X-Correlation-ID", correlationID)

		// Add to context
		ctx := WithCorrelationID(c.Request.Context(), correlationID)
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
}

package validation

import (
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/go-playground/validator/v10"
)

// CustomValidator wraps the validator with custom validation rules
type CustomValidator struct {
	validator *validator.Validate
}

// NewCustomValidator creates a new custom validator instance
func NewCustomValidator() *CustomValidator {
	v := validator.New()

	// Register custom validation functions
	v.RegisterValidation("strong_password", validateStrongPassword)
	v.RegisterValidation("safe_string", validateSafeString)
	v.RegisterValidation("no_sql_injection", validateNoSQLInjection)
	v.RegisterValidation("no_xss", validateNoXSS)
	v.RegisterValidation("phone_number", validatePhoneNumber)
	v.RegisterValidation("safe_filename", validateSafeFilename)
	v.RegisterValidation("positive_number", validatePositiveNumber)
	v.RegisterValidation("future_date", validateFutureDate)
	v.RegisterValidation("past_date", validatePastDate)
	v.RegisterValidation("alphanumeric_spaces", validateAlphanumericSpaces)
	v.RegisterValidation("no_html", validateNoHTML)
	v.RegisterValidation("safe_url", validateSafeURL)

	return &CustomValidator{validator: v}
}

// Validate validates a struct using custom rules
func (cv *CustomValidator) Validate(i interface{}) error {
	return cv.validator.Struct(i)
}

// GetValidator returns the underlying validator instance
func (cv *CustomValidator) GetValidator() *validator.Validate {
	return cv.validator
}

// validateStrongPassword validates password strength
func validateStrongPassword(fl validator.FieldLevel) bool {
	password := fl.Field().String()

	if len(password) < 8 {
		return false
	}

	if len(password) > 128 {
		return false
	}

	hasUpper := false
	hasLower := false
	hasDigit := false
	hasSpecial := false

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsDigit(char):
			hasDigit = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	return hasUpper && hasLower && hasDigit && hasSpecial
}

// validateSafeString validates that a string doesn't contain dangerous characters
func validateSafeString(fl validator.FieldLevel) bool {
	str := fl.Field().String()

	// Check for null bytes
	if strings.Contains(str, "\x00") {
		return false
	}

	// Check for control characters (except common ones)
	for _, r := range str {
		if r < 32 && r != 9 && r != 10 && r != 13 {
			return false
		}
	}

	return true
}

// validateNoSQLInjection checks for SQL injection patterns
func validateNoSQLInjection(fl validator.FieldLevel) bool {
	str := strings.ToLower(fl.Field().String())

	// Common SQL injection patterns
	sqlPatterns := []string{
		"union select",
		"drop table",
		"delete from",
		"insert into",
		"update set",
		"exec(",
		"execute(",
		"xp_cmdshell",
		"sp_executesql",
		"' or '1'='1",
		"\" or \"1\"=\"1",
		"' or 1=1",
		"\" or 1=1",
		"'; drop",
		"\"; drop",
		"/*",
		"*/",
		"--",
		"#",
	}

	for _, pattern := range sqlPatterns {
		if strings.Contains(str, pattern) {
			return false
		}
	}

	return true
}

// validateNoXSS checks for XSS patterns
func validateNoXSS(fl validator.FieldLevel) bool {
	str := strings.ToLower(fl.Field().String())

	// Common XSS patterns
	xssPatterns := []string{
		"<script",
		"</script>",
		"javascript:",
		"vbscript:",
		"onload=",
		"onerror=",
		"onclick=",
		"onmouseover=",
		"onfocus=",
		"onblur=",
		"onchange=",
		"onsubmit=",
		"<iframe",
		"<object",
		"<embed",
		"<link",
		"<meta",
		"<style",
		"eval(",
		"expression(",
		"url(",
		"import(",
	}

	for _, pattern := range xssPatterns {
		if strings.Contains(str, pattern) {
			return false
		}
	}

	return true
}

// validatePhoneNumber validates phone number format
func validatePhoneNumber(fl validator.FieldLevel) bool {
	phone := fl.Field().String()

	// Remove common separators
	phone = strings.ReplaceAll(phone, " ", "")
	phone = strings.ReplaceAll(phone, "-", "")
	phone = strings.ReplaceAll(phone, "(", "")
	phone = strings.ReplaceAll(phone, ")", "")
	phone = strings.ReplaceAll(phone, "+", "")

	// Check if it's all digits and reasonable length
	if len(phone) < 10 || len(phone) > 15 {
		return false
	}

	for _, char := range phone {
		if !unicode.IsDigit(char) {
			return false
		}
	}

	return true
}

// validateSafeFilename validates filename safety
func validateSafeFilename(fl validator.FieldLevel) bool {
	filename := fl.Field().String()

	// Check for dangerous characters
	dangerousChars := []string{
		"..", "/", "\\", ":", "*", "?", "\"", "<", ">", "|",
		"\x00", "\n", "\r", "\t",
	}

	for _, char := range dangerousChars {
		if strings.Contains(filename, char) {
			return false
		}
	}

	// Check for reserved names (Windows)
	reservedNames := []string{
		"CON", "PRN", "AUX", "NUL",
		"COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
		"LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9",
	}

	upperFilename := strings.ToUpper(filename)
	for _, reserved := range reservedNames {
		if upperFilename == reserved || strings.HasPrefix(upperFilename, reserved+".") {
			return false
		}
	}

	return len(filename) > 0 && len(filename) <= 255
}

// validatePositiveNumber validates that a number is positive
func validatePositiveNumber(fl validator.FieldLevel) bool {
	switch fl.Field().Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return fl.Field().Int() > 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return fl.Field().Uint() > 0
	case reflect.Float32, reflect.Float64:
		return fl.Field().Float() > 0
	case reflect.String:
		val, err := strconv.ParseFloat(fl.Field().String(), 64)
		return err == nil && val > 0
	}
	return false
}

// validateFutureDate validates that a date is in the future
func validateFutureDate(fl validator.FieldLevel) bool {
	switch fl.Field().Interface().(type) {
	case time.Time:
		return fl.Field().Interface().(time.Time).After(time.Now())
	case string:
		dateStr := fl.Field().String()
		date, err := time.Parse(time.RFC3339, dateStr)
		if err != nil {
			// Try other common formats
			formats := []string{
				"2006-01-02",
				"2006-01-02 15:04:05",
				"01/02/2006",
				"02-01-2006",
			}
			for _, format := range formats {
				if date, err = time.Parse(format, dateStr); err == nil {
					break
				}
			}
		}
		return err == nil && date.After(time.Now())
	}
	return false
}

// validatePastDate validates that a date is in the past
func validatePastDate(fl validator.FieldLevel) bool {
	switch fl.Field().Interface().(type) {
	case time.Time:
		return fl.Field().Interface().(time.Time).Before(time.Now())
	case string:
		dateStr := fl.Field().String()
		date, err := time.Parse(time.RFC3339, dateStr)
		if err != nil {
			// Try other common formats
			formats := []string{
				"2006-01-02",
				"2006-01-02 15:04:05",
				"01/02/2006",
				"02-01-2006",
			}
			for _, format := range formats {
				if date, err = time.Parse(format, dateStr); err == nil {
					break
				}
			}
		}
		return err == nil && date.Before(time.Now())
	}
	return false
}

// validateAlphanumericSpaces validates alphanumeric characters and spaces only
func validateAlphanumericSpaces(fl validator.FieldLevel) bool {
	str := fl.Field().String()

	for _, char := range str {
		if !unicode.IsLetter(char) && !unicode.IsDigit(char) && char != ' ' {
			return false
		}
	}

	return true
}

// validateNoHTML validates that string doesn't contain HTML tags
func validateNoHTML(fl validator.FieldLevel) bool {
	str := fl.Field().String()

	// Simple HTML tag detection
	htmlRegex := regexp.MustCompile(`<[^>]*>`)
	return !htmlRegex.MatchString(str)
}

// validateSafeURL validates URL safety
func validateSafeURL(fl validator.FieldLevel) bool {
	url := strings.ToLower(fl.Field().String())

	// Must start with http or https
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return false
	}

	// Check for dangerous protocols
	dangerousProtocols := []string{
		"javascript:",
		"vbscript:",
		"data:",
		"file:",
		"ftp:",
	}

	for _, protocol := range dangerousProtocols {
		if strings.Contains(url, protocol) {
			return false
		}
	}

	return true
}

// ValidationError represents a validation error with details
type ValidationError struct {
	Field   string `json:"field"`
	Tag     string `json:"tag"`
	Value   string `json:"value"`
	Message string `json:"message"`
}

// FormatValidationErrors formats validator errors into a readable format
func FormatValidationErrors(err error) []ValidationError {
	var errors []ValidationError

	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validationErrors {
			errors = append(errors, ValidationError{
				Field:   e.Field(),
				Tag:     e.Tag(),
				Value:   fmt.Sprintf("%v", e.Value()),
				Message: getErrorMessage(e),
			})
		}
	}

	return errors
}

// getErrorMessage returns a user-friendly error message for validation tags
func getErrorMessage(e validator.FieldError) string {
	switch e.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", e.Field())
	case "email":
		return fmt.Sprintf("%s must be a valid email address", e.Field())
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", e.Field(), e.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s characters long", e.Field(), e.Param())
	case "len":
		return fmt.Sprintf("%s must be exactly %s characters long", e.Field(), e.Param())
	case "strong_password":
		return fmt.Sprintf("%s must contain at least 8 characters with uppercase, lowercase, digit, and special character", e.Field())
	case "safe_string":
		return fmt.Sprintf("%s contains invalid characters", e.Field())
	case "no_sql_injection":
		return fmt.Sprintf("%s contains potentially dangerous SQL patterns", e.Field())
	case "no_xss":
		return fmt.Sprintf("%s contains potentially dangerous script patterns", e.Field())
	case "phone_number":
		return fmt.Sprintf("%s must be a valid phone number", e.Field())
	case "safe_filename":
		return fmt.Sprintf("%s must be a safe filename", e.Field())
	case "positive_number":
		return fmt.Sprintf("%s must be a positive number", e.Field())
	case "future_date":
		return fmt.Sprintf("%s must be a future date", e.Field())
	case "past_date":
		return fmt.Sprintf("%s must be a past date", e.Field())
	case "alphanumeric_spaces":
		return fmt.Sprintf("%s must contain only letters, numbers, and spaces", e.Field())
	case "no_html":
		return fmt.Sprintf("%s must not contain HTML tags", e.Field())
	case "safe_url":
		return fmt.Sprintf("%s must be a safe URL starting with http:// or https://", e.Field())
	case "uuid":
		return fmt.Sprintf("%s must be a valid UUID", e.Field())
	case "numeric":
		return fmt.Sprintf("%s must be a number", e.Field())
	case "alpha":
		return fmt.Sprintf("%s must contain only letters", e.Field())
	case "alphanum":
		return fmt.Sprintf("%s must contain only letters and numbers", e.Field())
	default:
		return fmt.Sprintf("%s is invalid", e.Field())
	}
}

// SanitizeInput sanitizes input by removing/escaping dangerous characters
func SanitizeInput(input string) string {
	// Remove null bytes
	input = strings.ReplaceAll(input, "\x00", "")

	// Remove control characters except common ones
	var result strings.Builder
	for _, r := range input {
		if r >= 32 || r == 9 || r == 10 || r == 13 {
			result.WriteRune(r)
		}
	}

	return strings.TrimSpace(result.String())
}

// ValidateAndSanitize validates and sanitizes input
func ValidateAndSanitize(input string, maxLength int) (string, error) {
	// Sanitize first
	sanitized := SanitizeInput(input)

	// Check length
	if len(sanitized) > maxLength {
		return "", fmt.Errorf("input too long (max %d characters)", maxLength)
	}

	// Check for dangerous patterns
	if !validateNoSQLInjection(validator.FieldLevel(nil)) {
		return "", fmt.Errorf("input contains dangerous SQL patterns")
	}

	if !validateNoXSS(validator.FieldLevel(nil)) {
		return "", fmt.Errorf("input contains dangerous script patterns")
	}

	return sanitized, nil
}

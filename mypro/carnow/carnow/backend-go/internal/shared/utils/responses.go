package responses

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// APIResponse represents the standard API response format
type APIResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Error     *ErrorInfo  `json:"error,omitempty"`
	Meta      *Meta       `json:"meta,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// ErrorInfo represents error information
type ErrorInfo struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Meta represents pagination and additional metadata
type Meta struct {
	Page       int   `json:"page,omitempty"`
	PageSize   int   `json:"page_size,omitempty"`
	Total      int64 `json:"total,omitempty"`
	TotalPages int   `json:"total_pages,omitempty"`
	HasNext    bool  `json:"has_next,omitempty"`
	HasPrev    bool  `json:"has_prev,omitempty"`
}

// PaginatedData represents paginated data structure
type PaginatedData struct {
	Items interface{} `json:"items"`
	Meta  Meta        `json:"meta"`
}

// NewPaginatedData creates a new paginated data structure
func NewPaginatedData(items interface{}, page, pageSize int, total int64) *PaginatedData {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	return &PaginatedData{
		Items: items,
		Meta: Meta{
			Page:       page,
			PageSize:   pageSize,
			Total:      total,
			TotalPages: totalPages,
			HasNext:    page < totalPages,
			HasPrev:    page > 1,
		},
	}
}

// Success responses
func Success(c *gin.Context, data interface{}, message ...string) {
	msg := "Success"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Message:   msg,
		Data:      data,
		Timestamp: time.Now(),
	})
}

func Created(c *gin.Context, data interface{}, message ...string) {
	msg := "Created successfully"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusCreated, APIResponse{
		Success:   true,
		Message:   msg,
		Data:      data,
		Timestamp: time.Now(),
	})
}

func NoContent(c *gin.Context, message ...string) {
	msg := "No content"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusNoContent, APIResponse{
		Success:   true,
		Message:   msg,
		Timestamp: time.Now(),
	})
}

func Paginated(c *gin.Context, data interface{}, page, pageSize int, total int64, message ...string) {
	msg := "Success"
	if len(message) > 0 {
		msg = message[0]
	}

	paginatedData := NewPaginatedData(data, page, pageSize, total)

	c.JSON(http.StatusOK, APIResponse{
		Success:   true,
		Message:   msg,
		Data:      paginatedData.Items,
		Meta:      &paginatedData.Meta,
		Timestamp: time.Now(),
	})
}

// Error responses
func BadRequest(c *gin.Context, message string, details ...string) {
	errorInfo := &ErrorInfo{
		Code:    "BAD_REQUEST",
		Message: message,
	}
	if len(details) > 0 {
		errorInfo.Details = details[0]
	}

	c.JSON(http.StatusBadRequest, APIResponse{
		Success:   false,
		Error:     errorInfo,
		Timestamp: time.Now(),
	})
}

func Unauthorized(c *gin.Context, message ...string) {
	msg := "Unauthorized"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusUnauthorized, APIResponse{
		Success: false,
		Error: &ErrorInfo{
			Code:    "UNAUTHORIZED",
			Message: msg,
		},
		Timestamp: time.Now(),
	})
}

func Forbidden(c *gin.Context, message ...string) {
	msg := "Forbidden"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusForbidden, APIResponse{
		Success: false,
		Error: &ErrorInfo{
			Code:    "FORBIDDEN",
			Message: msg,
		},
		Timestamp: time.Now(),
	})
}

func NotFound(c *gin.Context, message ...string) {
	msg := "Resource not found"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusNotFound, APIResponse{
		Success: false,
		Error: &ErrorInfo{
			Code:    "NOT_FOUND",
			Message: msg,
		},
		Timestamp: time.Now(),
	})
}

func UnprocessableEntity(c *gin.Context, message string, details ...string) {
	errorInfo := &ErrorInfo{
		Code:    "UNPROCESSABLE_ENTITY",
		Message: message,
	}
	if len(details) > 0 {
		errorInfo.Details = details[0]
	}

	c.JSON(http.StatusUnprocessableEntity, APIResponse{
		Success:   false,
		Error:     errorInfo,
		Timestamp: time.Now(),
	})
}

func InternalServerError(c *gin.Context, message ...string) {
	msg := "Internal server error"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusInternalServerError, APIResponse{
		Success: false,
		Error: &ErrorInfo{
			Code:    "INTERNAL_SERVER_ERROR",
			Message: msg,
		},
		Timestamp: time.Now(),
	})
}

func Conflict(c *gin.Context, message string, details ...string) {
	errorInfo := &ErrorInfo{
		Code:    "CONFLICT",
		Message: message,
	}
	if len(details) > 0 {
		errorInfo.Details = details[0]
	}

	c.JSON(http.StatusConflict, APIResponse{
		Success:   false,
		Error:     errorInfo,
		Timestamp: time.Now(),
	})
}

func TooManyRequests(c *gin.Context, message ...string) {
	msg := "Too many requests"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusTooManyRequests, APIResponse{
		Success: false,
		Error: &ErrorInfo{
			Code:    "TOO_MANY_REQUESTS",
			Message: msg,
		},
		Timestamp: time.Now(),
	})
}

// ServiceUnavailable handles service unavailable errors
func ServiceUnavailable(c *gin.Context, message ...string) {
	msg := "Service unavailable"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusServiceUnavailable, APIResponse{
		Success: false,
		Error: &ErrorInfo{
			Code:    "SERVICE_UNAVAILABLE",
			Message: msg,
		},
		Timestamp: time.Now(),
	})
}

// ValidationError handles validation errors
func ValidationError(c *gin.Context, message string, details ...string) {
	errorInfo := &ErrorInfo{
		Code:    "VALIDATION_ERROR",
		Message: message,
	}
	if len(details) > 0 {
		errorInfo.Details = details[0]
	}

	c.JSON(http.StatusBadRequest, APIResponse{
		Success:   false,
		Error:     errorInfo,
		Timestamp: time.Now(),
	})
}

// DatabaseError handles database errors
func DatabaseError(c *gin.Context, message ...string) {
	msg := "Database error"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusInternalServerError, APIResponse{
		Success: false,
		Error: &ErrorInfo{
			Code:    "DATABASE_ERROR",
			Message: msg,
		},
		Timestamp: time.Now(),
	})
}

// ExternalServiceError handles external service errors
func ExternalServiceError(c *gin.Context, message ...string) {
	msg := "External service error"
	if len(message) > 0 {
		msg = message[0]
	}

	c.JSON(http.StatusBadGateway, APIResponse{
		Success: false,
		Error: &ErrorInfo{
			Code:    "EXTERNAL_SERVICE_ERROR",
			Message: msg,
		},
		Timestamp: time.Now(),
	})
}

package middleware

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AdvancedSecurityConfig holds configuration for advanced security middleware
type AdvancedSecurityConfig struct {
	// Rate limiting with Redis backend
	EnableRedisRateLimit bool
	RedisAddr            string
	RedisPassword        string
	RedisDB              int

	// IP-based security
	EnableIPWhitelist bool
	WhitelistedIPs    []string
	EnableIPBlacklist bool
	BlacklistedIPs    []string
	EnableGeoBlocking bool
	BlockedCountries  []string

	// Request fingerprinting
	EnableFingerprinting bool
	MaxFingerprintAge    time.Duration

	// Anomaly detection
	EnableAnomalyDetection bool
	AnomalyThreshold       int
	AnomalyWindow          time.Duration

	// Security headers
	EnableSecurityHeaders bool
	CSPPolicy             string
	HSTSMaxAge            int

	// Bot detection
	EnableBotDetection bool
	BotUserAgents      []string

	Logger *zap.Logger
}

// DefaultAdvancedSecurityConfig returns default configuration
func DefaultAdvancedSecurityConfig() *AdvancedSecurityConfig {
	logger, _ := zap.NewProduction()
	return &AdvancedSecurityConfig{
		EnableRedisRateLimit:   false, // Disabled by default, requires Redis
		EnableIPWhitelist:      false,
		EnableIPBlacklist:      true,
		EnableGeoBlocking:      false,
		EnableFingerprinting:   true,
		MaxFingerprintAge:      24 * time.Hour,
		EnableAnomalyDetection: true,
		AnomalyThreshold:       100, // 100 requests per minute from same fingerprint
		AnomalyWindow:          time.Minute,
		EnableSecurityHeaders:  true,
		CSPPolicy:              "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
		HSTSMaxAge:             31536000, // 1 year
		EnableBotDetection:     true,
		BotUserAgents: []string{
			"bot", "crawler", "spider", "scraper", "curl", "wget",
			"python-requests", "go-http-client", "java", "apache-httpclient",
		},
		Logger: logger,
	}
}

// AdvancedSecurityMiddleware creates advanced security middleware
func AdvancedSecurityMiddleware(config *AdvancedSecurityConfig) gin.HandlerFunc {
	if config == nil {
		config = DefaultAdvancedSecurityConfig()
	}

	// Initialize security manager
	securityManager := NewSecurityManager(config)

	return func(c *gin.Context) {
		clientIP := getClientIP(c)
		userAgent := c.GetHeader("User-Agent")

		// Apply security headers
		if config.EnableSecurityHeaders {
			applySecurityHeaders(c, config)
		}

		// IP whitelist check
		if config.EnableIPWhitelist && !securityManager.IsIPWhitelisted(clientIP) {
			config.Logger.Warn("IP not in whitelist",
				zap.String("client_ip", clientIP),
				zap.String("path", c.Request.URL.Path),
			)
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "Access denied",
				"code":    "IP_NOT_WHITELISTED",
			})
			c.Abort()
			return
		}

		// IP blacklist check
		if config.EnableIPBlacklist && securityManager.IsIPBlacklisted(clientIP) {
			config.Logger.Warn("Blocked IP attempted access",
				zap.String("client_ip", clientIP),
				zap.String("path", c.Request.URL.Path),
			)
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "Access denied",
				"code":    "IP_BLACKLISTED",
			})
			c.Abort()
			return
		}

		// Bot detection
		if config.EnableBotDetection && securityManager.IsBotRequest(userAgent) {
			config.Logger.Info("Bot request detected",
				zap.String("client_ip", clientIP),
				zap.String("user_agent", userAgent),
				zap.String("path", c.Request.URL.Path),
			)

			// Apply stricter rate limiting for bots
			if !securityManager.AllowBotRequest(clientIP) {
				c.JSON(http.StatusTooManyRequests, gin.H{
					"success": false,
					"error":   "Bot rate limit exceeded",
					"code":    "BOT_RATE_LIMIT_EXCEEDED",
				})
				c.Abort()
				return
			}
		}

		// Request fingerprinting and anomaly detection
		if config.EnableFingerprinting || config.EnableAnomalyDetection {
			fingerprint := securityManager.GenerateFingerprint(c)

			if config.EnableAnomalyDetection {
				if securityManager.DetectAnomaly(fingerprint, clientIP) {
					config.Logger.Warn("Anomalous behavior detected",
						zap.String("client_ip", clientIP),
						zap.String("fingerprint", fingerprint),
						zap.String("path", c.Request.URL.Path),
					)

					// Temporarily block this fingerprint
					securityManager.BlockFingerprint(fingerprint, 15*time.Minute)

					c.JSON(http.StatusTooManyRequests, gin.H{
						"success": false,
						"error":   "Suspicious activity detected",
						"code":    "ANOMALY_DETECTED",
					})
					c.Abort()
					return
				}
			}
		}

		// Log successful security check
		config.Logger.Debug("Security checks passed",
			zap.String("client_ip", clientIP),
			zap.String("path", c.Request.URL.Path),
			zap.String("method", c.Request.Method),
		)

		c.Next()
	}
}

// SecurityManager manages advanced security features
type SecurityManager struct {
	config       *AdvancedSecurityConfig
	fingerprints map[string]*FingerprintInfo
	botRequests  map[string]*BotInfo
	mutex        sync.RWMutex
}

// FingerprintInfo holds information about a request fingerprint
type FingerprintInfo struct {
	Count     int
	FirstSeen time.Time
	LastSeen  time.Time
	Blocked   bool
	BlockedAt time.Time
	IPs       map[string]int
}

// BotInfo holds information about bot requests
type BotInfo struct {
	Count     int
	FirstSeen time.Time
	LastSeen  time.Time
}

// NewSecurityManager creates a new security manager
func NewSecurityManager(config *AdvancedSecurityConfig) *SecurityManager {
	sm := &SecurityManager{
		config:       config,
		fingerprints: make(map[string]*FingerprintInfo),
		botRequests:  make(map[string]*BotInfo),
	}

	// Start cleanup routine
	go sm.startCleanup()

	return sm
}

// GenerateFingerprint generates a unique fingerprint for the request
func (sm *SecurityManager) GenerateFingerprint(c *gin.Context) string {
	// Combine various request attributes to create a fingerprint
	attributes := []string{
		c.GetHeader("User-Agent"),
		c.GetHeader("Accept"),
		c.GetHeader("Accept-Language"),
		c.GetHeader("Accept-Encoding"),
		c.GetHeader("Connection"),
		c.Request.Method,
	}

	// Create hash of combined attributes
	combined := strings.Join(attributes, "|")
	hash := sha256.Sum256([]byte(combined))
	return hex.EncodeToString(hash[:])
}

// DetectAnomaly detects anomalous behavior based on request patterns
func (sm *SecurityManager) DetectAnomaly(fingerprint, clientIP string) bool {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	now := time.Now()
	info, exists := sm.fingerprints[fingerprint]

	if !exists {
		sm.fingerprints[fingerprint] = &FingerprintInfo{
			Count:     1,
			FirstSeen: now,
			LastSeen:  now,
			IPs:       map[string]int{clientIP: 1},
		}
		return false
	}

	// Check if fingerprint is currently blocked
	if info.Blocked && now.Sub(info.BlockedAt) < 15*time.Minute {
		return true
	}

	// Reset block status if expired
	if info.Blocked && now.Sub(info.BlockedAt) >= 15*time.Minute {
		info.Blocked = false
	}

	// Update fingerprint info
	info.LastSeen = now
	info.IPs[clientIP]++

	// Check for anomalies within the time window
	if now.Sub(info.FirstSeen) <= sm.config.AnomalyWindow {
		info.Count++

		// Check if threshold exceeded
		if info.Count > sm.config.AnomalyThreshold {
			return true
		}

		// Check for distributed attacks (same fingerprint from many IPs)
		if len(info.IPs) > 10 {
			sm.config.Logger.Warn("Distributed attack detected",
				zap.String("fingerprint", fingerprint),
				zap.Int("unique_ips", len(info.IPs)),
			)
			return true
		}
	} else {
		// Reset counter for new window
		info.Count = 1
		info.FirstSeen = now
		info.IPs = map[string]int{clientIP: 1}
	}

	return false
}

// BlockFingerprint blocks a fingerprint for a specified duration
func (sm *SecurityManager) BlockFingerprint(fingerprint string, duration time.Duration) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	info, exists := sm.fingerprints[fingerprint]
	if !exists {
		info = &FingerprintInfo{
			FirstSeen: time.Now(),
			LastSeen:  time.Now(),
			IPs:       make(map[string]int),
		}
		sm.fingerprints[fingerprint] = info
	}

	info.Blocked = true
	info.BlockedAt = time.Now()

	sm.config.Logger.Info("Fingerprint blocked",
		zap.String("fingerprint", fingerprint),
		zap.Duration("duration", duration),
	)
}

// IsIPWhitelisted checks if an IP is whitelisted
func (sm *SecurityManager) IsIPWhitelisted(ip string) bool {
	for _, whitelistedIP := range sm.config.WhitelistedIPs {
		if ip == whitelistedIP {
			return true
		}
	}
	return false
}

// IsIPBlacklisted checks if an IP is blacklisted
func (sm *SecurityManager) IsIPBlacklisted(ip string) bool {
	for _, blacklistedIP := range sm.config.BlacklistedIPs {
		if ip == blacklistedIP {
			return true
		}
	}
	return false
}

// IsBotRequest checks if the request is from a bot
func (sm *SecurityManager) IsBotRequest(userAgent string) bool {
	lowerUA := strings.ToLower(userAgent)
	for _, botUA := range sm.config.BotUserAgents {
		if strings.Contains(lowerUA, strings.ToLower(botUA)) {
			return true
		}
	}
	return false
}

// AllowBotRequest checks if a bot request should be allowed (rate limiting for bots)
func (sm *SecurityManager) AllowBotRequest(clientIP string) bool {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	now := time.Now()
	info, exists := sm.botRequests[clientIP]

	if !exists {
		sm.botRequests[clientIP] = &BotInfo{
			Count:     1,
			FirstSeen: now,
			LastSeen:  now,
		}
		return true
	}

	info.LastSeen = now

	// Allow 10 requests per minute for bots
	if now.Sub(info.FirstSeen) <= time.Minute {
		info.Count++
		return info.Count <= 10
	} else {
		// Reset counter for new minute
		info.Count = 1
		info.FirstSeen = now
		return true
	}
}

// startCleanup starts the cleanup routine
func (sm *SecurityManager) startCleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		sm.cleanup()
	}
}

// cleanup removes old entries
func (sm *SecurityManager) cleanup() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	now := time.Now()
	cutoff := now.Add(-sm.config.MaxFingerprintAge)

	// Clean fingerprints
	for fingerprint, info := range sm.fingerprints {
		if info.LastSeen.Before(cutoff) && !info.Blocked {
			delete(sm.fingerprints, fingerprint)
		}
	}

	// Clean bot requests
	for ip, info := range sm.botRequests {
		if info.LastSeen.Before(cutoff) {
			delete(sm.botRequests, ip)
		}
	}

	sm.config.Logger.Debug("Security manager cleanup completed",
		zap.Int("active_fingerprints", len(sm.fingerprints)),
		zap.Int("active_bot_requests", len(sm.botRequests)),
	)
}

// applySecurityHeaders applies security headers to the response
func applySecurityHeaders(c *gin.Context, config *AdvancedSecurityConfig) {
	// Content Security Policy
	if config.CSPPolicy != "" {
		c.Header("Content-Security-Policy", config.CSPPolicy)
	}

	// HTTP Strict Transport Security
	if config.HSTSMaxAge > 0 {
		c.Header("Strict-Transport-Security", fmt.Sprintf("max-age=%d; includeSubDomains", config.HSTSMaxAge))
	}

	// X-Frame-Options
	c.Header("X-Frame-Options", "DENY")

	// X-Content-Type-Options
	c.Header("X-Content-Type-Options", "nosniff")

	// X-XSS-Protection
	c.Header("X-XSS-Protection", "1; mode=block")

	// Referrer Policy
	c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

	// Permissions Policy
	c.Header("Permissions-Policy", "geolocation=(), microphone=(), camera=()")

	// Remove server information
	c.Header("Server", "")
	c.Header("X-Powered-By", "")
}

// GetSecurityStats returns security statistics
func (sm *SecurityManager) GetSecurityStats() map[string]interface{} {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	blockedFingerprints := 0
	activeFingerprints := 0
	now := time.Now()

	for _, info := range sm.fingerprints {
		if info.Blocked {
			blockedFingerprints++
		}
		if now.Sub(info.LastSeen) < time.Hour {
			activeFingerprints++
		}
	}

	return map[string]interface{}{
		"total_fingerprints":   len(sm.fingerprints),
		"blocked_fingerprints": blockedFingerprints,
		"active_fingerprints":  activeFingerprints,
		"bot_requests":         len(sm.botRequests),
		"config": map[string]interface{}{
			"anomaly_threshold":   sm.config.AnomalyThreshold,
			"anomaly_window":      sm.config.AnomalyWindow.String(),
			"fingerprint_max_age": sm.config.MaxFingerprintAge.String(),
			"security_headers":    sm.config.EnableSecurityHeaders,
			"bot_detection":       sm.config.EnableBotDetection,
			"anomaly_detection":   sm.config.EnableAnomalyDetection,
		},
	}
}

// BlockIP manually blocks an IP address
func (sm *SecurityManager) BlockIP(ip string) {
	sm.config.BlacklistedIPs = append(sm.config.BlacklistedIPs, ip)
	sm.config.Logger.Info("IP manually blocked", zap.String("ip", ip))
}

// UnblockIP manually unblocks an IP address
func (sm *SecurityManager) UnblockIP(ip string) {
	for i, blockedIP := range sm.config.BlacklistedIPs {
		if blockedIP == ip {
			sm.config.BlacklistedIPs = append(sm.config.BlacklistedIPs[:i], sm.config.BlacklistedIPs[i+1:]...)
			sm.config.Logger.Info("IP manually unblocked", zap.String("ip", ip))
			return
		}
	}
}

// SecurityHealthCheck performs a security health check
func (sm *SecurityManager) SecurityHealthCheck(ctx context.Context) map[string]interface{} {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	now := time.Now()
	recentThreats := 0
	activeBlocks := 0

	for _, info := range sm.fingerprints {
		if info.Blocked && now.Sub(info.BlockedAt) < time.Hour {
			recentThreats++
		}
		if info.Blocked {
			activeBlocks++
		}
	}

	status := "healthy"
	if recentThreats > 10 {
		status = "under_attack"
	} else if recentThreats > 5 {
		status = "elevated_threat"
	}

	return map[string]interface{}{
		"status":         status,
		"recent_threats": recentThreats,
		"active_blocks":  activeBlocks,
		"timestamp":      now.Unix(),
		"uptime":         time.Since(now).String(),
	}
}

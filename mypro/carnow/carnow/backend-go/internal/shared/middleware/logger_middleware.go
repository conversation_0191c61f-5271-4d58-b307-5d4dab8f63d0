package middleware

import (
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AppLog struct {
	Level     string
	Message   string
	Metadata  string // JSONB in postgres, so we'll use a string for the JSON
	Timestamp time.Time
}

func RequestLogger(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		c.Next()

		end := time.Now()
		latency := end.Sub(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()
		errorMessage := c.Errors.ByType(gin.ErrorTypePrivate).String()

		if raw != "" {
			path = path + "?" + raw
		}

		// Log to console (keep this for Render's default logs)
		log.Printf("[GIN] %s | %3d | %13v | %15s | %-7s %s\n%s",
			end.Format("2006/01/02 - 15:04:05"),
			statusCode,
			latency,
			clientIP,
			method,
			path,
			errorMessage,
		)

		// Also log to database
		logMessage := map[string]interface{}{
			"latency":    latency.String(),
			"client_ip":  clientIP,
			"method":     method,
			"path":       path,
			"status":     statusCode,
			"error":      errorMessage,
			"user_agent": c.Request.UserAgent(),
		}

		// Use a simple insert for now
		if err := db.Table("public.app_logs").Create(&map[string]interface{}{
			"level":     "info",
			"message":   "Incoming request",
			"metadata":  logMessage,
			"timestamp": time.Now(),
		}).Error; err != nil {
			log.Printf("Failed to log request to database: %v", err)
		}
	}
}

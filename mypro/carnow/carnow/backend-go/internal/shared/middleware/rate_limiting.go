package middleware

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RateLimiter represents a rate limiter instance
type RateLimiter struct {
	requests map[string]*ClientInfo
	mutex    sync.RWMutex
	config   *RateLimitConfig
	logger   *zap.Logger
}

// ClientInfo holds information about a client's requests
type ClientInfo struct {
	Count     int
	FirstSeen time.Time
	LastSeen  time.Time
	Blocked   bool
	BlockedAt time.Time
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	RequestsPerMinute int           // Number of requests allowed per minute
	RequestsPerHour   int           // Number of requests allowed per hour
	RequestsPerDay    int           // Number of requests allowed per day
	BlockDuration     time.Duration // How long to block after exceeding limits
	CleanupInterval   time.Duration // How often to clean up old entries
	WhitelistedIPs    []string      // IPs that bypass rate limiting
	Logger            *zap.Logger   // Logger instance
}

// DefaultRateLimitConfig returns default rate limiting configuration
func DefaultRateLimitConfig() *RateLimitConfig {
	logger, _ := zap.NewProduction()
	return &RateLimitConfig{
		RequestsPerMinute: 60,    // 60 requests per minute
		RequestsPerHour:   1000,  // 1000 requests per hour
		RequestsPerDay:    10000, // 10000 requests per day
		BlockDuration:     15 * time.Minute,
		CleanupInterval:   5 * time.Minute,
		WhitelistedIPs:    []string{"127.0.0.1", "::1"},
		Logger:            logger,
	}
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(config *RateLimitConfig) *RateLimiter {
	if config == nil {
		config = DefaultRateLimitConfig()
	}

	rl := &RateLimiter{
		requests: make(map[string]*ClientInfo),
		config:   config,
		logger:   config.Logger,
	}

	// Start cleanup routine
	go rl.startCleanup()

	return rl
}

// RateLimitMiddleware creates a rate limiting middleware
func RateLimitMiddleware(config *RateLimitConfig) gin.HandlerFunc {
	limiter := NewRateLimiter(config)

	return func(c *gin.Context) {
		clientIP := getClientIP(c)

		// Check if IP is whitelisted
		if limiter.isWhitelisted(clientIP) {
			c.Next()
			return
		}

		// Check rate limit
		allowed, resetTime, err := limiter.Allow(clientIP)
		if err != nil {
			limiter.logger.Error("Rate limiter error", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Internal server error",
				"code":    "RATE_LIMITER_ERROR",
			})
			c.Abort()
			return
		}

		if !allowed {
			limiter.logger.Warn("Rate limit exceeded",
				zap.String("client_ip", clientIP),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method),
				zap.Time("reset_time", resetTime),
			)

			// Set rate limit headers
			c.Header("X-RateLimit-Limit", strconv.Itoa(limiter.config.RequestsPerMinute))
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("X-RateLimit-Reset", strconv.FormatInt(resetTime.Unix(), 10))
			c.Header("Retry-After", strconv.FormatInt(int64(resetTime.Sub(time.Now()).Seconds()), 10))

			c.JSON(http.StatusTooManyRequests, gin.H{
				"success":     false,
				"error":       "Rate limit exceeded",
				"code":        "RATE_LIMIT_EXCEEDED",
				"message":     fmt.Sprintf("Too many requests. Try again after %v", resetTime.Sub(time.Now()).Round(time.Second)),
				"retry_after": resetTime.Unix(),
			})
			c.Abort()
			return
		}

		// Set rate limit headers for successful requests
		remaining := limiter.getRemainingRequests(clientIP)
		c.Header("X-RateLimit-Limit", strconv.Itoa(limiter.config.RequestsPerMinute))
		c.Header("X-RateLimit-Remaining", strconv.Itoa(remaining))
		c.Header("X-RateLimit-Reset", strconv.FormatInt(resetTime.Unix(), 10))

		c.Next()
	}
}

// Allow checks if a request from the given IP is allowed
func (rl *RateLimiter) Allow(clientIP string) (bool, time.Time, error) {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()
	client, exists := rl.requests[clientIP]

	if !exists {
		// First request from this IP
		rl.requests[clientIP] = &ClientInfo{
			Count:     1,
			FirstSeen: now,
			LastSeen:  now,
			Blocked:   false,
		}
		return true, now.Add(time.Minute), nil
	}

	// Check if client is currently blocked
	if client.Blocked {
		if now.Sub(client.BlockedAt) < rl.config.BlockDuration {
			// Still blocked
			return false, client.BlockedAt.Add(rl.config.BlockDuration), nil
		} else {
			// Block period expired, reset
			client.Blocked = false
			client.Count = 0
			client.FirstSeen = now
		}
	}

	// Update last seen
	client.LastSeen = now

	// Check different time windows
	minuteAgo := now.Add(-time.Minute)
	// Remove old entries
	// hourAgo := now.Add(-time.Hour)
	// dayAgo := now.Add(-24 * time.Hour)

	// For simplicity, we'll use a sliding window approach
	// In production, you might want to use a more sophisticated algorithm

	// Check requests per minute
	if client.FirstSeen.After(minuteAgo) {
		client.Count++
		if client.Count > rl.config.RequestsPerMinute {
			client.Blocked = true
			client.BlockedAt = now
			rl.logger.Warn("Client blocked for exceeding per-minute limit",
				zap.String("client_ip", clientIP),
				zap.Int("requests", client.Count),
				zap.Int("limit", rl.config.RequestsPerMinute),
			)
			return false, now.Add(rl.config.BlockDuration), nil
		}
	} else {
		// Reset counter for new minute window
		client.Count = 1
		client.FirstSeen = now
	}

	// Additional checks for hour and day limits would go here
	// For now, we'll focus on per-minute limiting

	return true, now.Add(time.Minute), nil
}

// getRemainingRequests calculates remaining requests for a client
func (rl *RateLimiter) getRemainingRequests(clientIP string) int {
	rl.mutex.RLock()
	defer rl.mutex.RUnlock()

	client, exists := rl.requests[clientIP]
	if !exists {
		return rl.config.RequestsPerMinute
	}

	remaining := rl.config.RequestsPerMinute - client.Count
	if remaining < 0 {
		return 0
	}
	return remaining
}

// isWhitelisted checks if an IP is whitelisted
func (rl *RateLimiter) isWhitelisted(ip string) bool {
	for _, whitelistedIP := range rl.config.WhitelistedIPs {
		if ip == whitelistedIP {
			return true
		}
	}
	return false
}

// startCleanup starts the cleanup routine to remove old entries
func (rl *RateLimiter) startCleanup() {
	ticker := time.NewTicker(rl.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		rl.cleanup()
	}
}

// cleanup removes old entries from the rate limiter
func (rl *RateLimiter) cleanup() {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()
	cutoff := now.Add(-24 * time.Hour) // Remove entries older than 24 hours

	for ip, client := range rl.requests {
		if client.LastSeen.Before(cutoff) && !client.Blocked {
			delete(rl.requests, ip)
		}
	}

	rl.logger.Debug("Rate limiter cleanup completed",
		zap.Int("active_clients", len(rl.requests)),
	)
}

// getClientIP extracts the real client IP from the request
func getClientIP(c *gin.Context) string {
	// Check X-Forwarded-For header first
	if xff := c.GetHeader("X-Forwarded-For"); xff != "" {
		// X-Forwarded-For can contain multiple IPs, take the first one
		if idx := strings.Index(xff, ","); idx != -1 {
			return strings.TrimSpace(xff[:idx])
		}
		return strings.TrimSpace(xff)
	}

	// Check X-Real-IP header
	if xri := c.GetHeader("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// Fall back to RemoteAddr
	return c.ClientIP()
}

// GetStats returns statistics about the rate limiter
func (rl *RateLimiter) GetStats() map[string]interface{} {
	rl.mutex.RLock()
	defer rl.mutex.RUnlock()

	totalClients := len(rl.requests)
	blockedClients := 0
	activeClients := 0

	now := time.Now()
	for _, client := range rl.requests {
		if client.Blocked {
			blockedClients++
		}
		if now.Sub(client.LastSeen) < time.Hour {
			activeClients++
		}
	}

	return map[string]interface{}{
		"total_clients":   totalClients,
		"blocked_clients": blockedClients,
		"active_clients":  activeClients,
		"config": map[string]interface{}{
			"requests_per_minute": rl.config.RequestsPerMinute,
			"requests_per_hour":   rl.config.RequestsPerHour,
			"requests_per_day":    rl.config.RequestsPerDay,
			"block_duration":      rl.config.BlockDuration.String(),
		},
	}
}

// Reset resets the rate limiter for a specific IP
func (rl *RateLimiter) Reset(clientIP string) {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	delete(rl.requests, clientIP)
	rl.logger.Info("Rate limiter reset for client", zap.String("client_ip", clientIP))
}

// Block manually blocks a client IP
func (rl *RateLimiter) Block(clientIP string, duration time.Duration) {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()
	client, exists := rl.requests[clientIP]
	if !exists {
		client = &ClientInfo{
			FirstSeen: now,
			LastSeen:  now,
		}
		rl.requests[clientIP] = client
	}

	client.Blocked = true
	client.BlockedAt = now

	// Override block duration if specified
	if duration > 0 {
		// We'll need to store custom block duration per client
		// For now, we'll use the default duration
	}

	rl.logger.Warn("Client manually blocked",
		zap.String("client_ip", clientIP),
		zap.Duration("duration", duration),
	)
}

// Unblock manually unblocks a client IP
func (rl *RateLimiter) Unblock(clientIP string) {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	client, exists := rl.requests[clientIP]
	if exists {
		client.Blocked = false
		client.Count = 0
		client.FirstSeen = time.Now()
	}

	rl.logger.Info("Client manually unblocked", zap.String("client_ip", clientIP))
}

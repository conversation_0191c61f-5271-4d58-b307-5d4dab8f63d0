package middleware

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
)

// SupabaseUser represents user data from Supabase Auth API
type SupabaseUser struct {
	ID    string `json:"id"`
	Email string `json:"email"`
	Role  string `json:"role"`
	Aud   string `json:"aud,omitempty"`
}

// DatabaseInterface defines the interface for database operations needed by middleware
type DatabaseInterface interface {
	QueryRow(ctx context.Context, query string, args ...interface{}) pgx.Row
	Exec(ctx context.Context, query string, args ...interface{}) error
	// Add other methods as needed
}

// SimpleJWTMiddleware validates JWT tokens using secure JWT service
// This follows the Forever Plan: simple architecture with enhanced security
func SimpleJWTMiddleware(cfg *config.Config, db DatabaseInterface) gin.HandlerFunc {
	// Initialize JWT service
	jwtService, err := services.NewJWTService(cfg)
	if err != nil {
		log.Fatalf("❌ Failed to initialize JWT service: %v", err)
	}

	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			log.Println("❌ JWT Middleware: No authorization header")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Authorization header required",
				"code":    "MISSING_AUTH_HEADER",
				"message": "Please provide a valid Bearer token",
			})
			c.Abort()
			return
		}

		// Extract Bearer token
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			log.Println("❌ JWT Middleware: Invalid authorization header format")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid authorization header format",
				"code":    "INVALID_AUTH_FORMAT",
				"message": "Authorization header must be in format: Bearer <token>",
			})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]
		log.Printf("🔍 JWT Middleware: Validating token (length: %d)", len(tokenString))

		// Try secure JWT validation first
		claims, err := jwtService.ValidateToken(tokenString)
		if err != nil {
			log.Printf("🔄 JWT Middleware: Secure validation failed, trying Supabase fallback: %v", err)

			// Fallback to Supabase validation for backward compatibility
			user, supabaseErr := validateTokenWithSupabase(cfg, tokenString)
			if supabaseErr != nil {
				log.Printf("❌ JWT Middleware: Both validations failed. Secure: %v, Supabase: %v", err, supabaseErr)
				c.JSON(http.StatusUnauthorized, gin.H{
					"error":   "Invalid token",
					"code":    "TOKEN_VALIDATION_FAILED",
					"message": "Token validation failed",
				})
				c.Abort()
				return
			}

			// Use Supabase user data
			log.Printf("✅ JWT Middleware: Supabase token validated for user: %s (%s)", user.Email, user.ID)

			// Set user information in context
			c.Set("user_id", user.ID)
			c.Set("user_email", user.Email)
			c.Set("user_role", user.Role)
			c.Set("token_type", "supabase")
		} else {
			// Use secure JWT claims
			log.Printf("✅ JWT Middleware: Secure token validated for user: %s (%s)", claims.Email, claims.UserID)

			// Ensure it's an access token
			if claims.TokenType != "access" {
				log.Printf("❌ JWT Middleware: Invalid token type: %s", claims.TokenType)
				c.JSON(http.StatusUnauthorized, gin.H{
					"error":   "Invalid token type",
					"code":    "INVALID_TOKEN_TYPE",
					"message": "Access token required",
				})
				c.Abort()
				return
			}

			// Set user information in context
			c.Set("user_id", claims.UserID)
			c.Set("user_email", claims.Email)
			c.Set("user_role", claims.Role)
			c.Set("token_type", "secure")
			c.Set("token_id", claims.ID)
		}

		// Auto-create user if doesn't exist (using pgx)
		userID := c.GetString("user_id")
		userEmail := c.GetString("user_email")
		userRole := c.GetString("user_role")

		user := &SupabaseUser{
			ID:    userID,
			Email: userEmail,
			Role:  userRole,
		}

		if err := ensureUserExistsWithPgx(db, user); err != nil {
			log.Printf("❌ JWT Middleware: Failed to ensure user exists: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to process user information",
				"code":    "USER_PROCESSING_ERROR",
				"message": "Internal server error",
			})
			c.Abort()
			return
		}

		log.Printf("✅ JWT Middleware: User context set for %s", userEmail)
		c.Next()
	}
}

// OptionalJWTMiddleware allows both authenticated and non-authenticated requests
func OptionalJWTMiddleware(cfg *config.Config, db DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")

		// If no auth header, continue without setting user context
		if authHeader == "" {
			log.Println("🔓 Optional JWT Middleware: No auth header, continuing without authentication")
			c.Next()
			return
		}

		// If auth header exists, validate it
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			log.Println("⚠️ Optional JWT Middleware: Invalid auth format, continuing without authentication")
			c.Next()
			return
		}

		tokenString := tokenParts[1]
		user, err := validateTokenWithSupabase(cfg, tokenString)

		// If valid token, set user context
		if err == nil {
			log.Printf("✅ Optional JWT Middleware: Token validated for user: %s", user.Email)

			// Auto-create user if doesn't exist
			_ = ensureUserExistsWithPgx(db, user)

			c.Set("user_id", user.ID)
			c.Set("user_email", user.Email)
			c.Set("user_role", user.Role)
		} else {
			log.Printf("⚠️ Optional JWT Middleware: Token validation failed: %v", err)
		}

		c.Next()
	}
}

// validateTokenWithSupabase validates JWT token by calling Supabase Auth API
func validateTokenWithSupabase(cfg *config.Config, token string) (*SupabaseUser, error) {
	// Create request to Supabase Auth API
	url := fmt.Sprintf("%s/auth/v1/user", cfg.Supabase.URL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("apikey", cfg.Supabase.AnonKey)

	// Make request with timeout
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to validate token: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("invalid token - status: %d", resp.StatusCode)
	}

	// Parse response
	var supabaseUser SupabaseUser
	if err := json.NewDecoder(resp.Body).Decode(&supabaseUser); err != nil {
		return nil, fmt.Errorf("failed to parse user data: %v", err)
	}

	// Set default role if not provided
	if supabaseUser.Role == "" {
		supabaseUser.Role = "authenticated"
	}

	return &supabaseUser, nil
}

// ensureUserExistsWithPgx checks if user exists in Go backend, creates if not (using pgx)
// This follows the Forever Plan: simple database operations with pgx only
func ensureUserExistsWithPgx(db DatabaseInterface, user *SupabaseUser) error {
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	// Validate user ID format
	_, err := uuid.Parse(user.ID)
	if err != nil {
		return fmt.Errorf("invalid user ID format: %v", err)
	}

	// Check if user exists in auth.users table (Supabase auth table)
	var existingUserID string
	checkQuery := "SELECT id FROM auth.users WHERE id = $1"

	err = db.QueryRow(ctx, checkQuery, user.ID).Scan(&existingUserID)
	if err != nil && err != pgx.ErrNoRows {
		return fmt.Errorf("database error checking user: %v", err)
	}

	if existingUserID != "" {
		// User exists, no need to create
		log.Printf("✅ User exists in database: %s", user.Email)
		return nil
	}

	// User doesn't exist, but this shouldn't happen since they authenticated with Supabase
	// Just log this scenario
	log.Printf("⚠️ User authenticated with Supabase but not found in auth.users table: %s", user.Email)

	// In Forever Plan, we don't create users manually - they should exist in auth.users
	// We just log and continue, as the user is authenticated
	return nil
}

// GetUserIDFromContext extracts user ID from Gin context
func GetUserIDFromContext(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return ""
}

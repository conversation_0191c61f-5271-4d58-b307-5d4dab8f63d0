package middleware

import (
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// RecoveryWithLogger is a Gin middleware that recovers from any panics and
// returns a JSON formatted 500 response. It also logs the panic so we have
// visibility in the server logs.
func RecoveryWithLogger() gin.HandlerFunc {
	return gin.CustomRecoveryWithWriter(gin.DefaultErrorWriter, func(c *gin.Context, recovered interface{}) {
		// Log the panic with stack trace for debugging purposes.
		log.Printf("🔥 Panic recovered: %v", recovered)

		// If the response has already been sent, we cannot send another one.
		if c.Writer.Written() {
			return
		}

		// Return a generic 500 error to the client.
		c.AbortWithStatusJSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Error: &EnhancedError{
				Code:       "INTERNAL_SERVER_ERROR",
				Message:    "Internal server error",
				Category:   CategorySystem,
				Severity:   SeverityHigh,
				HTTPStatus: http.StatusInternalServerError,
				Timestamp:  time.Now(),
			},
			Timestamp: time.Now(),
		})
	})
}

package middleware

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Enhanced Rate Limiting for Phase 4: Performance Optimization
//
// Features:
// - Implement rate limiting for all public APIs
// - Add different rate limits for different user tiers
// - Create rate limit monitoring and alerting
// - Implement graceful rate limit handling
// - Adaptive rate limiting based on system load
// - Sliding window rate limiting
// - Distributed rate limiting support
// - Request prioritization

// Rate limit alert types
type RateLimitAlertType string

const (
	AlertRateLimitHigh       RateLimitAlertType = "HIGH_UTILIZATION"
	AlertRateLimitLow        RateLimitAlertType = "LOW_UTILIZATION"
	AlertRateLimitTimeouts   RateLimitAlertType = "ACQUIRE_TIMEOUTS"
	AlertRateLimitErrors     RateLimitAlertType = "CONNECTION_ERRORS"
	AlertRateLimitFailures   RateLimitAlertType = "HEALTH_CHECK_FAILURES"
	AlertRateLimitLeaks      RateLimitAlertType = "POTENTIAL_LEAKS"
	AlertRateLimitExhaustion RateLimitAlertType = "POOL_EXHAUSTION"
)

// Rate limit alert severity levels
type RateLimitSeverity string

const (
	RateLimitSeverityInfo     RateLimitSeverity = "info"
	RateLimitSeverityWarning  RateLimitSeverity = "warning"
	RateLimitSeverityError    RateLimitSeverity = "error"
	RateLimitSeverityCritical RateLimitSeverity = "critical"
)

// EnhancedRateLimiter provides advanced rate limiting capabilities
type EnhancedRateLimiter struct {
	limiters      map[string]*UserTierLimiter // Per-user limiters
	globalLimiter *GlobalLimiter              // Global system limiter
	config        *EnhancedRateLimitConfig
	logger        *zap.Logger
	metrics       *RateLimitMetrics
	monitor       *RateLimitMonitor
	mutex         sync.RWMutex
}

// EnhancedRateLimitConfig contains advanced rate limiting configuration
type EnhancedRateLimitConfig struct {
	// User tier configurations
	UserTiers map[UserTier]TierConfig

	// Global rate limiting
	GlobalLimits GlobalLimitsConfig

	// Adaptive rate limiting
	AdaptiveConfig AdaptiveRateLimitConfig

	// Sliding window configuration
	WindowConfig SlidingWindowConfig

	// Monitoring configuration
	MonitoringConfig MonitoringConfig

	// Graceful handling configuration
	GracefulConfig GracefulHandlingConfig

	// Distributed configuration
	DistributedConfig DistributedConfig

	// General configuration
	DefaultTier          UserTier
	HeaderPrefix         string
	WhitelistedIPs       []string
	WhitelistedUserIDs   []string
	CleanupInterval      time.Duration
	EnableMetrics        bool
	LogSlowRequests      bool
	SlowRequestThreshold time.Duration
}

// UserTier represents different user tiers with different rate limits
type UserTier string

const (
	TierFree       UserTier = "free"
	TierBasic      UserTier = "basic"
	TierPremium    UserTier = "premium"
	TierEnterprise UserTier = "enterprise"
	TierAdmin      UserTier = "admin"
	TierInternal   UserTier = "internal"
)

// TierConfig contains rate limiting configuration for a user tier
type TierConfig struct {
	RequestsPerSecond  int           // Requests per second
	RequestsPerMinute  int           // Requests per minute
	RequestsPerHour    int           // Requests per hour
	RequestsPerDay     int           // Requests per day
	BurstSize          int           // Burst allowance
	ConcurrentRequests int           // Maximum concurrent requests
	PriorityLevel      int           // Priority level (1-10)
	BlockDuration      time.Duration // How long to block after exceeding limits
	WarningThreshold   float64       // Warning threshold (0.0-1.0)
	GracePeriod        time.Duration // Grace period for new users
}

// GlobalLimitsConfig contains global system-wide rate limits
type GlobalLimitsConfig struct {
	MaxRequestsPerSecond  int        // System-wide requests per second
	MaxConcurrentRequests int        // Maximum concurrent requests
	EmergencyThreshold    float64    // Threshold to activate emergency mode
	EmergencyLimits       TierConfig // Limits during emergency mode
	LoadBalancing         bool       // Enable load-based rate limiting
	HealthCheckEndpoints  []string   // Endpoints excluded from rate limiting
}

// AdaptiveRateLimitConfig configures adaptive rate limiting
type AdaptiveRateLimitConfig struct {
	Enabled            bool          // Enable adaptive rate limiting
	CPUThreshold       float64       // CPU threshold to reduce limits
	MemoryThreshold    float64       // Memory threshold to reduce limits
	ErrorRateThreshold float64       // Error rate threshold to reduce limits
	AdaptationFactor   float64       // Factor to adjust limits (0.1-1.0)
	RecoveryFactor     float64       // Factor to recover limits
	CheckInterval      time.Duration // How often to check system metrics
}

// SlidingWindowConfig configures sliding window rate limiting
type SlidingWindowConfig struct {
	WindowSize  time.Duration // Size of sliding window
	SubWindows  int           // Number of sub-windows
	Precision   time.Duration // Precision of rate limiting
	EnableDecay bool          // Enable request count decay
	DecayFactor float64       // Decay factor for old requests
}

// MonitoringConfig configures rate limiting monitoring
type MonitoringConfig struct {
	EnableDetailedMetrics bool          // Enable detailed metrics collection
	MetricsInterval       time.Duration // Metrics collection interval
	AlertThresholds       AlertThresholds
	LogVerbosity          LogLevel
	ExportMetrics         bool // Export metrics to external systems
	DashboardEnabled      bool // Enable rate limiting dashboard
}

// AlertThresholds defines thresholds for rate limiting alerts
type AlertThresholds struct {
	HighUtilization    float64       // Alert when utilization exceeds this
	BlockedRequests    int64         // Alert when blocked requests exceed this
	ErrorRate          float64       // Alert when error rate exceeds this
	ResponseTimeP99    time.Duration // Alert when P99 response time exceeds this
	ConcurrentRequests int           // Alert when concurrent requests exceed this
}

// GracefulHandlingConfig configures graceful rate limit handling
type GracefulHandlingConfig struct {
	QueueEnabled       bool          // Enable request queuing
	QueueSize          int           // Maximum queue size
	QueueTimeout       time.Duration // Maximum time in queue
	RetryAfterStrategy string        // Strategy for Retry-After header
	CustomErrorMessage string        // Custom error message for rate limited requests
	RedirectURL        string        // URL to redirect rate limited requests
	EnableGradualBlock bool          // Gradually increase blocking
}

// DistributedConfig configures distributed rate limiting
type DistributedConfig struct {
	Enabled         bool          // Enable distributed rate limiting
	RedisCluster    []string      // Redis cluster for distributed state
	SyncInterval    time.Duration // How often to sync with Redis
	LocalCacheSize  int           // Size of local cache
	ConsistencyMode string        // "strong", "eventual", "local"
}

// LogLevel defines logging verbosity levels
type LogLevel string

const (
	LogLevelNone    LogLevel = "none"
	LogLevelError   LogLevel = "error"
	LogLevelWarn    LogLevel = "warn"
	LogLevelInfo    LogLevel = "info"
	LogLevelDebug   LogLevel = "debug"
	LogLevelVerbose LogLevel = "verbose"
)

// UserTierLimiter manages rate limiting for a specific user tier
type UserTierLimiter struct {
	tier            UserTier
	config          TierConfig
	slidingWindows  map[time.Duration]*SlidingWindow
	currentRequests int64
	concurrentCount int64
	lastReset       time.Time
	totalRequests   int64
	blockedRequests int64
	lastRequestTime time.Time
	isInGracePeriod bool
	gracePeriodEnd  time.Time
	mutex           sync.RWMutex
}

// GlobalLimiter manages system-wide rate limiting
type GlobalLimiter struct {
	config             GlobalLimitsConfig
	currentRPS         int64
	concurrentRequests int64
	isEmergencyMode    bool
	emergencyModeStart time.Time
	totalRequests      int64
	systemMetrics      *SystemMetrics
	mutex              sync.RWMutex
}

// SlidingWindow implements a sliding window rate limiter
type SlidingWindow struct {
	windowSize    time.Duration
	subWindows    []SubWindow
	currentIndex  int
	totalRequests int64
	mutex         sync.RWMutex
}

// SubWindow represents a sub-window in the sliding window
type SubWindow struct {
	startTime  time.Time
	count      int64
	lastUpdate time.Time
}

// SystemMetrics tracks system performance metrics
type SystemMetrics struct {
	CPUUsage        float64
	MemoryUsage     float64
	ErrorRate       float64
	AvgResponseTime time.Duration
	P99ResponseTime time.Duration
	LastUpdated     time.Time
}

// RateLimitMetrics tracks rate limiting metrics
type RateLimitMetrics struct {
	// Request metrics by tier
	RequestsByTier map[UserTier]int64
	BlockedByTier  map[UserTier]int64
	QueuedByTier   map[UserTier]int64

	// Global metrics
	TotalRequests  int64
	TotalBlocked   int64
	TotalQueued    int64
	ConcurrentPeak int64

	// Performance metrics
	AverageProcessingTime time.Duration
	P95ProcessingTime     time.Duration
	P99ProcessingTime     time.Duration

	// System metrics
	EmergencyModeActivations int64
	AdaptiveAdjustments      int64

	// Error metrics
	InternalErrors    int64
	ConfigErrors      int64
	DistributedErrors int64

	// Timing
	LastUpdated   time.Time
	UptimeSeconds int64
}

// RateLimitMonitor monitors rate limiting performance and triggers alerts
type RateLimitMonitor struct {
	config    MonitoringConfig
	metrics   *RateLimitMetrics
	logger    *zap.Logger
	alerts    []RateLimitAlert
	isRunning bool
	mutex     sync.RWMutex
}

// RateLimitAlert represents a rate limiting alert
type RateLimitAlert struct {
	Type         RateLimitAlertType
	Severity     RateLimitSeverity
	Message      string
	Timestamp    time.Time
	Count        int64
	Acknowledged bool
	UserTier     UserTier
	Endpoint     string
}

// DefaultEnhancedRateLimitConfig returns optimized default configuration
func DefaultEnhancedRateLimitConfig() *EnhancedRateLimitConfig {
	return &EnhancedRateLimitConfig{
		UserTiers: map[UserTier]TierConfig{
			TierFree: {
				RequestsPerSecond:  5,
				RequestsPerMinute:  100,
				RequestsPerHour:    1000,
				RequestsPerDay:     10000,
				BurstSize:          10,
				ConcurrentRequests: 5,
				PriorityLevel:      1,
				BlockDuration:      15 * time.Minute,
				WarningThreshold:   0.8,
				GracePeriod:        24 * time.Hour,
			},
			TierBasic: {
				RequestsPerSecond:  20,
				RequestsPerMinute:  500,
				RequestsPerHour:    5000,
				RequestsPerDay:     50000,
				BurstSize:          50,
				ConcurrentRequests: 10,
				PriorityLevel:      3,
				BlockDuration:      10 * time.Minute,
				WarningThreshold:   0.8,
				GracePeriod:        7 * 24 * time.Hour,
			},
			TierPremium: {
				RequestsPerSecond:  100,
				RequestsPerMinute:  2000,
				RequestsPerHour:    20000,
				RequestsPerDay:     200000,
				BurstSize:          200,
				ConcurrentRequests: 50,
				PriorityLevel:      5,
				BlockDuration:      5 * time.Minute,
				WarningThreshold:   0.85,
				GracePeriod:        30 * 24 * time.Hour,
			},
			TierEnterprise: {
				RequestsPerSecond:  500,
				RequestsPerMinute:  10000,
				RequestsPerHour:    100000,
				RequestsPerDay:     1000000,
				BurstSize:          1000,
				ConcurrentRequests: 200,
				PriorityLevel:      8,
				BlockDuration:      2 * time.Minute,
				WarningThreshold:   0.9,
				GracePeriod:        365 * 24 * time.Hour,
			},
			TierAdmin: {
				RequestsPerSecond:  1000,
				RequestsPerMinute:  0, // Unlimited
				RequestsPerHour:    0, // Unlimited
				RequestsPerDay:     0, // Unlimited
				BurstSize:          2000,
				ConcurrentRequests: 500,
				PriorityLevel:      10,
				BlockDuration:      time.Minute,
				WarningThreshold:   0.95,
				GracePeriod:        0, // No grace period needed
			},
		},
		GlobalLimits: GlobalLimitsConfig{
			MaxRequestsPerSecond:  10000,
			MaxConcurrentRequests: 1000,
			EmergencyThreshold:    0.9,
			LoadBalancing:         true,
			HealthCheckEndpoints:  []string{"/health", "/metrics", "/status"},
		},
		AdaptiveConfig: AdaptiveRateLimitConfig{
			Enabled:            true,
			CPUThreshold:       0.8,
			MemoryThreshold:    0.85,
			ErrorRateThreshold: 0.05,
			AdaptationFactor:   0.7,
			RecoveryFactor:     1.1,
			CheckInterval:      30 * time.Second,
		},
		WindowConfig: SlidingWindowConfig{
			WindowSize:  time.Minute,
			SubWindows:  60, // 1 second precision
			Precision:   time.Second,
			EnableDecay: true,
			DecayFactor: 0.95,
		},
		MonitoringConfig: MonitoringConfig{
			EnableDetailedMetrics: true,
			MetricsInterval:       30 * time.Second,
			AlertThresholds: AlertThresholds{
				HighUtilization:    0.8,
				BlockedRequests:    1000,
				ErrorRate:          0.05,
				ResponseTimeP99:    time.Second,
				ConcurrentRequests: 800,
			},
			LogVerbosity:     LogLevelInfo,
			ExportMetrics:    true,
			DashboardEnabled: true,
		},
		GracefulConfig: GracefulHandlingConfig{
			QueueEnabled:       true,
			QueueSize:          1000,
			QueueTimeout:       30 * time.Second,
			RetryAfterStrategy: "exponential",
			EnableGradualBlock: true,
		},
		DistributedConfig: DistributedConfig{
			Enabled:         false, // Disabled by default
			SyncInterval:    time.Second,
			LocalCacheSize:  10000,
			ConsistencyMode: "eventual",
		},
		DefaultTier:          TierFree,
		HeaderPrefix:         "X-RateLimit",
		CleanupInterval:      5 * time.Minute,
		EnableMetrics:        true,
		LogSlowRequests:      true,
		SlowRequestThreshold: 100 * time.Millisecond,
	}
}

// NewEnhancedRateLimiter creates a new enhanced rate limiter
func NewEnhancedRateLimiter(config *EnhancedRateLimitConfig, logger *zap.Logger) *EnhancedRateLimiter {
	if config == nil {
		config = DefaultEnhancedRateLimitConfig()
	}

	limiter := &EnhancedRateLimiter{
		limiters: make(map[string]*UserTierLimiter),
		globalLimiter: &GlobalLimiter{
			config:        config.GlobalLimits,
			systemMetrics: &SystemMetrics{},
		},
		config: config,
		logger: logger,
		metrics: &RateLimitMetrics{
			RequestsByTier: make(map[UserTier]int64),
			BlockedByTier:  make(map[UserTier]int64),
			QueuedByTier:   make(map[UserTier]int64),
			LastUpdated:    time.Now(),
		},
	}

	// Initialize monitor if monitoring is enabled
	if config.MonitoringConfig.EnableDetailedMetrics {
		limiter.monitor = &RateLimitMonitor{
			config:  config.MonitoringConfig,
			metrics: limiter.metrics,
			logger:  logger,
		}
		go limiter.monitor.Start()
	}

	// Start cleanup routine
	go limiter.startCleanup()

	// Start adaptive rate limiting if enabled
	if config.AdaptiveConfig.Enabled {
		go limiter.startAdaptiveAdjustment()
	}

	logger.Info("Enhanced rate limiter initialized",
		zap.Int("user_tiers", len(config.UserTiers)),
		zap.Int("global_max_rps", config.GlobalLimits.MaxRequestsPerSecond),
		zap.Bool("adaptive_enabled", config.AdaptiveConfig.Enabled),
		zap.Bool("monitoring_enabled", config.MonitoringConfig.EnableDetailedMetrics),
	)

	return limiter
}

// EnhancedRateLimitMiddleware creates the enhanced rate limiting middleware
func EnhancedRateLimitMiddleware(config *EnhancedRateLimitConfig, logger *zap.Logger) gin.HandlerFunc {
	limiter := NewEnhancedRateLimiter(config, logger)

	return func(c *gin.Context) {
		start := time.Now()

		// Skip health check endpoints
		if limiter.isHealthCheckEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Check if IP is whitelisted
		clientIP := getClientIP(c)
		if limiter.isWhitelisted(clientIP) {
			c.Next()
			return
		}

		// Get user information
		userID := getUserID(c)
		userTier := limiter.getUserTier(c)

		// Check global limits first
		if !limiter.checkGlobalLimits() {
			limiter.handleRateLimit(c, "GLOBAL_LIMIT_EXCEEDED", TierFree, time.Minute)
			return
		}

		// Check user-specific limits
		allowed, retryAfter, reason := limiter.checkUserLimits(userID, userTier)
		if !allowed {
			limiter.handleRateLimit(c, reason, userTier, retryAfter)
			return
		}

		// Record the request
		limiter.recordRequest(userID, userTier)

		// Set rate limit headers
		limiter.setRateLimitHeaders(c, userTier)

		// Execute the request
		c.Next()

		// Record processing time
		processingTime := time.Since(start)
		limiter.recordProcessingTime(processingTime)

		// Log slow requests if enabled
		if config.LogSlowRequests && processingTime > config.SlowRequestThreshold {
			logger.Warn("Slow request detected",
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method),
				zap.Duration("processing_time", processingTime),
				zap.String("user_tier", string(userTier)),
				zap.String("client_ip", clientIP),
			)
		}
	}
}

// checkGlobalLimits checks if global system limits are exceeded
func (erl *EnhancedRateLimiter) checkGlobalLimits() bool {
	erl.globalLimiter.mutex.Lock()
	defer erl.globalLimiter.mutex.Unlock()

	now := time.Now()

	// Check if in emergency mode
	if erl.globalLimiter.isEmergencyMode {
		// Check if emergency mode should be disabled
		if erl.shouldExitEmergencyMode() {
			erl.globalLimiter.isEmergencyMode = false
			erl.logger.Info("Exiting emergency mode")
		} else {
			// Apply emergency limits
			return erl.checkEmergencyLimits()
		}
	}

	// Check concurrent requests
	if erl.globalLimiter.concurrentRequests >= int64(erl.config.GlobalLimits.MaxConcurrentRequests) {
		erl.logger.Warn("Global concurrent request limit exceeded",
			zap.Int64("current", erl.globalLimiter.concurrentRequests),
			zap.Int("limit", erl.config.GlobalLimits.MaxConcurrentRequests),
		)
		return false
	}

	// Check RPS limits
	// Simple implementation - in production, use sliding window
	if erl.globalLimiter.currentRPS >= int64(erl.config.GlobalLimits.MaxRequestsPerSecond) {
		return false
	}

	// Check if we should enter emergency mode
	utilizationRate := float64(erl.globalLimiter.currentRPS) / float64(erl.config.GlobalLimits.MaxRequestsPerSecond)
	if utilizationRate >= erl.config.GlobalLimits.EmergencyThreshold {
		erl.globalLimiter.isEmergencyMode = true
		erl.globalLimiter.emergencyModeStart = now
		erl.metrics.EmergencyModeActivations++
		erl.logger.Warn("Entering emergency mode",
			zap.Float64("utilization", utilizationRate),
			zap.Float64("threshold", erl.config.GlobalLimits.EmergencyThreshold),
		)
	}

	return true
}

// checkUserLimits checks if user-specific limits are exceeded
func (erl *EnhancedRateLimiter) checkUserLimits(userID string, tier UserTier) (bool, time.Duration, string) {
	limiterKey := fmt.Sprintf("%s:%s", string(tier), userID)

	erl.mutex.Lock()
	userLimiter, exists := erl.limiters[limiterKey]
	if !exists {
		userLimiter = erl.createUserLimiter(tier)
		erl.limiters[limiterKey] = userLimiter
	}
	erl.mutex.Unlock()

	return userLimiter.checkLimits()
}

// createUserLimiter creates a new user tier limiter
func (erl *EnhancedRateLimiter) createUserLimiter(tier UserTier) *UserTierLimiter {
	config := erl.config.UserTiers[tier]

	limiter := &UserTierLimiter{
		tier:            tier,
		config:          config,
		slidingWindows:  make(map[time.Duration]*SlidingWindow),
		lastReset:       time.Now(),
		isInGracePeriod: config.GracePeriod > 0,
	}

	if limiter.isInGracePeriod {
		limiter.gracePeriodEnd = time.Now().Add(config.GracePeriod)
	}

	// Initialize sliding windows for different time periods
	if config.RequestsPerSecond > 0 {
		limiter.slidingWindows[time.Second] = NewSlidingWindow(erl.config.WindowConfig)
	}
	if config.RequestsPerMinute > 0 {
		limiter.slidingWindows[time.Minute] = NewSlidingWindow(erl.config.WindowConfig)
	}
	if config.RequestsPerHour > 0 {
		limiter.slidingWindows[time.Hour] = NewSlidingWindow(erl.config.WindowConfig)
	}

	return limiter
}

// checkLimits checks if the user has exceeded their rate limits
func (utl *UserTierLimiter) checkLimits() (bool, time.Duration, string) {
	utl.mutex.Lock()
	defer utl.mutex.Unlock()

	now := time.Now()

	// Check if still in grace period
	if utl.isInGracePeriod && now.Before(utl.gracePeriodEnd) {
		// Allow requests during grace period with relaxed limits
		return true, 0, ""
	} else if utl.isInGracePeriod && now.After(utl.gracePeriodEnd) {
		utl.isInGracePeriod = false
	}

	// Check concurrent requests
	if utl.concurrentCount >= int64(utl.config.ConcurrentRequests) {
		return false, utl.config.BlockDuration, "CONCURRENT_LIMIT_EXCEEDED"
	}

	// Check sliding window limits
	for duration, window := range utl.slidingWindows {
		var limit int
		switch duration {
		case time.Second:
			limit = utl.config.RequestsPerSecond
		case time.Minute:
			limit = utl.config.RequestsPerMinute
		case time.Hour:
			limit = utl.config.RequestsPerHour
		}

		if limit > 0 {
			if !window.Allow(1, limit) {
				reason := fmt.Sprintf("RATE_LIMIT_EXCEEDED_%s", strings.ToUpper(duration.String()))
				return false, utl.config.BlockDuration, reason
			}
		}
	}

	return true, 0, ""
}

// Allow checks if the sliding window allows the request
func (sw *SlidingWindow) Allow(tokens, limit int) bool {
	sw.mutex.Lock()
	defer sw.mutex.Unlock()

	now := time.Now()

	// Clean expired sub-windows
	sw.cleanExpiredWindows(now)

	// Check if adding tokens would exceed the limit
	if sw.totalRequests+int64(tokens) > int64(limit) {
		return false
	}

	// Add tokens to current sub-window
	sw.addTokens(tokens, now)
	return true
}

// NewSlidingWindow creates a new sliding window
func NewSlidingWindow(config SlidingWindowConfig) *SlidingWindow {
	return &SlidingWindow{
		windowSize: config.WindowSize,
		subWindows: make([]SubWindow, config.SubWindows),
	}
}

// Helper methods for sliding window implementation
func (sw *SlidingWindow) cleanExpiredWindows(now time.Time) {
	cutoff := now.Add(-sw.windowSize)
	for i := range sw.subWindows {
		if sw.subWindows[i].startTime.Before(cutoff) {
			sw.totalRequests -= sw.subWindows[i].count
			sw.subWindows[i] = SubWindow{}
		}
	}
}

func (sw *SlidingWindow) addTokens(tokens int, now time.Time) {
	// Find or create appropriate sub-window
	index := sw.findSubWindowIndex(now)
	if sw.subWindows[index].startTime.IsZero() {
		sw.subWindows[index].startTime = now
	}
	sw.subWindows[index].count += int64(tokens)
	sw.subWindows[index].lastUpdate = now
	sw.totalRequests += int64(tokens)
}

func (sw *SlidingWindow) findSubWindowIndex(now time.Time) int {
	// Simple implementation - use time-based index
	return int(now.Unix()) % len(sw.subWindows)
}

// handleRateLimit handles rate limited requests
func (erl *EnhancedRateLimiter) handleRateLimit(c *gin.Context, reason string, tier UserTier, retryAfter time.Duration) {
	erl.recordBlockedRequest(tier, reason)

	// Set rate limit headers
	erl.setRateLimitHeaders(c, tier)

	// Set Retry-After header
	retryAfterSeconds := int(retryAfter.Seconds())
	c.Header("Retry-After", strconv.Itoa(retryAfterSeconds))

	// Check if request should be queued
	if erl.config.GracefulConfig.QueueEnabled && erl.shouldQueueRequest(tier) {
		if erl.queueRequest(c, retryAfter) {
			return // Request was queued and will be processed later
		}
	}

	// Log the rate limit event
	erl.logger.Warn("Request rate limited",
		zap.String("reason", reason),
		zap.String("tier", string(tier)),
		zap.String("client_ip", getClientIP(c)),
		zap.String("path", c.Request.URL.Path),
		zap.Duration("retry_after", retryAfter),
	)

	// Return rate limit response
	message := erl.config.GracefulConfig.CustomErrorMessage
	if message == "" {
		message = fmt.Sprintf("Rate limit exceeded. Try again in %v", retryAfter)
	}

	c.JSON(http.StatusTooManyRequests, gin.H{
		"success":      false,
		"error":        "Rate limit exceeded",
		"code":         reason,
		"message":      message,
		"retry_after":  retryAfterSeconds,
		"current_tier": string(tier),
	})
	c.Abort()
}

// setRateLimitHeaders sets appropriate rate limit headers
func (erl *EnhancedRateLimiter) setRateLimitHeaders(c *gin.Context, tier UserTier) {
	config := erl.config.UserTiers[tier]
	prefix := erl.config.HeaderPrefix

	// Set tier-specific headers
	c.Header(fmt.Sprintf("%s-Tier", prefix), string(tier))
	c.Header(fmt.Sprintf("%s-Limit-Minute", prefix), strconv.Itoa(config.RequestsPerMinute))
	c.Header(fmt.Sprintf("%s-Limit-Hour", prefix), strconv.Itoa(config.RequestsPerHour))
	c.Header(fmt.Sprintf("%s-Limit-Day", prefix), strconv.Itoa(config.RequestsPerDay))

	// Get current usage (simplified implementation)
	userID := getUserID(c)
	limiterKey := fmt.Sprintf("%s:%s", string(tier), userID)

	erl.mutex.RLock()
	if userLimiter, exists := erl.limiters[limiterKey]; exists {
		remaining := config.RequestsPerMinute - int(userLimiter.currentRequests)
		if remaining < 0 {
			remaining = 0
		}
		c.Header(fmt.Sprintf("%s-Remaining", prefix), strconv.Itoa(remaining))
	}
	erl.mutex.RUnlock()
}

// Helper functions

func (erl *EnhancedRateLimiter) getUserTier(c *gin.Context) UserTier {
	// Check if user tier is set in context (from JWT claims)
	if tierStr, exists := c.Get("user_tier"); exists {
		if tier, ok := tierStr.(string); ok {
			return UserTier(tier)
		}
	}

	// Check if user role can be mapped to tier
	if roleStr, exists := c.Get("user_role"); exists {
		if role, ok := roleStr.(string); ok {
			switch role {
			case "admin", "super_admin":
				return TierAdmin
			case "premium":
				return TierPremium
			case "basic":
				return TierBasic
			default:
				return TierFree
			}
		}
	}

	return erl.config.DefaultTier
}

func getUserID(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	// Fallback to IP address for anonymous users
	return getClientIP(c)
}

func (erl *EnhancedRateLimiter) isHealthCheckEndpoint(path string) bool {
	for _, endpoint := range erl.config.GlobalLimits.HealthCheckEndpoints {
		if strings.HasPrefix(path, endpoint) {
			return true
		}
	}
	return false
}

func (erl *EnhancedRateLimiter) isWhitelisted(ip string) bool {
	for _, whitelistedIP := range erl.config.WhitelistedIPs {
		if ip == whitelistedIP {
			return true
		}
	}
	return false
}

// Metrics and monitoring methods

func (erl *EnhancedRateLimiter) recordRequest(userID string, tier UserTier) {
	erl.mutex.Lock()
	defer erl.mutex.Unlock()

	erl.metrics.TotalRequests++
	erl.metrics.RequestsByTier[tier]++

	// Update global limiter
	erl.globalLimiter.mutex.Lock()
	erl.globalLimiter.totalRequests++
	erl.globalLimiter.currentRPS++
	erl.globalLimiter.concurrentRequests++
	erl.globalLimiter.mutex.Unlock()
}

func (erl *EnhancedRateLimiter) recordBlockedRequest(tier UserTier, reason string) {
	erl.mutex.Lock()
	defer erl.mutex.Unlock()

	erl.metrics.TotalBlocked++
	erl.metrics.BlockedByTier[tier]++

	erl.logger.Debug("Request blocked",
		zap.String("tier", string(tier)),
		zap.String("reason", reason),
	)
}

func (erl *EnhancedRateLimiter) recordProcessingTime(duration time.Duration) {
	erl.mutex.Lock()
	defer erl.mutex.Unlock()

	// Update average processing time
	if erl.metrics.AverageProcessingTime == 0 {
		erl.metrics.AverageProcessingTime = duration
	} else {
		erl.metrics.AverageProcessingTime = (erl.metrics.AverageProcessingTime + duration) / 2
	}

	// Update P99 (simplified implementation)
	if duration > erl.metrics.P99ProcessingTime {
		erl.metrics.P99ProcessingTime = duration
	}
}

// Cleanup and maintenance methods

func (erl *EnhancedRateLimiter) startCleanup() {
	ticker := time.NewTicker(erl.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		erl.performCleanup()
	}
}

func (erl *EnhancedRateLimiter) performCleanup() {
	erl.mutex.Lock()
	defer erl.mutex.Unlock()

	now := time.Now()
	cutoff := now.Add(-time.Hour) // Remove limiters inactive for 1 hour

	for key, limiter := range erl.limiters {
		if limiter.lastRequestTime.Before(cutoff) {
			delete(erl.limiters, key)
		}
	}

	erl.logger.Debug("Rate limiter cleanup completed",
		zap.Int("active_limiters", len(erl.limiters)),
	)
}

// Adaptive rate limiting methods

func (erl *EnhancedRateLimiter) startAdaptiveAdjustment() {
	ticker := time.NewTicker(erl.config.AdaptiveConfig.CheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		erl.performAdaptiveAdjustment()
	}
}

func (erl *EnhancedRateLimiter) performAdaptiveAdjustment() {
	// Get current system metrics
	metrics := erl.getSystemMetrics()

	// Check if adjustment is needed
	adjustmentFactor := 1.0

	if metrics.CPUUsage > erl.config.AdaptiveConfig.CPUThreshold {
		adjustmentFactor *= erl.config.AdaptiveConfig.AdaptationFactor
	}

	if metrics.MemoryUsage > erl.config.AdaptiveConfig.MemoryThreshold {
		adjustmentFactor *= erl.config.AdaptiveConfig.AdaptationFactor
	}

	if metrics.ErrorRate > erl.config.AdaptiveConfig.ErrorRateThreshold {
		adjustmentFactor *= erl.config.AdaptiveConfig.AdaptationFactor
	}

	// Apply adjustments if needed
	if adjustmentFactor < 1.0 {
		erl.applyAdaptiveAdjustment(adjustmentFactor)
		erl.metrics.AdaptiveAdjustments++

		erl.logger.Info("Applied adaptive rate limit adjustment",
			zap.Float64("adjustment_factor", adjustmentFactor),
			zap.Float64("cpu_usage", metrics.CPUUsage),
			zap.Float64("memory_usage", metrics.MemoryUsage),
			zap.Float64("error_rate", metrics.ErrorRate),
		)
	}
}

func (erl *EnhancedRateLimiter) getSystemMetrics() *SystemMetrics {
	// In production, this would collect real system metrics
	// For now, return mock metrics
	return &SystemMetrics{
		CPUUsage:        0.5,
		MemoryUsage:     0.6,
		ErrorRate:       0.02,
		AvgResponseTime: 50 * time.Millisecond,
		P99ResponseTime: 200 * time.Millisecond,
		LastUpdated:     time.Now(),
	}
}

func (erl *EnhancedRateLimiter) applyAdaptiveAdjustment(factor float64) {
	// Apply adjustment to global limits
	erl.globalLimiter.mutex.Lock()
	originalLimit := erl.config.GlobalLimits.MaxRequestsPerSecond
	newLimit := int(float64(originalLimit) * factor)
	if newLimit < originalLimit/4 { // Don't reduce below 25% of original
		newLimit = originalLimit / 4
	}
	erl.config.GlobalLimits.MaxRequestsPerSecond = newLimit
	erl.globalLimiter.mutex.Unlock()

	erl.logger.Debug("Adaptive adjustment applied",
		zap.Int("original_limit", originalLimit),
		zap.Int("new_limit", newLimit),
		zap.Float64("factor", factor),
	)
}

// Emergency mode methods

func (erl *EnhancedRateLimiter) shouldExitEmergencyMode() bool {
	// Check if system metrics have improved
	metrics := erl.getSystemMetrics()

	return metrics.CPUUsage < erl.config.AdaptiveConfig.CPUThreshold*0.8 &&
		metrics.MemoryUsage < erl.config.AdaptiveConfig.MemoryThreshold*0.8 &&
		metrics.ErrorRate < erl.config.AdaptiveConfig.ErrorRateThreshold*0.5
}

func (erl *EnhancedRateLimiter) checkEmergencyLimits() bool {
	// Apply stricter emergency limits
	emergencyConfig := erl.config.GlobalLimits.EmergencyLimits

	// Simplified emergency check
	erl.globalLimiter.mutex.RLock()
	currentRPS := erl.globalLimiter.currentRPS
	erl.globalLimiter.mutex.RUnlock()

	return currentRPS < int64(emergencyConfig.RequestsPerSecond)
}

// Request queuing methods (simplified implementation)

func (erl *EnhancedRateLimiter) shouldQueueRequest(tier UserTier) bool {
	config := erl.config.UserTiers[tier]
	return config.PriorityLevel >= 3 // Only queue requests from basic tier and above
}

func (erl *EnhancedRateLimiter) queueRequest(c *gin.Context, retryAfter time.Duration) bool {
	// Simplified queuing implementation
	// In production, this would use a proper request queue

	if retryAfter > erl.config.GracefulConfig.QueueTimeout {
		return false // Don't queue if retry time is too long
	}

	// For now, just delay the request
	time.Sleep(retryAfter)
	return false // Continue with normal processing
}

// GetMetrics returns current rate limiting metrics
func (erl *EnhancedRateLimiter) GetMetrics() *RateLimitMetrics {
	erl.mutex.RLock()
	defer erl.mutex.RUnlock()

	// Update timing metrics
	erl.metrics.LastUpdated = time.Now()
	erl.metrics.UptimeSeconds = int64(time.Since(erl.metrics.LastUpdated).Seconds())

	// Return a copy
	metricsCopy := *erl.metrics
	return &metricsCopy
}

// Monitor implementation (simplified)

func (rm *RateLimitMonitor) Start() {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if rm.isRunning {
		return
	}

	rm.isRunning = true
	go rm.monitoringLoop()

	rm.logger.Info("Rate limit monitor started")
}

func (rm *RateLimitMonitor) monitoringLoop() {
	ticker := time.NewTicker(rm.config.MetricsInterval)
	defer ticker.Stop()

	for range ticker.C {
		rm.checkAlertConditions()
	}
}

func (rm *RateLimitMonitor) checkAlertConditions() {
	// Check various alert conditions and trigger alerts as needed
	rm.mutex.RLock()
	metrics := *rm.metrics
	rm.mutex.RUnlock()

	// Check high utilization
	if metrics.TotalRequests > 0 {
		blockRate := float64(metrics.TotalBlocked) / float64(metrics.TotalRequests)
		if blockRate > rm.config.AlertThresholds.HighUtilization {
			rm.triggerAlert(AlertRateLimitHigh, RateLimitSeverityWarning,
				fmt.Sprintf("High block rate: %.2f%%", blockRate*100), TierFree, "")
		}
	}

	// Check blocked requests threshold
	if metrics.TotalBlocked > rm.config.AlertThresholds.BlockedRequests {
		rm.triggerAlert(RateLimitAlertType("HIGH_BLOCKED_REQUESTS"), RateLimitSeverityCritical,
			fmt.Sprintf("Blocked requests exceeded threshold: %d", metrics.TotalBlocked), TierFree, "")
	}
}

func (rm *RateLimitMonitor) triggerAlert(alertType RateLimitAlertType, severity RateLimitSeverity, message string, tier UserTier, endpoint string) {
	alert := RateLimitAlert{
		Type:      alertType,
		Severity:  severity,
		Message:   message,
		Timestamp: time.Now(),
		Count:     1,
		UserTier:  tier,
		Endpoint:  endpoint,
	}

	rm.mutex.Lock()
	rm.alerts = append(rm.alerts, alert)
	rm.mutex.Unlock()

	rm.logger.Warn("Rate limiting alert triggered",
		zap.String("type", string(alertType)),
		zap.String("severity", string(severity)),
		zap.String("message", message),
		zap.String("tier", string(tier)),
		zap.String("endpoint", endpoint),
	)
}

package middleware

import (
	"bytes"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
)

func TestInputValidationMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		method         string
		path           string
		body           string
		contentType    string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "valid JSON request",
			method:         "POST",
			path:           "/api/test",
			body:           `{"name": "<PERSON>", "email": "<EMAIL>"}`,
			contentType:    "application/json",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "invalid JSON request",
			method:         "POST",
			path:           "/api/test",
			body:           `{"name": "<PERSON>", "email": "<EMAIL>"`,
			contentType:    "application/json",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "INVALID_REQUEST_DATA",
		},
		{
			name:           "XSS attempt in JSON",
			method:         "POST",
			path:           "/api/test",
			body:           `{"name": "<script>alert('xss')</script>", "email": "<EMAIL>"}`,
			contentType:    "application/json",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "INVALID_REQUEST_DATA",
		},
		{
			name:           "SQL injection attempt",
			method:         "POST",
			path:           "/api/test",
			body:           `{"name": "John'; DROP TABLE users; --", "email": "<EMAIL>"}`,
			contentType:    "application/json",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "INVALID_REQUEST_DATA",
		},
		{
			name:           "request too large",
			method:         "POST",
			path:           "/api/test",
			body:           strings.Repeat("a", 2*1024*1024), // 2MB
			contentType:    "application/json",
			expectedStatus: http.StatusRequestEntityTooLarge,
			expectedError:  "REQUEST_TOO_LARGE",
		},
		{
			name:           "missing content type",
			method:         "POST",
			path:           "/api/test",
			body:           `{"name": "John Doe"}`,
			contentType:    "",
			expectedStatus: http.StatusUnsupportedMediaType,
			expectedError:  "UNSUPPORTED_CONTENT_TYPE",
		},
		{
			name:           "GET request with query params",
			method:         "GET",
			path:           "/api/test?name=John&email=<EMAIL>",
			body:           "",
			contentType:    "",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "GET request with XSS in query",
			method:         "GET",
			path:           "/api/test?name=<script>alert('xss')</script>",
			body:           "",
			contentType:    "",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "INVALID_QUERY_PARAMS",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test router
			router := gin.New()

			// Add input validation middleware
			config := DefaultInputValidationConfig()
			config.Logger = zaptest.NewLogger(t)
			router.Use(InputValidationMiddleware(config))

			// Add test endpoint
			router.Any("/api/test", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"status": "ok"})
			})

			// Create request
			req := httptest.NewRequest(tt.method, tt.path, bytes.NewBufferString(tt.body))
			if tt.contentType != "" {
				req.Header.Set("Content-Type", tt.contentType)
			}

			// Create response recorder
			w := httptest.NewRecorder()

			// Perform request
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Check error code if expected
			if tt.expectedError != "" {
				assert.Contains(t, w.Body.String(), tt.expectedError)
			}
		})
	}
}

func TestValidateStringContent(t *testing.T) {
	config := DefaultInputValidationConfig()

	tests := []struct {
		name    string
		input   string
		wantErr bool
	}{
		{
			name:    "normal string",
			input:   "Hello World",
			wantErr: false,
		},
		{
			name:    "string with null byte",
			input:   "Hello\x00World",
			wantErr: true,
		},
		{
			name:    "string with control characters",
			input:   "Hello\x01World",
			wantErr: true,
		},
		{
			name:    "string with allowed control characters",
			input:   "Hello\nWorld\tTest\r",
			wantErr: false,
		},
		{
			name:    "XSS attempt",
			input:   "<script>alert('xss')</script>",
			wantErr: true,
		},
		{
			name:    "SQL injection attempt",
			input:   "'; DROP TABLE users; --",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateStringContent(tt.input, config)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestCheckXSSPatterns(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		wantErr bool
	}{
		{
			name:    "normal string",
			input:   "Hello World",
			wantErr: false,
		},
		{
			name:    "script tag",
			input:   "<script>alert('xss')</script>",
			wantErr: true,
		},
		{
			name:    "javascript protocol",
			input:   "javascript:alert('xss')",
			wantErr: true,
		},
		{
			name:    "onload event",
			input:   "<img onload='alert(1)'>",
			wantErr: true,
		},
		{
			name:    "iframe tag",
			input:   "<iframe src='evil.com'></iframe>",
			wantErr: true,
		},
		{
			name:    "case insensitive script",
			input:   "<SCRIPT>alert('xss')</SCRIPT>",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := checkXSSPatterns(tt.input)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestCheckSQLInjectionPatterns(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		wantErr bool
	}{
		{
			name:    "normal string",
			input:   "Hello World",
			wantErr: false,
		},
		{
			name:    "union select",
			input:   "1' UNION SELECT * FROM users --",
			wantErr: true,
		},
		{
			name:    "drop table",
			input:   "'; DROP TABLE users; --",
			wantErr: true,
		},
		{
			name:    "or 1=1",
			input:   "admin' OR 1=1 --",
			wantErr: true,
		},
		{
			name:    "case insensitive",
			input:   "admin' or 1=1 --",
			wantErr: true,
		},
		{
			name:    "comment",
			input:   "test -- comment",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := checkSQLInjectionPatterns(tt.input)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateEmail(t *testing.T) {
	tests := []struct {
		name    string
		email   string
		wantErr bool
	}{
		{
			name:    "valid email",
			email:   "<EMAIL>",
			wantErr: false,
		},
		{
			name:    "valid email with subdomain",
			email:   "<EMAIL>",
			wantErr: false,
		},
		{
			name:    "empty email",
			email:   "",
			wantErr: true,
		},
		{
			name:    "invalid format",
			email:   "invalid-email",
			wantErr: true,
		},
		{
			name:    "missing @",
			email:   "userexample.com",
			wantErr: true,
		},
		{
			name:    "missing domain",
			email:   "user@",
			wantErr: true,
		},
		{
			name:    "too long",
			email:   strings.Repeat("a", 250) + "@example.com",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateEmail(tt.email)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidatePassword(t *testing.T) {
	tests := []struct {
		name     string
		password string
		wantErr  bool
	}{
		{
			name:     "strong password",
			password: "StrongP@ssw0rd!",
			wantErr:  false,
		},
		{
			name:     "too short",
			password: "Weak1!",
			wantErr:  true,
		},
		{
			name:     "no uppercase",
			password: "weakpassword1!",
			wantErr:  true,
		},
		{
			name:     "no lowercase",
			password: "WEAKPASSWORD1!",
			wantErr:  true,
		},
		{
			name:     "no digit",
			password: "WeakPassword!",
			wantErr:  true,
		},
		{
			name:     "no special character",
			password: "WeakPassword1",
			wantErr:  true,
		},
		{
			name:     "too long",
			password: strings.Repeat("A1a!", 50), // 200 characters
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidatePassword(tt.password)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestSanitizeString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "normal string",
			input:    "Hello World",
			expected: "Hello World",
		},
		{
			name:     "string with null bytes",
			input:    "Hello\x00World",
			expected: "HelloWorld",
		},
		{
			name:     "string with control characters",
			input:    "Hello\x01\x02World",
			expected: "HelloWorld",
		},
		{
			name:     "string with allowed control characters",
			input:    "Hello\nWorld\tTest\r",
			expected: "Hello\nWorld\tTest\r",
		},
		{
			name:     "string with leading/trailing spaces",
			input:    "  Hello World  ",
			expected: "Hello World",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SanitizeString(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func BenchmarkInputValidationMiddleware(b *testing.B) {
	gin.SetMode(gin.TestMode)

	// Create test router
	router := gin.New()
	config := DefaultInputValidationConfig()
	router.Use(InputValidationMiddleware(config))
	router.POST("/api/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// Test data
	body := `{"name": "John Doe", "email": "<EMAIL>", "age": 30}`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("POST", "/api/test", bytes.NewBufferString(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

func BenchmarkValidateStringContent(b *testing.B) {
	config := DefaultInputValidationConfig()
	testString := "This is a normal test string with some content"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		validateStringContent(testString, config)
	}
}

func BenchmarkCheckXSSPatterns(b *testing.B) {
	testString := "This is a normal test string with some content"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		checkXSSPatterns(testString)
	}
}

func BenchmarkCheckSQLInjectionPatterns(b *testing.B) {
	testString := "This is a normal test string with some content"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		checkSQLInjectionPatterns(testString)
	}
}

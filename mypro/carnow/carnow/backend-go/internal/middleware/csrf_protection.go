package middleware

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CSRFProtection provides CSRF protection middleware
type CSRFProtection struct {
	config *CSRFConfig
	logger *zap.Logger
	tokens map[string]*CSRFToken
	mutex  sync.RWMutex
}

// CSRFConfig holds CSRF protection configuration
type CSRFConfig struct {
	// Token settings
	TokenLength    int           `json:"token_length"`
	TokenLifetime  time.Duration `json:"token_lifetime"`
	CookieName     string        `json:"cookie_name"`
	HeaderName     string        `json:"header_name"`
	FormFieldName  string        `json:"form_field_name"`
	
	// Security settings
	SecureOnly     bool     `json:"secure_only"`
	SameSite       string   `json:"same_site"`
	Domain         string   `json:"domain"`
	Path           string   `json:"path"`
	
	// Validation settings
	ValidateOrigin bool     `json:"validate_origin"`
	TrustedOrigins []string `json:"trusted_origins"`
	
	// Exemptions
	ExemptMethods  []string `json:"exempt_methods"`
	ExemptPaths    []string `json:"exempt_paths"`
	
	// Error handling
	ErrorHandler   func(*gin.Context, error) `json:"-"`
	
	// Logging
	Logger *zap.Logger `json:"-"`
}

// CSRFToken represents a CSRF token
type CSRFToken struct {
	Value     string    `json:"value"`
	Hash      string    `json:"hash"`
	SessionID string    `json:"session_id"`
	UserID    string    `json:"user_id"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
	Used      bool      `json:"used"`
}

// DefaultCSRFConfig returns default CSRF configuration
func DefaultCSRFConfig() *CSRFConfig {
	logger, _ := zap.NewProduction()
	return &CSRFConfig{
		TokenLength:    32,
		TokenLifetime:  24 * time.Hour,
		CookieName:     "csrf_token",
		HeaderName:     "X-CSRF-Token",
		FormFieldName:  "_csrf_token",
		SecureOnly:     true,
		SameSite:       "Strict",
		Path:           "/",
		ValidateOrigin: true,
		TrustedOrigins: []string{},
		ExemptMethods:  []string{"GET", "HEAD", "OPTIONS"},
		ExemptPaths:    []string{"/api/v1/health", "/api/v1/auth/login", "/api/v1/auth/register"},
		Logger:         logger,
	}
}

// CarNowCSRFConfig returns CarNow-specific CSRF configuration
func CarNowCSRFConfig() *CSRFConfig {
	config := DefaultCSRFConfig()
	config.TrustedOrigins = []string{
		"http://localhost:3000",
		"https://carnow.app",
		"https://www.carnow.app",
		"https://api.carnow.app",
	}
	config.ExemptPaths = []string{
		"/api/v1/health",
		"/api/v1/auth/login",
		"/api/v1/auth/register",
		"/api/v1/auth/google",
		"/api/v1/auth/refresh",
		"/api/v1/products",
		"/api/v1/categories",
		"/api/v1/cities",
	}
	return config
}

// NewCSRFProtection creates a new CSRF protection middleware
func NewCSRFProtection(config *CSRFConfig) *CSRFProtection {
	if config == nil {
		config = DefaultCSRFConfig()
	}

	csrf := &CSRFProtection{
		config: config,
		logger: config.Logger,
		tokens: make(map[string]*CSRFToken),
	}

	// Start cleanup routine
	go csrf.startCleanupRoutine()

	return csrf
}

// Middleware returns the CSRF protection middleware
func (c *CSRFProtection) Middleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Check if method is exempt
		if c.isMethodExempt(ctx.Request.Method) {
			ctx.Next()
			return
		}

		// Check if path is exempt
		if c.isPathExempt(ctx.Request.URL.Path) {
			ctx.Next()
			return
		}

		// Validate origin if enabled
		if c.config.ValidateOrigin {
			if err := c.validateOrigin(ctx); err != nil {
				c.handleError(ctx, fmt.Errorf("origin validation failed: %w", err))
				return
			}
		}

		// For state-changing methods, validate CSRF token
		if c.isStateChangingMethod(ctx.Request.Method) {
			if err := c.validateCSRFToken(ctx); err != nil {
				c.handleError(ctx, fmt.Errorf("CSRF validation failed: %w", err))
				return
			}
		}

		// Generate and set CSRF token for the response
		if err := c.setCSRFToken(ctx); err != nil {
			c.logger.Warn("Failed to set CSRF token",
				zap.String("path", ctx.Request.URL.Path),
				zap.Error(err),
			)
		}

		ctx.Next()
	}
}

// GenerateToken generates a new CSRF token
func (c *CSRFProtection) GenerateToken(sessionID, userID string) (*CSRFToken, error) {
	// Generate random token
	tokenBytes := make([]byte, c.config.TokenLength)
	if _, err := rand.Read(tokenBytes); err != nil {
		return nil, fmt.Errorf("failed to generate random token: %w", err)
	}

	tokenValue := base64.URLEncoding.EncodeToString(tokenBytes)
	tokenHash := c.hashToken(tokenValue)

	token := &CSRFToken{
		Value:     tokenValue,
		Hash:      tokenHash,
		SessionID: sessionID,
		UserID:    userID,
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(c.config.TokenLifetime),
		Used:      false,
	}

	// Store token
	c.mutex.Lock()
	c.tokens[tokenHash] = token
	c.mutex.Unlock()

	c.logger.Debug("CSRF token generated",
		zap.String("session_id", sessionID),
		zap.String("user_id", userID),
		zap.String("token_hash", tokenHash[:8]+"..."),
	)

	return token, nil
}

// ValidateToken validates a CSRF token
func (c *CSRFProtection) ValidateToken(tokenValue, sessionID string) error {
	if tokenValue == "" {
		return fmt.Errorf("CSRF token is required")
	}

	tokenHash := c.hashToken(tokenValue)

	c.mutex.RLock()
	token, exists := c.tokens[tokenHash]
	c.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("invalid CSRF token")
	}

	// Check if token is expired
	if time.Now().After(token.ExpiresAt) {
		c.mutex.Lock()
		delete(c.tokens, tokenHash)
		c.mutex.Unlock()
		return fmt.Errorf("CSRF token expired")
	}

	// Check if token is already used (optional - depends on use case)
	if token.Used {
		return fmt.Errorf("CSRF token already used")
	}

	// Validate session ID
	if token.SessionID != sessionID {
		return fmt.Errorf("CSRF token session mismatch")
	}

	// Mark token as used (optional)
	c.mutex.Lock()
	token.Used = true
	c.mutex.Unlock()

	c.logger.Debug("CSRF token validated successfully",
		zap.String("session_id", sessionID),
		zap.String("token_hash", tokenHash[:8]+"..."),
	)

	return nil
}

// validateCSRFToken validates CSRF token from request
func (c *CSRFProtection) validateCSRFToken(ctx *gin.Context) error {
	// Get session ID from context
	sessionID := c.getSessionID(ctx)
	if sessionID == "" {
		return fmt.Errorf("session ID not found")
	}

	// Try to get token from header first
	token := ctx.GetHeader(c.config.HeaderName)
	
	// If not in header, try form field
	if token == "" {
		token = ctx.PostForm(c.config.FormFieldName)
	}

	// If still not found, try cookie
	if token == "" {
		cookie, err := ctx.Cookie(c.config.CookieName)
		if err == nil {
			token = cookie
		}
	}

	return c.ValidateToken(token, sessionID)
}

// setCSRFToken sets CSRF token in response
func (c *CSRFProtection) setCSRFToken(ctx *gin.Context) error {
	sessionID := c.getSessionID(ctx)
	userID := c.getUserID(ctx)

	token, err := c.GenerateToken(sessionID, userID)
	if err != nil {
		return err
	}

	// Set token in cookie
	ctx.SetCookie(
		c.config.CookieName,
		token.Value,
		int(c.config.TokenLifetime.Seconds()),
		c.config.Path,
		c.config.Domain,
		c.config.SecureOnly,
		true, // HttpOnly
	)

	// Set token in header for JavaScript access
	ctx.Header(c.config.HeaderName, token.Value)

	return nil
}

// validateOrigin validates request origin
func (c *CSRFProtection) validateOrigin(ctx *gin.Context) error {
	origin := ctx.GetHeader("Origin")
	referer := ctx.GetHeader("Referer")

	// If no origin header, check referer
	if origin == "" {
		if referer == "" {
			return fmt.Errorf("missing origin and referer headers")
		}
		origin = referer
	}

	// Check if origin is in trusted list
	for _, trustedOrigin := range c.config.TrustedOrigins {
		if strings.HasPrefix(origin, trustedOrigin) {
			return nil
		}
	}

	return fmt.Errorf("untrusted origin: %s", origin)
}

// Helper methods

func (c *CSRFProtection) isMethodExempt(method string) bool {
	for _, exemptMethod := range c.config.ExemptMethods {
		if method == exemptMethod {
			return true
		}
	}
	return false
}

func (c *CSRFProtection) isPathExempt(path string) bool {
	for _, exemptPath := range c.config.ExemptPaths {
		if strings.HasPrefix(path, exemptPath) {
			return true
		}
	}
	return false
}

func (c *CSRFProtection) isStateChangingMethod(method string) bool {
	stateChangingMethods := []string{"POST", "PUT", "PATCH", "DELETE"}
	for _, scMethod := range stateChangingMethods {
		if method == scMethod {
			return true
		}
	}
	return false
}

func (c *CSRFProtection) getSessionID(ctx *gin.Context) string {
	if sessionID, exists := ctx.Get("session_id"); exists {
		return sessionID.(string)
	}
	
	// Try to get from cookie or header
	if cookie, err := ctx.Cookie("session_id"); err == nil {
		return cookie
	}
	
	return ctx.GetHeader("X-Session-ID")
}

func (c *CSRFProtection) getUserID(ctx *gin.Context) string {
	if userID, exists := ctx.Get("user_id"); exists {
		return userID.(string)
	}
	return ""
}

func (c *CSRFProtection) hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

func (c *CSRFProtection) handleError(ctx *gin.Context, err error) {
	if c.config.ErrorHandler != nil {
		c.config.ErrorHandler(ctx, err)
		return
	}

	c.logger.Warn("CSRF protection error",
		zap.String("path", ctx.Request.URL.Path),
		zap.String("method", ctx.Request.Method),
		zap.String("client_ip", ctx.ClientIP()),
		zap.Error(err),
	)

	ctx.JSON(http.StatusForbidden, gin.H{
		"success": false,
		"error":   "CSRF protection error",
		"code":    "CSRF_VALIDATION_FAILED",
		"message": "Request blocked by CSRF protection",
	})
	ctx.Abort()
}

// startCleanupRoutine starts a routine to clean up expired tokens
func (c *CSRFProtection) startCleanupRoutine() {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		c.cleanupExpiredTokens()
	}
}

// cleanupExpiredTokens removes expired tokens from memory
func (c *CSRFProtection) cleanupExpiredTokens() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	now := time.Now()
	deletedCount := 0

	for hash, token := range c.tokens {
		if now.After(token.ExpiresAt) {
			delete(c.tokens, hash)
			deletedCount++
		}
	}

	if deletedCount > 0 {
		c.logger.Debug("Cleaned up expired CSRF tokens",
			zap.Int("deleted_count", deletedCount),
			zap.Int("remaining_count", len(c.tokens)),
		)
	}
}

package handlers

import (
	"net/http"

	"carnow-backend/internal/config"
	"carnow-backend/internal/middleware"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CSRFHandlers handles CSRF protection endpoints
type CSRFHandlers struct {
	config         *config.Config
	logger         *zap.Logger
	csrfProtection *middleware.CSRFProtection
}

// CSRF Response structures
type CSRFResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Token   string      `json:"token,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// NewCSRFHandlers creates new CSRF handlers
func NewCSRFHandlers(config *config.Config, logger *zap.Logger) *CSRFHandlers {
	csrfConfig := middleware.CarNowCSRFConfig()
	return &CSRFHandlers{
		config:         config,
		logger:         logger,
		csrfProtection: middleware.NewCSRFProtection(csrfConfig),
	}
}

// GetCSRFToken generates and returns a new CSRF token
// GET /api/v1/csrf/token
func (h *CSRFHandlers) GetCSRFToken(c *gin.Context) {
	// Get session ID from context or generate one
	sessionID := h.getSessionID(c)
	if sessionID == "" {
		// Generate a temporary session ID for anonymous users
		sessionID = "anonymous-" + h.generateTempID()
	}

	// Get user ID if authenticated
	userID := h.getUserID(c)

	// Generate CSRF token
	token, err := h.csrfProtection.GenerateToken(sessionID, userID)
	if err != nil {
		h.logger.Error("Failed to generate CSRF token",
			zap.String("session_id", sessionID),
			zap.String("user_id", userID),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, CSRFResponse{
			Success: false,
			Error:   "Failed to generate CSRF token",
		})
		return
	}

	h.logger.Debug("CSRF token generated",
		zap.String("session_id", sessionID),
		zap.String("user_id", userID),
	)

	c.JSON(http.StatusOK, CSRFResponse{
		Success: true,
		Message: "CSRF token generated successfully",
		Token:   token.Value,
		Data: map[string]interface{}{
			"expires_at": token.ExpiresAt,
			"session_id": sessionID,
		},
	})
}

// ValidateCSRFToken validates a CSRF token
// POST /api/v1/csrf/validate
func (h *CSRFHandlers) ValidateCSRFToken(c *gin.Context) {
	type ValidateRequest struct {
		Token     string `json:"token" binding:"required"`
		SessionID string `json:"session_id" binding:"required"`
	}

	var req ValidateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid CSRF validation request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, CSRFResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Validate token
	err := h.csrfProtection.ValidateToken(req.Token, req.SessionID)
	if err != nil {
		h.logger.Warn("CSRF token validation failed",
			zap.String("session_id", req.SessionID),
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusForbidden, CSRFResponse{
			Success: false,
			Error:   "Invalid CSRF token",
		})
		return
	}

	h.logger.Debug("CSRF token validated successfully",
		zap.String("session_id", req.SessionID),
	)

	c.JSON(http.StatusOK, CSRFResponse{
		Success: true,
		Message: "CSRF token is valid",
	})
}

// GetCSRFConfig returns CSRF configuration for client-side implementation
// GET /api/v1/csrf/config
func (h *CSRFHandlers) GetCSRFConfig(c *gin.Context) {
	config := map[string]interface{}{
		"header_name":     "X-CSRF-Token",
		"cookie_name":     "csrf_token",
		"form_field_name": "_csrf_token",
		"token_lifetime":  "24h",
		"methods_exempt":  []string{"GET", "HEAD", "OPTIONS"},
		"paths_exempt": []string{
			"/api/v1/health",
			"/api/v1/auth/login",
			"/api/v1/auth/register",
			"/api/v1/auth/google",
			"/api/v1/products",
			"/api/v1/categories",
		},
	}

	c.JSON(http.StatusOK, CSRFResponse{
		Success: true,
		Message: "CSRF configuration retrieved successfully",
		Data:    config,
	})
}

// RefreshCSRFToken refreshes an existing CSRF token
// POST /api/v1/csrf/refresh
func (h *CSRFHandlers) RefreshCSRFToken(c *gin.Context) {
	type RefreshRequest struct {
		OldToken  string `json:"old_token" binding:"required"`
		SessionID string `json:"session_id" binding:"required"`
	}

	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid CSRF refresh request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, CSRFResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Validate old token first
	err := h.csrfProtection.ValidateToken(req.OldToken, req.SessionID)
	if err != nil {
		h.logger.Warn("CSRF token refresh failed - invalid old token",
			zap.String("session_id", req.SessionID),
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusForbidden, CSRFResponse{
			Success: false,
			Error:   "Invalid old CSRF token",
		})
		return
	}

	// Get user ID if authenticated
	userID := h.getUserID(c)

	// Generate new token
	newToken, err := h.csrfProtection.GenerateToken(req.SessionID, userID)
	if err != nil {
		h.logger.Error("Failed to generate new CSRF token",
			zap.String("session_id", req.SessionID),
			zap.String("user_id", userID),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, CSRFResponse{
			Success: false,
			Error:   "Failed to generate new CSRF token",
		})
		return
	}

	h.logger.Debug("CSRF token refreshed successfully",
		zap.String("session_id", req.SessionID),
		zap.String("user_id", userID),
	)

	c.JSON(http.StatusOK, CSRFResponse{
		Success: true,
		Message: "CSRF token refreshed successfully",
		Token:   newToken.Value,
		Data: map[string]interface{}{
			"expires_at": newToken.ExpiresAt,
			"session_id": req.SessionID,
		},
	})
}

// GetCSRFStatus returns CSRF protection status and statistics
// GET /api/v1/csrf/status
func (h *CSRFHandlers) GetCSRFStatus(c *gin.Context) {
	// This endpoint is mainly for debugging and monitoring
	// In production, you might want to restrict access to admins only

	status := map[string]interface{}{
		"csrf_enabled":    true,
		"protection_mode": "strict",
		"token_lifetime":  "24h",
		"cleanup_enabled": true,
		"security_level":  "high",
		"features": map[string]bool{
			"origin_validation":    true,
			"referer_validation":   true,
			"token_rotation":       true,
			"session_binding":      true,
			"automatic_cleanup":    true,
			"suspicious_detection": true,
		},
	}

	c.JSON(http.StatusOK, CSRFResponse{
		Success: true,
		Message: "CSRF status retrieved successfully",
		Data:    status,
	})
}

// Helper methods

func (h *CSRFHandlers) getSessionID(c *gin.Context) string {
	if sessionID, exists := c.Get("session_id"); exists {
		return sessionID.(string)
	}

	// Try to get from cookie or header
	if cookie, err := c.Cookie("session_id"); err == nil {
		return cookie
	}

	return c.GetHeader("X-Session-ID")
}

func (h *CSRFHandlers) getUserID(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		return userID.(string)
	}
	return ""
}

func (h *CSRFHandlers) generateTempID() string {
	// Simple temporary ID generation
	// In production, you might want to use a more sophisticated method
	return "temp-" + h.generateRandomString(16)
}

func (h *CSRFHandlers) generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[h.randomInt(len(charset))]
	}
	return string(b)
}

func (h *CSRFHandlers) randomInt(max int) int {
	// Simple random number generation
	// In production, use crypto/rand for better security
	return int(h.config.Security.EncryptionKey[0]) % max
}

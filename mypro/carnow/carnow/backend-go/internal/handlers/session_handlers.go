package handlers

import (
	"net/http"
	"strconv"

	"carnow-backend/internal/config"
	"carnow-backend/internal/services"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SessionHandlers handles enhanced session management endpoints
type SessionHandlers struct {
	config         *config.Config
	db             *gorm.DB
	logger         *zap.Logger
	sessionService *services.EnhancedSessionService
}

// Session Request/Response structures
type CreateSessionRequest struct {
	DeviceFingerprint string `json:"device_fingerprint" binding:"required"`
	DeviceType        string `json:"device_type" binding:"required"`
	DeviceName        string `json:"device_name"`
	UserAgent         string `json:"user_agent"`
	MFAVerified       bool   `json:"mfa_verified"`
}

type SessionResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    interface{}             `json:"data,omitempty"`
	Session *services.UserSession  `json:"session,omitempty"`
	Error   string                  `json:"error,omitempty"`
}

// NewSessionHandlers creates new session handlers
func NewSessionHandlers(config *config.Config, db *gorm.DB, logger *zap.Logger) *SessionHandlers {
	return &SessionHandlers{
		config:         config,
		db:             db,
		logger:         logger,
		sessionService: services.NewEnhancedSessionService(config, db, logger),
	}
}

// CreateSession creates a new enhanced session
// POST /api/v1/auth/sessions
func (h *SessionHandlers) CreateSession(c *gin.Context) {
	var req CreateSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid create session request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, SessionResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// Prepare device info
	deviceInfo := services.DeviceInfo{
		Fingerprint: req.DeviceFingerprint,
		Type:        services.DeviceType(req.DeviceType),
		Name:        req.DeviceName,
		UserAgent:   req.UserAgent,
		IPAddress:   c.ClientIP(),
		Location:    h.sessionService.GetLocationFromIP(c.ClientIP()),
	}

	// If device type not provided, detect from user agent
	if req.DeviceType == "" {
		deviceInfo.Type = h.sessionService.DetectDeviceType(c.GetHeader("User-Agent"))
	}

	// If user agent not provided, get from header
	if req.UserAgent == "" {
		deviceInfo.UserAgent = c.GetHeader("User-Agent")
	}

	// Create session
	session, err := h.sessionService.CreateSession(userIDStr, deviceInfo, req.MFAVerified)
	if err != nil {
		h.logger.Error("Failed to create session",
			zap.String("user_id", userIDStr),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, SessionResponse{
			Success: false,
			Error:   "Failed to create session",
		})
		return
	}

	h.logger.Info("Session created successfully",
		zap.String("user_id", userIDStr),
		zap.String("session_id", session.ID),
	)

	c.JSON(http.StatusCreated, SessionResponse{
		Success: true,
		Message: "Session created successfully",
		Session: session,
	})
}

// GetUserSessions retrieves all active sessions for the current user
// GET /api/v1/auth/sessions
func (h *SessionHandlers) GetUserSessions(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// Get user sessions
	sessions, err := h.sessionService.GetUserSessions(userIDStr)
	if err != nil {
		h.logger.Error("Failed to get user sessions",
			zap.String("user_id", userIDStr),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, SessionResponse{
			Success: false,
			Error:   "Failed to get sessions",
		})
		return
	}

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Sessions retrieved successfully",
		Data: map[string]interface{}{
			"sessions": sessions,
			"count":    len(sessions),
		},
	})
}

// RevokeSession revokes a specific session
// DELETE /api/v1/auth/sessions/:session_id
func (h *SessionHandlers) RevokeSession(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, SessionResponse{
			Success: false,
			Error:   "Session ID is required",
		})
		return
	}

	// Get user ID from context for authorization
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// Revoke session
	if err := h.sessionService.RevokeSession(sessionID); err != nil {
		h.logger.Error("Failed to revoke session",
			zap.String("user_id", userIDStr),
			zap.String("session_id", sessionID),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, SessionResponse{
			Success: false,
			Error:   "Failed to revoke session",
		})
		return
	}

	h.logger.Info("Session revoked successfully",
		zap.String("user_id", userIDStr),
		zap.String("session_id", sessionID),
	)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Session revoked successfully",
	})
}

// RevokeAllSessions revokes all sessions for the current user
// DELETE /api/v1/auth/sessions
func (h *SessionHandlers) RevokeAllSessions(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// Get current session ID to exclude it (optional)
	currentSessionID, _ := c.Get("session_id")
	currentSessionIDStr := ""
	if currentSessionID != nil {
		currentSessionIDStr = currentSessionID.(string)
	}

	// Check if user wants to keep current session
	keepCurrent := c.Query("keep_current") == "true"
	exceptSessionID := ""
	if keepCurrent {
		exceptSessionID = currentSessionIDStr
	}

	// Revoke all sessions
	if err := h.sessionService.RevokeAllUserSessions(userIDStr, exceptSessionID); err != nil {
		h.logger.Error("Failed to revoke all sessions",
			zap.String("user_id", userIDStr),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, SessionResponse{
			Success: false,
			Error:   "Failed to revoke sessions",
		})
		return
	}

	h.logger.Info("All sessions revoked successfully",
		zap.String("user_id", userIDStr),
		zap.String("except_session_id", exceptSessionID),
	)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "All sessions revoked successfully",
	})
}

// RefreshSession refreshes a session using refresh token
// POST /api/v1/auth/sessions/refresh
func (h *SessionHandlers) RefreshSession(c *gin.Context) {
	type RefreshRequest struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid refresh session request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, SessionResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Refresh session
	session, err := h.sessionService.RefreshSession(req.RefreshToken)
	if err != nil {
		h.logger.Warn("Failed to refresh session",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "Invalid or expired refresh token",
		})
		return
	}

	h.logger.Info("Session refreshed successfully",
		zap.String("user_id", session.UserID),
		zap.String("session_id", session.ID),
	)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Session refreshed successfully",
		Session: session,
	})
}

// ValidateSession validates a session token
// POST /api/v1/auth/sessions/validate
func (h *SessionHandlers) ValidateSession(c *gin.Context) {
	type ValidateRequest struct {
		SessionToken string `json:"session_token" binding:"required"`
	}

	var req ValidateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid validate session request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, SessionResponse{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Validate session
	session, err := h.sessionService.ValidateSession(req.SessionToken)
	if err != nil {
		h.logger.Warn("Session validation failed",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "Invalid or expired session",
		})
		return
	}

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Session is valid",
		Session: session,
	})
}

// GetSessionActivity retrieves session activity for the current user
// GET /api/v1/auth/sessions/activity
func (h *SessionHandlers) GetSessionActivity(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, SessionResponse{
			Success: false,
			Error:   "User not authenticated",
		})
		return
	}

	userIDStr := userID.(string)

	// Get pagination parameters
	page := 1
	limit := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// TODO: Implement session activity retrieval
	// For now, return empty activity
	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Session activity retrieved successfully",
		Data: map[string]interface{}{
			"activity": []interface{}{},
			"page":     page,
			"limit":    limit,
			"total":    0,
		},
	})
}

// GenerateDeviceFingerprint generates a device fingerprint
// POST /api/v1/auth/sessions/fingerprint
func (h *SessionHandlers) GenerateDeviceFingerprint(c *gin.Context) {
	type FingerprintRequest struct {
		UserAgent      string            `json:"user_agent"`
		AdditionalData map[string]string `json:"additional_data"`
	}

	var req FingerprintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		req.UserAgent = c.GetHeader("User-Agent")
		req.AdditionalData = make(map[string]string)
	}

	// Generate fingerprint
	fingerprint := h.sessionService.GenerateDeviceFingerprint(
		req.UserAgent,
		c.ClientIP(),
		req.AdditionalData,
	)

	// Detect device type
	deviceType := h.sessionService.DetectDeviceType(req.UserAgent)

	c.JSON(http.StatusOK, SessionResponse{
		Success: true,
		Message: "Device fingerprint generated successfully",
		Data: map[string]interface{}{
			"fingerprint":  fingerprint,
			"device_type":  deviceType,
			"ip_address":   c.ClientIP(),
			"user_agent":   req.UserAgent,
		},
	})
}

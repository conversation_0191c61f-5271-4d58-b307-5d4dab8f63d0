package handlers

import (
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ProductAttribute represents a product attribute
type ProductAttribute struct {
	ID                  string `json:"id"`
	ProductID           string `json:"product_id"`
	CategoryAttributeID string `json:"category_attribute_id"`
	AttributeValue      string `json:"attribute_value"`
	CreatedAt           string `json:"created_at"`
	UpdatedAt           string `json:"updated_at"`
}

// CreateProductAttribute handles POST /api/v1/product-attributes
func (api *CleanAPI) CreateProductAttribute(c *gin.Context) {
	ctx := c.Request.Context()

	var request struct {
		ProductID           string `json:"product_id" binding:"required"`
		CategoryAttributeID string `json:"category_attribute_id" binding:"required"`
		AttributeValue      string `json:"attribute_value" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.J<PERSON>N(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if product exists
	var productExists bool
	err := api.DB.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM products WHERE id = $1 AND is_deleted = false)", request.ProductID).Scan(&productExists)
	if err != nil {
		log.Printf("Failed to check product existence: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	if !productExists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Product not found"})
		return
	}

	// Check if category attribute exists
	var attributeExists bool
	err = api.DB.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM category_attributes WHERE id = $1 AND is_deleted = false)", request.CategoryAttributeID).Scan(&attributeExists)
	if err != nil {
		log.Printf("Failed to check category attribute existence: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	if !attributeExists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category attribute not found"})
		return
	}

	// Check if attribute already exists for this product
	var existingAttributeID string
	err = api.DB.QueryRow(ctx, "SELECT id FROM product_attributes WHERE product_id = $1 AND category_attribute_id = $2", request.ProductID, request.CategoryAttributeID).Scan(&existingAttributeID)
	if err == nil {
		// Attribute already exists, update it
		err = api.DB.Exec(ctx, "UPDATE product_attributes SET attribute_value = $1, updated_at = $2 WHERE id = $3", request.AttributeValue, time.Now().UTC().Format(time.RFC3339), existingAttributeID)
		if err != nil {
			log.Printf("Failed to update product attribute: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"id":      existingAttributeID,
			"message": "Product attribute updated successfully",
		})
		return
	}

	// Create new attribute
	attributeID := uuid.New().String()
	now := time.Now().UTC().Format(time.RFC3339)

	query := `
		INSERT INTO product_attributes (
			id, product_id, category_attribute_id, attribute_value, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6)
		RETURNING id
	`

	err = api.DB.QueryRow(ctx, query,
		attributeID, request.ProductID, request.CategoryAttributeID,
		request.AttributeValue, now, now,
	).Scan(&attributeID)

	if err != nil {
		log.Printf("Failed to create product attribute: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"id":      attributeID,
		"message": "Product attribute created successfully",
	})
}

// UpdateProductAttribute handles PUT /api/v1/product-attributes/:id
func (api *CleanAPI) UpdateProductAttribute(c *gin.Context) {
	ctx := c.Request.Context()
	attributeID := c.Param("id")

	var request struct {
		AttributeValue string `json:"attribute_value" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if attribute exists
	var exists bool
	err := api.DB.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM product_attributes WHERE id = $1)", attributeID).Scan(&exists)
	if err != nil {
		log.Printf("Failed to check product attribute existence: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Product attribute not found"})
		return
	}

	// Update attribute
	err = api.DB.Exec(ctx, "UPDATE product_attributes SET attribute_value = $1, updated_at = $2 WHERE id = $3", request.AttributeValue, time.Now().UTC().Format(time.RFC3339), attributeID)
	if err != nil {
		log.Printf("Failed to update product attribute: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Product attribute updated successfully",
	})
}

// DeleteProductAttribute handles DELETE /api/v1/product-attributes/:id
func (api *CleanAPI) DeleteProductAttribute(c *gin.Context) {
	ctx := c.Request.Context()
	attributeID := c.Param("id")

	// Check if attribute exists
	var exists bool
	err := api.DB.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM product_attributes WHERE id = $1)", attributeID).Scan(&exists)
	if err != nil {
		log.Printf("Failed to check product attribute existence: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Product attribute not found"})
		return
	}

	// Delete attribute
	err = api.DB.Exec(ctx, "DELETE FROM product_attributes WHERE id = $1", attributeID)
	if err != nil {
		log.Printf("Failed to delete product attribute: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Product attribute deleted successfully",
	})
}

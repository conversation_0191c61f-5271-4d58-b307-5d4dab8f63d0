package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// IJWTService interface for testing
type IJWTService interface {
	GenerateTokenPair(userID, email, role string) (*services.TokenPair, error)
	ValidateToken(token string) (*services.JWTClaims, error)
	RefreshToken(refreshToken string) (*services.TokenPair, error)
	RevokeToken(tokenID string) error
}

// IGoogleOAuthService interface for testing
type IGoogleOAuthService interface {
	VerifyIDToken(ctx context.Context, idToken string) (*services.GoogleUserInfo, error)
	VerifyIDTokenWithOAuth2(ctx context.Context, idToken string) (*services.GoogleUserInfo, error)
	VerifyIDTokenWithFallback(ctx context.Context, idToken string) (*services.GoogleUserInfo, error)
}

// MockJWTService is a mock of JWTService
type MockJWTService struct {
	mock.Mock
}

func (m *MockJWTService) GenerateTokenPair(userID, email, role string) (*services.TokenPair, error) {
	args := m.Called(userID, email, role)
	return args.Get(0).(*services.TokenPair), args.Error(1)
}

func (m *MockJWTService) ValidateToken(token string) (*services.JWTClaims, error) {
	args := m.Called(token)
	return args.Get(0).(*services.JWTClaims), args.Error(1)
}

func (m *MockJWTService) RefreshToken(refreshToken string) (*services.TokenPair, error) {
	args := m.Called(refreshToken)
	return args.Get(0).(*services.TokenPair), args.Error(1)
}

func (m *MockJWTService) RevokeToken(tokenID string) error {
	args := m.Called(tokenID)
	return args.Error(0)
}

// MockGoogleOAuthService is a mock of GoogleOAuthService
type MockGoogleOAuthService struct {
	mock.Mock
}

func (m *MockGoogleOAuthService) VerifyIDToken(ctx context.Context, idToken string) (*services.GoogleUserInfo, error) {
	args := m.Called(ctx, idToken)
	return args.Get(0).(*services.GoogleUserInfo), args.Error(1)
}

func (m *MockGoogleOAuthService) VerifyIDTokenWithOAuth2(ctx context.Context, idToken string) (*services.GoogleUserInfo, error) {
	args := m.Called(ctx, idToken)
	return args.Get(0).(*services.GoogleUserInfo), args.Error(1)
}

func (m *MockGoogleOAuthService) VerifyIDTokenWithFallback(ctx context.Context, idToken string) (*services.GoogleUserInfo, error) {
	args := m.Called(ctx, idToken)
	return args.Get(0).(*services.GoogleUserInfo), args.Error(1)
}

// GetTestConfig returns a mock config for testing
func GetTestConfig() *config.Config {
	return &config.Config{
		Supabase: config.SupabaseConfig{
			URL:            "http://localhost:54321",
			AnonKey:        "test-anon-key",
			ServiceRoleKey: "test-service-role-key",
			JWTSecret:      "test-jwt-secret",
			ProjectRef:     "test-project-ref",
		},
		JWT: config.JWTConfig{
			Secret:   "test-jwt-secret",
			Issuer:   "carnow-test",
			Audience: "carnow-api-test",
		},
		Google: config.GoogleConfig{
			ClientID:     "test-client-id.apps.googleusercontent.com",
			ClientSecret: "test-client-secret",
		},
	}
}

// Using LoginRequest from auth_handlers.go

// RegisterRequest represents registration request
type RegisterRequest struct {
	Email           string `json:"email"`
	Password        string `json:"password"`
	FirstName       string `json:"first_name"`
	LastName        string `json:"last_name"`
	ConfirmPassword string `json:"confirm_password"`
}

// GoogleAuthRequest represents Google Auth request
type GoogleAuthRequest struct {
	IDToken string `json:"id_token"`
}

// RefreshTokenRequest represents refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token"`
}

func TestLogin(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		requestBody    LoginRequest
		expectedStatus int
		setupMocks     func(*MockJWTService, *MockGoogleOAuthService)
	}{
		{
			name: "successful login",
			requestBody: LoginRequest{
				Email:    "<EMAIL>",
				Password: "password123",
			},
			expectedStatus: http.StatusOK,
			setupMocks: func(jwtService *MockJWTService, googleService *MockGoogleOAuthService) {
				jwtService.On("GenerateTokenPair", mock.AnythingOfType("string"), "<EMAIL>", "user").
					Return(&services.TokenPair{
						AccessToken:  "access_token",
						RefreshToken: "refresh_token",
						ExpiresAt:    time.Now().Add(15 * time.Minute),
						TokenType:    "Bearer",
					}, nil)
			},
		},
		{
			name: "missing email",
			requestBody: LoginRequest{
				Password: "password123",
			},
			expectedStatus: http.StatusBadRequest,
			setupMocks:     func(*MockJWTService, *MockGoogleOAuthService) {},
		},
		{
			name: "missing password",
			requestBody: LoginRequest{
				Email: "<EMAIL>",
			},
			expectedStatus: http.StatusBadRequest,
			setupMocks:     func(*MockJWTService, *MockGoogleOAuthService) {},
		},
		{
			name: "invalid email format",
			requestBody: LoginRequest{
				Email:    "invalid-email",
				Password: "password123",
			},
			expectedStatus: http.StatusBadRequest,
			setupMocks:     func(*MockJWTService, *MockGoogleOAuthService) {},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockJWT := new(MockJWTService)
			mockGoogle := new(MockGoogleOAuthService)
			mockConfig := GetTestConfig()

			if test.setupMocks != nil {
				test.setupMocks(mockJWT, mockGoogle)
			}

			// Create real services for testing (mocks will override behavior)
			jwtSvc, _ := services.NewJWTService(mockConfig)
			googleSvc, _ := services.NewGoogleOAuthService(mockConfig.Google.ClientID, mockConfig.Google.ClientSecret)

			handler := &UnifiedAuthHandlers{
				jwtService:    jwtSvc,
				googleService: googleSvc,
				config:        mockConfig,
				httpClient:    &http.Client{Timeout: 10 * time.Second},
			}

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			jsonBody, _ := json.Marshal(test.requestBody)
			c.Request, _ = http.NewRequest("POST", "/auth/login", bytes.NewBuffer(jsonBody))
			c.Request.Header.Set("Content-Type", "application/json")

			handler.Login(c)

			assert.Equal(t, test.expectedStatus, w.Code)
		})
	}
}

func TestRegister(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		requestBody    RegisterRequest
		expectedStatus int
		setupMocks     func(*MockJWTService, *MockGoogleOAuthService)
	}{
		{
			name: "successful registration",
			requestBody: RegisterRequest{
				Email:           "<EMAIL>",
				Password:        "password123",
				FirstName:       "John",
				LastName:        "Doe",
				ConfirmPassword: "password123",
			},
			expectedStatus: http.StatusOK,
			setupMocks: func(jwtService *MockJWTService, googleService *MockGoogleOAuthService) {
				jwtService.On("GenerateTokenPair", mock.AnythingOfType("string"), "<EMAIL>", "user").
					Return(&services.TokenPair{
						AccessToken:  "access_token",
						RefreshToken: "refresh_token",
						ExpiresAt:    time.Now().Add(15 * time.Minute),
						TokenType:    "Bearer",
					}, nil)
			},
		},
		{
			name: "password mismatch",
			requestBody: RegisterRequest{
				Email:           "<EMAIL>",
				Password:        "password123",
				FirstName:       "John",
				LastName:        "Doe",
				ConfirmPassword: "different123",
			},
			expectedStatus: http.StatusBadRequest,
			setupMocks:     func(*MockJWTService, *MockGoogleOAuthService) {},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockJWT := new(MockJWTService)
			mockGoogle := new(MockGoogleOAuthService)
			mockConfig := GetTestConfig()

			if test.setupMocks != nil {
				test.setupMocks(mockJWT, mockGoogle)
			}

			// Create real services for testing (mocks will override behavior)
			jwtSvc, _ := services.NewJWTService(mockConfig)
			googleSvc, _ := services.NewGoogleOAuthService(mockConfig.Google.ClientID, mockConfig.Google.ClientSecret)

			handler := &UnifiedAuthHandlers{
				jwtService:    jwtSvc,
				googleService: googleSvc,
				config:        mockConfig,
				httpClient:    &http.Client{Timeout: 10 * time.Second},
			}

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			jsonBody, _ := json.Marshal(test.requestBody)
			c.Request, _ = http.NewRequest("POST", "/auth/register", bytes.NewBuffer(jsonBody))
			c.Request.Header.Set("Content-Type", "application/json")

			handler.Register(c)

			assert.Equal(t, test.expectedStatus, w.Code)
		})
	}
}

func TestGoogleAuth(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		requestBody    GoogleAuthRequest
		expectedStatus int
		setupMocks     func(*MockJWTService, *MockGoogleOAuthService)
	}{
		{
			name: "successful google auth",
			requestBody: GoogleAuthRequest{
				IDToken: "valid_id_token",
			},
			expectedStatus: http.StatusOK,
			setupMocks: func(jwtService *MockJWTService, googleService *MockGoogleOAuthService) {
				googleUser := &services.GoogleUserInfo{
					ID:            "google-123",
					Email:         "<EMAIL>",
					VerifiedEmail: true,
					Name:          "Test User",
					GivenName:     "Test",
					FamilyName:    "User",
					Picture:       "https://example.com/profile.jpg",
					Locale:        "en",
				}
				googleService.On("VerifyIDTokenWithFallback", mock.Anything, "valid_id_token").Return(googleUser, nil)

				tokenPair := &services.TokenPair{
					AccessToken:  "test-access-token",
					RefreshToken: "test-refresh-token",
					ExpiresAt:    time.Now().Add(15 * time.Minute),
					TokenType:    "Bearer",
				}
				jwtService.On("GenerateTokenPair", "google-123", "<EMAIL>", mock.Anything).Return(tokenPair, nil)
			},
		},
		{
			name: "invalid google token",
			requestBody: GoogleAuthRequest{
				IDToken: "invalid_id_token",
			},
			expectedStatus: http.StatusUnauthorized,
			setupMocks: func(jwtService *MockJWTService, googleService *MockGoogleOAuthService) {
				googleService.On("VerifyIDTokenWithFallback", mock.Anything, "invalid_id_token").Return((*services.GoogleUserInfo)(nil), fmt.Errorf("invalid token"))
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockJWT := new(MockJWTService)
			mockGoogle := new(MockGoogleOAuthService)
			mockConfig := GetTestConfig()

			if test.setupMocks != nil {
				test.setupMocks(mockJWT, mockGoogle)
			}

			// Create real services for testing (mocks will override behavior)
			jwtSvc, _ := services.NewJWTService(mockConfig)
			googleSvc, _ := services.NewGoogleOAuthService(mockConfig.Google.ClientID, mockConfig.Google.ClientSecret)

			handler := &UnifiedAuthHandlers{
				jwtService:    jwtSvc,
				googleService: googleSvc,
				config:        mockConfig,
				httpClient:    &http.Client{Timeout: 10 * time.Second},
			}

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			jsonBody, _ := json.Marshal(test.requestBody)
			c.Request, _ = http.NewRequest("POST", "/auth/google", bytes.NewBuffer(jsonBody))
			c.Request.Header.Set("Content-Type", "application/json")

			handler.GoogleAuth(c)

			assert.Equal(t, test.expectedStatus, w.Code)
		})
	}
}

func TestRefreshToken(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		requestBody    RefreshTokenRequest
		expectedStatus int
		setupMocks     func(*MockJWTService, *MockGoogleOAuthService)
	}{
		{
			name: "successful refresh",
			requestBody: RefreshTokenRequest{
				RefreshToken: "valid_refresh_token",
			},
			expectedStatus: http.StatusOK,
			setupMocks: func(jwtService *MockJWTService, googleService *MockGoogleOAuthService) {
				tokenPair := &services.TokenPair{
					AccessToken:  "test-access-token",
					RefreshToken: "test-refresh-token",
					ExpiresAt:    time.Now().Add(15 * time.Minute),
					TokenType:    "Bearer",
				}
				jwtService.On("RefreshToken", "valid_refresh_token").Return(tokenPair, nil)
			},
		},
		{
			name: "invalid refresh token",
			requestBody: RefreshTokenRequest{
				RefreshToken: "invalid_refresh_token",
			},
			expectedStatus: http.StatusUnauthorized,
			setupMocks: func(jwtService *MockJWTService, googleService *MockGoogleOAuthService) {
				jwtService.On("RefreshToken", "invalid_refresh_token").Return((*services.TokenPair)(nil), fmt.Errorf("invalid token"))
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockJWT := new(MockJWTService)
			mockGoogle := new(MockGoogleOAuthService)
			mockConfig := GetTestConfig()

			if test.setupMocks != nil {
				test.setupMocks(mockJWT, mockGoogle)
			}

			// Create real services for testing (mocks will override behavior)
			jwtSvc, _ := services.NewJWTService(mockConfig)
			googleSvc, _ := services.NewGoogleOAuthService(mockConfig.Google.ClientID, mockConfig.Google.ClientSecret)

			handler := &UnifiedAuthHandlers{
				jwtService:    jwtSvc,
				googleService: googleSvc,
				config:        mockConfig,
				httpClient:    &http.Client{Timeout: 10 * time.Second},
			}

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			jsonBody, _ := json.Marshal(test.requestBody)
			c.Request, _ = http.NewRequest("POST", "/auth/refresh", bytes.NewBuffer(jsonBody))
			c.Request.Header.Set("Content-Type", "application/json")

			handler.RefreshToken(c)

			assert.Equal(t, test.expectedStatus, w.Code)
		})
	}
}

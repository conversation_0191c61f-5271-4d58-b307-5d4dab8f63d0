package handlers

import (
	"database/sql"
	"net/http"
	"strings"

	"log"

	"github.com/gin-gonic/gin"
)

// Product represents a product in the system
type Product struct {
	ID                     string   `json:"id"`
	MainCategoryID         string   `json:"main_category_id"`
	NameEn                 string   `json:"name_en"`
	NameAr                 string   `json:"name_ar"`
	DescriptionEn          *string  `json:"description_en,omitempty"`
	DescriptionAr          *string  `json:"description_ar,omitempty"`
	ManufacturerPartNumber *string  `json:"manufacturer_part_number,omitempty"`
	OEMPartNumbers         []string `json:"oem_part_numbers,omitempty"`
	Brand                  *string  `json:"brand,omitempty"`
	ConditionType          string   `json:"condition_type"`
	FitmentType            *string  `json:"fitment_type,omitempty"`
	CoreCharge             float64  `json:"core_charge"`
	ManufacturerWarranty   *string  `json:"manufacturer_warranty,omitempty"`
	Price                  *float64 `json:"price,omitempty"`
	SalePrice              *float64 `json:"sale_price,omitempty"`
	Currency               string   `json:"currency"`
	StockQuantity          int      `json:"stock_quantity"`
	MinOrderQuantity       int      `json:"min_order_quantity"`
	WeightKg               *float64 `json:"weight_kg,omitempty"`
	DimensionsCm           *string  `json:"dimensions_cm,omitempty"`
	CountryOfOrigin        *string  `json:"country_of_origin,omitempty"`
	SurfaceFinish          *string  `json:"surface_finish,omitempty"`
	IsActive               bool     `json:"is_active"`
	IsFeatured             bool     `json:"is_featured"`
	IsBestseller           bool     `json:"is_bestseller"`
	SortOrder              int      `json:"sort_order"`
	CreatedAt              string   `json:"created_at"`
	UpdatedAt              string   `json:"updated_at"`
	CreatedBy              *string  `json:"created_by,omitempty"`
	UpdatedBy              *string  `json:"updated_by,omitempty"`
}

// ProductSearchRequest represents a product search request
type ProductSearchRequest struct {
	Query          string            `json:"query"`
	MainCategoryID string            `json:"main_category_id"`
	Filters        map[string]string `json:"filters"`
	Page           int               `json:"page"`
	Limit          int               `json:"limit"`
	SortBy         string            `json:"sort_by"`
	SortOrder      string            `json:"sort_order"`
}

// ProductSearchResponse represents a product search response
type ProductSearchResponse struct {
	Data       []Product `json:"data"`
	Total      int       `json:"total"`
	Page       int       `json:"page"`
	Limit      int       `json:"limit"`
	TotalPages int       `json:"total_pages"`
	HasNext    bool      `json:"has_next"`
	HasPrev    bool      `json:"has_prev"`
}

// GetProduct handles GET /api/v1/products/:id
func (api *CleanAPI) GetProduct(c *gin.Context) {
	ctx := c.Request.Context()
	productID := c.Param("id")

	query := `
		SELECT 
			id, main_category_id, name_en, name_ar, description_en, description_ar,
			manufacturer_part_number, oem_part_numbers, brand, condition_type, fitment_type,
			core_charge, manufacturer_warranty, price, sale_price, currency, stock_quantity,
			min_order_quantity, weight_kg, dimensions_cm, country_of_origin, surface_finish,
			is_active, is_featured, is_bestseller, sort_order, created_at, updated_at,
			created_by, updated_by
		FROM products 
		WHERE id = $1 AND is_deleted = false
	`

	var product Product
	var oemPartNumbers sql.NullString

	err := api.DB.QueryRow(ctx, query, productID).Scan(
		&product.ID, &product.MainCategoryID, &product.NameEn, &product.NameAr,
		&product.DescriptionEn, &product.DescriptionAr, &product.ManufacturerPartNumber,
		&oemPartNumbers, &product.Brand, &product.ConditionType, &product.FitmentType,
		&product.CoreCharge, &product.ManufacturerWarranty, &product.Price,
		&product.SalePrice, &product.Currency, &product.StockQuantity,
		&product.MinOrderQuantity, &product.WeightKg, &product.DimensionsCm,
		&product.CountryOfOrigin, &product.SurfaceFinish, &product.IsActive,
		&product.IsFeatured, &product.IsBestseller, &product.SortOrder,
		&product.CreatedAt, &product.UpdatedAt, &product.CreatedBy, &product.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
			return
		}
		log.Printf("Failed to get product: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	// Parse OEM part numbers
	if oemPartNumbers.Valid && oemPartNumbers.String != "" {
		product.OEMPartNumbers = strings.Split(oemPartNumbers.String, ",")
	}

	c.JSON(http.StatusOK, product)
}

// GetProductAttributes handles GET /api/v1/products/:id/attributes
func (api *CleanAPI) GetProductAttributes(c *gin.Context) {
	ctx := c.Request.Context()
	productID := c.Param("id")

	query := `
		SELECT 
			pa.id, pa.product_id, pa.category_attribute_id, pa.attribute_value,
			pa.created_at, pa.updated_at,
			ca.attribute_name_en, ca.attribute_name_ar
		FROM product_attributes pa
		JOIN category_attributes ca ON pa.category_attribute_id = ca.id
		WHERE pa.product_id = $1
		ORDER BY ca.sort_order
	`

	rows, err := api.DB.Query(ctx, query, productID)
	if err != nil {
		log.Printf("Failed to get product attributes: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	defer rows.Close()

	var attributes []map[string]interface{}
	for rows.Next() {
		var attr ProductAttribute
		var attributeNameEn, attributeNameAr string

		err := rows.Scan(
			&attr.ID, &attr.ProductID, &attr.CategoryAttributeID, &attr.AttributeValue,
			&attr.CreatedAt, &attr.UpdatedAt, &attributeNameEn, &attributeNameAr,
		)

		if err != nil {
			log.Printf("Failed to scan product attribute: %v", err)
			continue
		}

		attributes = append(attributes, map[string]interface{}{
			"id":                    attr.ID,
			"product_id":            attr.ProductID,
			"category_attribute_id": attr.CategoryAttributeID,
			"attribute_value":       attr.AttributeValue,
			"attribute_name_en":     attributeNameEn,
			"attribute_name_ar":     attributeNameAr,
			"created_at":            attr.CreatedAt,
			"updated_at":            attr.UpdatedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": attributes})
}

// GetFeaturedProducts handles GET /api/v1/products/featured
func (api *CleanAPI) GetFeaturedProducts(c *gin.Context) {
	ctx := c.Request.Context()

	query := `
		SELECT 
			id, main_category_id, name_en, name_ar, description_en, description_ar,
			manufacturer_part_number, oem_part_numbers, brand, condition_type, fitment_type,
			core_charge, manufacturer_warranty, price, sale_price, currency, stock_quantity,
			min_order_quantity, weight_kg, dimensions_cm, country_of_origin, surface_finish,
			is_active, is_featured, is_bestseller, sort_order, created_at, updated_at,
			created_by, updated_by
		FROM products 
		WHERE is_featured = true AND is_active = true AND is_deleted = false
		ORDER BY sort_order, created_at DESC
		LIMIT 10
	`

	rows, err := api.DB.Query(ctx, query)
	if err != nil {
		log.Printf("Failed to get featured products: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	defer rows.Close()

	var products []Product
	for rows.Next() {
		var product Product
		var oemPartNumbers sql.NullString

		err := rows.Scan(
			&product.ID, &product.MainCategoryID, &product.NameEn, &product.NameAr,
			&product.DescriptionEn, &product.DescriptionAr, &product.ManufacturerPartNumber,
			&oemPartNumbers, &product.Brand, &product.ConditionType, &product.FitmentType,
			&product.CoreCharge, &product.ManufacturerWarranty, &product.Price,
			&product.SalePrice, &product.Currency, &product.StockQuantity,
			&product.MinOrderQuantity, &product.WeightKg, &product.DimensionsCm,
			&product.CountryOfOrigin, &product.SurfaceFinish, &product.IsActive,
			&product.IsFeatured, &product.IsBestseller, &product.SortOrder,
			&product.CreatedAt, &product.UpdatedAt, &product.CreatedBy, &product.UpdatedBy,
		)

		if err != nil {
			log.Printf("Failed to scan featured product: %v", err)
			continue
		}

		// Parse OEM part numbers
		if oemPartNumbers.Valid && oemPartNumbers.String != "" {
			product.OEMPartNumbers = strings.Split(oemPartNumbers.String, ",")
		}

		products = append(products, product)
	}

	c.JSON(http.StatusOK, gin.H{"data": products})
}

// GetBestsellerProducts handles GET /api/v1/products/bestsellers
func (api *CleanAPI) GetBestsellerProducts(c *gin.Context) {
	ctx := c.Request.Context()

	query := `
		SELECT 
			id, main_category_id, name_en, name_ar, description_en, description_ar,
			manufacturer_part_number, oem_part_numbers, brand, condition_type, fitment_type,
			core_charge, manufacturer_warranty, price, sale_price, currency, stock_quantity,
			min_order_quantity, weight_kg, dimensions_cm, country_of_origin, surface_finish,
			is_active, is_featured, is_bestseller, sort_order, created_at, updated_at,
			created_by, updated_by
		FROM products 
		WHERE is_bestseller = true AND is_active = true AND is_deleted = false
		ORDER BY sort_order, created_at DESC
		LIMIT 10
	`

	rows, err := api.DB.Query(ctx, query)
	if err != nil {
		log.Printf("Failed to get bestseller products: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	defer rows.Close()

	var products []Product
	for rows.Next() {
		var product Product
		var oemPartNumbers sql.NullString

		err := rows.Scan(
			&product.ID, &product.MainCategoryID, &product.NameEn, &product.NameAr,
			&product.DescriptionEn, &product.DescriptionAr, &product.ManufacturerPartNumber,
			&oemPartNumbers, &product.Brand, &product.ConditionType, &product.FitmentType,
			&product.CoreCharge, &product.ManufacturerWarranty, &product.Price,
			&product.SalePrice, &product.Currency, &product.StockQuantity,
			&product.MinOrderQuantity, &product.WeightKg, &product.DimensionsCm,
			&product.CountryOfOrigin, &product.SurfaceFinish, &product.IsActive,
			&product.IsFeatured, &product.IsBestseller, &product.SortOrder,
			&product.CreatedAt, &product.UpdatedAt, &product.CreatedBy, &product.UpdatedBy,
		)

		if err != nil {
			log.Printf("Failed to scan bestseller product: %v", err)
			continue
		}

		// Parse OEM part numbers
		if oemPartNumbers.Valid && oemPartNumbers.String != "" {
			product.OEMPartNumbers = strings.Split(oemPartNumbers.String, ",")
		}

		products = append(products, product)
	}

	c.JSON(http.StatusOK, gin.H{"data": products})
}

package handlers

import (
	"io"
	"net/http"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/services"
)

// ImageHandler handles image-related HTTP requests following Forever Plan architecture
type ImageHandler struct {
	imageService *services.ImageService
	logger       *zap.Logger
}

// NewImageHandler creates a new image handler instance
func NewImageHandler(imageService *services.ImageService, logger *zap.Logger) *ImageHandler {
	return &ImageHandler{
		imageService: imageService,
		logger:       logger,
	}
}

// UploadProductImage handles POST /images/products - Upload and process product image
func (h *ImageHandler) UploadProductImage(c *gin.Context) {
	h.logger.Info("Product image upload request received")

	// Get user ID from context (for authorization)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	// Parse multipart form
	err := c.Request.ParseMultipartForm(10 << 20) // 10MB max
	if err != nil {
		h.logger.Error("Failed to parse multipart form", zap.Error(err))
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Failed to parse form data"),
		})
		return
	}

	// Get uploaded file
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		h.logger.Error("Failed to get uploaded file", zap.Error(err))
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("No image file provided"),
		})
		return
	}
	defer file.Close()

	// Validate file type
	if !h.isValidImageType(header.Filename) {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid image format. Supported formats: JPEG, PNG, WebP, GIF"),
		})
		return
	}

	// Read file data
	imageData, err := io.ReadAll(file)
	if err != nil {
		h.logger.Error("Failed to read image data", zap.Error(err))
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error:   stringPtr("Failed to read image data"),
		})
		return
	}

	// Process image
	processedImage, err := h.imageService.ProcessProductImage(c.Request.Context(), imageData, header.Filename)
	if err != nil {
		h.logger.Error("Failed to process image", zap.Error(err))
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error:   stringPtr("Failed to process image: " + err.Error()),
		})
		return
	}

	h.logger.Info("Product image uploaded and processed successfully",
		zap.String("user_id", userID.(string)),
		zap.String("image_id", processedImage.ID),
		zap.String("filename", header.Filename))

	c.JSON(http.StatusCreated, APIResponse{
		Success: true,
		Data:    processedImage,
		Message: stringPtr("Image uploaded and processed successfully"),
	})
}

// GetImage handles GET /images/:id/:filename - Serve processed image
func (h *ImageHandler) GetImage(c *gin.Context) {
	imageID := c.Param("id")
	filename := c.Param("filename")

	if imageID == "" || filename == "" {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Image ID and filename are required"),
		})
		return
	}

	// Construct file path
	filePath := filepath.Join("./uploads/images", imageID, filename)

	// Check if file exists and serve it
	if _, err := filepath.Abs(filePath); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid file path"),
		})
		return
	}

	// Set appropriate content type
	contentType := h.getContentType(filename)
	c.Header("Content-Type", contentType)
	c.Header("Cache-Control", "public, max-age=31536000") // Cache for 1 year

	// Serve file
	c.File(filePath)
}

// UploadMultipleImages handles POST /images/products/batch - Upload multiple product images
func (h *ImageHandler) UploadMultipleImages(c *gin.Context) {
	h.logger.Info("Multiple product images upload request received")

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	// Parse multipart form
	err := c.Request.ParseMultipartForm(50 << 20) // 50MB max for multiple files
	if err != nil {
		h.logger.Error("Failed to parse multipart form", zap.Error(err))
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Failed to parse form data"),
		})
		return
	}

	// Get uploaded files
	form := c.Request.MultipartForm
	files := form.File["images"]

	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("No image files provided"),
		})
		return
	}

	// Limit number of files
	maxFiles := 10
	if len(files) > maxFiles {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Too many files. Maximum 10 files allowed"),
		})
		return
	}

	var processedImages []*services.ProcessedImage
	var errors []string

	// Process each file
	for _, fileHeader := range files {
		file, err := fileHeader.Open()
		if err != nil {
			errors = append(errors, "Failed to open file: "+fileHeader.Filename)
			continue
		}

		// Validate file type
		if !h.isValidImageType(fileHeader.Filename) {
			file.Close()
			errors = append(errors, "Invalid format for file: "+fileHeader.Filename)
			continue
		}

		// Read file data
		imageData, err := io.ReadAll(file)
		file.Close()
		if err != nil {
			errors = append(errors, "Failed to read file: "+fileHeader.Filename)
			continue
		}

		// Process image
		processedImage, err := h.imageService.ProcessProductImage(c.Request.Context(), imageData, fileHeader.Filename)
		if err != nil {
			errors = append(errors, "Failed to process file: "+fileHeader.Filename+" - "+err.Error())
			continue
		}

		processedImages = append(processedImages, processedImage)
	}

	h.logger.Info("Multiple product images processed",
		zap.String("user_id", userID.(string)),
		zap.Int("successful", len(processedImages)),
		zap.Int("failed", len(errors)))

	response := map[string]interface{}{
		"processed_images": processedImages,
		"successful_count": len(processedImages),
		"failed_count":     len(errors),
	}

	if len(errors) > 0 {
		response["errors"] = errors
	}

	statusCode := http.StatusCreated
	if len(processedImages) == 0 {
		statusCode = http.StatusBadRequest
	} else if len(errors) > 0 {
		statusCode = http.StatusPartialContent
	}

	c.JSON(statusCode, APIResponse{
		Success: len(processedImages) > 0,
		Data:    response,
		Message: stringPtr("Image processing completed"),
	})
}

// GetImageInfo handles GET /images/:id/info - Get image information
func (h *ImageHandler) GetImageInfo(c *gin.Context) {
	imageID := c.Param("id")

	if imageID == "" {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Image ID is required"),
		})
		return
	}

	// TODO: Implement database lookup for image info
	// For now, return basic info
	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: map[string]interface{}{
			"id":      imageID,
			"message": "Image info endpoint - to be implemented with database lookup",
		},
		Message: stringPtr("Image information retrieved"),
	})
}

// isValidImageType checks if the file has a valid image extension
func (h *ImageHandler) isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExtensions := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}

	for _, validExt := range validExtensions {
		if ext == validExt {
			return true
		}
	}

	return false
}

// getContentType returns the appropriate content type for the file
func (h *ImageHandler) getContentType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))

	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	default:
		return "application/octet-stream"
	}
}

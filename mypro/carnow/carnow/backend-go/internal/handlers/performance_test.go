package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"sync"
	"testing"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/infrastructure/database"
	"carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// PerformanceTestConfig defines the configuration for performance tests
type PerformanceTestConfig struct {
	Duration          time.Duration
	ConcurrentUsers   int
	RequestsPerSecond int
	MaxResponseTime   time.Duration
}

// PerformanceResult holds the results of a performance test
type PerformanceResult struct {
	TotalRequests     int
	SuccessfulReqs    int
	FailedReqs        int
	AverageResponse   time.Duration
	MaxResponse       time.Duration
	MinResponse       time.Duration
	RequestsPerSecond float64
	ErrorRate         float64
}

func setupPerformanceTestServer() (*gin.Engine, *CleanAPI, *AuthHandlers) {
	gin.SetMode(gin.TestMode)

	// Create test config
	cfg := &config.Config{
		App: config.AppConfig{
			Name:        "carnow-perf-test",
			Environment: "test",
			Debug:       false, // Disable debug for performance testing
		},
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-32-characters-long",
			ExpiresIn:        15 * time.Minute,
			RefreshExpiresIn: 7 * 24 * time.Hour,
			Issuer:           "carnow-test",
		},
		Database: config.DatabaseConfig{
			Host:            "localhost",
			Port:            5432,
			Username:        "test",
			Password:        "test",
			Database:        "test_db",
			SSLMode:         "disable",
			MaxOpenConns:    50, // Optimized for performance testing
			MaxIdleConns:    10,
			ConnMaxLifetime: time.Hour,
			ConnMaxIdleTime: 30 * time.Minute,
		},
	}

	// Create mock database
	var mockDB *database.SimpleDB = nil

	// Create API instances
	cleanAPI := NewCleanAPI(mockDB)
	authHandlers, _ := NewAuthHandlers(cfg, mockDB)
	if authHandlers == nil {
		jwtService, _ := services.NewJWTService(cfg)
		authHandlers = &AuthHandlers{
			config:     cfg,
			db:         mockDB,
			jwtService: jwtService,
		}
	}

	// Create router with minimal middleware for performance
	router := gin.New()
	router.Use(gin.Recovery())

	// Setup routes
	v1 := router.Group("/api/v1")
	{
		v1.GET("/products", cleanAPI.GetProducts)
		v1.GET("/search", cleanAPI.SearchProducts)
		v1.POST("/products", cleanAPI.CreateProduct)
		v1.PUT("/products/:id", cleanAPI.UpdateProduct)
		v1.DELETE("/products/:id", cleanAPI.DeleteProduct)
		v1.GET("/categories", cleanAPI.GetCategories)
	}

	auth := router.Group("/auth")
	{
		auth.POST("/login", authHandlers.Login)
		auth.POST("/refresh", authHandlers.RefreshToken)
		auth.POST("/logout", authHandlers.Logout)
		auth.POST("/google", authHandlers.GoogleOAuth)
		auth.GET("/public-key", authHandlers.GetPublicKey)
	}

	return router, cleanAPI, authHandlers
}

// runLoadTest executes a load test with the given configuration
func runLoadTest(t *testing.T, router *gin.Engine, endpoint string, method string, body []byte, config PerformanceTestConfig) *PerformanceResult {
	t.Helper()

	var wg sync.WaitGroup
	var mu sync.Mutex

	result := &PerformanceResult{
		MinResponse: time.Hour, // Start with high value
	}

	startTime := time.Now()
	endTime := startTime.Add(config.Duration)

	// Channel to control request rate
	rateLimiter := time.NewTicker(time.Second / time.Duration(config.RequestsPerSecond))
	defer rateLimiter.Stop()

	// Launch concurrent users
	for i := 0; i < config.ConcurrentUsers; i++ {
		wg.Add(1)
		go func(userID int) {
			defer wg.Done()

			for {
				select {
				case <-rateLimiter.C:
					if time.Now().After(endTime) {
						return
					}

					// Create request
					var req *http.Request
					if body != nil {
						req = httptest.NewRequest(method, endpoint, bytes.NewBuffer(body))
						req.Header.Set("Content-Type", "application/json")
					} else {
						req = httptest.NewRequest(method, endpoint, nil)
					}

					// Execute request
					w := httptest.NewRecorder()
					requestStart := time.Now()
					router.ServeHTTP(w, req)
					responseTime := time.Since(requestStart)

					// Update results
					mu.Lock()
					result.TotalRequests++
					if w.Code >= 200 && w.Code < 400 {
						result.SuccessfulReqs++
					} else {
						result.FailedReqs++
					}

					// Update response time statistics
					if responseTime > result.MaxResponse {
						result.MaxResponse = responseTime
					}
					if responseTime < result.MinResponse {
						result.MinResponse = responseTime
					}

					// Calculate running average
					if result.TotalRequests == 1 {
						result.AverageResponse = responseTime
					} else {
						// Running average formula: new_avg = old_avg + (new_value - old_avg) / count
						result.AverageResponse = result.AverageResponse +
							(responseTime-result.AverageResponse)/time.Duration(result.TotalRequests)
					}
					mu.Unlock()

				case <-time.After(config.Duration):
					return
				}
			}
		}(i)
	}

	wg.Wait()

	// Calculate final statistics
	actualDuration := time.Since(startTime)
	result.RequestsPerSecond = float64(result.TotalRequests) / actualDuration.Seconds()
	result.ErrorRate = float64(result.FailedReqs) / float64(result.TotalRequests) * 100

	return result
}

func TestPerformance_GetProducts(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	router, _, _ := setupPerformanceTestServer()

	config := PerformanceTestConfig{
		Duration:          10 * time.Second,
		ConcurrentUsers:   10,
		RequestsPerSecond: 50,
		MaxResponseTime:   200 * time.Millisecond,
	}

	t.Logf("Running load test for GET /api/v1/products")
	t.Logf("Config: %d users, %d RPS, %v duration",
		config.ConcurrentUsers, config.RequestsPerSecond, config.Duration)

	result := runLoadTest(t, router, "/api/v1/products", "GET", nil, config)

	// Log results
	t.Logf("Results:")
	t.Logf("  Total Requests: %d", result.TotalRequests)
	t.Logf("  Successful: %d", result.SuccessfulReqs)
	t.Logf("  Failed: %d", result.FailedReqs)
	t.Logf("  Average Response Time: %v", result.AverageResponse)
	t.Logf("  Max Response Time: %v", result.MaxResponse)
	t.Logf("  Min Response Time: %v", result.MinResponse)
	t.Logf("  Requests Per Second: %.2f", result.RequestsPerSecond)
	t.Logf("  Error Rate: %.2f%%", result.ErrorRate)

	// Performance assertions
	assert.Less(t, result.ErrorRate, 5.0, "Error rate should be less than 5%")
	assert.Less(t, result.AverageResponse, config.MaxResponseTime,
		"Average response time should be less than %v", config.MaxResponseTime)
	assert.Greater(t, result.RequestsPerSecond, float64(config.RequestsPerSecond)*0.8,
		"Should achieve at least 80% of target RPS")
}

func TestPerformance_AuthLogin(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	router, _, _ := setupPerformanceTestServer()

	loginBody := map[string]string{
		"email":    "<EMAIL>",
		"password": "password123",
	}
	bodyBytes, _ := json.Marshal(loginBody)

	config := PerformanceTestConfig{
		Duration:          5 * time.Second,
		ConcurrentUsers:   5,
		RequestsPerSecond: 20,
		MaxResponseTime:   500 * time.Millisecond,
	}

	t.Logf("Running load test for POST /auth/login")
	result := runLoadTest(t, router, "/auth/login", "POST", bodyBytes, config)

	// Log results
	t.Logf("Auth Login Results:")
	t.Logf("  Total Requests: %d", result.TotalRequests)
	t.Logf("  Average Response Time: %v", result.AverageResponse)
	t.Logf("  Error Rate: %.2f%%", result.ErrorRate)

	// Auth endpoints can have higher response times due to security operations
	assert.Less(t, result.ErrorRate, 10.0, "Error rate should be less than 10%")
	assert.Less(t, result.AverageResponse, config.MaxResponseTime)
}

func TestPerformance_CreateProduct(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	router, _, _ := setupPerformanceTestServer()

	productBody := map[string]interface{}{
		"name":        "Performance Test Product",
		"description": "A product created during performance testing",
		"price":       99.99,
		"category_id": "550e8400-e29b-41d4-a716-446655440000",
	}
	bodyBytes, _ := json.Marshal(productBody)

	config := PerformanceTestConfig{
		Duration:          5 * time.Second,
		ConcurrentUsers:   3,
		RequestsPerSecond: 10,
		MaxResponseTime:   300 * time.Millisecond,
	}

	t.Logf("Running load test for POST /api/v1/products")
	result := runLoadTest(t, router, "/api/v1/products", "POST", bodyBytes, config)

	// Log results
	t.Logf("Create Product Results:")
	t.Logf("  Total Requests: %d", result.TotalRequests)
	t.Logf("  Average Response Time: %v", result.AverageResponse)
	t.Logf("  Error Rate: %.2f%%", result.ErrorRate)

	// Write operations typically have higher response times
	assert.Less(t, result.ErrorRate, 15.0, "Error rate should be less than 15%")
	assert.Less(t, result.AverageResponse, config.MaxResponseTime)
}

func TestPerformance_SearchProducts(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	router, _, _ := setupPerformanceTestServer()

	config := PerformanceTestConfig{
		Duration:          8 * time.Second,
		ConcurrentUsers:   8,
		RequestsPerSecond: 30,
		MaxResponseTime:   250 * time.Millisecond,
	}

	t.Logf("Running load test for GET /api/v1/search")
	result := runLoadTest(t, router, "/api/v1/search?q=test", "GET", nil, config)

	// Log results
	t.Logf("Search Products Results:")
	t.Logf("  Total Requests: %d", result.TotalRequests)
	t.Logf("  Average Response Time: %v", result.AverageResponse)
	t.Logf("  Requests Per Second: %.2f", result.RequestsPerSecond)
	t.Logf("  Error Rate: %.2f%%", result.ErrorRate)

	assert.Less(t, result.ErrorRate, 8.0, "Error rate should be less than 8%")
	assert.Less(t, result.AverageResponse, config.MaxResponseTime)
}

// Stress test - pushes the system beyond normal limits
func TestStress_HighConcurrency(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping stress test in short mode")
	}

	router, _, _ := setupPerformanceTestServer()

	config := PerformanceTestConfig{
		Duration:          15 * time.Second,
		ConcurrentUsers:   50,              // High concurrency
		RequestsPerSecond: 200,             // High request rate
		MaxResponseTime:   1 * time.Second, // More lenient for stress test
	}

	t.Logf("Running STRESS test for GET /api/v1/products")
	t.Logf("High Concurrency: %d users, %d RPS", config.ConcurrentUsers, config.RequestsPerSecond)

	result := runLoadTest(t, router, "/api/v1/products", "GET", nil, config)

	// Log stress test results
	t.Logf("STRESS Test Results:")
	t.Logf("  Total Requests: %d", result.TotalRequests)
	t.Logf("  Successful: %d", result.SuccessfulReqs)
	t.Logf("  Failed: %d", result.FailedReqs)
	t.Logf("  Average Response Time: %v", result.AverageResponse)
	t.Logf("  Max Response Time: %v", result.MaxResponse)
	t.Logf("  Requests Per Second: %.2f", result.RequestsPerSecond)
	t.Logf("  Error Rate: %.2f%%", result.ErrorRate)

	// More lenient assertions for stress testing
	assert.Less(t, result.ErrorRate, 25.0, "Error rate should be less than 25% under stress")
	assert.Less(t, result.AverageResponse, config.MaxResponseTime,
		"Average response time should be reasonable under stress")

	// System should handle at least some requests successfully
	assert.Greater(t, result.SuccessfulReqs, 100, "Should handle at least 100 requests successfully")
}

// Memory and resource usage test
func TestPerformance_MemoryUsage(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping memory test in short mode")
	}

	router, _, _ := setupPerformanceTestServer()

	// Run a sustained load to test memory usage
	config := PerformanceTestConfig{
		Duration:          30 * time.Second, // Longer duration
		ConcurrentUsers:   20,
		RequestsPerSecond: 100,
		MaxResponseTime:   500 * time.Millisecond,
	}

	t.Logf("Running memory usage test for 30 seconds")

	// Monitor memory usage (simplified - in production use runtime.MemStats)
	ctx, cancel := context.WithTimeout(context.Background(), config.Duration+5*time.Second)
	defer cancel()

	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				t.Logf("Memory test running...")
			case <-ctx.Done():
				return
			}
		}
	}()

	result := runLoadTest(t, router, "/api/v1/products", "GET", nil, config)

	t.Logf("Memory Test Results:")
	t.Logf("  Total Requests: %d", result.TotalRequests)
	t.Logf("  Average Response Time: %v", result.AverageResponse)
	t.Logf("  Error Rate: %.2f%%", result.ErrorRate)

	// System should maintain performance over time
	assert.Less(t, result.ErrorRate, 10.0, "Error rate should remain low over time")
	assert.Less(t, result.AverageResponse, 300*time.Millisecond,
		"Response time should not degrade significantly over time")
}

// Benchmark for specific operations
func BenchmarkGetProducts_Sequential(b *testing.B) {
	router, _, _ := setupPerformanceTestServer()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("GET", "/api/v1/products", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

func BenchmarkGetProducts_Parallel(b *testing.B) {
	router, _, _ := setupPerformanceTestServer()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			req := httptest.NewRequest("GET", "/api/v1/products", nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
		}
	})
}

func BenchmarkAuthLogin_Sequential(b *testing.B) {
	router, _, _ := setupPerformanceTestServer()

	loginBody := map[string]string{
		"email":    "<EMAIL>",
		"password": "password123",
	}
	bodyBytes, _ := json.Marshal(loginBody)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("POST", "/auth/login", bytes.NewBuffer(bodyBytes))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

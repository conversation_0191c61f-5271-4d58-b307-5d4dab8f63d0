package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/infrastructure/database"
	"carnow-backend/internal/shared/services"
	"carnow-backend/internal/shared/validation"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SecureAuthHandlers handles authentication with enhanced security validation
type SecureAuthHandlers struct {
	config     *config.Config
	db         *database.SimpleDB
	jwtService *services.JWTService
	validator  *validation.CustomValidator
	logger     *zap.Logger
}

// NewSecureAuthHandlers creates new secure authentication handlers
func NewSecureAuthHandlers(cfg *config.Config, db *database.SimpleDB) (*SecureAuthHandlers, error) {
	jwtService, err := services.NewJWTService(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize JWT service: %w", err)
	}

	logger, err := zap.NewProduction()
	if err != nil {
		return nil, fmt.Errorf("failed to initialize logger: %w", err)
	}

	return &SecureAuthHandlers{
		config:     cfg,
		db:         db,
		jwtService: jwtService,
		validator:  validation.NewCustomValidator(),
		logger:     logger,
	}, nil
}

// SecureLoginRequest represents the secure login request with validation
type SecureLoginRequest struct {
	Email    string `json:"email" validate:"required,email,max=254,safe_string,no_xss"`
	Password string `json:"password" validate:"required,min=6,max=128,safe_string"`
}

// SecureRegisterRequest represents the secure registration request
type SecureRegisterRequest struct {
	Email           string `json:"email" validate:"required,email,max=254,safe_string,no_xss"`
	Password        string `json:"password" validate:"required,strong_password,max=128"`
	ConfirmPassword string `json:"confirm_password" validate:"required,eqfield=Password"`
	FirstName       string `json:"first_name" validate:"required,min=2,max=50,alphanumeric_spaces,no_html,safe_string"`
	LastName        string `json:"last_name" validate:"required,min=2,max=50,alphanumeric_spaces,no_html,safe_string"`
	PhoneNumber     string `json:"phone_number,omitempty" validate:"omitempty,phone_number,max=20"`
}

// SecureRefreshRequest represents the secure token refresh request
type SecureRefreshRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required,min=10,max=1000,safe_string"`
}

// PasswordResetRequest represents password reset request
type PasswordResetRequest struct {
	Email string `json:"email" validate:"required,email,max=254,safe_string,no_xss"`
}

// PasswordResetConfirmRequest represents password reset confirmation
type PasswordResetConfirmRequest struct {
	Token           string `json:"token" validate:"required,min=10,max=500,safe_string"`
	Password        string `json:"password" validate:"required,strong_password,max=128"`
	ConfirmPassword string `json:"confirm_password" validate:"required,eqfield=Password"`
}

// ChangePasswordRequest represents password change request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required,min=6,max=128,safe_string"`
	NewPassword     string `json:"new_password" validate:"required,strong_password,max=128"`
	ConfirmPassword string `json:"confirm_password" validate:"required,eqfield=NewPassword"`
}

// SecureLoginResponse represents the secure login response
type SecureLoginResponse struct {
	Success      bool                   `json:"success"`
	AccessToken  string                 `json:"access_token"`
	RefreshToken string                 `json:"refresh_token"`
	ExpiresAt    time.Time              `json:"expires_at"`
	TokenType    string                 `json:"token_type"`
	User         map[string]interface{} `json:"user"`
	Message      string                 `json:"message"`
}

// SecureLogin handles user authentication with enhanced security validation
func (h *SecureAuthHandlers) SecureLogin(c *gin.Context) {
	var req SecureLoginRequest

	// Bind and validate JSON
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid JSON in login request",
			zap.String("client_ip", c.ClientIP()),
			zap.String("user_agent", c.GetHeader("User-Agent")),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"code":    "INVALID_JSON",
			"message": "Request body must be valid JSON",
		})
		return
	}

	// Validate request data
	if err := h.validator.Validate(&req); err != nil {
		validationErrors := validation.FormatValidationErrors(err)
		h.logger.Warn("Login validation failed",
			zap.String("client_ip", c.ClientIP()),
			zap.String("email", req.Email),
			zap.Any("validation_errors", validationErrors),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Validation failed",
			"code":    "VALIDATION_ERROR",
			"message": "Please check your input data",
			"details": validationErrors,
		})
		return
	}

	// Sanitize inputs
	req.Email = validation.SanitizeInput(req.Email)
	req.Password = validation.SanitizeInput(req.Password)

	h.logger.Info("Secure login attempt",
		zap.String("email", req.Email),
		zap.String("client_ip", c.ClientIP()),
		zap.String("user_agent", c.GetHeader("User-Agent")),
	)

	// Authenticate with Supabase
	user, err := h.authenticateWithSupabase(req.Email, req.Password)
	if err != nil {
		h.logger.Warn("Authentication failed",
			zap.String("email", req.Email),
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "Authentication failed",
			"code":    "AUTH_FAILED",
			"message": "Invalid email or password",
		})
		return
	}

	// Generate secure JWT token pair
	tokenPair, err := h.jwtService.GenerateTokenPair(user.ID, user.Email, user.Role)
	if err != nil {
		h.logger.Error("Token generation failed",
			zap.String("email", req.Email),
			zap.String("user_id", user.ID),
			zap.Error(err),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to generate authentication tokens",
			"code":    "TOKEN_GENERATION_FAILED",
			"message": "Internal server error",
		})
		return
	}

	h.logger.Info("Successful secure login",
		zap.String("email", req.Email),
		zap.String("user_id", user.ID),
		zap.String("client_ip", c.ClientIP()),
	)

	// Return secure tokens
	c.JSON(http.StatusOK, SecureLoginResponse{
		Success:      true,
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt,
		TokenType:    tokenPair.TokenType,
		User: map[string]interface{}{
			"id":    user.ID,
			"email": user.Email,
			"role":  user.Role,
		},
		Message: "Login successful",
	})
}

// SecureRegister handles user registration with enhanced validation
func (h *SecureAuthHandlers) SecureRegister(c *gin.Context) {
	var req SecureRegisterRequest

	// Bind and validate JSON
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid JSON in register request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"code":    "INVALID_JSON",
			"message": "Request body must be valid JSON",
		})
		return
	}

	// Validate request data
	if err := h.validator.Validate(&req); err != nil {
		validationErrors := validation.FormatValidationErrors(err)
		h.logger.Warn("Registration validation failed",
			zap.String("client_ip", c.ClientIP()),
			zap.String("email", req.Email),
			zap.Any("validation_errors", validationErrors),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Validation failed",
			"code":    "VALIDATION_ERROR",
			"message": "Please check your input data",
			"details": validationErrors,
		})
		return
	}

	// Sanitize inputs
	req.Email = validation.SanitizeInput(req.Email)
	req.FirstName = validation.SanitizeInput(req.FirstName)
	req.LastName = validation.SanitizeInput(req.LastName)
	req.PhoneNumber = validation.SanitizeInput(req.PhoneNumber)

	h.logger.Info("Secure registration attempt",
		zap.String("email", req.Email),
		zap.String("client_ip", c.ClientIP()),
	)

	// Register with Supabase
	user, err := h.registerWithSupabase(req)
	if err != nil {
		h.logger.Warn("Registration failed",
			zap.String("email", req.Email),
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Registration failed",
			"code":    "REGISTRATION_FAILED",
			"message": err.Error(),
		})
		return
	}

	h.logger.Info("Successful registration",
		zap.String("email", req.Email),
		zap.String("user_id", user.ID),
		zap.String("client_ip", c.ClientIP()),
	)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Registration successful. Please check your email for verification.",
		"user": map[string]interface{}{
			"id":    user.ID,
			"email": user.Email,
		},
	})
}

// SecureRefreshToken handles token refresh with enhanced validation
func (h *SecureAuthHandlers) SecureRefreshToken(c *gin.Context) {
	var req SecureRefreshRequest

	// Bind and validate JSON
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid JSON in refresh request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"code":    "INVALID_JSON",
			"message": "Request body must be valid JSON",
		})
		return
	}

	// Validate request data
	if err := h.validator.Validate(&req); err != nil {
		validationErrors := validation.FormatValidationErrors(err)
		h.logger.Warn("Refresh token validation failed",
			zap.String("client_ip", c.ClientIP()),
			zap.Any("validation_errors", validationErrors),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Validation failed",
			"code":    "VALIDATION_ERROR",
			"message": "Invalid refresh token format",
			"details": validationErrors,
		})
		return
	}

	// Sanitize input
	req.RefreshToken = validation.SanitizeInput(req.RefreshToken)

	h.logger.Info("Token refresh attempt",
		zap.String("client_ip", c.ClientIP()),
	)

	// Refresh tokens using JWT service
	tokenPair, err := h.jwtService.RefreshToken(req.RefreshToken)
	if err != nil {
		h.logger.Warn("Token refresh failed",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "Invalid refresh token",
			"code":    "REFRESH_FAILED",
			"message": "Please login again",
		})
		return
	}

	h.logger.Info("Successful token refresh",
		zap.String("client_ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, SecureLoginResponse{
		Success:      true,
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt,
		TokenType:    tokenPair.TokenType,
		Message:      "Token refresh successful",
	})
}

// SecureLogout handles user logout with enhanced security
func (h *SecureAuthHandlers) SecureLogout(c *gin.Context) {
	// Get token ID from context (set by middleware)
	tokenID, exists := c.Get("token_id")
	if exists && tokenID != "" {
		// Revoke the token
		if err := h.jwtService.RevokeToken(tokenID.(string)); err != nil {
			h.logger.Warn("Failed to revoke token during logout",
				zap.String("token_id", tokenID.(string)),
				zap.Error(err),
			)
		}
	}

	userEmail := c.GetString("user_email")
	h.logger.Info("Secure logout",
		zap.String("user_email", userEmail),
		zap.String("client_ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Logout successful",
	})
}

// RequestPasswordReset handles password reset requests
func (h *SecureAuthHandlers) RequestPasswordReset(c *gin.Context) {
	var req PasswordResetRequest

	// Bind and validate JSON
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid JSON in password reset request",
			zap.String("client_ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"code":    "INVALID_JSON",
		})
		return
	}

	// Validate request data
	if err := h.validator.Validate(&req); err != nil {
		validationErrors := validation.FormatValidationErrors(err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Validation failed",
			"code":    "VALIDATION_ERROR",
			"details": validationErrors,
		})
		return
	}

	// Sanitize input
	req.Email = validation.SanitizeInput(req.Email)

	h.logger.Info("Password reset requested",
		zap.String("email", req.Email),
		zap.String("client_ip", c.ClientIP()),
	)

	// Always return success to prevent email enumeration
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "If the email exists, a password reset link has been sent",
	})
}

// ChangePassword handles password change requests
func (h *SecureAuthHandlers) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest

	// Bind and validate JSON
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"code":    "INVALID_JSON",
		})
		return
	}

	// Validate request data
	if err := h.validator.Validate(&req); err != nil {
		validationErrors := validation.FormatValidationErrors(err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Validation failed",
			"code":    "VALIDATION_ERROR",
			"details": validationErrors,
		})
		return
	}

	userEmail := c.GetString("user_email")
	userID := c.GetString("user_id")

	h.logger.Info("Password change attempt",
		zap.String("user_email", userEmail),
		zap.String("user_id", userID),
		zap.String("client_ip", c.ClientIP()),
	)

	// TODO: Implement password change logic with Supabase
	// For now, return success
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Password changed successfully",
	})
}

// Helper methods

// authenticateWithSupabase authenticates user credentials with Supabase
func (h *SecureAuthHandlers) authenticateWithSupabase(email, password string) (*SupabaseUser, error) {
	// Create authentication request to Supabase
	url := fmt.Sprintf("%s/auth/v1/token?grant_type=password", h.config.Supabase.URL)

	payload := map[string]string{
		"email":    email,
		"password": password,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", url, strings.NewReader(string(payloadBytes)))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", h.config.Supabase.AnonKey)

	// Make request with timeout
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("authentication request failed: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("authentication failed - status: %d", resp.StatusCode)
	}

	// Parse response
	var authResp SupabaseAuthResponse
	if err := json.NewDecoder(resp.Body).Decode(&authResp); err != nil {
		return nil, fmt.Errorf("failed to parse authentication response: %w", err)
	}

	// Validate response
	if authResp.User.ID == "" || authResp.User.Email == "" {
		return nil, fmt.Errorf("invalid user data in response")
	}

	// Set default role if not provided
	role := authResp.User.Role
	if role == "" {
		role = "authenticated"
	}

	return &SupabaseUser{
		ID:    authResp.User.ID,
		Email: authResp.User.Email,
		Role:  role,
	}, nil
}

// registerWithSupabase registers a new user with Supabase
func (h *SecureAuthHandlers) registerWithSupabase(req SecureRegisterRequest) (*SupabaseUser, error) {
	// Create registration request to Supabase
	url := fmt.Sprintf("%s/auth/v1/signup", h.config.Supabase.URL)

	payload := map[string]interface{}{
		"email":    req.Email,
		"password": req.Password,
		"data": map[string]string{
			"first_name":   req.FirstName,
			"last_name":    req.LastName,
			"phone_number": req.PhoneNumber,
		},
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequest("POST", url, strings.NewReader(string(payloadBytes)))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("apikey", h.config.Supabase.AnonKey)

	// Make request with timeout
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("registration request failed: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != 200 && resp.StatusCode != 201 {
		return nil, fmt.Errorf("registration failed - status: %d", resp.StatusCode)
	}

	// Parse response
	var authResp SupabaseAuthResponse
	if err := json.NewDecoder(resp.Body).Decode(&authResp); err != nil {
		return nil, fmt.Errorf("failed to parse registration response: %w", err)
	}

	// Validate response
	if authResp.User.ID == "" || authResp.User.Email == "" {
		return nil, fmt.Errorf("invalid user data in response")
	}

	return &SupabaseUser{
		ID:    authResp.User.ID,
		Email: authResp.User.Email,
		Role:  "authenticated",
	}, nil
}

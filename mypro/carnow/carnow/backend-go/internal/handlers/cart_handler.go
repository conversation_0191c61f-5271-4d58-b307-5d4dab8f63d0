package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/services"
)

// CartHandler handles cart-related HTTP requests according to Forever Plan architecture
type CartHandler struct {
	cartService *services.CartService
	logger      *zap.Logger
}

// NewCartHandler creates a new cart handler instance
func NewCartHandler(cartService *services.CartService, logger *zap.Logger) *CartHandler {
	return &CartHandler{
		cartService: cartService,
		logger:      logger,
	}
}

// AddCartItemRequest represents the request body for adding items to cart
type AddCartItemRequest struct {
	ProductID string `json:"product_id" binding:"required"`
	Quantity  int    `json:"quantity" binding:"required,min=1"`
}

// UpdateCartItemRequest represents the request body for updating cart items
type UpdateCartItemRequest struct {
	Quantity int `json:"quantity" binding:"required,min=0"`
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   *string     `json:"error,omitempty"`
	Message *string     `json:"message,omitempty"`
}

// GetCart handles GET /cart - Get user's cart with all items
func (h *CartHandler) GetCart(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get user ID from JWT token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid user ID"),
		})
		return
	}

	h.logger.Info("Getting cart for user", zap.String("user_id", userIDStr))

	// Get cart using service
	cart, err := h.cartService.GetOrCreateCart(ctx, userIDStr)
	if err != nil {
		h.logger.Error("Failed to get cart", zap.Error(err), zap.String("user_id", userIDStr))
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error:   stringPtr("Failed to get cart"),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data:    cart,
		Message: stringPtr("Cart retrieved successfully"),
	})
}

// AddCartItem handles POST /cart/items - Add item to cart
func (h *CartHandler) AddCartItem(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get user ID from JWT token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid user ID"),
		})
		return
	}

	// Parse request body
	var req AddCartItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid request body: " + err.Error()),
		})
		return
	}

	h.logger.Info("Adding item to cart",
		zap.String("user_id", userIDStr),
		zap.String("product_id", req.ProductID),
		zap.Int("quantity", req.Quantity))

	// Add item using service
	err := h.cartService.AddItem(ctx, userIDStr, req.ProductID, req.Quantity)
	if err != nil {
		h.logger.Error("Failed to add item to cart", zap.Error(err))
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error:   stringPtr("Failed to add item to cart: " + err.Error()),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: gin.H{
			"user_id":    userIDStr,
			"product_id": req.ProductID,
			"quantity":   req.Quantity,
		},
		Message: stringPtr("Item added to cart successfully"),
	})
}

// UpdateCartItem handles PUT /cart/items/:id - Update cart item quantity
func (h *CartHandler) UpdateCartItem(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get user ID from JWT token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid user ID"),
		})
		return
	}

	// Get item ID from URL parameter
	itemID := c.Param("id")
	if itemID == "" {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Item ID is required"),
		})
		return
	}

	// Parse request body
	var req UpdateCartItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid request body: " + err.Error()),
		})
		return
	}

	h.logger.Info("Updating cart item",
		zap.String("user_id", userIDStr),
		zap.String("item_id", itemID),
		zap.Int("quantity", req.Quantity))

	// Update item using service
	err := h.cartService.UpdateItemQuantity(ctx, userIDStr, itemID, req.Quantity)
	if err != nil {
		h.logger.Error("Failed to update cart item", zap.Error(err))
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error:   stringPtr("Failed to update cart item: " + err.Error()),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: gin.H{
			"item_id":  itemID,
			"quantity": req.Quantity,
		},
		Message: stringPtr("Cart item updated successfully"),
	})
}

// RemoveCartItem handles DELETE /cart/items/:id - Remove item from cart
func (h *CartHandler) RemoveCartItem(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get user ID from JWT token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid user ID"),
		})
		return
	}

	// Get item ID from URL parameter
	itemID := c.Param("id")
	if itemID == "" {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Item ID is required"),
		})
		return
	}

	h.logger.Info("Removing cart item",
		zap.String("user_id", userIDStr),
		zap.String("item_id", itemID))

	// Remove item using service
	err := h.cartService.RemoveItem(ctx, userIDStr, itemID)
	if err != nil {
		h.logger.Error("Failed to remove cart item", zap.Error(err))
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error:   stringPtr("Failed to remove cart item: " + err.Error()),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: gin.H{
			"item_id": itemID,
		},
		Message: stringPtr("Cart item removed successfully"),
	})
}

// ClearCart handles DELETE /cart - Clear all items from cart
func (h *CartHandler) ClearCart(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get user ID from JWT token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid user ID"),
		})
		return
	}

	h.logger.Info("Clearing cart for user", zap.String("user_id", userIDStr))

	// Clear cart using service
	err := h.cartService.ClearCart(ctx, userIDStr)
	if err != nil {
		h.logger.Error("Failed to clear cart", zap.Error(err))
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error:   stringPtr("Failed to clear cart: " + err.Error()),
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data: gin.H{
			"user_id": userIDStr,
		},
		Message: stringPtr("Cart cleared successfully"),
	})
}

// GetCartItems handles GET /cart/items - Get cart items (legacy endpoint for compatibility)
func (h *CartHandler) GetCartItems(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get user ID from JWT token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, APIResponse{
			Success: false,
			Error:   stringPtr("User not authenticated"),
		})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, APIResponse{
			Success: false,
			Error:   stringPtr("Invalid user ID"),
		})
		return
	}

	h.logger.Info("Getting cart items for user", zap.String("user_id", userIDStr))

	// Get cart using service
	cart, err := h.cartService.GetOrCreateCart(ctx, userIDStr)
	if err != nil {
		h.logger.Error("Failed to get cart items", zap.Error(err))
		c.JSON(http.StatusInternalServerError, APIResponse{
			Success: false,
			Error:   stringPtr("Failed to get cart items"),
		})
		return
	}

	// Return just the items for legacy compatibility
	c.JSON(http.StatusOK, APIResponse{
		Success: true,
		Data:    cart.Items,
		Message: stringPtr("Cart items retrieved successfully"),
	})
}

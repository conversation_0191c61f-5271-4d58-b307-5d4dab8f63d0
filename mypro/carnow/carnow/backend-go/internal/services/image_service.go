package services

import (
	"bytes"
	"context"
	"fmt"
	"image"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/disintegration/imaging"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// ImageService handles image processing following Forever Plan architecture
type ImageService struct {
	db         *pgxpool.Pool
	logger     *zap.Logger
	config     *ImageConfig
	uploadPath string
}

// ImageConfig contains image processing configuration
type ImageConfig struct {
	MaxFileSize    int64  `json:"max_file_size"`   // Maximum file size in bytes
	Quality        int    `json:"quality"`         // JPEG quality (1-100)
	WebPQuality    int    `json:"webp_quality"`    // WebP quality (1-100)
	EnableWebP     bool   `json:"enable_webp"`     // Enable WebP conversion
	EnableAVIF     bool   `json:"enable_avif"`     // Enable AVIF conversion
	ThumbnailSizes []int  `json:"thumbnail_sizes"` // Thumbnail sizes
	WatermarkPath  string `json:"watermark_path"`  // Watermark image path
	UploadPath     string `json:"upload_path"`     // Upload directory path
}

// ProcessedImage represents a processed image with multiple formats and sizes
type ProcessedImage struct {
	ID          string                 `json:"id"`
	OriginalURL string                 `json:"original_url"`
	Formats     map[string]string      `json:"formats"` // format -> URL
	Sizes       map[string]string      `json:"sizes"`   // size -> URL
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
}

// ImageSize represents different image sizes
type ImageSize struct {
	Name   string `json:"name"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

// Predefined image sizes for CarNow
var (
	ImageSizes = []ImageSize{
		{Name: "thumbnail", Width: 150, Height: 150},
		{Name: "small", Width: 300, Height: 300},
		{Name: "medium", Width: 600, Height: 600},
		{Name: "large", Width: 1200, Height: 1200},
	}
)

// NewImageService creates a new image service instance
func NewImageService(db *pgxpool.Pool, config *ImageConfig, logger *zap.Logger) *ImageService {
	// Set default config if not provided
	if config == nil {
		config = &ImageConfig{
			MaxFileSize:    10 * 1024 * 1024, // 10MB
			Quality:        85,
			WebPQuality:    80,
			EnableWebP:     true,
			EnableAVIF:     false,
			ThumbnailSizes: []int{150, 300, 600},
			UploadPath:     "./uploads/images",
		}
	}

	// Create upload directory if it doesn't exist
	if err := os.MkdirAll(config.UploadPath, 0755); err != nil {
		logger.Error("Failed to create upload directory", zap.Error(err))
	}

	return &ImageService{
		db:         db,
		logger:     logger,
		config:     config,
		uploadPath: config.UploadPath,
	}
}

// ProcessProductImage processes a product image with optimization and multiple formats
func (s *ImageService) ProcessProductImage(ctx context.Context, imageData []byte, filename string) (*ProcessedImage, error) {
	s.logger.Info("Processing product image", zap.String("filename", filename))

	// Validate image
	if err := s.validateImage(imageData); err != nil {
		return nil, fmt.Errorf("image validation failed: %w", err)
	}

	// Generate unique ID for this image
	imageID := s.generateImageID()

	// Create directory for this image
	imageDir := filepath.Join(s.uploadPath, imageID)
	if err := os.MkdirAll(imageDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create image directory: %w", err)
	}

	processedImage := &ProcessedImage{
		ID:        imageID,
		Formats:   make(map[string]string),
		Sizes:     make(map[string]string),
		Metadata:  make(map[string]interface{}),
		CreatedAt: time.Now(),
	}

	// Get image metadata
	metadata, err := s.getImageMetadata(imageData)
	if err != nil {
		s.logger.Warn("Failed to get image metadata", zap.Error(err))
	} else {
		processedImage.Metadata = metadata
	}

	// Process original image
	originalPath := filepath.Join(imageDir, "original"+s.getFileExtension(filename))
	if err := s.saveImage(imageData, originalPath); err != nil {
		return nil, fmt.Errorf("failed to save original image: %w", err)
	}
	processedImage.OriginalURL = s.getImageURL(imageID, "original"+s.getFileExtension(filename))

	// Create optimized JPEG version
	optimizedJPEG, err := s.optimizeJPEG(imageData)
	if err != nil {
		s.logger.Warn("Failed to optimize JPEG", zap.Error(err))
	} else {
		jpegPath := filepath.Join(imageDir, "optimized.jpg")
		if err := s.saveImage(optimizedJPEG, jpegPath); err == nil {
			processedImage.Formats["jpeg"] = s.getImageURL(imageID, "optimized.jpg")
		}
	}

	// Create WebP version if enabled
	if s.config.EnableWebP {
		webpData, err := s.convertToWebP(imageData)
		if err != nil {
			s.logger.Warn("Failed to convert to WebP", zap.Error(err))
		} else {
			webpPath := filepath.Join(imageDir, "optimized.webp")
			if err := s.saveImage(webpData, webpPath); err == nil {
				processedImage.Formats["webp"] = s.getImageURL(imageID, "optimized.webp")
			}
		}
	}

	// Create different sizes
	for _, size := range ImageSizes {
		resizedData, err := s.resizeImage(imageData, size.Width, size.Height)
		if err != nil {
			s.logger.Warn("Failed to resize image",
				zap.Error(err),
				zap.String("size", size.Name))
			continue
		}

		sizePath := filepath.Join(imageDir, fmt.Sprintf("%s.jpg", size.Name))
		if err := s.saveImage(resizedData, sizePath); err == nil {
			processedImage.Sizes[size.Name] = s.getImageURL(imageID, fmt.Sprintf("%s.jpg", size.Name))
		}
	}

	// Save to database
	if err := s.saveImageRecord(ctx, processedImage); err != nil {
		s.logger.Error("Failed to save image record to database", zap.Error(err))
		// Don't fail the operation, just log the error
	}

	s.logger.Info("Image processed successfully",
		zap.String("image_id", imageID),
		zap.Int("formats", len(processedImage.Formats)),
		zap.Int("sizes", len(processedImage.Sizes)))

	return processedImage, nil
}

// validateImage validates the uploaded image
func (s *ImageService) validateImage(imageData []byte) error {
	// Check file size
	if int64(len(imageData)) > s.config.MaxFileSize {
		return fmt.Errorf("image size exceeds maximum allowed size of %d bytes", s.config.MaxFileSize)
	}

	// Check if it's a valid image
	_, format, err := image.DecodeConfig(bytes.NewReader(imageData))
	if err != nil {
		return fmt.Errorf("invalid image format: %w", err)
	}

	// Check supported formats
	supportedFormats := []string{"jpeg", "jpg", "png", "gif", "webp"}
	format = strings.ToLower(format)
	for _, supported := range supportedFormats {
		if format == supported {
			return nil
		}
	}

	return fmt.Errorf("unsupported image format: %s", format)
}

// optimizeJPEG optimizes JPEG image using imaging
func (s *ImageService) optimizeJPEG(imageData []byte) ([]byte, error) {
	// Decode image
	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Encode to JPEG with quality
	var buf bytes.Buffer
	if err := imaging.Encode(&buf, img, imaging.JPEG, imaging.JPEGQuality(s.config.Quality)); err != nil {
		return nil, fmt.Errorf("failed to encode optimized JPEG: %w", err)
	}

	return buf.Bytes(), nil
}

// convertToWebP converts image to WebP format (placeholder - WebP encoding requires additional library)
func (s *ImageService) convertToWebP(imageData []byte) ([]byte, error) {
	// For now, return optimized JPEG as WebP encoding requires additional setup
	// TODO: Implement WebP encoding when webp library is available
	s.logger.Warn("WebP conversion not implemented, returning optimized JPEG")
	return s.optimizeJPEG(imageData)
}

// resizeImage resizes image to specified dimensions
func (s *ImageService) resizeImage(imageData []byte, width, height int) ([]byte, error) {
	// Decode image
	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Resize image maintaining aspect ratio
	resized := imaging.Fit(img, width, height, imaging.Lanczos)

	// Encode to JPEG
	var buf bytes.Buffer
	if err := imaging.Encode(&buf, resized, imaging.JPEG, imaging.JPEGQuality(s.config.Quality)); err != nil {
		return nil, fmt.Errorf("failed to encode resized image: %w", err)
	}

	return buf.Bytes(), nil
}

// getImageMetadata extracts image metadata
func (s *ImageService) getImageMetadata(imageData []byte) (map[string]interface{}, error) {
	config, format, err := image.DecodeConfig(bytes.NewReader(imageData))
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"width":  config.Width,
		"height": config.Height,
		"format": format,
		"size":   len(imageData),
	}, nil
}

// saveImage saves image data to file
func (s *ImageService) saveImage(imageData []byte, filePath string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = io.Copy(file, bytes.NewReader(imageData))
	return err
}

// generateImageID generates a unique ID for the image
func (s *ImageService) generateImageID() string {
	return fmt.Sprintf("img_%d", time.Now().UnixNano())
}

// getFileExtension returns file extension from filename
func (s *ImageService) getFileExtension(filename string) string {
	return filepath.Ext(filename)
}

// getImageURL constructs the URL for accessing the image
func (s *ImageService) getImageURL(imageID, filename string) string {
	return fmt.Sprintf("/api/v1/images/%s/%s", imageID, filename)
}

// saveImageRecord saves image record to database
func (s *ImageService) saveImageRecord(ctx context.Context, processedImage *ProcessedImage) error {
	if s.db == nil {
		return nil // Skip if no database connection
	}

	query := `
		INSERT INTO public.processed_images (
			id, original_url, formats, sizes, metadata, created_at
		) VALUES ($1, $2, $3, $4, $5, $6)
	`

	_, err := s.db.Exec(ctx, query,
		processedImage.ID,
		processedImage.OriginalURL,
		processedImage.Formats,
		processedImage.Sizes,
		processedImage.Metadata,
		processedImage.CreatedAt,
	)

	return err
}

package services

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

// WebSocketService handles real-time communication following Forever Plan architecture
type WebSocketService struct {
	clients    map[string]*Client
	clientsMux sync.RWMutex
	upgrader   websocket.Upgrader
	logger     *zap.Logger
}

// Client represents a WebSocket client connection
type Client struct {
	ID       string
	UserID   string
	Conn     *websocket.Conn
	Send     chan []byte
	Service  *WebSocketService
	LastSeen time.Time
}

// Message represents a WebSocket message
type Message struct {
	Type      string                 `json:"type"`
	Event     string                 `json:"event"`
	Data      map[string]interface{} `json:"data"`
	UserID    string                 `json:"user_id,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// NewWebSocketService creates a new WebSocket service instance
func NewWebSocketService(logger *zap.Logger) *WebSocketService {
	return &WebSocketService{
		clients: make(map[string]*Client),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// In production, implement proper origin checking
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		logger: logger,
	}
}

// HandleWebSocket handles WebSocket connection upgrade
func (s *WebSocketService) HandleWebSocket(c *gin.Context) {
	// Get user ID from JWT token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Upgrade HTTP connection to WebSocket
	conn, err := s.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		s.logger.Error("Failed to upgrade WebSocket connection", zap.Error(err))
		return
	}

	// Create client
	client := &Client{
		ID:       fmt.Sprintf("%s_%d", userIDStr, time.Now().Unix()),
		UserID:   userIDStr,
		Conn:     conn,
		Send:     make(chan []byte, 256),
		Service:  s,
		LastSeen: time.Now(),
	}

	// Register client
	s.registerClient(client)

	// Start client goroutines
	go client.writePump()
	go client.readPump()

	s.logger.Info("WebSocket client connected",
		zap.String("client_id", client.ID),
		zap.String("user_id", userIDStr))
}

// registerClient registers a new client
func (s *WebSocketService) registerClient(client *Client) {
	s.clientsMux.Lock()
	defer s.clientsMux.Unlock()

	s.clients[client.ID] = client

	// Send welcome message
	welcomeMsg := &Message{
		Type:      "system",
		Event:     "connected",
		Data:      map[string]interface{}{"message": "Connected to CarNow real-time updates"},
		Timestamp: time.Now(),
	}

	s.sendToClient(client, welcomeMsg)
}

// unregisterClient unregisters a client
func (s *WebSocketService) unregisterClient(client *Client) {
	s.clientsMux.Lock()
	defer s.clientsMux.Unlock()

	if _, exists := s.clients[client.ID]; exists {
		delete(s.clients, client.ID)
		close(client.Send)

		s.logger.Info("WebSocket client disconnected",
			zap.String("client_id", client.ID),
			zap.String("user_id", client.UserID))
	}
}

// BroadcastToUser sends a message to all connections of a specific user
func (s *WebSocketService) BroadcastToUser(userID string, message *Message) {
	s.clientsMux.RLock()
	defer s.clientsMux.RUnlock()

	for _, client := range s.clients {
		if client.UserID == userID {
			s.sendToClient(client, message)
		}
	}
}

// BroadcastToAll sends a message to all connected clients
func (s *WebSocketService) BroadcastToAll(message *Message) {
	s.clientsMux.RLock()
	defer s.clientsMux.RUnlock()

	for _, client := range s.clients {
		s.sendToClient(client, message)
	}
}

// sendToClient sends a message to a specific client
func (s *WebSocketService) sendToClient(client *Client, message *Message) {
	data, err := json.Marshal(message)
	if err != nil {
		s.logger.Error("Failed to marshal WebSocket message", zap.Error(err))
		return
	}

	select {
	case client.Send <- data:
	default:
		s.unregisterClient(client)
	}
}

// NotifyCartUpdate notifies user about cart updates
func (s *WebSocketService) NotifyCartUpdate(userID string, cartData map[string]interface{}) {
	message := &Message{
		Type:      "cart",
		Event:     "updated",
		Data:      cartData,
		UserID:    userID,
		Timestamp: time.Now(),
	}

	s.BroadcastToUser(userID, message)
	s.logger.Info("Cart update notification sent", zap.String("user_id", userID))
}

// NotifyOrderUpdate notifies user about order status updates
func (s *WebSocketService) NotifyOrderUpdate(userID string, orderData map[string]interface{}) {
	message := &Message{
		Type:      "order",
		Event:     "status_updated",
		Data:      orderData,
		UserID:    userID,
		Timestamp: time.Now(),
	}

	s.BroadcastToUser(userID, message)
	s.logger.Info("Order update notification sent", zap.String("user_id", userID))
}

// NotifyInventoryUpdate notifies about inventory changes
func (s *WebSocketService) NotifyInventoryUpdate(productID string, stockQuantity int) {
	message := &Message{
		Type:  "inventory",
		Event: "stock_updated",
		Data: map[string]interface{}{
			"product_id":     productID,
			"stock_quantity": stockQuantity,
		},
		Timestamp: time.Now(),
	}

	s.BroadcastToAll(message)
	s.logger.Info("Inventory update notification sent", zap.String("product_id", productID))
}

// GetConnectedClients returns the number of connected clients
func (s *WebSocketService) GetConnectedClients() int {
	s.clientsMux.RLock()
	defer s.clientsMux.RUnlock()
	return len(s.clients)
}

// readPump handles reading messages from the WebSocket connection
func (c *Client) readPump() {
	defer func() {
		c.Service.unregisterClient(c)
		c.Conn.Close()
	}()

	// Set read deadline and pong handler
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		c.LastSeen = time.Now()
		return nil
	})

	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.Service.logger.Error("WebSocket read error", zap.Error(err))
			}
			break
		}

		// Handle incoming message (ping/pong, etc.)
		var msg Message
		if err := json.Unmarshal(message, &msg); err == nil {
			if msg.Type == "ping" {
				pongMsg := &Message{
					Type:      "pong",
					Event:     "heartbeat",
					Timestamp: time.Now(),
				}
				c.Service.sendToClient(c, pongMsg)
			}
		}

		c.LastSeen = time.Now()
	}
}

// writePump handles writing messages to the WebSocket connection
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				c.Service.logger.Error("WebSocket write error", zap.Error(err))
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

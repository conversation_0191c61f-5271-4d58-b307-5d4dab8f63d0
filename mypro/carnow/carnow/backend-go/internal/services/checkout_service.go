package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// CheckoutService handles the complete checkout process for CarNow platform
// Supports all product types: cars, clothes, electronics, home, sports, etc.
type CheckoutService struct {
	db             *pgxpool.Pool
	logger         *zap.Logger
	cartService    *CartService
	paymentService *PaymentService
	orderService   *OrderService
}

// CheckoutRequest represents a checkout request
type CheckoutRequest struct {
	CartID          string            `json:"cart_id"`
	UserID          string            `json:"user_id"`
	ShippingAddress ShippingAddress   `json:"shipping_address"`
	PaymentMethodID string            `json:"payment_method_id"`
	Currency        string            `json:"currency"`
	Metadata        map[string]string `json:"metadata"`
}

// ShippingAddress represents shipping information
type ShippingAddress struct {
	Name        string `json:"name"`
	Phone       string `json:"phone"`
	AddressLine string `json:"address_line"`
	City        string `json:"city"`
	State       string `json:"state"`
	PostalCode  string `json:"postal_code"`
	Country     string `json:"country"`
}

// CheckoutResult represents the result of a checkout process
type CheckoutResult struct {
	Success         bool   `json:"success"`
	OrderID         string `json:"order_id,omitempty"`
	PaymentIntentID string `json:"payment_intent_id,omitempty"`
	ClientSecret    string `json:"client_secret,omitempty"`
	Status          string `json:"status"`
	Message         string `json:"message"`
	Error           string `json:"error,omitempty"`
}

// NewCheckoutService creates a new checkout service
func NewCheckoutService(
	db *pgxpool.Pool,
	logger *zap.Logger,
	cartService *CartService,
	paymentService *PaymentService,
	orderService *OrderService,
) *CheckoutService {
	return &CheckoutService{
		db:             db,
		logger:         logger,
		cartService:    cartService,
		paymentService: paymentService,
		orderService:   orderService,
	}
}

// ProcessCheckout handles the complete checkout flow
func (s *CheckoutService) ProcessCheckout(ctx context.Context, req *CheckoutRequest) (*CheckoutResult, error) {
	s.logger.Info("Processing checkout for CarNow platform",
		zap.String("cart_id", req.CartID),
		zap.String("user_id", req.UserID),
		zap.String("currency", req.Currency))

	// Step 1: Validate cart and get items
	cart, err := s.cartService.GetOrCreateCart(ctx, req.UserID)
	if err != nil {
		s.logger.Error("Failed to get cart", zap.Error(err))
		return &CheckoutResult{
			Success: false,
			Status:  "failed",
			Error:   "Cart not found",
		}, err
	}

	if len(cart.Items) == 0 {
		return &CheckoutResult{
			Success: false,
			Status:  "failed",
			Error:   "Cart is empty",
		}, fmt.Errorf("cart is empty")
	}

	// Step 2: Calculate total amount
	totalAmount := s.calculateTotalAmount(cart)
	if totalAmount <= 0 {
		return &CheckoutResult{
			Success: false,
			Status:  "failed",
			Error:   "Invalid cart total",
		}, fmt.Errorf("invalid cart total: %d", totalAmount)
	}

	// Step 3: Create payment intent
	paymentMetadata := map[string]string{
		"cart_id":     req.CartID,
		"user_id":     req.UserID,
		"order_type":  "multi_product", // CarNow supports all product types
		"platform":    "carnow",
		"checkout_at": time.Now().Format(time.RFC3339),
	}

	// Add custom metadata
	for k, v := range req.Metadata {
		paymentMetadata[k] = v
	}

	paymentIntent, err := s.paymentService.CreatePaymentIntent(
		ctx,
		totalAmount,
		req.Currency,
		paymentMetadata,
	)
	if err != nil {
		s.logger.Error("Failed to create payment intent", zap.Error(err))
		return &CheckoutResult{
			Success: false,
			Status:  "failed",
			Error:   "Failed to create payment intent",
		}, err
	}

	// Step 4: Create order (pending payment)
	orderID := uuid.New().String()

	// Create order request for the existing order service
	orderReq := &OrderRequest{
		UserID: req.UserID,
		ShippingAddress: map[string]interface{}{
			"name":         req.ShippingAddress.Name,
			"phone":        req.ShippingAddress.Phone,
			"address_line": req.ShippingAddress.AddressLine,
			"city":         req.ShippingAddress.City,
			"state":        req.ShippingAddress.State,
			"postal_code":  req.ShippingAddress.PostalCode,
			"country":      req.ShippingAddress.Country,
		},
		PaymentMethod: req.PaymentMethodID,
		Notes:         "Order created from checkout process",
	}

	_, err = s.orderService.CreateOrderFromCart(ctx, orderReq)
	if err != nil {
		s.logger.Error("Failed to create order", zap.Error(err))
		return &CheckoutResult{
			Success: false,
			Status:  "failed",
			Error:   "Failed to create order",
		}, err
	}

	s.logger.Info("Checkout processed successfully",
		zap.String("order_id", orderID),
		zap.String("payment_intent_id", paymentIntent.ID),
		zap.Int64("amount", totalAmount))

	return &CheckoutResult{
		Success:         true,
		OrderID:         orderID,
		PaymentIntentID: paymentIntent.ID,
		ClientSecret:    paymentIntent.ClientSecret,
		Status:          "requires_payment",
		Message:         "Checkout processed successfully. Complete payment to finalize order.",
	}, nil
}

// calculateTotalAmount calculates the total amount for the cart in cents
func (s *CheckoutService) calculateTotalAmount(cart *Cart) int64 {
	var total float64
	for _, item := range cart.Items {
		total += item.Price * float64(item.Quantity)
	}

	// Add tax (example: 10%)
	tax := total * 0.10
	total += tax

	// Convert to cents for payment processing
	return int64(total * 100)
}

// ConfirmCheckout confirms the payment and finalizes the order
func (s *CheckoutService) ConfirmCheckout(ctx context.Context, paymentIntentID string) (*CheckoutResult, error) {
	s.logger.Info("Confirming checkout", zap.String("payment_intent_id", paymentIntentID))

	// Step 1: Confirm payment
	paymentResult, err := s.paymentService.ConfirmPayment(ctx, paymentIntentID, "")
	if err != nil || !paymentResult.Success {
		s.logger.Error("Payment confirmation failed", zap.Error(err))
		return &CheckoutResult{
			Success: false,
			Status:  "payment_failed",
			Error:   "Payment confirmation failed",
		}, err
	}

	// Step 2: Update order status
	// TODO: Implement order status update when OrderService.UpdateOrderStatus is available
	// err = s.orderService.UpdateOrderStatus(ctx, paymentIntentID, "confirmed")
	// if err != nil {
	// 	s.logger.Error("Failed to update order status", zap.Error(err))
	// 	return &CheckoutResult{
	// 		Success: false,
	// 		Status:  "failed",
	// 		Error:   "Failed to update order status",
	// 	}, err
	// }

	// Step 3: Clear cart
	// TODO: Implement cart clearing after successful payment

	s.logger.Info("Checkout confirmed successfully", zap.String("payment_intent_id", paymentIntentID))

	return &CheckoutResult{
		Success: true,
		Status:  "completed",
		Message: "Order confirmed successfully",
	}, nil
}

// Order represents an order in the system
type Order struct {
	ID              string          `json:"id"`
	UserID          string          `json:"user_id"`
	PaymentIntentID string          `json:"payment_intent_id"`
	Status          string          `json:"status"`
	TotalAmount     float64         `json:"total_amount"`
	Currency        string          `json:"currency"`
	ShippingAddress ShippingAddress `json:"shipping_address"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
}

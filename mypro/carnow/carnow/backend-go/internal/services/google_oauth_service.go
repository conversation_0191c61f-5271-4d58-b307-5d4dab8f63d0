package services

import (
	"context"
	"fmt"
	"net/http"

	"google.golang.org/api/idtoken"
	"google.golang.org/api/option"
)

// GoogleOAuthService handles Google OAuth verification
type GoogleOAuthService struct {
	clientID  string
	clientSecret string
}

// GoogleUser represents a Google user's information
type GoogleUser struct {
	ID            string `json:"sub"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	Name          string `json:"name"`
	Picture       string `json:"picture"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
}

// NewGoogleOAuthService creates a new Google OAuth service
func NewGoogleOAuthService(clientID, clientSecret string) (*GoogleOAuthService, error) {
	return &GoogleOAuthService{
		clientID:     clientID,
		clientSecret: clientSecret,
	}, nil
}

// VerifyIDToken verifies a Google ID token and returns the user information
func (s *GoogleOAuthService) VerifyIDToken(ctx context.Context, idToken string) (*GoogleUser, error) {
	// Use Google's ID token verification
	validator, err := idtoken.NewValidator(ctx, option.WithHTTPClient(http.DefaultClient))
	if err != nil {
		return nil, fmt.Errorf("failed to create token validator: %w", err)
	}

	// Verify the token
	payload, err := validator.Validate(ctx, idToken, s.clientID)
	if err != nil {
		return nil, fmt.Errorf("failed to verify ID token: %w", err)
	}

	// Extract user information
	user := &GoogleUser{
		ID:            payload.Claims["sub"].(string),
		Email:         payload.Claims["email"].(string),
		EmailVerified: payload.Claims["email_verified"].(bool),
		Name:          payload.Claims["name"].(string),
		Picture:       payload.Claims["picture"].(string),
		GivenName:     payload.Claims["given_name"].(string),
		FamilyName:    payload.Claims["family_name"].(string),
	}

	return user, nil
}

// GetClientID returns the Google OAuth client ID
func (s *GoogleOAuthService) GetClientID() string {
	return s.clientID
}

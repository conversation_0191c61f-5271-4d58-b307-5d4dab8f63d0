package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"carnow-backend/internal/config"

	"go.uber.org/zap"
)

// SMSService handles SMS operations
type SMSService struct {
	config *config.Config
	logger *zap.Logger
	client *http.Client
}

// SMSProvider represents different SMS providers
type SMSProvider string

const (
	SMSProviderTwilio   SMSProvider = "twilio"
	SMSProviderAWSSNS   SMSProvider = "aws_sns"
	SMSProviderLocal    SMSProvider = "local" // For development
)

// SMSRequest represents an SMS request
type SMSRequest struct {
	To      string `json:"to"`
	Message string `json:"message"`
	From    string `json:"from,omitempty"`
}

// SMSResponse represents an SMS response
type SMSResponse struct {
	Success   bool   `json:"success"`
	MessageID string `json:"message_id,omitempty"`
	Error     string `json:"error,omitempty"`
}

// TwilioSMSRequest represents Twilio API request
type TwilioSMSRequest struct {
	To   string `json:"To"`
	From string `json:"From"`
	Body string `json:"Body"`
}

// TwilioSMSResponse represents Twilio API response
type TwilioSMSResponse struct {
	SID         string `json:"sid"`
	Status      string `json:"status"`
	ErrorCode   *int   `json:"error_code"`
	ErrorMessage *string `json:"error_message"`
}

// NewSMSService creates a new SMS service
func NewSMSService(config *config.Config, logger *zap.Logger) *SMSService {
	return &SMSService{
		config: config,
		logger: logger,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// SendSMS sends an SMS message
func (s *SMSService) SendSMS(phoneNumber, message string) error {
	// Validate phone number format
	if !s.isValidPhoneNumber(phoneNumber) {
		return fmt.Errorf("invalid phone number format: %s", phoneNumber)
	}

	// Get SMS provider from config
	provider := s.getSMSProvider()

	switch provider {
	case SMSProviderTwilio:
		return s.sendTwilioSMS(phoneNumber, message)
	case SMSProviderAWSSNS:
		return s.sendAWSSNS(phoneNumber, message)
	case SMSProviderLocal:
		return s.sendLocalSMS(phoneNumber, message)
	default:
		return fmt.Errorf("unsupported SMS provider: %s", provider)
	}
}

// sendTwilioSMS sends SMS via Twilio
func (s *SMSService) sendTwilioSMS(phoneNumber, message string) error {
	// Get Twilio credentials from config
	accountSID := s.config.SMS.TwilioAccountSID
	authToken := s.config.SMS.TwilioAuthToken
	fromNumber := s.config.SMS.TwilioFromNumber

	if accountSID == "" || authToken == "" || fromNumber == "" {
		return fmt.Errorf("Twilio credentials not configured")
	}

	// Prepare request
	url := fmt.Sprintf("https://api.twilio.com/2010-04-01/Accounts/%s/Messages.json", accountSID)
	
	data := fmt.Sprintf("To=%s&From=%s&Body=%s", phoneNumber, fromNumber, message)
	
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(data))
	if err != nil {
		return fmt.Errorf("failed to create Twilio request: %w", err)
	}

	req.SetBasicAuth(accountSID, authToken)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// Send request
	resp, err := s.client.Do(req)
	if err != nil {
		s.logger.Error("Failed to send Twilio SMS",
			zap.String("phone", phoneNumber),
			zap.Error(err),
		)
		return fmt.Errorf("failed to send SMS via Twilio: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var twilioResp TwilioSMSResponse
	if err := json.NewDecoder(resp.Body).Decode(&twilioResp); err != nil {
		return fmt.Errorf("failed to parse Twilio response: %w", err)
	}

	if resp.StatusCode != 201 {
		errorMsg := "Unknown error"
		if twilioResp.ErrorMessage != nil {
			errorMsg = *twilioResp.ErrorMessage
		}
		return fmt.Errorf("Twilio SMS failed: %s", errorMsg)
	}

	s.logger.Info("SMS sent successfully via Twilio",
		zap.String("phone", phoneNumber),
		zap.String("message_id", twilioResp.SID),
	)

	return nil
}

// sendAWSSNS sends SMS via AWS SNS
func (s *SMSService) sendAWSSNS(phoneNumber, message string) error {
	// TODO: Implement AWS SNS SMS sending
	// This would require AWS SDK integration
	s.logger.Info("AWS SNS SMS sending not implemented yet",
		zap.String("phone", phoneNumber),
	)
	return fmt.Errorf("AWS SNS SMS not implemented")
}

// sendLocalSMS simulates SMS sending for development
func (s *SMSService) sendLocalSMS(phoneNumber, message string) error {
	s.logger.Info("Local SMS simulation",
		zap.String("phone", phoneNumber),
		zap.String("message", message),
	)

	// In development, just log the SMS
	fmt.Printf("\n=== SMS SIMULATION ===\n")
	fmt.Printf("To: %s\n", phoneNumber)
	fmt.Printf("Message: %s\n", message)
	fmt.Printf("======================\n\n")

	return nil
}

// isValidPhoneNumber validates phone number format
func (s *SMSService) isValidPhoneNumber(phoneNumber string) bool {
	// Basic validation - should start with + and contain only digits
	if len(phoneNumber) < 10 || len(phoneNumber) > 15 {
		return false
	}

	if phoneNumber[0] != '+' {
		return false
	}

	// Check if rest are digits
	for i := 1; i < len(phoneNumber); i++ {
		if phoneNumber[i] < '0' || phoneNumber[i] > '9' {
			return false
		}
	}

	return true
}

// getSMSProvider returns the configured SMS provider
func (s *SMSService) getSMSProvider() SMSProvider {
	if s.config.SMS.Provider != "" {
		return SMSProvider(s.config.SMS.Provider)
	}
	
	// Default to local for development
	return SMSProviderLocal
}

// SendBulkSMS sends SMS to multiple recipients
func (s *SMSService) SendBulkSMS(phoneNumbers []string, message string) error {
	var errors []string

	for _, phoneNumber := range phoneNumbers {
		if err := s.SendSMS(phoneNumber, message); err != nil {
			errors = append(errors, fmt.Sprintf("%s: %v", phoneNumber, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("bulk SMS errors: %v", errors)
	}

	s.logger.Info("Bulk SMS sent successfully",
		zap.Int("count", len(phoneNumbers)),
	)

	return nil
}

// GetSMSStatus checks the status of an SMS (if supported by provider)
func (s *SMSService) GetSMSStatus(messageID string) (string, error) {
	// TODO: Implement status checking for different providers
	return "delivered", nil
}

// ValidatePhoneNumber validates and formats a phone number
func (s *SMSService) ValidatePhoneNumber(phoneNumber string) (string, error) {
	// Remove spaces and dashes
	cleaned := ""
	for _, char := range phoneNumber {
		if char >= '0' && char <= '9' || char == '+' {
			cleaned += string(char)
		}
	}

	// Add + if missing and looks like international number
	if len(cleaned) > 10 && cleaned[0] != '+' {
		cleaned = "+" + cleaned
	}

	// Validate the cleaned number
	if !s.isValidPhoneNumber(cleaned) {
		return "", fmt.Errorf("invalid phone number format")
	}

	return cleaned, nil
}

// GetSMSCost estimates the cost of sending an SMS (if supported)
func (s *SMSService) GetSMSCost(phoneNumber string) (float64, error) {
	// TODO: Implement cost calculation based on destination
	// For now, return a default cost
	return 0.05, nil // $0.05 per SMS
}

package services

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// OrderService handles order creation and management following Forever Plan architecture
// Integrates with existing wallet system and financial transactions
type OrderService struct {
	db                  *pgxpool.Pool
	cartService         *CartService
	notificationService *NotificationService
	wsService           *WebSocketService
	logger              *zap.Logger
}

// OrderRequest represents an order creation request
type OrderRequest struct {
	UserID          string                 `json:"user_id"`
	ShippingAddress map[string]interface{} `json:"shipping_address"`
	PaymentMethod   string                 `json:"payment_method"`
	Notes           string                 `json:"notes,omitempty"`
}

// OrderResponse represents the created order
type OrderResponse struct {
	OrderID       int     `json:"order_id"`
	TotalAmount   float64 `json:"total_amount"`
	Status        string  `json:"status"`
	PaymentStatus string  `json:"payment_status"`
	CreatedAt     string  `json:"created_at"`
}

// NewOrderService creates a new order service instance
func NewOrderService(db *pgxpool.Pool, cartService *CartService, notificationService *NotificationService, wsService *WebSocketService, logger *zap.Logger) *OrderService {
	return &OrderService{
		db:                  db,
		cartService:         cartService,
		notificationService: notificationService,
		wsService:           wsService,
		logger:              logger,
	}
}

// CreateOrderFromCart creates an order from user's cart using existing wallet system
func (s *OrderService) CreateOrderFromCart(ctx context.Context, req *OrderRequest) (*OrderResponse, error) {
	s.logger.Info("Creating order from cart", zap.String("user_id", req.UserID))

	// Get user's cart
	cart, err := s.cartService.GetOrCreateCart(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}

	if len(cart.Items) == 0 {
		return nil, fmt.Errorf("cart is empty")
	}

	// Start transaction
	tx, err := s.db.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Create order using existing orders table
	orderID, err := s.createOrder(ctx, tx, req, cart)
	if err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	// Create order items
	err = s.createOrderItems(ctx, tx, orderID, cart.Items)
	if err != nil {
		return nil, fmt.Errorf("failed to create order items: %w", err)
	}

	// Process payment using existing wallet system
	err = s.processPaymentWithWallet(ctx, tx, req.UserID, orderID, cart.Total)
	if err != nil {
		return nil, fmt.Errorf("failed to process payment: %w", err)
	}

	// Update inventory
	err = s.updateInventory(ctx, tx, cart.Items)
	if err != nil {
		return nil, fmt.Errorf("failed to update inventory: %w", err)
	}

	// Clear cart after successful order
	err = s.clearCartAfterOrder(ctx, tx, cart.ID)
	if err != nil {
		s.logger.Warn("Failed to clear cart after order", zap.Error(err))
		// Don't fail the order for this
	}

	// Commit transaction
	if err = tx.Commit(ctx); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Send order confirmation email (async, don't fail order if email fails)
	go func() {
		emailCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if s.notificationService != nil {
			err := s.notificationService.SendOrderConfirmationEmail(emailCtx, req.UserID, orderID, cart.Total)
			if err != nil {
				s.logger.Warn("Failed to send order confirmation email", zap.Error(err))
			}
		}
	}()

	// Send real-time order update notification
	if s.wsService != nil {
		orderData := map[string]interface{}{
			"order_id":       orderID,
			"total_amount":   cart.Total,
			"status":         "confirmed",
			"payment_status": "paid",
			"created_at":     time.Now().Format(time.RFC3339),
		}
		s.wsService.NotifyOrderUpdate(req.UserID, orderData)
	}

	s.logger.Info("Order created successfully",
		zap.Int("order_id", orderID),
		zap.String("user_id", req.UserID),
		zap.Float64("total_amount", cart.Total))

	return &OrderResponse{
		OrderID:       orderID,
		TotalAmount:   cart.Total,
		Status:        "confirmed",
		PaymentStatus: "paid",
		CreatedAt:     time.Now().Format(time.RFC3339),
	}, nil
}

// createOrder creates the main order record
func (s *OrderService) createOrder(ctx context.Context, tx pgx.Tx, req *OrderRequest, cart *Cart) (int, error) {
	query := `
		INSERT INTO public.orders (
			user_id, total_amount, status, payment_status, 
			shipping_address, payment_method, notes, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
		RETURNING id
	`

	var orderID int
	err := tx.QueryRow(ctx, query,
		req.UserID, cart.Total, "confirmed", "paid",
		req.ShippingAddress, req.PaymentMethod, req.Notes,
	).Scan(&orderID)

	return orderID, err
}

// createOrderItems creates order items from cart items
func (s *OrderService) createOrderItems(ctx context.Context, tx pgx.Tx, orderID int, cartItems []CartItem) error {
	for _, item := range cartItems {
		query := `
			INSERT INTO public.order_items (
				order_id, product_id, quantity, price, total_price, created_at, updated_at
			) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
		`

		totalPrice := item.Price * float64(item.Quantity)
		_, err := tx.Exec(ctx, query,
			orderID, item.ProductID, item.Quantity, item.Price, totalPrice,
		)
		if err != nil {
			return fmt.Errorf("failed to create order item for product %s: %w", item.ProductID, err)
		}
	}

	return nil
}

// processPaymentWithWallet processes payment using existing wallet system
func (s *OrderService) processPaymentWithWallet(ctx context.Context, tx pgx.Tx, userID string, orderID int, amount float64) error {
	// Check if user has sufficient wallet balance
	var walletBalance float64
	walletQuery := `SELECT balance FROM public.wallets WHERE user_id = $1 AND is_active = true`
	err := tx.QueryRow(ctx, walletQuery, userID).Scan(&walletBalance)
	if err != nil {
		if err == pgx.ErrNoRows {
			return fmt.Errorf("wallet not found for user")
		}
		return fmt.Errorf("failed to get wallet balance: %w", err)
	}

	if walletBalance < amount {
		return fmt.Errorf("insufficient wallet balance: have %.3f, need %.3f", walletBalance, amount)
	}

	// Deduct amount from wallet
	updateWalletQuery := `
		UPDATE public.wallets 
		SET balance = balance - $1, updated_at = NOW() 
		WHERE user_id = $2 AND is_active = true
	`
	_, err = tx.Exec(ctx, updateWalletQuery, amount, userID)
	if err != nil {
		return fmt.Errorf("failed to deduct from wallet: %w", err)
	}

	// Create wallet transaction record
	walletTxQuery := `
		INSERT INTO public.wallet_transactions (
			user_id, transaction_type, amount, balance_after, 
			description, reference_id, reference_type, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
	`

	newBalance := walletBalance - amount
	_, err = tx.Exec(ctx, walletTxQuery,
		userID, "debit", amount, newBalance,
		fmt.Sprintf("Payment for order #%d", orderID), orderID, "order",
	)
	if err != nil {
		return fmt.Errorf("failed to create wallet transaction: %w", err)
	}

	// Create financial transaction record
	financialTxQuery := `
		INSERT INTO public.financial_transactions (
			user_id, transaction_type, amount, status, 
			description, reference_id, reference_type, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
	`

	_, err = tx.Exec(ctx, financialTxQuery,
		userID, "purchase", amount, "completed",
		fmt.Sprintf("Order payment #%d", orderID), orderID, "order",
	)
	if err != nil {
		return fmt.Errorf("failed to create financial transaction: %w", err)
	}

	return nil
}

// updateInventory updates product inventory after order
func (s *OrderService) updateInventory(ctx context.Context, tx pgx.Tx, cartItems []CartItem) error {
	for _, item := range cartItems {
		// Update inventory
		query := `
			UPDATE public."Products"
			SET stock_quantity = stock_quantity - $1, updated_at = NOW()
			WHERE id = $2 AND stock_quantity >= $1
			RETURNING stock_quantity, name
		`

		var newStock int
		var productName string
		err := tx.QueryRow(ctx, query, item.Quantity, item.ProductID).Scan(&newStock, &productName)
		if err != nil {
			if err == pgx.ErrNoRows {
				return fmt.Errorf("insufficient stock for product %s", item.ProductID)
			}
			return fmt.Errorf("failed to update inventory for product %s: %w", item.ProductID, err)
		}

		// Send real-time inventory update notification
		if s.wsService != nil {
			s.wsService.NotifyInventoryUpdate(item.ProductID, newStock)
		}

		// Check for low stock and send alert (async)
		if newStock <= 5 { // Low stock threshold
			go func(productID, productName string, stock int) {
				alertCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()

				if s.notificationService != nil {
					err := s.notificationService.SendLowStockAlert(alertCtx, productID, productName, stock)
					if err != nil {
						s.logger.Warn("Failed to send low stock alert", zap.Error(err))
					}
				}
			}(item.ProductID, productName, newStock)
		}
	}

	return nil
}

// clearCartAfterOrder clears the cart after successful order
func (s *OrderService) clearCartAfterOrder(ctx context.Context, tx pgx.Tx, cartID string) error {
	query := `
		UPDATE public.cart_items 
		SET is_deleted = TRUE, updated_at = NOW() 
		WHERE cart_id = $1 AND is_deleted = FALSE
	`

	_, err := tx.Exec(ctx, query, cartID)
	return err
}

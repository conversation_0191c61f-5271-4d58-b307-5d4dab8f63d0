package services

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// CartService handles all cart-related business logic according to Forever Plan architecture
type CartService struct {
	db          *pgxpool.Pool
	redisClient *redis.Client
	wsService   *WebSocketService
	logger      *zap.Logger
}

// CartItem represents a cart item with product details
type CartItem struct {
	ID        string    `json:"id"`
	CartID    string    `json:"cart_id"`
	UserID    string    `json:"user_id"`
	ProductID string    `json:"product_id"`
	Quantity  int       `json:"quantity"`
	Price     float64   `json:"price"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Cart represents a user's shopping cart
type Cart struct {
	ID        string     `json:"id"`
	UserID    string     `json:"user_id"`
	Items     []CartItem `json:"items"`
	Subtotal  float64    `json:"subtotal"`
	Tax       float64    `json:"tax"`
	Total     float64    `json:"total"`
	ItemCount int        `json:"item_count"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
}

// CartTotals represents calculated cart totals
type CartTotals struct {
	Subtotal  float64 `json:"subtotal"`
	Tax       float64 `json:"tax"`
	Total     float64 `json:"total"`
	ItemCount int     `json:"item_count"`
}

// NewCartService creates a new cart service instance
func NewCartService(db *pgxpool.Pool, redisClient *redis.Client, wsService *WebSocketService, logger *zap.Logger) *CartService {
	return &CartService{
		db:          db,
		redisClient: redisClient,
		wsService:   wsService,
		logger:      logger,
	}
}

// GetOrCreateCart gets existing cart or creates new one for user
func (s *CartService) GetOrCreateCart(ctx context.Context, userID string) (*Cart, error) {
	s.logger.Info("Getting or creating cart for user", zap.String("user_id", userID))

	// Get or create cart using database function
	var cartID string
	err := s.db.QueryRow(ctx, "SELECT public.get_or_create_cart($1)", userID).Scan(&cartID)
	if err != nil {
		s.logger.Error("Failed to get or create cart", zap.Error(err), zap.String("user_id", userID))
		return nil, fmt.Errorf("failed to get or create cart: %w", err)
	}

	// Get cart with items
	cart, err := s.getCartWithItems(ctx, cartID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart with items: %w", err)
	}

	s.logger.Info("Successfully retrieved cart",
		zap.String("cart_id", cartID),
		zap.String("user_id", userID),
		zap.Int("item_count", len(cart.Items)))

	return cart, nil
}

// getCartWithItems retrieves cart with all items and calculates totals
func (s *CartService) getCartWithItems(ctx context.Context, cartID, userID string) (*Cart, error) {
	// Get cart basic info
	var cart Cart
	cartQuery := `
		SELECT id, user_id, created_at, updated_at, expires_at
		FROM public.carts
		WHERE id = $1 AND user_id = $2 AND is_deleted = FALSE
	`

	var expiresAt *time.Time
	err := s.db.QueryRow(ctx, cartQuery, cartID, userID).Scan(
		&cart.ID, &cart.UserID, &cart.CreatedAt, &cart.UpdatedAt, &expiresAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart: %w", err)
	}
	cart.ExpiresAt = expiresAt

	// Get cart items
	itemsQuery := `
		SELECT ci.id, ci.cart_id, ci.user_id, ci.product_id, ci.quantity, 
		       COALESCE(ci.price, p.price) as price, ci.created_at, ci.updated_at
		FROM public.cart_items ci
		LEFT JOIN public."Products" p ON ci.product_id = p.id
		WHERE ci.cart_id = $1 AND ci.is_deleted = FALSE
		ORDER BY ci.created_at DESC
	`

	rows, err := s.db.Query(ctx, itemsQuery, cartID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart items: %w", err)
	}
	defer rows.Close()

	var items []CartItem
	for rows.Next() {
		var item CartItem
		err := rows.Scan(
			&item.ID, &item.CartID, &item.UserID, &item.ProductID,
			&item.Quantity, &item.Price, &item.CreatedAt, &item.UpdatedAt,
		)
		if err != nil {
			s.logger.Error("Failed to scan cart item", zap.Error(err))
			continue
		}
		items = append(items, item)
	}

	cart.Items = items

	// Calculate totals
	totals, err := s.calculateCartTotals(ctx, cartID)
	if err != nil {
		s.logger.Warn("Failed to calculate cart totals, using zero values", zap.Error(err))
		totals = &CartTotals{}
	}

	cart.Subtotal = totals.Subtotal
	cart.Tax = totals.Tax
	cart.Total = totals.Total
	cart.ItemCount = totals.ItemCount

	return &cart, nil
}

// calculateCartTotals calculates cart totals using database function
func (s *CartService) calculateCartTotals(ctx context.Context, cartID string) (*CartTotals, error) {
	var totals CartTotals

	query := "SELECT subtotal, tax, total, item_count FROM public.calculate_cart_totals($1)"
	err := s.db.QueryRow(ctx, query, cartID).Scan(
		&totals.Subtotal, &totals.Tax, &totals.Total, &totals.ItemCount,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate cart totals: %w", err)
	}

	return &totals, nil
}

// AddItem adds an item to the cart or updates quantity if it already exists
func (s *CartService) AddItem(ctx context.Context, userID, productID string, quantity int) error {
	if quantity <= 0 {
		return fmt.Errorf("quantity must be greater than zero")
	}

	s.logger.Info("Adding item to cart",
		zap.String("user_id", userID),
		zap.String("product_id", productID),
		zap.Int("quantity", quantity))

	// Get or create cart
	cart, err := s.GetOrCreateCart(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get cart: %w", err)
	}

	// Start transaction
	tx, err := s.db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Validate product exists and is active
	var productExists bool
	var productPrice float64
	productQuery := `
		SELECT EXISTS(SELECT 1 FROM public."Products" WHERE id = $1 AND is_deleted = false AND is_active = true),
		       COALESCE(price, 0)
		FROM public."Products"
		WHERE id = $1 AND is_deleted = false AND is_active = true
	`
	err = tx.QueryRow(ctx, productQuery, productID).Scan(&productExists, &productPrice)
	if err != nil {
		return fmt.Errorf("failed to validate product: %w", err)
	}

	if !productExists {
		return fmt.Errorf("product not found or unavailable")
	}

	// Check if item already exists in cart
	var existingItemID string
	var existingQuantity int
	checkQuery := `
		SELECT id, quantity
		FROM public.cart_items
		WHERE cart_id = $1 AND product_id = $2 AND is_deleted = FALSE
	`
	err = tx.QueryRow(ctx, checkQuery, cart.ID, productID).Scan(&existingItemID, &existingQuantity)

	if err == nil {
		// Item exists, update quantity
		newQuantity := existingQuantity + quantity
		updateQuery := `
			UPDATE public.cart_items
			SET quantity = $1, updated_at = NOW()
			WHERE id = $2
		`
		_, err = tx.Exec(ctx, updateQuery, newQuantity, existingItemID)
		if err != nil {
			return fmt.Errorf("failed to update cart item quantity: %w", err)
		}

		s.logger.Info("Updated existing cart item",
			zap.String("item_id", existingItemID),
			zap.Int("new_quantity", newQuantity))

	} else if err == pgx.ErrNoRows {
		// Item doesn't exist, create new one
		insertQuery := `
			INSERT INTO public.cart_items (cart_id, user_id, product_id, quantity, price, created_at, updated_at, is_deleted)
			VALUES ($1, $2, $3, $4, $5, NOW(), NOW(), false)
		`
		_, err = tx.Exec(ctx, insertQuery, cart.ID, userID, productID, quantity, productPrice)
		if err != nil {
			return fmt.Errorf("failed to add cart item: %w", err)
		}

		s.logger.Info("Added new cart item",
			zap.String("cart_id", cart.ID),
			zap.String("product_id", productID),
			zap.Int("quantity", quantity))

	} else {
		return fmt.Errorf("failed to check existing cart item: %w", err)
	}

	// Update cart timestamp
	_, err = tx.Exec(ctx, "UPDATE public.carts SET updated_at = NOW() WHERE id = $1", cart.ID)
	if err != nil {
		return fmt.Errorf("failed to update cart timestamp: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Invalidate cache
	s.invalidateCartCache(ctx, cart.ID)

	// Send real-time cart update notification
	s.notifyCartUpdate(ctx, userID)

	return nil
}

// UpdateItemQuantity updates the quantity of a cart item
func (s *CartService) UpdateItemQuantity(ctx context.Context, userID, itemID string, quantity int) error {
	if quantity < 0 {
		return fmt.Errorf("quantity cannot be negative")
	}

	s.logger.Info("Updating cart item quantity",
		zap.String("user_id", userID),
		zap.String("item_id", itemID),
		zap.Int("quantity", quantity))

	if quantity == 0 {
		return s.RemoveItem(ctx, userID, itemID)
	}

	// Start transaction
	tx, err := s.db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Verify item belongs to user and get cart_id
	var cartID string
	checkQuery := `
		SELECT ci.cart_id
		FROM public.cart_items ci
		JOIN public.carts c ON ci.cart_id = c.id
		WHERE ci.id = $1 AND c.user_id = $2 AND ci.is_deleted = FALSE AND c.is_deleted = FALSE
	`
	err = tx.QueryRow(ctx, checkQuery, itemID, userID).Scan(&cartID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return fmt.Errorf("cart item not found or access denied")
		}
		return fmt.Errorf("failed to verify cart item: %w", err)
	}

	// Update quantity
	updateQuery := `
		UPDATE public.cart_items
		SET quantity = $1, updated_at = NOW()
		WHERE id = $2
	`
	result, err := tx.Exec(ctx, updateQuery, quantity, itemID)
	if err != nil {
		return fmt.Errorf("failed to update cart item quantity: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("cart item not found")
	}

	// Update cart timestamp
	_, err = tx.Exec(ctx, "UPDATE public.carts SET updated_at = NOW() WHERE id = $1", cartID)
	if err != nil {
		return fmt.Errorf("failed to update cart timestamp: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Invalidate cache
	s.invalidateCartCache(ctx, cartID)

	// Send real-time cart update notification
	s.notifyCartUpdate(ctx, userID)

	s.logger.Info("Successfully updated cart item quantity",
		zap.String("item_id", itemID),
		zap.Int("quantity", quantity))

	return nil
}

// RemoveItem removes an item from the cart
func (s *CartService) RemoveItem(ctx context.Context, userID, itemID string) error {
	s.logger.Info("Removing item from cart",
		zap.String("user_id", userID),
		zap.String("item_id", itemID))

	// Start transaction
	tx, err := s.db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Verify item belongs to user and get cart_id
	var cartID string
	checkQuery := `
		SELECT ci.cart_id
		FROM public.cart_items ci
		JOIN public.carts c ON ci.cart_id = c.id
		WHERE ci.id = $1 AND c.user_id = $2 AND ci.is_deleted = FALSE AND c.is_deleted = FALSE
	`
	err = tx.QueryRow(ctx, checkQuery, itemID, userID).Scan(&cartID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return fmt.Errorf("cart item not found or access denied")
		}
		return fmt.Errorf("failed to verify cart item: %w", err)
	}

	// Soft delete the item
	deleteQuery := `
		UPDATE public.cart_items
		SET is_deleted = TRUE, updated_at = NOW()
		WHERE id = $1
	`
	result, err := tx.Exec(ctx, deleteQuery, itemID)
	if err != nil {
		return fmt.Errorf("failed to remove cart item: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("cart item not found")
	}

	// Update cart timestamp
	_, err = tx.Exec(ctx, "UPDATE public.carts SET updated_at = NOW() WHERE id = $1", cartID)
	if err != nil {
		return fmt.Errorf("failed to update cart timestamp: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Invalidate cache
	s.invalidateCartCache(ctx, cartID)

	// Send real-time cart update notification
	s.notifyCartUpdate(ctx, userID)

	s.logger.Info("Successfully removed cart item", zap.String("item_id", itemID))

	return nil
}

// ClearCart removes all items from the user's cart
func (s *CartService) ClearCart(ctx context.Context, userID string) error {
	s.logger.Info("Clearing cart for user", zap.String("user_id", userID))

	// Get user's cart
	cart, err := s.GetOrCreateCart(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get cart: %w", err)
	}

	// Start transaction
	tx, err := s.db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Soft delete all cart items
	clearQuery := `
		UPDATE public.cart_items
		SET is_deleted = TRUE, updated_at = NOW()
		WHERE cart_id = $1 AND is_deleted = FALSE
	`
	_, err = tx.Exec(ctx, clearQuery, cart.ID)
	if err != nil {
		return fmt.Errorf("failed to clear cart items: %w", err)
	}

	// Update cart timestamp
	_, err = tx.Exec(ctx, "UPDATE public.carts SET updated_at = NOW() WHERE id = $1", cart.ID)
	if err != nil {
		return fmt.Errorf("failed to update cart timestamp: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Invalidate cache
	s.invalidateCartCache(ctx, cart.ID)

	// Send real-time cart update notification
	s.notifyCartUpdate(ctx, userID)

	s.logger.Info("Successfully cleared cart", zap.String("cart_id", cart.ID))

	return nil
}

// invalidateCartCache invalidates Redis cache for the cart
func (s *CartService) invalidateCartCache(ctx context.Context, cartID string) {
	if s.redisClient == nil {
		return
	}

	cacheKey := fmt.Sprintf("cart:%s", cartID)
	err := s.redisClient.Del(ctx, cacheKey).Err()
	if err != nil {
		s.logger.Warn("Failed to invalidate cart cache",
			zap.Error(err),
			zap.String("cache_key", cacheKey))
	} else {
		s.logger.Debug("Invalidated cart cache", zap.String("cache_key", cacheKey))
	}
}

// notifyCartUpdate sends real-time cart update notification via WebSocket
func (s *CartService) notifyCartUpdate(ctx context.Context, userID string) {
	if s.wsService == nil {
		return
	}

	// Get updated cart data
	cart, err := s.GetOrCreateCart(ctx, userID)
	if err != nil {
		s.logger.Warn("Failed to get cart for WebSocket notification", zap.Error(err))
		return
	}

	// Prepare cart data for WebSocket
	cartData := map[string]interface{}{
		"id":         cart.ID,
		"user_id":    cart.UserID,
		"item_count": cart.ItemCount,
		"subtotal":   cart.Subtotal,
		"tax":        cart.Tax,
		"total":      cart.Total,
		"updated_at": cart.UpdatedAt,
	}

	// Send WebSocket notification
	s.wsService.NotifyCartUpdate(userID, cartData)
}

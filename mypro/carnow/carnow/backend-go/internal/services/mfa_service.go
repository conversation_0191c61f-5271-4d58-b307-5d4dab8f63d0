package services

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base32"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/core/domain"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MFAService handles Multi-Factor Authentication operations
type MFAService struct {
	config    *config.Config
	db        *gorm.DB
	logger    *zap.Logger
	smsService *SMSService
	emailService *EmailService
}

// MFAMethod represents different MFA methods
type MFAMethod string

const (
	MFAMethodSMS   MFAMethod = "sms"
	MFAMethodEmail MFAMethod = "email"
	MFAMethodTOTP  MFAMethod = "totp"
)

// MFAChallenge represents an MFA challenge
type MFAChallenge struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	UserID      string    `json:"user_id" gorm:"not null;index"`
	Method      MFAMethod `json:"method" gorm:"not null"`
	Code        string    `json:"-" gorm:"not null"` // Never expose in JSON
	CodeHash    string    `json:"-" gorm:"not null"` // Hashed version
	ExpiresAt   time.Time `json:"expires_at" gorm:"not null"`
	Attempts    int       `json:"attempts" gorm:"default:0"`
	MaxAttempts int       `json:"max_attempts" gorm:"default:3"`
	Used        bool      `json:"used" gorm:"default:false"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// MFASettings represents user's MFA settings
type MFASettings struct {
	ID              string    `json:"id" gorm:"primaryKey"`
	UserID          string    `json:"user_id" gorm:"not null;uniqueIndex"`
	SMSEnabled      bool      `json:"sms_enabled" gorm:"default:false"`
	EmailEnabled    bool      `json:"email_enabled" gorm:"default:false"`
	TOTPEnabled     bool      `json:"totp_enabled" gorm:"default:false"`
	TOTPSecret      string    `json:"-" gorm:"column:totp_secret"` // Never expose
	TOTPSecretHash  string    `json:"-" gorm:"column:totp_secret_hash"`
	BackupCodes     []string  `json:"-" gorm:"serializer:json"` // Never expose
	BackupCodesHash []string  `json:"-" gorm:"serializer:json"`
	PhoneNumber     string    `json:"phone_number"`
	PhoneVerified   bool      `json:"phone_verified" gorm:"default:false"`
	EmailAddress    string    `json:"email_address"`
	EmailVerified   bool      `json:"email_verified" gorm:"default:false"`
	CreatedAt       time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt       time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// MFAVerificationResult represents the result of MFA verification
type MFAVerificationResult struct {
	Success       bool      `json:"success"`
	Method        MFAMethod `json:"method"`
	RemainingAttempts int   `json:"remaining_attempts"`
	CooldownUntil *time.Time `json:"cooldown_until,omitempty"`
	Error         string    `json:"error,omitempty"`
}

// NewMFAService creates a new MFA service
func NewMFAService(config *config.Config, db *gorm.DB, logger *zap.Logger) *MFAService {
	return &MFAService{
		config:       config,
		db:          db,
		logger:      logger,
		smsService:  NewSMSService(config, logger),
		emailService: NewEmailService(config, logger),
	}
}

// InitializeMFA sets up MFA for a user
func (s *MFAService) InitializeMFA(userID string) error {
	// Check if MFA settings already exist
	var existing MFASettings
	if err := s.db.Where("user_id = ?", userID).First(&existing).Error; err == nil {
		return nil // Already exists
	}

	// Create new MFA settings
	settings := MFASettings{
		ID:     uuid.New().String(),
		UserID: userID,
	}

	if err := s.db.Create(&settings).Error; err != nil {
		s.logger.Error("Failed to initialize MFA settings",
			zap.String("user_id", userID),
			zap.Error(err),
		)
		return fmt.Errorf("failed to initialize MFA: %w", err)
	}

	s.logger.Info("MFA settings initialized",
		zap.String("user_id", userID),
	)

	return nil
}

// GenerateOTP generates a 6-digit OTP code
func (s *MFAService) GenerateOTP() (string, error) {
	// Generate 6-digit OTP
	max := big.NewInt(999999)
	min := big.NewInt(100000)
	
	n, err := rand.Int(rand.Reader, max.Sub(max, min).Add(max, big.NewInt(1)))
	if err != nil {
		return "", fmt.Errorf("failed to generate OTP: %w", err)
	}
	
	otp := fmt.Sprintf("%06d", n.Add(n, min).Int64())
	return otp, nil
}

// SendSMSOTP sends SMS OTP to user
func (s *MFAService) SendSMSOTP(userID, phoneNumber string) (*MFAChallenge, error) {
	// Generate OTP
	otp, err := s.GenerateOTP()
	if err != nil {
		return nil, err
	}

	// Create challenge
	challenge := &MFAChallenge{
		ID:          uuid.New().String(),
		UserID:      userID,
		Method:      MFAMethodSMS,
		Code:        otp,
		CodeHash:    s.hashCode(otp),
		ExpiresAt:   time.Now().Add(5 * time.Minute), // 5 minutes expiry
		MaxAttempts: 3,
	}

	// Save challenge to database
	if err := s.db.Create(challenge).Error; err != nil {
		s.logger.Error("Failed to save SMS OTP challenge",
			zap.String("user_id", userID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to save challenge: %w", err)
	}

	// Send SMS
	message := fmt.Sprintf("كود التحقق الخاص بك في CarNow: %s\nصالح لمدة 5 دقائق", otp)
	if err := s.smsService.SendSMS(phoneNumber, message); err != nil {
		s.logger.Error("Failed to send SMS OTP",
			zap.String("user_id", userID),
			zap.String("phone", phoneNumber),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to send SMS: %w", err)
	}

	s.logger.Info("SMS OTP sent successfully",
		zap.String("user_id", userID),
		zap.String("challenge_id", challenge.ID),
	)

	// Clear sensitive data before returning
	challenge.Code = ""
	return challenge, nil
}

// SendEmailOTP sends Email OTP to user
func (s *MFAService) SendEmailOTP(userID, emailAddress string) (*MFAChallenge, error) {
	// Generate OTP
	otp, err := s.GenerateOTP()
	if err != nil {
		return nil, err
	}

	// Create challenge
	challenge := &MFAChallenge{
		ID:          uuid.New().String(),
		UserID:      userID,
		Method:      MFAMethodEmail,
		Code:        otp,
		CodeHash:    s.hashCode(otp),
		ExpiresAt:   time.Now().Add(10 * time.Minute), // 10 minutes expiry
		MaxAttempts: 3,
	}

	// Save challenge to database
	if err := s.db.Create(challenge).Error; err != nil {
		s.logger.Error("Failed to save Email OTP challenge",
			zap.String("user_id", userID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to save challenge: %w", err)
	}

	// Send Email
	subject := "كود التحقق - CarNow"
	body := fmt.Sprintf(`
		<div dir="rtl" style="font-family: Arial, sans-serif;">
			<h2>كود التحقق الخاص بك</h2>
			<p>مرحباً،</p>
			<p>كود التحقق الخاص بك في CarNow هو:</p>
			<h1 style="color: #1B5E20; font-size: 32px; letter-spacing: 5px;">%s</h1>
			<p>هذا الكود صالح لمدة 10 دقائق فقط.</p>
			<p>إذا لم تطلب هذا الكود، يرجى تجاهل هذه الرسالة.</p>
			<br>
			<p>فريق CarNow</p>
		</div>
	`, otp)

	if err := s.emailService.SendEmail(emailAddress, subject, body); err != nil {
		s.logger.Error("Failed to send Email OTP",
			zap.String("user_id", userID),
			zap.String("email", emailAddress),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to send email: %w", err)
	}

	s.logger.Info("Email OTP sent successfully",
		zap.String("user_id", userID),
		zap.String("challenge_id", challenge.ID),
	)

	// Clear sensitive data before returning
	challenge.Code = ""
	return challenge, nil
}

// VerifyOTP verifies an OTP code
func (s *MFAService) VerifyOTP(challengeID, code string) (*MFAVerificationResult, error) {
	// Get challenge from database
	var challenge MFAChallenge
	if err := s.db.Where("id = ? AND used = false", challengeID).First(&challenge).Error; err != nil {
		return &MFAVerificationResult{
			Success: false,
			Error:   "Invalid or expired challenge",
		}, nil
	}

	// Check if challenge is expired
	if time.Now().After(challenge.ExpiresAt) {
		return &MFAVerificationResult{
			Success: false,
			Error:   "Challenge expired",
		}, nil
	}

	// Check if max attempts exceeded
	if challenge.Attempts >= challenge.MaxAttempts {
		cooldownUntil := challenge.UpdatedAt.Add(15 * time.Minute)
		return &MFAVerificationResult{
			Success:           false,
			RemainingAttempts: 0,
			CooldownUntil:     &cooldownUntil,
			Error:            "Maximum attempts exceeded",
		}, nil
	}

	// Increment attempts
	challenge.Attempts++
	s.db.Save(&challenge)

	// Verify code
	if !s.verifyCode(code, challenge.CodeHash) {
		remainingAttempts := challenge.MaxAttempts - challenge.Attempts
		return &MFAVerificationResult{
			Success:           false,
			Method:            challenge.Method,
			RemainingAttempts: remainingAttempts,
			Error:            "Invalid code",
		}, nil
	}

	// Mark challenge as used
	challenge.Used = true
	s.db.Save(&challenge)

	s.logger.Info("OTP verified successfully",
		zap.String("user_id", challenge.UserID),
		zap.String("challenge_id", challengeID),
		zap.String("method", string(challenge.Method)),
	)

	return &MFAVerificationResult{
		Success: true,
		Method:  challenge.Method,
	}, nil
}

// hashCode creates a hash of the code for secure storage
func (s *MFAService) hashCode(code string) string {
	hash := sha256.Sum256([]byte(code + s.config.Security.EncryptionKey))
	return hex.EncodeToString(hash[:])
}

// verifyCode verifies a code against its hash
func (s *MFAService) verifyCode(code, hash string) bool {
	return s.hashCode(code) == hash
}

// GenerateTOTPSecret generates a new TOTP secret for a user
func (s *MFAService) GenerateTOTPSecret(userID string) (string, error) {
	// Generate 32-byte secret
	secret := make([]byte, 32)
	if _, err := rand.Read(secret); err != nil {
		return "", fmt.Errorf("failed to generate TOTP secret: %w", err)
	}

	// Encode as base32
	secretBase32 := base32.StdEncoding.EncodeToString(secret)
	secretBase32 = strings.TrimRight(secretBase32, "=") // Remove padding

	// Update user's MFA settings
	var settings MFASettings
	if err := s.db.Where("user_id = ?", userID).First(&settings).Error; err != nil {
		return "", fmt.Errorf("MFA settings not found: %w", err)
	}

	settings.TOTPSecret = secretBase32
	settings.TOTPSecretHash = s.hashCode(secretBase32)

	if err := s.db.Save(&settings).Error; err != nil {
		return "", fmt.Errorf("failed to save TOTP secret: %w", err)
	}

	s.logger.Info("TOTP secret generated",
		zap.String("user_id", userID),
	)

	return secretBase32, nil
}

// GetMFASettings retrieves user's MFA settings
func (s *MFAService) GetMFASettings(userID string) (*MFASettings, error) {
	var settings MFASettings
	if err := s.db.Where("user_id = ?", userID).First(&settings).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Initialize MFA if not exists
			if initErr := s.InitializeMFA(userID); initErr != nil {
				return nil, initErr
			}
			// Try again
			if err := s.db.Where("user_id = ?", userID).First(&settings).Error; err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}

	// Clear sensitive data
	settings.TOTPSecret = ""
	settings.TOTPSecretHash = ""
	settings.BackupCodes = nil
	settings.BackupCodesHash = nil

	return &settings, nil
}

// CleanupExpiredChallenges removes expired MFA challenges
func (s *MFAService) CleanupExpiredChallenges() error {
	result := s.db.Where("expires_at < ? OR used = true", time.Now().Add(-1*time.Hour)).Delete(&MFAChallenge{})
	if result.Error != nil {
		return result.Error
	}

	s.logger.Info("Cleaned up expired MFA challenges",
		zap.Int64("deleted_count", result.RowsAffected),
	)

	return nil
}

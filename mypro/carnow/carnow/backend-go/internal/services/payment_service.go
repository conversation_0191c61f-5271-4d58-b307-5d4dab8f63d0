package services

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// PaymentService handles payment processing following Forever Plan architecture
type PaymentService struct {
	db     *pgxpool.Pool
	logger *zap.Logger
	config *PaymentConfig
}

// PaymentConfig contains payment service configuration
type PaymentConfig struct {
	StripeSecretKey     string
	StripeWebhookSecret string
	Currency            string
	TaxRate             float64
}

// PaymentIntent represents a payment intent
type PaymentIntent struct {
	ID            string    `json:"id"`
	Amount        int64     `json:"amount"`
	Currency      string    `json:"currency"`
	Status        string    `json:"status"`
	ClientSecret  string    `json:"client_secret"`
	PaymentMethod string    `json:"payment_method,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// PaymentResult represents the result of a payment operation
type PaymentResult struct {
	Success       bool   `json:"success"`
	PaymentID     string `json:"payment_id,omitempty"`
	TransactionID string `json:"transaction_id,omitempty"`
	Status        string `json:"status"`
	Message       string `json:"message"`
	Error         string `json:"error,omitempty"`
}

// NewPaymentService creates a new payment service instance
func NewPaymentService(db *pgxpool.Pool, config *PaymentConfig, logger *zap.Logger) *PaymentService {
	return &PaymentService{
		db:     db,
		logger: logger,
		config: config,
	}
}

// CreatePaymentIntent creates a new payment intent for the given amount
// Enhanced for CarNow multi-product platform (cars, clothes, electronics, etc.)
func (s *PaymentService) CreatePaymentIntent(ctx context.Context, amount int64, currency string, metadata map[string]string) (*PaymentIntent, error) {
	s.logger.Info("Creating payment intent for CarNow platform",
		zap.Int64("amount", amount),
		zap.String("currency", currency),
		zap.Any("metadata", metadata))

	// TODO: Replace with real Stripe integration
	// For now, create a structured mock payment intent
	paymentIntent := &PaymentIntent{
		ID:           fmt.Sprintf("pi_carnow_%d", time.Now().Unix()),
		Amount:       amount,
		Currency:     currency,
		Status:       "requires_payment_method",
		ClientSecret: fmt.Sprintf("pi_carnow_%d_secret_%s", time.Now().Unix(), currency),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Store payment intent in database with enhanced metadata
	enhancedMetadata := make(map[string]string)
	for k, v := range metadata {
		enhancedMetadata[k] = v
	}
	enhancedMetadata["platform"] = "carnow"
	enhancedMetadata["created_at"] = time.Now().Format(time.RFC3339)

	err := s.storePaymentIntent(ctx, paymentIntent, enhancedMetadata)
	if err != nil {
		s.logger.Error("Failed to store payment intent", zap.Error(err))
		return nil, fmt.Errorf("failed to store payment intent: %w", err)
	}

	s.logger.Info("Payment intent created successfully for CarNow",
		zap.String("payment_intent_id", paymentIntent.ID),
		zap.String("currency", currency))
	return paymentIntent, nil
}

// ConfirmPayment confirms a payment intent
func (s *PaymentService) ConfirmPayment(ctx context.Context, paymentIntentID string, paymentMethodID string) (*PaymentResult, error) {
	s.logger.Info("Confirming payment",
		zap.String("payment_intent_id", paymentIntentID),
		zap.String("payment_method_id", paymentMethodID))

	// Get payment intent from database
	_, err := s.getPaymentIntent(ctx, paymentIntentID)
	if err != nil {
		return &PaymentResult{
			Success: false,
			Status:  "failed",
			Error:   "Payment intent not found",
		}, err
	}

	// For now, simulate payment confirmation
	// In production, this would integrate with Stripe
	result := &PaymentResult{
		Success:       true,
		PaymentID:     paymentIntentID,
		TransactionID: fmt.Sprintf("txn_%d", time.Now().Unix()),
		Status:        "succeeded",
		Message:       "Payment confirmed successfully",
	}

	// Update payment intent status
	err = s.updatePaymentIntentStatus(ctx, paymentIntentID, "succeeded", result.TransactionID)
	if err != nil {
		s.logger.Error("Failed to update payment intent status", zap.Error(err))
		return &PaymentResult{
			Success: false,
			Status:  "failed",
			Error:   "Failed to update payment status",
		}, err
	}

	s.logger.Info("Payment confirmed successfully",
		zap.String("payment_intent_id", paymentIntentID),
		zap.String("transaction_id", result.TransactionID))

	return result, nil
}

// RefundPayment processes a refund for a payment
func (s *PaymentService) RefundPayment(ctx context.Context, paymentIntentID string, amount int64, reason string) (*PaymentResult, error) {
	s.logger.Info("Processing refund",
		zap.String("payment_intent_id", paymentIntentID),
		zap.Int64("amount", amount),
		zap.String("reason", reason))

	// Get payment intent from database
	paymentIntent, err := s.getPaymentIntent(ctx, paymentIntentID)
	if err != nil {
		return &PaymentResult{
			Success: false,
			Status:  "failed",
			Error:   "Payment intent not found",
		}, err
	}

	if paymentIntent.Status != "succeeded" {
		return &PaymentResult{
			Success: false,
			Status:  "failed",
			Error:   "Payment not eligible for refund",
		}, fmt.Errorf("payment status is %s, cannot refund", paymentIntent.Status)
	}

	// For now, simulate refund processing
	// In production, this would integrate with Stripe
	result := &PaymentResult{
		Success:       true,
		PaymentID:     paymentIntentID,
		TransactionID: fmt.Sprintf("re_%d", time.Now().Unix()),
		Status:        "refunded",
		Message:       "Refund processed successfully",
	}

	// Store refund information
	err = s.storeRefund(ctx, paymentIntentID, amount, reason, result.TransactionID)
	if err != nil {
		s.logger.Error("Failed to store refund", zap.Error(err))
		return &PaymentResult{
			Success: false,
			Status:  "failed",
			Error:   "Failed to store refund information",
		}, err
	}

	s.logger.Info("Refund processed successfully",
		zap.String("payment_intent_id", paymentIntentID),
		zap.String("refund_id", result.TransactionID))

	return result, nil
}

// GetPaymentStatus gets the current status of a payment
func (s *PaymentService) GetPaymentStatus(ctx context.Context, paymentIntentID string) (*PaymentIntent, error) {
	return s.getPaymentIntent(ctx, paymentIntentID)
}

// storePaymentIntent stores a payment intent in the database
func (s *PaymentService) storePaymentIntent(ctx context.Context, intent *PaymentIntent, metadata map[string]string) error {
	query := `
		INSERT INTO payment_intents (id, amount, currency, status, client_secret, created_at, updated_at, metadata)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	_, err := s.db.Exec(ctx, query,
		intent.ID, intent.Amount, intent.Currency, intent.Status,
		intent.ClientSecret, intent.CreatedAt, intent.UpdatedAt, metadata)

	return err
}

// getPaymentIntent retrieves a payment intent from the database
func (s *PaymentService) getPaymentIntent(ctx context.Context, paymentIntentID string) (*PaymentIntent, error) {
	query := `
		SELECT id, amount, currency, status, client_secret, payment_method, created_at, updated_at
		FROM payment_intents
		WHERE id = $1
	`

	var intent PaymentIntent
	var paymentMethod *string

	err := s.db.QueryRow(ctx, query, paymentIntentID).Scan(
		&intent.ID, &intent.Amount, &intent.Currency, &intent.Status,
		&intent.ClientSecret, &paymentMethod, &intent.CreatedAt, &intent.UpdatedAt,
	)

	if paymentMethod != nil {
		intent.PaymentMethod = *paymentMethod
	}

	return &intent, err
}

// updatePaymentIntentStatus updates the status of a payment intent
func (s *PaymentService) updatePaymentIntentStatus(ctx context.Context, paymentIntentID, status, transactionID string) error {
	query := `
		UPDATE payment_intents 
		SET status = $1, payment_method = $2, updated_at = NOW()
		WHERE id = $3
	`

	_, err := s.db.Exec(ctx, query, status, transactionID, paymentIntentID)
	return err
}

// storeRefund stores refund information in the database
func (s *PaymentService) storeRefund(ctx context.Context, paymentIntentID string, amount int64, reason, refundID string) error {
	query := `
		INSERT INTO payment_refunds (id, payment_intent_id, amount, reason, status, created_at)
		VALUES ($1, $2, $3, $4, 'succeeded', NOW())
	`

	_, err := s.db.Exec(ctx, query, refundID, paymentIntentID, amount, reason)
	return err
}

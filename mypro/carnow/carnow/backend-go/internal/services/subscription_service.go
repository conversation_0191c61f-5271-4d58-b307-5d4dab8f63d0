package services

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"carnow-backend/internal/domain"
)

// SubscriptionService handles subscription-related operations
type SubscriptionService struct {
	db              *gorm.DB
	discountService *DiscountService
	logger          *zap.Logger
}

// NewSubscriptionService creates a new subscription service
func NewSubscriptionService(db *gorm.DB, discountService *DiscountService, logger *zap.Logger) *SubscriptionService {
	return &SubscriptionService{
		db:              db,
		discountService: discountService,
		logger:          logger,
	}
}

// GetActiveSubscriptionPlans retrieves all active subscription plans
func (s *SubscriptionService) GetActiveSubscriptionPlans(ctx context.Context) ([]domain.SubscriptionPlan, error) {
	var plans []domain.SubscriptionPlan

	err := s.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("price_monthly asc").
		Find(&plans).Error

	if err != nil {
		s.logger.Error("Failed to fetch subscription plans", zap.Error(err))
		return nil, fmt.Errorf("failed to fetch subscription plans: %w", err)
	}

	return plans, nil
}

// GetSubscriptionPlanByID retrieves a subscription plan by ID
func (s *SubscriptionService) GetSubscriptionPlanByID(ctx context.Context, planID uint) (*domain.SubscriptionPlan, error) {
	var plan domain.SubscriptionPlan

	err := s.db.WithContext(ctx).
		Where("id = ? AND is_active = ?", planID, true).
		First(&plan).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("subscription plan not found: %d", planID)
		}
		s.logger.Error("Failed to fetch subscription plan", zap.Error(err), zap.Uint("plan_id", planID))
		return nil, fmt.Errorf("failed to fetch subscription plan: %w", err)
	}

	return &plan, nil
}

// CalculateSubscriptionPrice calculates the price for a subscription with optional discount
func (s *SubscriptionService) CalculateSubscriptionPrice(
	ctx context.Context,
	planID uint,
	billingCycle string, // "monthly" or "yearly"
	discountCode string,
) (*domain.DiscountCalculation, error) {
	// Get the subscription plan
	plan, err := s.GetSubscriptionPlanByID(ctx, planID)
	if err != nil {
		return nil, err
	}

	// Determine the base price based on billing cycle
	var basePrice float64
	switch billingCycle {
	case "monthly":
		basePrice = plan.PriceMonthly
	case "yearly":
		basePrice = plan.PriceYearly
		// Apply automatic yearly discount if available
		yearlyDiscount, err := s.discountService.GetYearlySubscriptionDiscount(ctx)
		if err == nil && yearlyDiscount != nil {
			// Calculate yearly discount
			discountAmount := basePrice * (yearlyDiscount.DiscountValue / 100)
			basePrice = basePrice - discountAmount
		}
	default:
		return nil, fmt.Errorf("invalid billing cycle: %s", billingCycle)
	}

	// Apply additional discount code if provided
	var discountCodeObj *domain.DiscountCode
	if discountCode != "" {
		discountCodeObj, err = s.discountService.GetDiscountByCode(ctx, discountCode)
		if err != nil {
			return nil, fmt.Errorf("invalid discount code: %w", err)
		}
	}

	// Calculate final price with discount
	calculation, err := s.discountService.CalculateDiscount(ctx, basePrice, discountCodeObj)
	if err != nil {
		return nil, err
	}

	return calculation, nil
}

// CreateSubscription creates a new subscription for a user
func (s *SubscriptionService) CreateSubscription(
	ctx context.Context,
	userID string,
	planID uint,
	billingCycle string,
	discountCode string,
) (*domain.Subscription, error) {
	// Calculate the final price
	calculation, err := s.CalculateSubscriptionPrice(ctx, planID, billingCycle, discountCode)
	if err != nil {
		return nil, err
	}

	// Get the plan details
	plan, err := s.GetSubscriptionPlanByID(ctx, planID)
	if err != nil {
		return nil, err
	}

	// Determine subscription duration
	var durationMonths int
	switch billingCycle {
	case "monthly":
		durationMonths = 1
	case "yearly":
		durationMonths = 12
	default:
		return nil, fmt.Errorf("invalid billing cycle: %s", billingCycle)
	}

	// Create subscription
	subscription := domain.Subscription{
		PlanID:         planID,
		UserAuthID:     userID,
		StartDate:      time.Now(),
		EndDate:        time.Now().AddDate(0, durationMonths, 0),
		Status:         "active",
		BillingCycle:   billingCycle,
		Amount:         calculation.FinalAmount,
		OriginalAmount: calculation.OriginalAmount,
		DiscountAmount: calculation.DiscountAmount,
		MaxListings:    plan.MaxListings,
		UsedListings:   0,
		Features:       plan.Features,
	}

	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Create subscription
		if err := tx.Create(&subscription).Error; err != nil {
			return fmt.Errorf("failed to create subscription: %w", err)
		}

		// Apply discount if code was used
		if discountCode != "" {
			discountCodeObj, err := s.discountService.GetDiscountByCode(ctx, discountCode)
			if err != nil {
				return fmt.Errorf("failed to get discount code: %w", err)
			}

			_, err = s.discountService.ApplyDiscount(
				ctx,
				userID,
				calculation.OriginalAmount,
				discountCodeObj,
				&subscription.ID,
			)
			if err != nil {
				return fmt.Errorf("failed to apply discount: %w", err)
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	s.logger.Info("Subscription created successfully",
		zap.String("user_id", userID),
		zap.Uint("plan_id", planID),
		zap.String("billing_cycle", billingCycle),
		zap.Float64("amount", calculation.FinalAmount),
	)

	return &subscription, nil
}

// GetUserActiveSubscription retrieves the active subscription for a user
func (s *SubscriptionService) GetUserActiveSubscription(ctx context.Context, userID string) (*domain.Subscription, error) {
	var subscription domain.Subscription

	err := s.db.WithContext(ctx).
		Preload("Plan").
		Where("user_auth_id = ? AND status = ? AND end_date > ?", userID, "active", time.Now()).
		First(&subscription).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // No active subscription
		}
		s.logger.Error("Failed to fetch user subscription", zap.Error(err), zap.String("user_id", userID))
		return nil, fmt.Errorf("failed to fetch user subscription: %w", err)
	}

	return &subscription, nil
}

// UpdateSubscriptionUsage updates the listing usage for a subscription
func (s *SubscriptionService) UpdateSubscriptionUsage(ctx context.Context, subscriptionID uint, usedListings int) error {
	err := s.db.WithContext(ctx).
		Model(&domain.Subscription{}).
		Where("id = ?", subscriptionID).
		Update("used_listings", usedListings).Error

	if err != nil {
		s.logger.Error("Failed to update subscription usage", zap.Error(err), zap.Uint("subscription_id", subscriptionID))
		return fmt.Errorf("failed to update subscription usage: %w", err)
	}

	return nil
}

// CancelSubscription cancels a user's subscription
func (s *SubscriptionService) CancelSubscription(ctx context.Context, userID string) error {
	err := s.db.WithContext(ctx).
		Model(&domain.Subscription{}).
		Where("user_auth_id = ? AND status = ?", userID, "active").
		Update("status", "cancelled").Error

	if err != nil {
		s.logger.Error("Failed to cancel subscription", zap.Error(err), zap.String("user_id", userID))
		return fmt.Errorf("failed to cancel subscription: %w", err)
	}

	s.logger.Info("Subscription cancelled successfully", zap.String("user_id", userID))
	return nil
}

package services

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// NotificationService handles email and SMS notifications following Forever Plan architecture
type NotificationService struct {
	db     *pgxpool.Pool
	logger *zap.Logger
	config *NotificationConfig
}

// NotificationConfig contains notification service configuration
type NotificationConfig struct {
	SMTPHost     string
	SMTPPort     int
	SMTPUsername string
	SMTPPassword string
	FromEmail    string
	FromName     string
	SMSProvider  string
	SMSAPIKey    string
}

// EmailNotification represents an email notification
type EmailNotification struct {
	To      string
	Subject string
	Body    string
	IsHTML  bool
}

// SMSNotification represents an SMS notification
type SMSNotification struct {
	To      string
	Message string
}

// NewNotificationService creates a new notification service instance
func NewNotificationService(db *pgxpool.Pool, config *NotificationConfig, logger *zap.Logger) *NotificationService {
	return &NotificationService{
		db:     db,
		logger: logger,
		config: config,
	}
}

// SendOrderConfirmationEmail sends order confirmation email to customer
func (s *NotificationService) SendOrderConfirmationEmail(ctx context.Context, userID string, orderID int, orderTotal float64) error {
	s.logger.Info("Sending order confirmation email",
		zap.String("user_id", userID),
		zap.Int("order_id", orderID))

	// Get user email from database
	userEmail, userName, err := s.getUserEmailAndName(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user email: %w", err)
	}

	// Create email content
	subject := fmt.Sprintf("تأكيد الطلب #%d - CarNow", orderID)
	body := s.generateOrderConfirmationEmailBody(userName, orderID, orderTotal)

	// Send email
	notification := &EmailNotification{
		To:      userEmail,
		Subject: subject,
		Body:    body,
		IsHTML:  true,
	}

	err = s.sendEmail(ctx, notification)
	if err != nil {
		s.logger.Error("Failed to send order confirmation email", zap.Error(err))
		return fmt.Errorf("failed to send email: %w", err)
	}

	// Log notification in database
	err = s.logNotification(ctx, userID, "email", "order_confirmation", fmt.Sprintf("Order #%d confirmation", orderID))
	if err != nil {
		s.logger.Warn("Failed to log email notification", zap.Error(err))
		// Don't fail the operation for logging issues
	}

	s.logger.Info("Order confirmation email sent successfully",
		zap.String("user_id", userID),
		zap.Int("order_id", orderID))

	return nil
}

// SendOrderStatusUpdateEmail sends order status update email
func (s *NotificationService) SendOrderStatusUpdateEmail(ctx context.Context, userID string, orderID int, newStatus string) error {
	s.logger.Info("Sending order status update email",
		zap.String("user_id", userID),
		zap.Int("order_id", orderID),
		zap.String("new_status", newStatus))

	// Get user email from database
	userEmail, userName, err := s.getUserEmailAndName(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user email: %w", err)
	}

	// Create email content
	subject := fmt.Sprintf("تحديث حالة الطلب #%d - CarNow", orderID)
	body := s.generateOrderStatusUpdateEmailBody(userName, orderID, newStatus)

	// Send email
	notification := &EmailNotification{
		To:      userEmail,
		Subject: subject,
		Body:    body,
		IsHTML:  true,
	}

	err = s.sendEmail(ctx, notification)
	if err != nil {
		s.logger.Error("Failed to send order status update email", zap.Error(err))
		return fmt.Errorf("failed to send email: %w", err)
	}

	// Log notification in database
	err = s.logNotification(ctx, userID, "email", "order_status_update", fmt.Sprintf("Order #%d status: %s", orderID, newStatus))
	if err != nil {
		s.logger.Warn("Failed to log email notification", zap.Error(err))
	}

	s.logger.Info("Order status update email sent successfully")

	return nil
}

// SendLowStockAlert sends low stock alert to admin
func (s *NotificationService) SendLowStockAlert(ctx context.Context, productID string, productName string, currentStock int) error {
	s.logger.Info("Sending low stock alert",
		zap.String("product_id", productID),
		zap.String("product_name", productName),
		zap.Int("current_stock", currentStock))

	// For now, just log the alert
	// In production, this would send email to admin
	s.logger.Warn("Low stock alert",
		zap.String("product_id", productID),
		zap.String("product_name", productName),
		zap.Int("current_stock", currentStock))

	// Log notification in database
	err := s.logNotification(ctx, "system", "alert", "low_stock", fmt.Sprintf("Product %s low stock: %d", productName, currentStock))
	if err != nil {
		s.logger.Warn("Failed to log low stock alert", zap.Error(err))
	}

	return nil
}

// SendSellerSubscriptionStatusEmail sends subscription status update email to seller
func (s *NotificationService) SendSellerSubscriptionStatusEmail(ctx context.Context, sellerID string, status string, adminNotes string, rejectionReason string) error {
	s.logger.Info("Sending seller subscription status email",
		zap.String("seller_id", sellerID),
		zap.String("status", status))

	// Get seller email from database
	sellerEmail, sellerName, err := s.getUserEmailAndName(ctx, sellerID)
	if err != nil {
		return fmt.Errorf("failed to get seller email: %w", err)
	}

	// Create email content based on status
	var subject, body string
	switch status {
	case "approved":
		subject = "تم قبول طلب الاشتراك - CarNow"
		body = s.generateSellerSubscriptionApprovedEmailBody(sellerName, adminNotes)
	case "rejected":
		subject = "تم رفض طلب الاشتراك - CarNow"
		body = s.generateSellerSubscriptionRejectedEmailBody(sellerName, rejectionReason, adminNotes)
	default:
		subject = "تحديث حالة طلب الاشتراك - CarNow"
		body = s.generateSellerSubscriptionStatusUpdateEmailBody(sellerName, status, adminNotes)
	}

	// Send email
	notification := &EmailNotification{
		To:      sellerEmail,
		Subject: subject,
		Body:    body,
		IsHTML:  true,
	}

	err = s.sendEmail(ctx, notification)
	if err != nil {
		s.logger.Error("Failed to send seller subscription status email", zap.Error(err))
		return fmt.Errorf("failed to send email: %w", err)
	}

	// Create in-app notification in database
	var notificationTitle, notificationMessage string
	switch status {
	case "approved":
		notificationTitle = "تم قبول طلب الاشتراك"
		notificationMessage = "مبروك! تم قبول طلب الاشتراك الخاص بك. يمكنك الآن الوصول إلى جميع خصائص البائع."
	case "rejected":
		notificationTitle = "تم رفض طلب الاشتراك"
		if rejectionReason != "" {
			notificationMessage = fmt.Sprintf("نأسف لعدم قبول طلب الاشتراك. السبب: %s", rejectionReason)
		} else {
			notificationMessage = "نأسف لعدم قبول طلب الاشتراك الخاص بك في الوقت الحالي."
		}
	default:
		notificationTitle = "تحديث حالة طلب الاشتراك"
		notificationMessage = fmt.Sprintf("تم تحديث حالة طلب الاشتراك الخاص بك إلى: %s", status)
	}

	// Add notification to database
	err = s.createInAppNotification(ctx, sellerID, notificationTitle, notificationMessage, "subscription_status", "seller_subscription")
	if err != nil {
		s.logger.Warn("Failed to create in-app notification", zap.Error(err))
		// Don't fail the operation for notification issues
	}

	// Log email notification in database
	err = s.logNotification(ctx, sellerID, "email", "seller_subscription_status", fmt.Sprintf("Subscription status: %s", status))
	if err != nil {
		s.logger.Warn("Failed to log seller subscription status email notification", zap.Error(err))
	}

	s.logger.Info("Seller subscription status email and notification sent successfully")

	return nil
}

// getUserEmailAndName gets user email and name from database
func (s *NotificationService) getUserEmailAndName(ctx context.Context, userID string) (string, string, error) {
	query := `
		SELECT email, COALESCE(full_name, email) as name
		FROM auth.users 
		WHERE id = $1
	`

	var email, name string
	err := s.db.QueryRow(ctx, query, userID).Scan(&email, &name)
	if err != nil {
		return "", "", fmt.Errorf("failed to get user info: %w", err)
	}

	return email, name, nil
}

// sendEmail sends an email notification (mock implementation)
func (s *NotificationService) sendEmail(_ context.Context, notification *EmailNotification) error {
	// For now, this is a mock implementation
	// In production, this would integrate with SMTP server or email service
	s.logger.Info("Mock email sent",
		zap.String("to", notification.To),
		zap.String("subject", notification.Subject))

	return nil
}

// createInAppNotification creates an in-app notification in the database
func (s *NotificationService) createInAppNotification(ctx context.Context, userID, title, message, notificationType, relatedEntityType string) error {
	query := `
		INSERT INTO public.notifications (
			user_id, title, message, type, related_entity_type,
			is_read, is_deleted, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, false, false, NOW(), NOW())
	`

	_, err := s.db.Exec(ctx, query, userID, title, message, notificationType, relatedEntityType)
	if err != nil {
		s.logger.Error("Failed to create in-app notification", zap.Error(err))
		return fmt.Errorf("failed to create in-app notification: %w", err)
	}

	s.logger.Info("In-app notification created successfully",
		zap.String("user_id", userID),
		zap.String("title", title),
		zap.String("type", notificationType))

	return nil
}

// logNotification logs notification in database using existing table structure
func (s *NotificationService) logNotification(ctx context.Context, userID, notificationType, category, message string) error {
	query := `
		INSERT INTO public.notifications (
			user_id, title, message, type, related_entity_type,
			is_read, is_deleted, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, false, false, NOW(), NOW())
	`

	title := fmt.Sprintf("إشعار %s", category)
	_, err := s.db.Exec(ctx, query, userID, title, message, notificationType, category)
	return err
}

// generateOrderConfirmationEmailBody generates HTML email body for order confirmation
func (s *NotificationService) generateOrderConfirmationEmailBody(userName string, orderID int, orderTotal float64) string {
	return fmt.Sprintf(`
		<html>
		<body dir="rtl" style="font-family: Arial, sans-serif;">
			<h2>مرحباً %s،</h2>
			<p>شكراً لك على طلبك من CarNow!</p>
			<p><strong>رقم الطلب:</strong> #%d</p>
			<p><strong>المبلغ الإجمالي:</strong> %.3f د.ل</p>
			<p>سيتم معالجة طلبك وشحنه في أقرب وقت ممكن.</p>
			<p>يمكنك متابعة حالة طلبك من خلال التطبيق.</p>
			<br>
			<p>شكراً لاختيارك CarNow</p>
			<p>فريق CarNow</p>
		</body>
		</html>
	`, userName, orderID, orderTotal)
}

// generateOrderStatusUpdateEmailBody generates HTML email body for order status update
func (s *NotificationService) generateOrderStatusUpdateEmailBody(userName string, orderID int, newStatus string) string {
	statusText := map[string]string{
		"confirmed":  "تم تأكيد الطلب",
		"processing": "جاري المعالجة",
		"shipped":    "تم الشحن",
		"delivered":  "تم التسليم",
		"cancelled":  "تم الإلغاء",
	}

	status, exists := statusText[newStatus]
	if !exists {
		status = newStatus
	}

	return fmt.Sprintf(`
		<html>
		<body dir="rtl" style="font-family: Arial, sans-serif;">
			<h2>مرحباً %s،</h2>
			<p>تم تحديث حالة طلبك #%d</p>
			<p><strong>الحالة الجديدة:</strong> %s</p>
			<p>يمكنك متابعة تفاصيل طلبك من خلال التطبيق.</p>
			<br>
			<p>شكراً لاختيارك CarNow</p>
			<p>فريق CarNow</p>
		</body>
		</html>
	`, userName, orderID, status)
}

// generateSellerSubscriptionApprovedEmailBody generates HTML email body for approved subscription
func (s *NotificationService) generateSellerSubscriptionApprovedEmailBody(sellerName string, adminNotes string) string {
	notesSection := ""
	if adminNotes != "" {
		notesSection = fmt.Sprintf(`<p><strong>ملاحظات الإدارة:</strong> %s</p>`, adminNotes)
	}

	return fmt.Sprintf(`
		<html>
		<body dir="rtl" style="font-family: Arial, sans-serif;">
			<h2>مبروك %s!</h2>
			<p>نحن سعداء لإعلامك بأنه تم قبول طلب الاشتراك الخاص بك في CarNow.</p>
			<p>يمكنك الآن الوصول إلى جميع خصائص البائع والبدء في بيع منتجاتك.</p>
			%s
			<h3>الخطوات التالية:</h3>
			<ul>
				<li>قم بتسجيل الدخول إلى التطبيق</li>
				<li>انتقل إلى لوحة تحكم البائع</li>
				<li>ابدأ في إضافة منتجاتك</li>
				<li>قم بإعداد متجرك</li>
			</ul>
			<p>مرحباً بك في عائلة البائعين في CarNow!</p>
			<br>
			<p>فريق CarNow</p>
		</body>
		</html>
	`, sellerName, notesSection)
}

// generateSellerSubscriptionRejectedEmailBody generates HTML email body for rejected subscription
func (s *NotificationService) generateSellerSubscriptionRejectedEmailBody(sellerName string, rejectionReason string, adminNotes string) string {
	reasonSection := ""
	if rejectionReason != "" {
		reasonSection = fmt.Sprintf(`<p><strong>سبب الرفض:</strong> %s</p>`, rejectionReason)
	}

	notesSection := ""
	if adminNotes != "" {
		notesSection = fmt.Sprintf(`<p><strong>ملاحظات الإدارة:</strong> %s</p>`, adminNotes)
	}

	return fmt.Sprintf(`
		<html>
		<body dir="rtl" style="font-family: Arial, sans-serif;">
			<h2>عزيزي %s،</h2>
			<p>نأسف لإعلامك بأنه لم يتم قبول طلب الاشتراك الخاص بك في CarNow في الوقت الحالي.</p>
			%s
			%s
			<p>يمكنك تقديم طلب جديد بعد معالجة الأسباب المذكورة أعلاه.</p>
			<p>إذا كان لديك أي استفسارات، يرجى التواصل مع فريق الدعم.</p>
			<br>
			<p>فريق CarNow</p>
		</body>
		</html>
	`, sellerName, reasonSection, notesSection)
}

// generateSellerSubscriptionStatusUpdateEmailBody generates HTML email body for subscription status update
func (s *NotificationService) generateSellerSubscriptionStatusUpdateEmailBody(sellerName string, status string, adminNotes string) string {
	statusText := map[string]string{
		"pending":      "قيد الانتظار",
		"under_review": "قيد المراجعة",
		"active":       "نشط",
		"suspended":    "معلق",
		"cancelled":    "ملغي",
	}

	statusDisplay, exists := statusText[status]
	if !exists {
		statusDisplay = status
	}

	notesSection := ""
	if adminNotes != "" {
		notesSection = fmt.Sprintf(`<p><strong>ملاحظات الإدارة:</strong> %s</p>`, adminNotes)
	}

	return fmt.Sprintf(`
		<html>
		<body dir="rtl" style="font-family: Arial, sans-serif;">
			<h2>عزيزي %s،</h2>
			<p>تم تحديث حالة طلب الاشتراك الخاص بك في CarNow.</p>
			<p><strong>الحالة الجديدة:</strong> %s</p>
			%s
			<p>يمكنك متابعة حالة طلبك من خلال التطبيق.</p>
			<br>
			<p>فريق CarNow</p>
		</body>
		</html>
	`, sellerName, statusDisplay, notesSection)
}

// GetUserNotifications gets paginated notifications for a user
func (s *NotificationService) GetUserNotifications(ctx context.Context, userID string, page, limit int, unreadOnly bool) ([]map[string]interface{}, int, error) {
	offset := (page - 1) * limit

	var whereClause string
	var args []interface{}

	if unreadOnly {
		whereClause = "WHERE user_id = $1 AND is_read = false AND is_deleted = false"
		args = []interface{}{userID}
	} else {
		whereClause = "WHERE user_id = $1 AND is_deleted = false"
		args = []interface{}{userID}
	}

	// Get total count
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM public.notifications %s", whereClause)
	var total int
	err := s.db.QueryRow(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get notification count: %w", err)
	}

	// Get notifications
	query := fmt.Sprintf(`
		SELECT id, user_id, title, message, type, related_entity_type,
			   is_read, created_at, updated_at
		FROM public.notifications
		%s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, len(args)+1, len(args)+2)

	args = append(args, limit, offset)

	rows, err := s.db.Query(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query notifications: %w", err)
	}
	defer rows.Close()

	var notifications []map[string]interface{}
	for rows.Next() {
		var notification map[string]interface{} = make(map[string]interface{})
		var id, userID, title, message, notificationType, relatedEntityType string
		var isRead bool
		var createdAt, updatedAt interface{}

		err := rows.Scan(&id, &userID, &title, &message, &notificationType, &relatedEntityType, &isRead, &createdAt, &updatedAt)
		if err != nil {
			s.logger.Error("Failed to scan notification row", zap.Error(err))
			continue
		}

		notification["id"] = id
		notification["user_id"] = userID
		notification["title"] = title
		notification["message"] = message
		notification["type"] = notificationType
		notification["related_entity_type"] = relatedEntityType
		notification["is_read"] = isRead
		notification["created_at"] = createdAt
		notification["updated_at"] = updatedAt

		notifications = append(notifications, notification)
	}

	return notifications, total, nil
}

// MarkNotificationAsRead marks a specific notification as read
func (s *NotificationService) MarkNotificationAsRead(ctx context.Context, notificationID, userID string) error {
	query := `
		UPDATE public.notifications
		SET is_read = true, updated_at = NOW()
		WHERE id = $1 AND user_id = $2 AND is_deleted = false
	`

	result, err := s.db.Exec(ctx, query, notificationID, userID)
	if err != nil {
		return fmt.Errorf("failed to mark notification as read: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("notification not found or already deleted")
	}

	s.logger.Info("Notification marked as read",
		zap.String("notification_id", notificationID),
		zap.String("user_id", userID))

	return nil
}

// MarkAllNotificationsAsRead marks all notifications as read for a user
func (s *NotificationService) MarkAllNotificationsAsRead(ctx context.Context, userID string) error {
	query := `
		UPDATE public.notifications
		SET is_read = true, updated_at = NOW()
		WHERE user_id = $1 AND is_read = false AND is_deleted = false
	`

	result, err := s.db.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to mark all notifications as read: %w", err)
	}

	rowsAffected := result.RowsAffected()
	s.logger.Info("All notifications marked as read",
		zap.String("user_id", userID),
		zap.Int64("rows_affected", rowsAffected))

	return nil
}

// DeleteNotification soft deletes a notification
func (s *NotificationService) DeleteNotification(ctx context.Context, notificationID, userID string) error {
	query := `
		UPDATE public.notifications
		SET is_deleted = true, updated_at = NOW()
		WHERE id = $1 AND user_id = $2
	`

	result, err := s.db.Exec(ctx, query, notificationID, userID)
	if err != nil {
		return fmt.Errorf("failed to delete notification: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("notification not found")
	}

	s.logger.Info("Notification deleted",
		zap.String("notification_id", notificationID),
		zap.String("user_id", userID))

	return nil
}

// GetUnreadNotificationCount gets the count of unread notifications for a user
func (s *NotificationService) GetUnreadNotificationCount(ctx context.Context, userID string) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM public.notifications
		WHERE user_id = $1 AND is_read = false AND is_deleted = false
	`

	var count int
	err := s.db.QueryRow(ctx, query, userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get unread notification count: %w", err)
	}

	return count, nil
}

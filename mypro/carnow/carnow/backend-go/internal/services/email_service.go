package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/smtp"
	"strings"
	"time"

	"carnow-backend/internal/config"

	"go.uber.org/zap"
)

// EmailService handles email operations
type EmailService struct {
	config *config.Config
	logger *zap.Logger
	client *http.Client
}

// EmailProvider represents different email providers
type EmailProvider string

const (
	EmailProviderSMTP     EmailProvider = "smtp"
	EmailProviderSendGrid EmailProvider = "sendgrid"
	EmailProviderAWSSES   EmailProvider = "aws_ses"
	EmailProviderLocal    EmailProvider = "local" // For development
)

// EmailRequest represents an email request
type EmailRequest struct {
	To      []string `json:"to"`
	Subject string   `json:"subject"`
	Body    string   `json:"body"`
	IsHTML  bool     `json:"is_html"`
	From    string   `json:"from,omitempty"`
}

// EmailResponse represents an email response
type EmailResponse struct {
	Success   bool   `json:"success"`
	MessageID string `json:"message_id,omitempty"`
	Error     string `json:"error,omitempty"`
}

// SendGridRequest represents SendGrid API request
type SendGridRequest struct {
	Personalizations []SendGridPersonalization `json:"personalizations"`
	From             SendGridEmail             `json:"from"`
	Subject          string                    `json:"subject"`
	Content          []SendGridContent         `json:"content"`
}

type SendGridPersonalization struct {
	To []SendGridEmail `json:"to"`
}

type SendGridEmail struct {
	Email string `json:"email"`
	Name  string `json:"name,omitempty"`
}

type SendGridContent struct {
	Type  string `json:"type"`
	Value string `json:"value"`
}

// NewEmailService creates a new email service
func NewEmailService(config *config.Config, logger *zap.Logger) *EmailService {
	return &EmailService{
		config: config,
		logger: logger,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// SendEmail sends an email
func (s *EmailService) SendEmail(to, subject, body string) error {
	return s.SendEmailToMultiple([]string{to}, subject, body, true)
}

// SendEmailToMultiple sends email to multiple recipients
func (s *EmailService) SendEmailToMultiple(to []string, subject, body string, isHTML bool) error {
	// Validate email addresses
	for _, email := range to {
		if !s.isValidEmail(email) {
			return fmt.Errorf("invalid email address: %s", email)
		}
	}

	// Get email provider from config
	provider := s.getEmailProvider()

	switch provider {
	case EmailProviderSMTP:
		return s.sendSMTPEmail(to, subject, body, isHTML)
	case EmailProviderSendGrid:
		return s.sendSendGridEmail(to, subject, body, isHTML)
	case EmailProviderAWSSES:
		return s.sendAWSSESEmail(to, subject, body, isHTML)
	case EmailProviderLocal:
		return s.sendLocalEmail(to, subject, body, isHTML)
	default:
		return fmt.Errorf("unsupported email provider: %s", provider)
	}
}

// sendSMTPEmail sends email via SMTP
func (s *EmailService) sendSMTPEmail(to []string, subject, body string, isHTML bool) error {
	// Get SMTP configuration
	host := s.config.Email.SMTPHost
	port := s.config.Email.SMTPPort
	username := s.config.Email.SMTPUsername
	password := s.config.Email.SMTPPassword
	fromEmail := s.config.Email.FromEmail

	if host == "" || username == "" || password == "" {
		return fmt.Errorf("SMTP configuration not complete")
	}

	// Setup authentication
	auth := smtp.PlainAuth("", username, password, host)

	// Prepare message
	contentType := "text/plain"
	if isHTML {
		contentType = "text/html"
	}

	message := fmt.Sprintf("From: %s\r\n", fromEmail)
	message += fmt.Sprintf("To: %s\r\n", strings.Join(to, ","))
	message += fmt.Sprintf("Subject: %s\r\n", subject)
	message += fmt.Sprintf("Content-Type: %s; charset=UTF-8\r\n", contentType)
	message += "\r\n"
	message += body

	// Send email
	addr := fmt.Sprintf("%s:%d", host, port)
	err := smtp.SendMail(addr, auth, fromEmail, to, []byte(message))
	if err != nil {
		s.logger.Error("Failed to send SMTP email",
			zap.Strings("to", to),
			zap.String("subject", subject),
			zap.Error(err),
		)
		return fmt.Errorf("failed to send email via SMTP: %w", err)
	}

	s.logger.Info("Email sent successfully via SMTP",
		zap.Strings("to", to),
		zap.String("subject", subject),
	)

	return nil
}

// sendSendGridEmail sends email via SendGrid
func (s *EmailService) sendSendGridEmail(to []string, subject, body string, isHTML bool) error {
	apiKey := s.config.Email.SendGridAPIKey
	fromEmail := s.config.Email.FromEmail
	fromName := s.config.Email.FromName

	if apiKey == "" {
		return fmt.Errorf("SendGrid API key not configured")
	}

	// Prepare recipients
	var recipients []SendGridEmail
	for _, email := range to {
		recipients = append(recipients, SendGridEmail{Email: email})
	}

	// Prepare content type
	contentType := "text/plain"
	if isHTML {
		contentType = "text/html"
	}

	// Prepare request
	request := SendGridRequest{
		Personalizations: []SendGridPersonalization{
			{To: recipients},
		},
		From:    SendGridEmail{Email: fromEmail, Name: fromName},
		Subject: subject,
		Content: []SendGridContent{
			{Type: contentType, Value: body},
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal SendGrid request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", "https://api.sendgrid.com/v3/mail/send", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create SendGrid request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := s.client.Do(req)
	if err != nil {
		s.logger.Error("Failed to send SendGrid email",
			zap.Strings("to", to),
			zap.String("subject", subject),
			zap.Error(err),
		)
		return fmt.Errorf("failed to send email via SendGrid: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 202 {
		return fmt.Errorf("SendGrid API returned status: %d", resp.StatusCode)
	}

	s.logger.Info("Email sent successfully via SendGrid",
		zap.Strings("to", to),
		zap.String("subject", subject),
	)

	return nil
}

// sendAWSSESEmail sends email via AWS SES
func (s *EmailService) sendAWSSESEmail(to []string, subject, body string, isHTML bool) error {
	// TODO: Implement AWS SES email sending
	// This would require AWS SDK integration
	s.logger.Info("AWS SES email sending not implemented yet",
		zap.Strings("to", to),
		zap.String("subject", subject),
	)
	return fmt.Errorf("AWS SES email not implemented")
}

// sendLocalEmail simulates email sending for development
func (s *EmailService) sendLocalEmail(to []string, subject, body string, isHTML bool) error {
	s.logger.Info("Local email simulation",
		zap.Strings("to", to),
		zap.String("subject", subject),
	)

	// In development, just log the email
	fmt.Printf("\n=== EMAIL SIMULATION ===\n")
	fmt.Printf("To: %s\n", strings.Join(to, ", "))
	fmt.Printf("Subject: %s\n", subject)
	fmt.Printf("Content Type: %s\n", map[bool]string{true: "HTML", false: "Plain Text"}[isHTML])
	fmt.Printf("Body:\n%s\n", body)
	fmt.Printf("========================\n\n")

	return nil
}

// isValidEmail validates email address format
func (s *EmailService) isValidEmail(email string) bool {
	// Basic email validation
	if !strings.Contains(email, "@") {
		return false
	}

	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}

	if len(parts[0]) == 0 || len(parts[1]) == 0 {
		return false
	}

	if !strings.Contains(parts[1], ".") {
		return false
	}

	return true
}

// getEmailProvider returns the configured email provider
func (s *EmailService) getEmailProvider() EmailProvider {
	if s.config.Email.Provider != "" {
		return EmailProvider(s.config.Email.Provider)
	}
	
	// Default to local for development
	return EmailProviderLocal
}

// SendTemplateEmail sends an email using a template
func (s *EmailService) SendTemplateEmail(to []string, templateID string, data map[string]interface{}) error {
	// TODO: Implement template-based email sending
	return fmt.Errorf("template email not implemented")
}

// SendWelcomeEmail sends a welcome email to new users
func (s *EmailService) SendWelcomeEmail(to, userName string) error {
	subject := "مرحباً بك في CarNow!"
	body := fmt.Sprintf(`
		<div dir="rtl" style="font-family: Arial, sans-serif;">
			<h1 style="color: #1B5E20;">مرحباً %s!</h1>
			<p>نرحب بك في CarNow، منصتك المفضلة لقطع غيار السيارات.</p>
			<p>يمكنك الآن:</p>
			<ul>
				<li>تصفح آلاف قطع الغيار</li>
				<li>مقارنة الأسعار</li>
				<li>الطلب بسهولة</li>
				<li>تتبع طلباتك</li>
			</ul>
			<p>نتمنى لك تجربة رائعة!</p>
			<br>
			<p>فريق CarNow</p>
		</div>
	`, userName)

	return s.SendEmail(to, subject, body)
}

// SendPasswordResetEmail sends a password reset email
func (s *EmailService) SendPasswordResetEmail(to, resetToken string) error {
	subject := "إعادة تعيين كلمة المرور - CarNow"
	resetURL := fmt.Sprintf("%s/reset-password?token=%s", s.config.App.FrontendURL, resetToken)
	
	body := fmt.Sprintf(`
		<div dir="rtl" style="font-family: Arial, sans-serif;">
			<h2>إعادة تعيين كلمة المرور</h2>
			<p>تم طلب إعادة تعيين كلمة المرور لحسابك في CarNow.</p>
			<p>اضغط على الرابط التالي لإعادة تعيين كلمة المرور:</p>
			<a href="%s" style="background-color: #1B5E20; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">إعادة تعيين كلمة المرور</a>
			<p>هذا الرابط صالح لمدة 24 ساعة فقط.</p>
			<p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذه الرسالة.</p>
			<br>
			<p>فريق CarNow</p>
		</div>
	`, resetURL)

	return s.SendEmail(to, subject, body)
}

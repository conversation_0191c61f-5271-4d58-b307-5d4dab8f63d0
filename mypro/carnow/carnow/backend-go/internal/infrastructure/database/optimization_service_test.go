package database

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestOptimizationService_CalculatePerformanceScore(t *testing.T) {
	os := &OptimizationService{}

	tests := []struct {
		name     string
		report   *OptimizationReport
		expected float64
	}{
		{
			name: "perfect performance",
			report: &OptimizationReport{
				PerformanceMetrics: &PerformanceMetrics{
					TotalQueries:   1000,
					SlowQueries:    0,
					AverageLatency: 50 * time.Millisecond,
				},
				DatabaseStats: map[string]interface{}{
					"cache_hit_ratio": 0.98,
				},
				IndexSuggestions: []IndexSuggestion{},
			},
			expected: 10.0,
		},
		{
			name: "poor performance with slow queries",
			report: &OptimizationReport{
				PerformanceMetrics: &PerformanceMetrics{
					TotalQueries:   1000,
					SlowQueries:    100, // 10% slow queries
					AverageLatency: 200 * time.Millisecond,
				},
				DatabaseStats: map[string]interface{}{
					"cache_hit_ratio": 0.85, // Poor cache hit ratio
				},
				IndexSuggestions: []IndexSuggestion{
					{Priority: "high"},
					{Priority: "high"},
					{Priority: "medium"},
				},
			},
			expected: 5.0, // Should be around 5.0 due to multiple issues
		},
		{
			name: "moderate performance",
			report: &OptimizationReport{
				PerformanceMetrics: &PerformanceMetrics{
					TotalQueries:   1000,
					SlowQueries:    20, // 2% slow queries
					AverageLatency: 80 * time.Millisecond,
				},
				DatabaseStats: map[string]interface{}{
					"cache_hit_ratio": 0.96,
				},
				IndexSuggestions: []IndexSuggestion{
					{Priority: "medium"},
				},
			},
			expected: 9.4, // Should be around 9.4
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			score := os.calculatePerformanceScore(tt.report)
			assert.InDelta(t, tt.expected, score, 0.5, "Score should be within 0.5 of expected")
		})
	}
}

func TestOptimizationService_GenerateRecommendations(t *testing.T) {
	os := &OptimizationService{}

	report := &OptimizationReport{
		PerformanceMetrics: &PerformanceMetrics{
			TotalQueries:   1000,
			SlowQueries:    50,
			AverageLatency: 150 * time.Millisecond,
		},
		DatabaseStats: map[string]interface{}{
			"cache_hit_ratio": 0.85,
			"connection_pool": map[string]interface{}{
				"max_conns":      int32(10),
				"acquired_conns": int32(9),
			},
		},
		IndexSuggestions: []IndexSuggestion{
			{Priority: "high", TableName: "users", ColumnNames: []string{"email"}},
			{Priority: "high", TableName: "orders", ColumnNames: []string{"user_id"}},
			{Priority: "medium", TableName: "products", ColumnNames: []string{"category"}},
		},
		SlowQueries: []SlowQuery{
			{Query: "SELECT * FROM users WHERE email = ?", ExecutionTime: 200 * time.Millisecond},
		},
	}

	recommendations := os.generateRecommendations(report)

	// Should have multiple recommendations
	assert.Greater(t, len(recommendations), 3)

	// Check for specific recommendations
	recommendationText := ""
	for _, rec := range recommendations {
		recommendationText += rec + " "
	}

	assert.Contains(t, recommendationText, "slow queries")
	assert.Contains(t, recommendationText, "latency")
	assert.Contains(t, recommendationText, "cache hit ratio")
	assert.Contains(t, recommendationText, "indexes")
	assert.Contains(t, recommendationText, "connection pool")
}

func TestOptimizationService_GenerateCommonQueryPatterns(t *testing.T) {
	os := &OptimizationService{}

	patterns := os.generateCommonQueryPatterns("public.users")

	assert.Greater(t, len(patterns), 0)
	assert.Contains(t, patterns[0], "public.users")
	assert.Contains(t, patterns[0], "WHERE")

	// Check that all patterns contain the table name
	for _, pattern := range patterns {
		assert.Contains(t, pattern, "public.users")
	}
}

func BenchmarkOptimizationService_CalculatePerformanceScore(b *testing.B) {
	os := &OptimizationService{}
	report := &OptimizationReport{
		PerformanceMetrics: &PerformanceMetrics{
			TotalQueries:   1000,
			SlowQueries:    50,
			AverageLatency: 150 * time.Millisecond,
		},
		DatabaseStats: map[string]interface{}{
			"cache_hit_ratio": 0.85,
		},
		IndexSuggestions: []IndexSuggestion{
			{Priority: "high"},
			{Priority: "medium"},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = os.calculatePerformanceScore(report)
	}
}

func BenchmarkOptimizationService_GenerateRecommendations(b *testing.B) {
	os := &OptimizationService{}
	report := &OptimizationReport{
		PerformanceMetrics: &PerformanceMetrics{
			TotalQueries:   1000,
			SlowQueries:    50,
			AverageLatency: 150 * time.Millisecond,
		},
		DatabaseStats: map[string]interface{}{
			"cache_hit_ratio": 0.85,
		},
		IndexSuggestions: []IndexSuggestion{
			{Priority: "high"},
			{Priority: "medium"},
		},
		SlowQueries: []SlowQuery{
			{Query: "SELECT * FROM users", ExecutionTime: 200 * time.Millisecond},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = os.generateRecommendations(report)
	}
}

package database

import (
	"context"
	"fmt"
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
)

// QueryOptimizer provides database query optimization and analysis capabilities
type QueryOptimizer struct {
	pool   *pgxpool.Pool
	logger *log.Logger
}

// QueryPlan represents the execution plan for a database query
type QueryPlan struct {
	Query         string                   `json:"query"`
	ExecutionPlan []map[string]interface{} `json:"execution_plan"`
	Cost          float64                  `json:"cost"`
	Rows          int64                    `json:"rows"`
	Width         int                      `json:"width"`
	ActualTime    float64                  `json:"actual_time_ms"`
	Suggestions   []string                 `json:"suggestions"`
}

// IndexSuggestion represents a suggested database index
type IndexSuggestion struct {
	TableName   string   `json:"table_name"`
	ColumnNames []string `json:"column_names"`
	IndexType   string   `json:"index_type"`
	Reason      string   `json:"reason"`
	Priority    string   `json:"priority"`
	CreateSQL   string   `json:"create_sql"`
}

// SlowQuery represents a slow query that needs optimization
type SlowQuery struct {
	Query         string        `json:"query"`
	ExecutionTime time.Duration `json:"execution_time"`
	CallCount     int64         `json:"call_count"`
	MeanTime      time.Duration `json:"mean_time"`
	TotalTime     time.Duration `json:"total_time"`
	FirstSeen     time.Time     `json:"first_seen"`
	LastSeen      time.Time     `json:"last_seen"`
}

// NewQueryOptimizer creates a new query optimizer instance
func NewQueryOptimizer(pool *pgxpool.Pool) *QueryOptimizer {
	return &QueryOptimizer{
		pool:   pool,
		logger: log.New(log.Writer(), "[QueryOptimizer] ", log.LstdFlags),
	}
}

// AnalyzeQuery analyzes a query and returns its execution plan with optimization suggestions
func (qo *QueryOptimizer) AnalyzeQuery(ctx context.Context, query string) (*QueryPlan, error) {
	qo.logger.Printf("🔍 Analyzing query: %s", truncateQuery(query))

	// Get query execution plan
	explainQuery := fmt.Sprintf("EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) %s", query)

	var planJSON []byte
	err := qo.pool.QueryRow(ctx, explainQuery).Scan(&planJSON)
	if err != nil {
		return nil, fmt.Errorf("failed to get query plan: %w", err)
	}

	// Parse the execution plan (simplified for this implementation)
	plan := &QueryPlan{
		Query:         query,
		ExecutionPlan: []map[string]interface{}{},
		Suggestions:   []string{},
	}

	// Extract basic metrics from plan
	if err := qo.extractPlanMetrics(plan, planJSON); err != nil {
		qo.logger.Printf("⚠️ Failed to extract plan metrics: %v", err)
	}

	// Generate optimization suggestions
	plan.Suggestions = qo.generateOptimizationSuggestions(query, plan)

	qo.logger.Printf("✅ Query analysis complete. Cost: %.2f, Suggestions: %d",
		plan.Cost, len(plan.Suggestions))

	return plan, nil
}

// SuggestIndexes analyzes table usage patterns and suggests optimal indexes
func (qo *QueryOptimizer) SuggestIndexes(ctx context.Context, tableName string, queries []string) ([]IndexSuggestion, error) {
	qo.logger.Printf("🔍 Analyzing index suggestions for table: %s", tableName)

	suggestions := []IndexSuggestion{}

	// Analyze WHERE clause patterns
	whereColumns := qo.extractWhereColumns(queries)
	for _, column := range whereColumns {
		suggestion := IndexSuggestion{
			TableName:   tableName,
			ColumnNames: []string{column},
			IndexType:   "btree",
			Reason:      "Frequently used in WHERE clauses",
			Priority:    "high",
			CreateSQL:   fmt.Sprintf("CREATE INDEX CONCURRENTLY idx_%s_%s ON %s (%s);", tableName, column, tableName, column),
		}
		suggestions = append(suggestions, suggestion)
	}

	// Analyze JOIN patterns
	joinColumns := qo.extractJoinColumns(queries)
	for _, column := range joinColumns {
		suggestion := IndexSuggestion{
			TableName:   tableName,
			ColumnNames: []string{column},
			IndexType:   "btree",
			Reason:      "Used in JOIN operations",
			Priority:    "medium",
			CreateSQL:   fmt.Sprintf("CREATE INDEX CONCURRENTLY idx_%s_%s_join ON %s (%s);", tableName, column, tableName, column),
		}
		suggestions = append(suggestions, suggestion)
	}

	// Analyze ORDER BY patterns
	orderColumns := qo.extractOrderByColumns(queries)
	for _, column := range orderColumns {
		suggestion := IndexSuggestion{
			TableName:   tableName,
			ColumnNames: []string{column},
			IndexType:   "btree",
			Reason:      "Used in ORDER BY clauses",
			Priority:    "medium",
			CreateSQL:   fmt.Sprintf("CREATE INDEX CONCURRENTLY idx_%s_%s_order ON %s (%s);", tableName, column, tableName, column),
		}
		suggestions = append(suggestions, suggestion)
	}

	qo.logger.Printf("✅ Generated %d index suggestions for table %s", len(suggestions), tableName)
	return suggestions, nil
}

// OptimizeQuery attempts to optimize a query by rewriting it
func (qo *QueryOptimizer) OptimizeQuery(query string) (string, error) {
	qo.logger.Printf("🔧 Optimizing query: %s", truncateQuery(query))

	optimizedQuery := query

	// Apply common optimization patterns
	optimizedQuery = qo.optimizeSelectClauses(optimizedQuery)
	optimizedQuery = qo.optimizeWhereClauses(optimizedQuery)
	optimizedQuery = qo.optimizeJoins(optimizedQuery)
	optimizedQuery = qo.optimizeSubqueries(optimizedQuery)

	if optimizedQuery != query {
		qo.logger.Printf("✅ Query optimized successfully")
	} else {
		qo.logger.Printf("ℹ️ No optimizations applied")
	}

	return optimizedQuery, nil
}

// GetSlowQueries retrieves slow queries from pg_stat_statements (if available)
func (qo *QueryOptimizer) GetSlowQueries(ctx context.Context, limit int) ([]SlowQuery, error) {
	qo.logger.Printf("🔍 Retrieving slow queries (limit: %d)", limit)

	// Check if pg_stat_statements extension is available
	var extensionExists bool
	err := qo.pool.QueryRow(ctx,
		"SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements')").Scan(&extensionExists)
	if err != nil {
		return nil, fmt.Errorf("failed to check pg_stat_statements extension: %w", err)
	}

	if !extensionExists {
		qo.logger.Printf("⚠️ pg_stat_statements extension not available")
		return []SlowQuery{}, nil
	}

	// Query slow queries from pg_stat_statements
	slowQueriesSQL := `
		SELECT 
			query,
			mean_exec_time,
			calls,
			total_exec_time,
			min_exec_time,
			max_exec_time
		FROM pg_stat_statements 
		WHERE calls > 10 
		ORDER BY mean_exec_time DESC 
		LIMIT $1`

	rows, err := qo.pool.Query(ctx, slowQueriesSQL, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query slow queries: %w", err)
	}
	defer rows.Close()

	var slowQueries []SlowQuery
	for rows.Next() {
		var sq SlowQuery
		var meanTime, totalTime, minTime, maxTime float64

		err := rows.Scan(&sq.Query, &meanTime, &sq.CallCount, &totalTime, &minTime, &maxTime)
		if err != nil {
			qo.logger.Printf("⚠️ Failed to scan slow query row: %v", err)
			continue
		}

		sq.MeanTime = time.Duration(meanTime * float64(time.Millisecond))
		sq.TotalTime = time.Duration(totalTime * float64(time.Millisecond))
		sq.ExecutionTime = time.Duration(maxTime * float64(time.Millisecond))
		sq.FirstSeen = time.Now().Add(-24 * time.Hour) // Approximate
		sq.LastSeen = time.Now()

		slowQueries = append(slowQueries, sq)
	}

	qo.logger.Printf("✅ Retrieved %d slow queries", len(slowQueries))
	return slowQueries, nil
}

// extractPlanMetrics extracts basic metrics from the execution plan JSON
func (qo *QueryOptimizer) extractPlanMetrics(plan *QueryPlan, planJSON []byte) error {
	// This is a simplified implementation
	// In a real implementation, you would parse the JSON properly
	planStr := string(planJSON)

	// Extract cost estimate (simplified regex parsing)
	costRegex := regexp.MustCompile(`"Total Cost":\s*([0-9.]+)`)
	if matches := costRegex.FindStringSubmatch(planStr); len(matches) > 1 {
		fmt.Sscanf(matches[1], "%f", &plan.Cost)
	}

	// Extract row estimate
	rowsRegex := regexp.MustCompile(`"Plan Rows":\s*([0-9]+)`)
	if matches := rowsRegex.FindStringSubmatch(planStr); len(matches) > 1 {
		fmt.Sscanf(matches[1], "%d", &plan.Rows)
	}

	return nil
}

// generateOptimizationSuggestions generates optimization suggestions based on query analysis
func (qo *QueryOptimizer) generateOptimizationSuggestions(query string, plan *QueryPlan) []string {
	suggestions := []string{}
	queryLower := strings.ToLower(query)

	// Check for missing WHERE clauses
	if strings.Contains(queryLower, "select") && !strings.Contains(queryLower, "where") {
		suggestions = append(suggestions, "Consider adding WHERE clause to limit result set")
	}

	// Check for SELECT *
	if strings.Contains(queryLower, "select *") {
		suggestions = append(suggestions, "Avoid SELECT *, specify only needed columns")
	}

	// Check for high cost queries
	if plan.Cost > 1000 {
		suggestions = append(suggestions, "High cost query detected, consider adding indexes")
	}

	// Check for large result sets
	if plan.Rows > 10000 {
		suggestions = append(suggestions, "Large result set, consider adding LIMIT clause")
	}

	// Check for subqueries that could be JOINs
	if strings.Contains(queryLower, "in (select") {
		suggestions = append(suggestions, "Consider converting subquery to JOIN for better performance")
	}

	return suggestions
}

// extractWhereColumns extracts column names used in WHERE clauses
func (qo *QueryOptimizer) extractWhereColumns(queries []string) []string {
	columns := make(map[string]bool)
	// Updated regex to handle more WHERE clause patterns
	whereRegex := regexp.MustCompile(`WHERE\s+(?:\w+\.)?\s*(\w+)\s*[=<>!]|AND\s+(?:\w+\.)?\s*(\w+)\s*[=<>!]`)

	for _, query := range queries {
		matches := whereRegex.FindAllStringSubmatch(strings.ToUpper(query), -1)
		for _, match := range matches {
			// Check both capture groups
			if len(match) > 1 && match[1] != "" {
				columns[strings.ToLower(match[1])] = true
			}
			if len(match) > 2 && match[2] != "" {
				columns[strings.ToLower(match[2])] = true
			}
		}
	}

	result := make([]string, 0, len(columns))
	for col := range columns {
		result = append(result, col)
	}
	return result
}

// extractJoinColumns extracts column names used in JOIN conditions
func (qo *QueryOptimizer) extractJoinColumns(queries []string) []string {
	columns := make(map[string]bool)
	joinRegex := regexp.MustCompile(`JOIN\s+\w+\s+ON\s+\w+\.(\w+)\s*=`)

	for _, query := range queries {
		matches := joinRegex.FindAllStringSubmatch(strings.ToUpper(query), -1)
		for _, match := range matches {
			if len(match) > 1 {
				columns[strings.ToLower(match[1])] = true
			}
		}
	}

	result := make([]string, 0, len(columns))
	for col := range columns {
		result = append(result, col)
	}
	return result
}

// extractOrderByColumns extracts column names used in ORDER BY clauses
func (qo *QueryOptimizer) extractOrderByColumns(queries []string) []string {
	columns := make(map[string]bool)
	orderRegex := regexp.MustCompile(`ORDER\s+BY\s+(\w+)`)

	for _, query := range queries {
		matches := orderRegex.FindAllStringSubmatch(strings.ToUpper(query), -1)
		for _, match := range matches {
			if len(match) > 1 {
				columns[strings.ToLower(match[1])] = true
			}
		}
	}

	result := make([]string, 0, len(columns))
	for col := range columns {
		result = append(result, col)
	}
	return result
}

// optimizeSelectClauses optimizes SELECT clauses
func (qo *QueryOptimizer) optimizeSelectClauses(query string) string {
	// Replace SELECT * with specific columns (placeholder implementation)
	if strings.Contains(strings.ToLower(query), "select *") {
		qo.logger.Printf("⚠️ SELECT * detected - consider specifying columns explicitly")
	}
	return query
}

// optimizeWhereClauses optimizes WHERE clauses
func (qo *QueryOptimizer) optimizeWhereClauses(query string) string {
	// Add optimizations for WHERE clauses
	return query
}

// optimizeJoins optimizes JOIN operations
func (qo *QueryOptimizer) optimizeJoins(query string) string {
	// Add JOIN optimizations
	return query
}

// optimizeSubqueries optimizes subqueries
func (qo *QueryOptimizer) optimizeSubqueries(query string) string {
	// Convert IN subqueries to JOINs where appropriate
	return query
}

// QueryOptimizerMetrics holds metrics for the query optimizer
type QueryOptimizerMetrics struct {
	QueriesAnalyzed      int64         `json:"queries_analyzed"`
	OptimizationsApplied int64         `json:"optimizations_applied"`
	AverageQueryTime     time.Duration `json:"average_query_time"`
	IndexSuggestions     int64         `json:"index_suggestions"`
}

// GetMetrics returns query optimizer metrics
func (qo *QueryOptimizer) GetMetrics() *QueryOptimizerMetrics {
	// For now, return basic metrics
	// In a real implementation, you would track these metrics
	return &QueryOptimizerMetrics{
		QueriesAnalyzed:      0,
		OptimizationsApplied: 0,
		AverageQueryTime:     0,
		IndexSuggestions:     0,
	}
}

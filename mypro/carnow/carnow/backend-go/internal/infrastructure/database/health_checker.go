package database

import (
	"context"
	"sync"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// DatabaseHealthChecker monitors database health and manages failover
type DatabaseHealthChecker struct {
	db     *ResilientDatabase
	config DatabaseHealthConfig
	logger *zap.Logger

	// Health state
	isRunning           bool
	consecutiveFailures int
	lastCheckTime       time.Time
	mutex               sync.RWMutex

	// Control channels
	stopChan   chan struct{}
	health<PERSON>han chan bool

	// Ticker for periodic health checks
	ticker *time.Ticker
}

// NewDatabaseHealthChecker creates a new database health checker
func NewDatabaseHealthChecker(db *ResilientDatabase, config DatabaseHealthConfig, logger *zap.Logger) *DatabaseHealthChecker {
	return &DatabaseHealthChecker{
		db:         db,
		config:     config,
		logger:     logger,
		stopChan:   make(chan struct{}),
		healthChan: make(chan bool, 10),
	}
}

// Start begins the health monitoring process
func (hc *DatabaseHealthChecker) Start() {
	if !hc.config.Enabled {
		hc.logger.Info("Database health checking is disabled")
		return
	}

	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	if hc.isRunning {
		hc.logger.Warn("Health checker is already running")
		return
	}

	hc.isRunning = true
	hc.ticker = time.NewTicker(hc.config.CheckInterval)

	hc.logger.Info("Starting database health checker",
		zap.Duration("check_interval", hc.config.CheckInterval),
		zap.Duration("timeout", hc.config.Timeout),
		zap.Int("failure_threshold", hc.config.FailureThreshold),
	)

	go hc.run()
}

// Stop stops the health monitoring process
func (hc *DatabaseHealthChecker) Stop() {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	if !hc.isRunning {
		return
	}

	hc.isRunning = false
	if hc.ticker != nil {
		hc.ticker.Stop()
	}

	close(hc.stopChan)
	hc.logger.Info("Database health checker stopped")
}

// run is the main health checking loop
func (hc *DatabaseHealthChecker) run() {
	// Perform initial health check
	hc.performHealthCheck()

	for {
		select {
		case <-hc.ticker.C:
			hc.performHealthCheck()

		case <-hc.stopChan:
			return
		}
	}
}

// performHealthCheck executes a health check on the database
func (hc *DatabaseHealthChecker) performHealthCheck() {
	ctx, cancel := context.WithTimeout(context.Background(), hc.config.Timeout)
	defer cancel()

	start := time.Now()
	isHealthy := hc.checkDatabaseHealth(ctx)
	duration := time.Since(start)

	hc.mutex.Lock()
	hc.lastCheckTime = time.Now()
	hc.mutex.Unlock()

	if isHealthy {
		hc.handleHealthyCheck(duration)
	} else {
		hc.handleUnhealthyCheck(duration)
	}

	// Update database health status
	hc.db.mutex.Lock()
	hc.db.isHealthy = isHealthy
	if isHealthy {
		hc.db.metrics.HealthChecksPassed++
	} else {
		hc.db.metrics.HealthChecksFailed++
	}
	hc.db.metrics.LastHealthCheck = time.Now()
	hc.db.mutex.Unlock()

	// Send health status to channel for monitoring
	select {
	case hc.healthChan <- isHealthy:
	default:
		// Channel is full, skip
	}
}

// checkDatabaseHealth performs the actual health check operations
func (hc *DatabaseHealthChecker) checkDatabaseHealth(ctx context.Context) bool {
	// Check primary database
	if !hc.checkPoolHealth(ctx, hc.db.primaryPool, "primary") {
		return false
	}

	// Check replica database if available
	if hc.db.replicaPool != nil {
		if !hc.checkPoolHealth(ctx, hc.db.replicaPool, "replica") {
			hc.logger.Warn("Read replica health check failed, switching to primary only")
			hc.triggerReplicaFailover()
		}
	}

	return true
}

// checkPoolHealth checks the health of a specific connection pool
func (hc *DatabaseHealthChecker) checkPoolHealth(ctx context.Context, pool *pgxpool.Pool, poolName string) bool {
	if pool == nil {
		return false
	}

	// Test with a simple ping
	if err := pool.Ping(ctx); err != nil {
		hc.logger.Error("Database ping failed",
			zap.String("pool", poolName),
			zap.Error(err),
		)
		return false
	}

	// Test with a simple query
	var result int
	err := pool.QueryRow(ctx, "SELECT 1").Scan(&result)
	if err != nil {
		hc.logger.Error("Database query test failed",
			zap.String("pool", poolName),
			zap.Error(err),
		)
		return false
	}

	if result != 1 {
		hc.logger.Error("Database query returned unexpected result",
			zap.String("pool", poolName),
			zap.Int("expected", 1),
			zap.Int("actual", result),
		)
		return false
	}

	// Check connection pool statistics
	stat := pool.Stat()
	if stat.AcquiredConns() > int32(float64(stat.MaxConns())*0.9) {
		hc.logger.Warn("Connection pool nearly exhausted",
			zap.String("pool", poolName),
			zap.Int32("acquired", stat.AcquiredConns()),
			zap.Int32("max", stat.MaxConns()),
		)
	}

	return true
}

// handleHealthyCheck processes a successful health check
func (hc *DatabaseHealthChecker) handleHealthyCheck(duration time.Duration) {
	hc.mutex.Lock()
	wasUnhealthy := hc.consecutiveFailures > 0
	hc.consecutiveFailures = 0
	hc.mutex.Unlock()

	if wasUnhealthy {
		hc.logger.Info("Database health recovered",
			zap.Duration("check_duration", duration),
		)
	} else {
		hc.logger.Debug("Database health check passed",
			zap.Duration("check_duration", duration),
		)
	}
}

// handleUnhealthyCheck processes a failed health check
func (hc *DatabaseHealthChecker) handleUnhealthyCheck(duration time.Duration) {
	hc.mutex.Lock()
	hc.consecutiveFailures++
	failures := hc.consecutiveFailures
	hc.mutex.Unlock()

	hc.logger.Warn("Database health check failed",
		zap.Int("consecutive_failures", failures),
		zap.Int("failure_threshold", hc.config.FailureThreshold),
		zap.Duration("check_duration", duration),
	)

	// Trigger actions based on failure count
	if failures >= hc.config.FailureThreshold {
		hc.triggerFailoverActions()
	}

	// Schedule retry sooner than normal interval
	if failures > 1 {
		go hc.scheduleRetryCheck()
	}
}

// triggerFailoverActions triggers failover procedures when threshold is reached
func (hc *DatabaseHealthChecker) triggerFailoverActions() {
	hc.logger.Error("Database failure threshold reached, triggering failover actions",
		zap.Int("consecutive_failures", hc.consecutiveFailures),
		zap.Int("failure_threshold", hc.config.FailureThreshold),
	)

	// Update database metrics
	hc.db.mutex.Lock()
	hc.db.metrics.FailoverEvents++
	hc.db.metrics.LastFailover = time.Now()
	hc.db.mutex.Unlock()

	// TODO: Implement additional failover actions:
	// - Send alerts to monitoring systems
	// - Trigger circuit breaker
	// - Switch to read-only mode
	// - Scale up connection pools
	// - Restart connection pools
}

// triggerReplicaFailover handles read replica failures
func (hc *DatabaseHealthChecker) triggerReplicaFailover() {
	hc.db.mutex.Lock()
	defer hc.db.mutex.Unlock()

	hc.db.usingReplica = true // Switch to primary for reads
	hc.logger.Warn("Switched to primary database for all operations due to replica failure")
}

// scheduleRetryCheck schedules a retry health check sooner than the regular interval
func (hc *DatabaseHealthChecker) scheduleRetryCheck() {
	time.Sleep(hc.config.RetryInterval)

	hc.mutex.RLock()
	isRunning := hc.isRunning
	hc.mutex.RUnlock()

	if isRunning {
		hc.logger.Debug("Performing retry health check")
		hc.performHealthCheck()
	}
}

// GetHealthStatus returns the current health status
func (hc *DatabaseHealthChecker) GetHealthStatus() HealthStatus {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	return HealthStatus{
		IsHealthy:           hc.consecutiveFailures == 0,
		ConsecutiveFailures: hc.consecutiveFailures,
		LastCheckTime:       hc.lastCheckTime,
		IsRunning:           hc.isRunning,
	}
}

// GetHealthChannel returns a channel that receives health status updates
func (hc *DatabaseHealthChecker) GetHealthChannel() <-chan bool {
	return hc.healthChan
}

// HealthStatus represents the current health status
type HealthStatus struct {
	IsHealthy           bool      `json:"is_healthy"`
	ConsecutiveFailures int       `json:"consecutive_failures"`
	LastCheckTime       time.Time `json:"last_check_time"`
	IsRunning           bool      `json:"is_running"`
}

// ForceHealthCheck manually triggers a health check
func (hc *DatabaseHealthChecker) ForceHealthCheck() {
	if !hc.isRunning {
		hc.logger.Warn("Cannot force health check - health checker is not running")
		return
	}

	hc.logger.Info("Forcing manual health check")
	go hc.performHealthCheck()
}

// ResetFailureCount resets the consecutive failure count
func (hc *DatabaseHealthChecker) ResetFailureCount() {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	oldCount := hc.consecutiveFailures
	hc.consecutiveFailures = 0

	if oldCount > 0 {
		hc.logger.Info("Reset database health failure count",
			zap.Int("previous_failures", oldCount),
		)
	}
}

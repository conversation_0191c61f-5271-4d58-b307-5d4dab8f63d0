package database

import (
	"context"
	"fmt"
	"sync"
	"time"

	"carnow-backend/internal/config"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// ConnectionRecoveryService handles database connection recovery and failover
// Forever Plan: Simplified to handle only primary database
type ConnectionRecoveryService struct {
	config              *config.Config
	logger              *zap.Logger
	primaryPool         *pgxpool.Pool
	recoveryAttempts    map[string]int
	lastRecoveryAttempt map[string]time.Time
	maxRecoveryAttempts int
	recoveryInterval    time.Duration
	connectionTimeout   time.Duration
	isRecovering        map[string]bool
	mu                  sync.RWMutex
}

// RecoveryConfig contains configuration for connection recovery
type RecoveryConfig struct {
	MaxRecoveryAttempts int           `json:"max_recovery_attempts"`
	RecoveryInterval    time.Duration `json:"recovery_interval"`
	ConnectionTimeout   time.Duration `json:"connection_timeout"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	EnableAutoRecovery  bool          `json:"enable_auto_recovery"`
}

// DefaultRecoveryConfig returns default recovery configuration
func DefaultRecoveryConfig() *RecoveryConfig {
	return &RecoveryConfig{
		MaxRecoveryAttempts: 5,
		RecoveryInterval:    30 * time.Second,
		ConnectionTimeout:   10 * time.Second,
		HealthCheckInterval: 15 * time.Second,
		EnableAutoRecovery:  true,
	}
}

// NewConnectionRecoveryService creates a new connection recovery service
// Forever Plan: Simplified for single database
func NewConnectionRecoveryService(
	config *config.Config,
	logger *zap.Logger,
	recoveryConfig *RecoveryConfig,
) *ConnectionRecoveryService {
	if recoveryConfig == nil {
		recoveryConfig = DefaultRecoveryConfig()
	}

	return &ConnectionRecoveryService{
		config:              config,
		logger:              logger,
		recoveryAttempts:    make(map[string]int),
		lastRecoveryAttempt: make(map[string]time.Time),
		isRecovering:        make(map[string]bool),
		maxRecoveryAttempts: recoveryConfig.MaxRecoveryAttempts,
		recoveryInterval:    recoveryConfig.RecoveryInterval,
		connectionTimeout:   recoveryConfig.ConnectionTimeout,
	}
}

// RegisterPrimaryPool registers the primary database pool for monitoring
func (crs *ConnectionRecoveryService) RegisterPrimaryPool(pool *pgxpool.Pool) {
	crs.mu.Lock()
	defer crs.mu.Unlock()
	crs.primaryPool = pool
}

// RecoverConnection attempts to recover a database connection
// Forever Plan: Only handles primary database recovery
func (crs *ConnectionRecoveryService) RecoverConnection(ctx context.Context, connectionType string) error {
	crs.mu.Lock()
	defer crs.mu.Unlock()

	// Check if already recovering
	if crs.isRecovering[connectionType] {
		return fmt.Errorf("recovery already in progress for %s", connectionType)
	}

	// Check if we've exceeded max recovery attempts
	if crs.recoveryAttempts[connectionType] >= crs.maxRecoveryAttempts {
		return fmt.Errorf("max recovery attempts reached for %s", connectionType)
	}

	// Check if enough time has passed since last recovery attempt
	if lastAttempt, exists := crs.lastRecoveryAttempt[connectionType]; exists {
		if time.Since(lastAttempt) < crs.recoveryInterval {
			return fmt.Errorf("recovery interval not reached for %s", connectionType)
		}
	}

	// Mark as recovering
	crs.isRecovering[connectionType] = true
	crs.recoveryAttempts[connectionType]++
	crs.lastRecoveryAttempt[connectionType] = time.Now()

	crs.logger.Info("Starting connection recovery",
		zap.String("connection_type", connectionType),
		zap.Int("attempt", crs.recoveryAttempts[connectionType]),
	)

	go func() {
		defer func() {
			crs.mu.Lock()
			crs.isRecovering[connectionType] = false
			crs.mu.Unlock()
		}()

		var err error
		switch connectionType {
		case "primary":
			_, err = crs.recoverPrimaryConnection(ctx)
		default:
			err = fmt.Errorf("unknown connection type: %s", connectionType)
		}

		if err != nil {
			crs.logger.Error("Connection recovery failed",
				zap.String("connection_type", connectionType),
				zap.Error(err),
			)
		} else {
			crs.logger.Info("Connection recovery successful",
				zap.String("connection_type", connectionType),
			)
			// Reset recovery attempts on success
			crs.mu.Lock()
			crs.recoveryAttempts[connectionType] = 0
			crs.mu.Unlock()
		}
	}()

	return nil
}

// recoverPrimaryConnection recovers the primary database connection
func (crs *ConnectionRecoveryService) recoverPrimaryConnection(ctx context.Context) (*pgxpool.Pool, error) {
	// Close existing connection if it exists
	if crs.primaryPool != nil {
		crs.primaryPool.Close()
		crs.primaryPool = nil
	}

	// Create new secure database connection
	secureDB, err := NewSecureDB(crs.config)
	if err != nil {
		return nil, fmt.Errorf("failed to create new primary connection: %w", err)
	}

	// Test the connection
	testCtx, cancel := context.WithTimeout(ctx, crs.connectionTimeout)
	defer cancel()

	if err := secureDB.Pool.Ping(testCtx); err != nil {
		secureDB.Close()
		return nil, fmt.Errorf("primary connection test failed: %w", err)
	}

	// Test with a simple query
	var result int
	err = secureDB.Pool.QueryRow(testCtx, "SELECT 1").Scan(&result)
	if err != nil {
		secureDB.Close()
		return nil, fmt.Errorf("primary connection query test failed: %w", err)
	}

	crs.primaryPool = secureDB.Pool
	return secureDB.Pool, nil
}

// PerformHealthCheck performs a health check on all registered connections
// Forever Plan: Only checks primary database
func (crs *ConnectionRecoveryService) PerformHealthCheck(ctx context.Context) map[string]bool {
	crs.mu.RLock()
	defer crs.mu.RUnlock()

	status := make(map[string]bool)

	// Check primary connection
	if crs.primaryPool != nil {
		healthy := crs.checkConnectionHealth(ctx, crs.primaryPool, "primary")
		status["primary"] = healthy

		if !healthy {
			crs.logger.Warn("Primary database connection is unhealthy")
			// Trigger recovery in background
			go func() {
				_ = crs.RecoverConnection(context.Background(), "primary")
			}()
		}
	} else {
		status["primary"] = false
		crs.logger.Warn("Primary database pool is nil")
	}

	return status
}

// checkConnectionHealth checks the health of a specific connection
func (crs *ConnectionRecoveryService) checkConnectionHealth(ctx context.Context, pool *pgxpool.Pool, name string) bool {
	if pool == nil {
		return false
	}

	// Test with ping
	pingCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	if err := pool.Ping(pingCtx); err != nil {
		crs.logger.Error("Connection ping failed",
			zap.String("connection", name),
			zap.Error(err),
		)
		return false
	}

	// Test with a simple query
	queryCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	var result int
	err := pool.QueryRow(queryCtx, "SELECT 1").Scan(&result)
	if err != nil {
		crs.logger.Error("Connection query test failed",
			zap.String("connection", name),
			zap.Error(err),
		)
		return false
	}

	if result != 1 {
		crs.logger.Error("Connection query returned unexpected result",
			zap.String("connection", name),
			zap.Int("expected", 1),
			zap.Int("actual", result),
		)
		return false
	}

	return true
}

// GetConnectionStatus returns the current status of all connections
// Forever Plan: Only primary connection status
func (crs *ConnectionRecoveryService) GetConnectionStatus() map[string]interface{} {
	crs.mu.RLock()
	defer crs.mu.RUnlock()

	status := make(map[string]interface{})

	// Primary connection status
	if crs.primaryPool != nil {
		stat := crs.primaryPool.Stat()
		status["primary"] = map[string]interface{}{
			"healthy":           true,
			"total_conns":       stat.TotalConns(),
			"acquired_conns":    stat.AcquiredConns(),
			"idle_conns":        stat.IdleConns(),
			"max_conns":         stat.MaxConns(),
			"recovery_attempts": crs.recoveryAttempts["primary"],
			"is_recovering":     crs.isRecovering["primary"],
		}
	} else {
		status["primary"] = map[string]interface{}{
			"healthy":           false,
			"recovery_attempts": crs.recoveryAttempts["primary"],
			"is_recovering":     crs.isRecovering["primary"],
		}
	}

	return status
}

// ResetRecoveryAttempts resets the recovery attempt count for a connection type
func (crs *ConnectionRecoveryService) ResetRecoveryAttempts(connectionType string) {
	crs.mu.Lock()
	defer crs.mu.Unlock()

	crs.recoveryAttempts[connectionType] = 0
	crs.logger.Info("Reset recovery attempts",
		zap.String("connection_type", connectionType),
	)
}

// IsRecovering returns whether a recovery is in progress for the given connection type
func (crs *ConnectionRecoveryService) IsRecovering(connectionType string) bool {
	crs.mu.RLock()
	defer crs.mu.RUnlock()

	return crs.isRecovering[connectionType]
}

// ForceRecovery forces a recovery attempt regardless of intervals and limits
func (crs *ConnectionRecoveryService) ForceRecovery(ctx context.Context, connectionType string) error {
	crs.mu.Lock()
	// Reset restrictions for forced recovery
	crs.recoveryAttempts[connectionType] = 0
	delete(crs.lastRecoveryAttempt, connectionType)
	crs.mu.Unlock()

	return crs.RecoverConnection(ctx, connectionType)
}

// StartAutoRecovery starts the automatic recovery monitoring
// Forever Plan: Simplified monitoring for primary database only
func (crs *ConnectionRecoveryService) StartAutoRecovery(ctx context.Context, interval time.Duration) {
	crs.logger.Info("Starting auto-recovery monitoring",
		zap.Duration("interval", interval),
	)

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			crs.logger.Info("Auto-recovery monitoring stopped")
			return
		case <-ticker.C:
			_ = crs.PerformHealthCheck(ctx)
		}
	}
}

// Close closes all connections managed by the recovery service
func (crs *ConnectionRecoveryService) Close() {
	crs.mu.Lock()
	defer crs.mu.Unlock()

	if crs.primaryPool != nil {
		crs.primaryPool.Close()
		crs.primaryPool = nil
	}

	crs.logger.Info("Connection recovery service closed")
}

// GetRecoveryMetrics returns metrics about recovery operations
func (crs *ConnectionRecoveryService) GetRecoveryMetrics() map[string]interface{} {
	crs.mu.RLock()
	defer crs.mu.RUnlock()

	metrics := make(map[string]interface{})

	for connType, attempts := range crs.recoveryAttempts {
		metrics[connType] = map[string]interface{}{
			"total_attempts":     attempts,
			"is_recovering":      crs.isRecovering[connType],
			"last_recovery_time": crs.lastRecoveryAttempt[connType],
			"max_attempts_limit": crs.maxRecoveryAttempts,
			"recovery_interval":  crs.recoveryInterval,
		}
	}

	return metrics
}

// WithTransaction executes a function within a database transaction with recovery support
func (crs *ConnectionRecoveryService) WithTransaction(ctx context.Context, fn func(tx pgx.Tx) error) error {
	if crs.primaryPool == nil {
		return fmt.Errorf("primary database pool not available")
	}

	tx, err := crs.primaryPool.Begin(ctx)
	if err != nil {
		// Trigger recovery on connection failure
		go func() {
			_ = crs.RecoverConnection(context.Background(), "primary")
		}()
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if p := recover(); p != nil {
			_ = tx.Rollback(ctx)
			panic(p)
		}
	}()

	if err := fn(tx); err != nil {
		if rollbackErr := tx.Rollback(ctx); rollbackErr != nil {
			crs.logger.Error("Failed to rollback transaction",
				zap.Error(rollbackErr),
			)
		}
		return err
	}

	return tx.Commit(ctx)
}

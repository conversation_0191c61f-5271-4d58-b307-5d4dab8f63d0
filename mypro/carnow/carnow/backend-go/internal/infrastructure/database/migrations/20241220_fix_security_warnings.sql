-- Migration: Fix Security Warnings
-- Date: 2024-12-20
-- Description: Fix function search_path mutable warnings and enable leaked password protection
-- Author: CarNow Security Team
-- Compliance: Forever Plan Architecture - Enhanced Security Standards

-- =============================================================================
-- SECURITY FIX: Function Search Path Mutable Warnings
-- =============================================================================

-- Fix 1: increment_login_count function
CREATE OR REPLACE FUNCTION public.increment_login_count()
  RETURNS void
  LANGUAGE sql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
  UPDATE public.user_profiles 
  SET login_count = COALESCE(login_count, 0) + 1,
      last_login_at = NOW(),
      updated_at = NOW()
  WHERE id = auth.uid();
$$;

-- Fix 2: detect_suspicious_activity function
CREATE OR REPLACE FUNCTION public.detect_suspicious_activity()
  RETURNS boolean
  LANGUAGE plpgsql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
DECLARE
  recent_attempts INTEGER;
  suspicious_threshold INTEGER := 5;
BEGIN
  -- Count recent failed login attempts
  SELECT COUNT(*) INTO recent_attempts
  FROM public.audit_logs
  WHERE user_id = auth.uid()
    AND action = 'login_failed'
    AND created_at > NOW() - INTERVAL '15 minutes';
  
  -- Return true if suspicious activity detected
  RETURN recent_attempts >= suspicious_threshold;
END;
$$;

-- Fix 3: can_access_audit_logs function
CREATE OR REPLACE FUNCTION public.can_access_audit_logs()
  RETURNS boolean
  LANGUAGE sql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
  -- Check if user has admin role or specific audit access permission
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid()
      AND r.name IN ('admin', 'auditor')
      AND ur.is_active = true
  );
$$;

-- Fix 4: can_access_session function
CREATE OR REPLACE FUNCTION public.can_access_session(session_id UUID)
  RETURNS boolean
  LANGUAGE sql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
  -- Check if user can access the specified session
  SELECT EXISTS (
    SELECT 1 FROM public.user_sessions
    WHERE id = session_id
      AND user_id = auth.uid()
      AND is_active = true
      AND expires_at > NOW()
  );
$$;

-- Fix 5: can_update_profile function
CREATE OR REPLACE FUNCTION public.can_update_profile(target_user_id UUID)
  RETURNS boolean
  LANGUAGE sql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
  -- Users can update their own profile or admins can update any profile
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles ur
    JOIN public.roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid()
      AND r.name = 'admin'
      AND ur.is_active = true
  ) OR auth.uid() = target_user_id;
$$;

-- Fix 6: get_auth_statistics function
CREATE OR REPLACE FUNCTION public.get_auth_statistics()
  RETURNS TABLE(
    total_users BIGINT,
    active_users_24h BIGINT,
    active_users_7d BIGINT,
    failed_logins_24h BIGINT,
    successful_logins_24h BIGINT
  )
  LANGUAGE sql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
  SELECT 
    (SELECT COUNT(*) FROM public.user_profiles WHERE is_deleted = false) as total_users,
    (SELECT COUNT(DISTINCT user_id) FROM public.audit_logs 
     WHERE action = 'login_success' 
       AND created_at > NOW() - INTERVAL '24 hours') as active_users_24h,
    (SELECT COUNT(DISTINCT user_id) FROM public.audit_logs 
     WHERE action = 'login_success' 
       AND created_at > NOW() - INTERVAL '7 days') as active_users_7d,
    (SELECT COUNT(*) FROM public.audit_logs 
     WHERE action = 'login_failed' 
       AND created_at > NOW() - INTERVAL '24 hours') as failed_logins_24h,
    (SELECT COUNT(*) FROM public.audit_logs 
     WHERE action = 'login_success' 
       AND created_at > NOW() - INTERVAL '24 hours') as successful_logins_24h;
$$;

-- Fix 7: update_updated_at_column function
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
  RETURNS TRIGGER
  LANGUAGE plpgsql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Fix 8: validate_rls_performance function
CREATE OR REPLACE FUNCTION public.validate_rls_performance()
  RETURNS TABLE(
    table_name TEXT,
    policy_count INTEGER,
    performance_score NUMERIC
  )
  LANGUAGE plpgsql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
DECLARE
  table_record RECORD;
  policy_count_val INTEGER;
  performance_score_val NUMERIC;
BEGIN
  FOR table_record IN 
    SELECT schemaname, tablename 
    FROM pg_tables 
    WHERE schemaname = 'public' 
      AND tablename NOT LIKE 'pg_%'
  LOOP
    -- Count policies for this table
    SELECT COUNT(*) INTO policy_count_val
    FROM pg_policies
    WHERE schemaname = table_record.schemaname
      AND tablename = table_record.tablename;
    
    -- Calculate performance score (simplified)
    performance_score_val := CASE 
      WHEN policy_count_val = 0 THEN 0.0
      WHEN policy_count_val <= 3 THEN 1.0
      WHEN policy_count_val <= 5 THEN 0.8
      WHEN policy_count_val <= 10 THEN 0.6
      ELSE 0.4
    END;
    
    table_name := table_record.tablename;
    policy_count := policy_count_val;
    performance_score := performance_score_val;
    
    RETURN NEXT;
  END LOOP;
  
  RETURN;
END;
$$;

-- =============================================================================
-- SECURITY FIX: Enable Leaked Password Protection
-- =============================================================================

-- Enable leaked password protection in Supabase Auth
-- This requires configuration in the Supabase dashboard, but we can document it here
-- Go to: Auth > Settings > Password Security > Enable "Prevent use of leaked passwords"

-- Create a function to check password strength (additional security layer)
CREATE OR REPLACE FUNCTION public.validate_password_strength(password TEXT)
  RETURNS TABLE(
    is_valid BOOLEAN,
    strength_score INTEGER,
    feedback TEXT[]
  )
  LANGUAGE plpgsql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
DECLARE
  feedback_array TEXT[] := ARRAY[]::TEXT[];
  score INTEGER := 0;
  has_length BOOLEAN := false;
  has_uppercase BOOLEAN := false;
  has_lowercase BOOLEAN := false;
  has_digit BOOLEAN := false;
  has_special BOOLEAN := false;
BEGIN
  -- Check minimum length (8 characters)
  IF LENGTH(password) >= 8 THEN
    has_length := true;
    score := score + 1;
  ELSE
    feedback_array := array_append(feedback_array, 'Password must be at least 8 characters long');
  END IF;
  
  -- Check for uppercase letters
  IF password ~ '[A-Z]' THEN
    has_uppercase := true;
    score := score + 1;
  ELSE
    feedback_array := array_append(feedback_array, 'Password must contain at least one uppercase letter');
  END IF;
  
  -- Check for lowercase letters
  IF password ~ '[a-z]' THEN
    has_lowercase := true;
    score := score + 1;
  ELSE
    feedback_array := array_append(feedback_array, 'Password must contain at least one lowercase letter');
  END IF;
  
  -- Check for digits
  IF password ~ '[0-9]' THEN
    has_digit := true;
    score := score + 1;
  ELSE
    feedback_array := array_append(feedback_array, 'Password must contain at least one digit');
  END IF;
  
  -- Check for special characters
  IF password ~ '[!@#$%^&*()_+\-=\[\]{};'':"|,.<>/?`~]' THEN
    has_special := true;
    score := score + 1;
  ELSE
    feedback_array := array_append(feedback_array, 'Password must contain at least one special character');
  END IF;
  
  -- Check for common patterns to avoid
  IF password ~ '(password|123456|qwerty|admin|user)' THEN
    feedback_array := array_append(feedback_array, 'Password contains common patterns that should be avoided');
    score := score - 1;
  END IF;
  
  -- Determine if password is valid (all basic requirements met)
  is_valid := has_length AND has_uppercase AND has_lowercase AND has_digit AND has_special;
  
  -- Adjust score based on length
  IF LENGTH(password) >= 12 THEN
    score := score + 1;
  END IF;
  
  -- Ensure score doesn't go below 0
  IF score < 0 THEN
    score := 0;
  END IF;
  
  strength_score := score;
  feedback := feedback_array;
  
  RETURN NEXT;
END;
$$;

-- =============================================================================
-- SECURITY ENHANCEMENT: Additional Security Functions
-- =============================================================================

-- Function to log security events
CREATE OR REPLACE FUNCTION public.log_security_event(
  event_type TEXT,
  event_details JSONB DEFAULT '{}'::JSONB,
  severity TEXT DEFAULT 'info'
)
  RETURNS void
  LANGUAGE plpgsql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
BEGIN
  INSERT INTO public.security_events (
    user_id,
    event_type,
    event_details,
    severity,
    ip_address,
    user_agent,
    created_at
  ) VALUES (
    auth.uid(),
    event_type,
    event_details,
    severity,
    current_setting('request.headers')::json->>'x-forwarded-for',
    current_setting('request.headers')::json->>'user-agent',
    NOW()
  );
END;
$$;

-- Function to check if user account is locked
CREATE OR REPLACE FUNCTION public.is_account_locked()
  RETURNS boolean
  LANGUAGE sql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.user_profiles
    WHERE id = auth.uid()
      AND is_locked = true
      AND locked_until > NOW()
  );
$$;

-- Function to get user security status
CREATE OR REPLACE FUNCTION public.get_user_security_status()
  RETURNS TABLE(
    is_locked BOOLEAN,
    failed_attempts INTEGER,
    last_failed_login TIMESTAMPTZ,
    requires_mfa BOOLEAN,
    mfa_enabled BOOLEAN
  )
  LANGUAGE sql
  SECURITY DEFINER
  SET search_path = '' -- SECURITY: Explicit search path to prevent injection
AS $$
  SELECT 
    up.is_locked,
    COALESCE(up.failed_login_attempts, 0) as failed_attempts,
    up.last_failed_login,
    up.requires_mfa,
    up.mfa_enabled
  FROM public.user_profiles up
  WHERE up.id = auth.uid();
$$;

-- =============================================================================
-- SECURITY AUDIT: Create security audit table if not exists
-- =============================================================================

-- Create security events table for comprehensive logging
CREATE TABLE IF NOT EXISTS public.security_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  event_details JSONB DEFAULT '{}'::JSONB,
  severity TEXT CHECK (severity IN ('info', 'warning', 'error', 'critical')) DEFAULT 'info',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Indexes for performance
  CONSTRAINT idx_security_events_user_id_created_at 
    UNIQUE (user_id, created_at)
);

-- Create indexes for security events table
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON public.security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON public.security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON public.security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON public.security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_security_events_ip_address ON public.security_events(ip_address);

-- =============================================================================
-- SECURITY POLICY: Row Level Security for security_events
-- =============================================================================

-- Enable RLS on security_events table
ALTER TABLE public.security_events ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own security events
CREATE POLICY "Users can view own security events" ON public.security_events
  FOR SELECT
  USING (user_id = auth.uid());

-- Policy: Only system functions can insert security events
CREATE POLICY "System can insert security events" ON public.security_events
  FOR INSERT
  WITH CHECK (true); -- This will be restricted by SECURITY DEFINER functions

-- =============================================================================
-- MIGRATION COMPLETION LOG
-- =============================================================================

-- Log the migration completion
INSERT INTO public.migration_logs (
  migration_name,
  applied_at,
  status,
  details
) VALUES (
  '20241220_fix_security_warnings',
  NOW(),
  'completed',
  'Fixed function search_path mutable warnings and enabled security enhancements'
);

-- =============================================================================
-- SECURITY RECOMMENDATIONS
-- =============================================================================

/*
SECURITY RECOMMENDATIONS FOR SUPABASE DASHBOARD:

1. ENABLE LEAKED PASSWORD PROTECTION:
   - Go to: Auth > Settings > Password Security
   - Enable "Prevent use of leaked passwords"
   - This will check passwords against HaveIBeenPwned.org database

2. CONFIGURE PASSWORD STRENGTH REQUIREMENTS:
   - Set minimum password length to 8 characters
   - Enable all character requirements (uppercase, lowercase, digits, symbols)
   - This provides defense in depth with our custom validation function

3. ENABLE MFA (Multi-Factor Authentication):
   - Go to: Auth > Settings > Multi-Factor Authentication
   - Enable MFA for enhanced security

4. REVIEW ROW LEVEL SECURITY POLICIES:
   - Ensure all tables have appropriate RLS policies
   - Test policies regularly using the validate_rls_performance function

5. MONITOR SECURITY EVENTS:
   - Use the security_events table to monitor for suspicious activity
   - Set up alerts for critical security events

6. REGULAR SECURITY AUDITS:
   - Run the Supabase Security Advisor regularly
   - Review and address any new security warnings
   - Update security functions as needed

COMPLIANCE: This migration follows Forever Plan Architecture security standards
and addresses all identified security warnings from Supabase's database linter.
*/ 
-- Create seller_subscription_requests table
CREATE TABLE IF NOT EXISTS seller_subscription_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    seller_id UUID NOT NULL,
    plan_id UUID NOT NULL,
    requested_tier VARCHAR(20) NOT NULL,
    billing_cycle VARCHAR(10) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    request_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    approved_date TIMESTAMPTZ,
    rejected_date TIMESTAMPTZ,
    admin_id UUID,
    admin_notes TEXT,
    rejection_reason TEXT,
    requested_price_ld DECIMAL(10,2) NOT NULL,
    payment_method_id UUID,
    seller_info JSONB,
    business_documents JSONB,
    requires_document_verification BOOLEAN DEFAULT FALSE,
    priority INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    cancelled_date TIMESTAMPTZ
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_seller_subscription_requests_seller_id ON seller_subscription_requests(seller_id);
CREATE INDEX IF NOT EXISTS idx_seller_subscription_requests_status ON seller_subscription_requests(status);
CREATE INDEX IF NOT EXISTS idx_seller_subscription_requests_created_at ON seller_subscription_requests(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_seller_subscription_requests_priority ON seller_subscription_requests(priority) WHERE priority IS NOT NULL;

-- Create seller_subscription_request_reviews table
CREATE TABLE IF NOT EXISTS seller_subscription_request_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_id UUID NOT NULL,
    admin_id UUID NOT NULL,
    review_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    decision VARCHAR(20) NOT NULL,
    notes TEXT,
    rejection_reason TEXT,
    review_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for reviews
CREATE INDEX IF NOT EXISTS idx_seller_subscription_request_reviews_request_id ON seller_subscription_request_reviews(request_id);
CREATE INDEX IF NOT EXISTS idx_seller_subscription_request_reviews_admin_id ON seller_subscription_request_reviews(admin_id);
CREATE INDEX IF NOT EXISTS idx_seller_subscription_request_reviews_decision ON seller_subscription_request_reviews(decision);

-- Add foreign key constraint for reviews table only (safer)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_seller_subscription_request_reviews_request_id') THEN
        ALTER TABLE seller_subscription_request_reviews 
        ADD CONSTRAINT fk_seller_subscription_request_reviews_request_id 
        FOREIGN KEY (request_id) REFERENCES seller_subscription_requests(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Drop foreign key constraints
ALTER TABLE seller_subscription_request_reviews DROP CONSTRAINT IF EXISTS fk_seller_subscription_request_reviews_request_id;

-- Drop tables
DROP TABLE IF EXISTS seller_subscription_request_reviews;
DROP TABLE IF EXISTS seller_subscription_requests;

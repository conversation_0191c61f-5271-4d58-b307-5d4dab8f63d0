-- Migration: Create Payment Tables for Stripe Integration
-- Date: 2024-12-20
-- Description: Create tables for payment intents, refunds, and order payments

-- Create payment_intents table for Stripe integration
CREATE TABLE IF NOT EXISTS public.payment_intents (
    id VARCHAR(255) PRIMARY KEY,
    amount BIGINT NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'LYD',
    status VARCHAR(50) NOT NULL DEFAULT 'requires_payment_method',
    client_secret VARCHAR(255) NOT NULL,
    payment_method VARCHAR(255),
    metadata JSONB DEFAULT '{}'::JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment_refunds table
CREATE TABLE IF NOT EXISTS public.payment_refunds (
    id VARCHAR(255) PRIMARY KEY,
    payment_intent_id VARCHAR(255) NOT NULL REFERENCES public.payment_intents(id),
    amount BIGINT NOT NULL,
    reason TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create order_payments table to link orders with payments
CREATE TABLE IF NOT EXISTS public.order_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id INTEGER NOT NULL REFERENCES public.orders(id),
    payment_intent_id VARCHAR(255) NOT NULL REFERENCES public.payment_intents(id),
    amount BIGINT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_payment_intents_status ON public.payment_intents(status);
CREATE INDEX IF NOT EXISTS idx_payment_intents_created_at ON public.payment_intents(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_refunds_payment_intent_id ON public.payment_refunds(payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_payment_refunds_status ON public.payment_refunds(status);
CREATE INDEX IF NOT EXISTS idx_order_payments_order_id ON public.order_payments(order_id);
CREATE INDEX IF NOT EXISTS idx_order_payments_payment_intent_id ON public.order_payments(payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_order_payments_status ON public.order_payments(status);

-- Add constraints
ALTER TABLE public.payment_intents 
ADD CONSTRAINT chk_payment_intents_amount_positive 
CHECK (amount > 0);

ALTER TABLE public.payment_refunds 
ADD CONSTRAINT chk_payment_refunds_amount_positive 
CHECK (amount > 0);

ALTER TABLE public.order_payments 
ADD CONSTRAINT chk_order_payments_amount_positive 
CHECK (amount > 0);

-- Add status constraints
ALTER TABLE public.payment_intents 
ADD CONSTRAINT chk_payment_intents_status 
CHECK (status IN ('requires_payment_method', 'requires_confirmation', 'requires_action', 'processing', 'succeeded', 'canceled'));

ALTER TABLE public.payment_refunds 
ADD CONSTRAINT chk_payment_refunds_status 
CHECK (status IN ('pending', 'succeeded', 'failed', 'canceled'));

ALTER TABLE public.order_payments 
ADD CONSTRAINT chk_order_payments_status 
CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'refunded'));

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_payment_intents_updated_at 
    BEFORE UPDATE ON public.payment_intents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_refunds_updated_at 
    BEFORE UPDATE ON public.payment_refunds 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_order_payments_updated_at 
    BEFORE UPDATE ON public.order_payments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

package database

import (
	"context"
	"fmt"
	"log"
	"time"

	"carnow-backend/internal/config"
)

// OptimizationService provides database optimization capabilities
type OptimizationService struct {
	enhancedDB *EnhancedDBManager
	config     *config.Config
	logger     *log.Logger
}

// OptimizationReport contains the results of database optimization analysis
type OptimizationReport struct {
	Timestamp           time.Time              `json:"timestamp"`
	DatabaseStats       map[string]interface{} `json:"database_stats"`
	PerformanceMetrics  *PerformanceMetrics    `json:"performance_metrics"`
	SlowQueries         []SlowQuery            `json:"slow_queries"`
	IndexSuggestions    []IndexSuggestion      `json:"index_suggestions"`
	OptimizationActions []OptimizationAction   `json:"optimization_actions"`
	OverallScore        float64                `json:"overall_score"`
	Recommendations     []string               `json:"recommendations"`
}

// OptimizationAction represents an action taken to optimize the database
type OptimizationAction struct {
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Query       string    `json:"query,omitempty"`
	Status      string    `json:"status"`
	ExecutedAt  time.Time `json:"executed_at"`
	Error       string    `json:"error,omitempty"`
}

// NewOptimizationService creates a new database optimization service
func NewOptimizationService(enhancedDB *EnhancedDBManager, cfg *config.Config) *OptimizationService {
	return &OptimizationService{
		enhancedDB: enhancedDB,
		config:     cfg,
		logger:     log.New(log.Writer(), "[DBOptimization] ", log.LstdFlags),
	}
}

// RunOptimizationAnalysis performs comprehensive database optimization analysis
func (os *OptimizationService) RunOptimizationAnalysis(ctx context.Context) (*OptimizationReport, error) {
	os.logger.Printf("🔍 Starting comprehensive database optimization analysis")

	report := &OptimizationReport{
		Timestamp:           time.Now(),
		OptimizationActions: []OptimizationAction{},
		Recommendations:     []string{},
	}

	// Get database statistics
	stats, err := os.enhancedDB.GetDatabaseStats(ctx)
	if err != nil {
		os.logger.Printf("⚠️ Failed to get database stats: %v", err)
	} else {
		report.DatabaseStats = stats
	}

	// Get performance metrics
	report.PerformanceMetrics = os.enhancedDB.GetPerformanceMetrics()

	// Get slow queries
	slowQueries, err := os.enhancedDB.GetSlowQueries(ctx, 10)
	if err != nil {
		os.logger.Printf("⚠️ Failed to get slow queries: %v", err)
	} else {
		report.SlowQueries = slowQueries
	}

	// Analyze tables and suggest indexes
	if err := os.analyzeTablesAndSuggestIndexes(ctx, report); err != nil {
		os.logger.Printf("⚠️ Failed to analyze tables: %v", err)
	}

	// Calculate overall performance score
	report.OverallScore = os.calculatePerformanceScore(report)

	// Generate recommendations
	report.Recommendations = os.generateRecommendations(report)

	os.logger.Printf("✅ Database optimization analysis completed. Score: %.2f/10", report.OverallScore)
	return report, nil
}

// analyzeTablesAndSuggestIndexes analyzes database tables and suggests optimizations
func (os *OptimizationService) analyzeTablesAndSuggestIndexes(ctx context.Context, report *OptimizationReport) error {
	os.logger.Printf("🔍 Analyzing tables for index optimization")

	// Get list of user tables
	rows, err := os.enhancedDB.Pool.Query(ctx, `
		SELECT schemaname, tablename 
		FROM pg_tables 
		WHERE schemaname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
		ORDER BY schemaname, tablename`)
	if err != nil {
		return fmt.Errorf("failed to get table list: %w", err)
	}
	defer rows.Close()

	allSuggestions := []IndexSuggestion{}

	for rows.Next() {
		var schema, table string
		if err := rows.Scan(&schema, &table); err != nil {
			continue
		}

		tableName := fmt.Sprintf("%s.%s", schema, table)

		// For now, we'll use common query patterns
		// In a real implementation, you'd analyze actual query logs
		commonQueries := os.generateCommonQueryPatterns(tableName)

		suggestions, err := os.enhancedDB.SuggestIndexes(ctx, tableName, commonQueries)
		if err != nil {
			os.logger.Printf("⚠️ Failed to get index suggestions for %s: %v", tableName, err)
			continue
		}

		allSuggestions = append(allSuggestions, suggestions...)
	}

	report.IndexSuggestions = allSuggestions
	os.logger.Printf("✅ Generated %d index suggestions", len(allSuggestions))
	return nil
}

// generateCommonQueryPatterns generates common query patterns for a table
func (os *OptimizationService) generateCommonQueryPatterns(tableName string) []string {
	// This is a simplified implementation
	// In production, you'd analyze actual query logs from pg_stat_statements
	return []string{
		fmt.Sprintf("SELECT * FROM %s WHERE id = $1", tableName),
		fmt.Sprintf("SELECT * FROM %s WHERE created_at > $1", tableName),
		fmt.Sprintf("SELECT * FROM %s WHERE updated_at > $1", tableName),
		fmt.Sprintf("SELECT * FROM %s ORDER BY created_at DESC", tableName),
		fmt.Sprintf("SELECT * FROM %s ORDER BY id DESC", tableName),
	}
}

// calculatePerformanceScore calculates an overall performance score (0-10)
func (os *OptimizationService) calculatePerformanceScore(report *OptimizationReport) float64 {
	score := 10.0

	// Deduct points for slow queries
	if report.PerformanceMetrics != nil {
		if report.PerformanceMetrics.TotalQueries > 0 {
			slowQueryRatio := float64(report.PerformanceMetrics.SlowQueries) / float64(report.PerformanceMetrics.TotalQueries)
			score -= slowQueryRatio * 3.0 // Deduct up to 3 points for slow queries
		}

		// Deduct points for high average latency
		if report.PerformanceMetrics.AverageLatency > 100*time.Millisecond {
			latencyPenalty := float64(report.PerformanceMetrics.AverageLatency.Milliseconds()) / 100.0
			score -= latencyPenalty * 0.5 // Deduct 0.5 points per 100ms
		}
	}

	// Deduct points for cache hit ratio
	if report.DatabaseStats != nil {
		if cacheHitRatio, ok := report.DatabaseStats["cache_hit_ratio"].(float64); ok {
			if cacheHitRatio < 0.95 { // Less than 95% cache hit ratio
				score -= (0.95 - cacheHitRatio) * 10.0 // Deduct up to 1 point
			}
		}
	}

	// Deduct points for missing indexes (based on suggestions)
	if len(report.IndexSuggestions) > 0 {
		highPriorityIndexes := 0
		for _, suggestion := range report.IndexSuggestions {
			if suggestion.Priority == "high" {
				highPriorityIndexes++
			}
		}
		score -= float64(highPriorityIndexes) * 0.5 // Deduct 0.5 points per high-priority missing index
	}

	// Ensure score is between 0 and 10
	if score < 0 {
		score = 0
	}
	if score > 10 {
		score = 10
	}

	return score
}

// generateRecommendations generates optimization recommendations based on analysis
func (os *OptimizationService) generateRecommendations(report *OptimizationReport) []string {
	recommendations := []string{}

	// Recommendations based on performance metrics
	if report.PerformanceMetrics != nil {
		if report.PerformanceMetrics.SlowQueries > 0 {
			recommendations = append(recommendations,
				fmt.Sprintf("Optimize %d slow queries to improve performance", report.PerformanceMetrics.SlowQueries))
		}

		if report.PerformanceMetrics.AverageLatency > 100*time.Millisecond {
			recommendations = append(recommendations,
				fmt.Sprintf("Average query latency is %.2fms, consider adding indexes or optimizing queries",
					float64(report.PerformanceMetrics.AverageLatency.Nanoseconds())/1e6))
		}
	}

	// Recommendations based on cache hit ratio
	if report.DatabaseStats != nil {
		if cacheHitRatio, ok := report.DatabaseStats["cache_hit_ratio"].(float64); ok {
			if cacheHitRatio < 0.95 {
				recommendations = append(recommendations,
					fmt.Sprintf("Cache hit ratio is %.2f%%, consider increasing shared_buffers or adding more RAM",
						cacheHitRatio*100))
			}
		}
	}

	// Recommendations based on index suggestions
	highPriorityIndexes := 0
	for _, suggestion := range report.IndexSuggestions {
		if suggestion.Priority == "high" {
			highPriorityIndexes++
		}
	}
	if highPriorityIndexes > 0 {
		recommendations = append(recommendations,
			fmt.Sprintf("Create %d high-priority indexes to improve query performance", highPriorityIndexes))
	}

	// Connection pool recommendations
	if report.DatabaseStats != nil {
		if poolStats, ok := report.DatabaseStats["connection_pool"].(map[string]interface{}); ok {
			if maxConns, ok := poolStats["max_conns"].(int32); ok {
				if acquiredConns, ok := poolStats["acquired_conns"].(int32); ok {
					utilization := float64(acquiredConns) / float64(maxConns)
					if utilization > 0.8 {
						recommendations = append(recommendations,
							"Connection pool utilization is high, consider increasing max_conns")
					}
				}
			}
		}
	}

	// General recommendations
	if len(report.SlowQueries) > 0 {
		recommendations = append(recommendations, "Review and optimize slow queries identified in the analysis")
	}

	if len(recommendations) == 0 {
		recommendations = append(recommendations, "Database performance is good, continue monitoring")
	}

	return recommendations
}

// ApplyOptimizations applies recommended optimizations automatically
func (os *OptimizationService) ApplyOptimizations(ctx context.Context, report *OptimizationReport, autoApply bool) error {
	os.logger.Printf("🔧 Applying database optimizations (auto-apply: %v)", autoApply)

	actions := []OptimizationAction{}

	// Apply safe optimizations automatically
	if autoApply {
		// Update table statistics
		action := OptimizationAction{
			Type:        "analyze_tables",
			Description: "Update table statistics for better query planning",
			Status:      "pending",
			ExecutedAt:  time.Now(),
		}

		if err := os.updateAllTableStatistics(ctx); err != nil {
			action.Status = "failed"
			action.Error = err.Error()
		} else {
			action.Status = "completed"
		}
		actions = append(actions, action)

		// Create high-priority indexes (with CONCURRENTLY to avoid blocking)
		for _, suggestion := range report.IndexSuggestions {
			if suggestion.Priority == "high" {
				action := OptimizationAction{
					Type:        "create_index",
					Description: fmt.Sprintf("Create index on %s.%s", suggestion.TableName, suggestion.ColumnNames[0]),
					Query:       suggestion.CreateSQL,
					Status:      "pending",
					ExecutedAt:  time.Now(),
				}

				if err := os.createIndexSafely(ctx, suggestion.CreateSQL); err != nil {
					action.Status = "failed"
					action.Error = err.Error()
					os.logger.Printf("⚠️ Failed to create index: %v", err)
				} else {
					action.Status = "completed"
					os.logger.Printf("✅ Created index: %s", suggestion.CreateSQL)
				}
				actions = append(actions, action)
			}
		}
	}

	// Update the report with applied actions
	report.OptimizationActions = actions

	os.logger.Printf("✅ Applied %d optimization actions", len(actions))
	return nil
}

// updateAllTableStatistics updates statistics for all user tables
func (os *OptimizationService) updateAllTableStatistics(ctx context.Context) error {
	os.logger.Printf("📊 Updating table statistics")

	// Run ANALYZE on all user tables
	_, err := os.enhancedDB.Pool.Exec(ctx, "ANALYZE")
	if err != nil {
		return fmt.Errorf("failed to analyze tables: %w", err)
	}

	os.logger.Printf("✅ Table statistics updated")
	return nil
}

// createIndexSafely creates an index using CONCURRENTLY to avoid blocking
func (os *OptimizationService) createIndexSafely(ctx context.Context, createSQL string) error {
	// Execute the CREATE INDEX CONCURRENTLY statement
	_, err := os.enhancedDB.Pool.Exec(ctx, createSQL)
	if err != nil {
		return fmt.Errorf("failed to create index: %w", err)
	}

	return nil
}

// SchedulePeriodicOptimization schedules periodic optimization tasks
func (os *OptimizationService) SchedulePeriodicOptimization(ctx context.Context, interval time.Duration) {
	os.logger.Printf("⏰ Scheduling periodic optimization (interval: %v)", interval)

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			os.logger.Printf("🛑 Stopping periodic optimization")
			return
		case <-ticker.C:
			os.logger.Printf("🔄 Running periodic optimization analysis")

			report, err := os.RunOptimizationAnalysis(ctx)
			if err != nil {
				os.logger.Printf("⚠️ Periodic optimization analysis failed: %v", err)
				continue
			}

			// Apply safe optimizations automatically
			if err := os.ApplyOptimizations(ctx, report, true); err != nil {
				os.logger.Printf("⚠️ Failed to apply optimizations: %v", err)
			}

			os.logger.Printf("📊 Periodic optimization completed. Score: %.2f/10", report.OverallScore)
		}
	}
}

package database

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"carnow-backend/internal/config"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// SecureDB represents a secure database connection with enhanced security features
type SecureDB struct {
	Pool           *pgxpool.Pool
	Config         *config.Config
	AuditLogger    *DatabaseAuditLogger
	ConnectionInfo *SecureConnectionInfo
}

// SecureConnectionInfo holds information about the secure connection
type SecureConnectionInfo struct {
	SSLMode          string
	TLSVersion       string
	CipherSuite      string
	CertificateInfo  string
	ConnectionTime   time.Time
	LastHealthCheck  time.Time
	SecurityFeatures []string
}

// NewSecureDB creates a new secure database connection with enhanced security
func NewSecureDB(cfg *config.Config) (*SecureDB, error) {
	log.Println("🔐 Creating secure database connection with enhanced security...")

	// Initialize audit logger
	auditLogger, err := NewDatabaseAuditLogger(cfg)
	if err != nil {
		log.Printf("⚠️ Failed to initialize audit logger: %v", err)
		// Continue without audit logging rather than failing
	}

	// Build secure connection string with encryption
	connString, err := buildSecureConnectionString(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to build secure connection string: %w", err)
	}

	// Parse config for pgxpool with security enhancements
	poolConfig, err := pgxpool.ParseConfig(connString)
	if err != nil {
		return nil, fmt.Errorf("failed to parse secure connection string: %w", err)
	}

	// Configure secure connection pool settings
	configureSecurePool(poolConfig, cfg)

	// Configure TLS/SSL settings
	if err := configureTLS(poolConfig, cfg); err != nil {
		return nil, fmt.Errorf("failed to configure TLS: %w", err)
	}

	// Create connection pool with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create secure connection pool: %w", err)
	}

	// Test secure connection
	log.Println("🔍 Testing secure database connection...")
	if err := testSecureConnection(ctx, pool); err != nil {
		pool.Close()
		return nil, fmt.Errorf("secure connection test failed: %w", err)
	}

	// Get connection info for security verification
	connInfo, err := getSecureConnectionInfo(ctx, pool, cfg)
	if err != nil {
		log.Printf("⚠️ Failed to get connection info: %v", err)
	}

	secureDB := &SecureDB{
		Pool:           pool,
		Config:         cfg,
		AuditLogger:    auditLogger,
		ConnectionInfo: connInfo,
	}

	// Log successful secure connection
	if auditLogger != nil {
		auditLogger.LogConnection("SECURE_CONNECTION_ESTABLISHED", "Database connection established with enhanced security")
	}

	log.Println("✅ Secure database connection established successfully")
	logSecurityFeatures(connInfo)

	return secureDB, nil
}

// buildSecureConnectionString creates a secure connection string with encryption
func buildSecureConnectionString(cfg *config.Config) (string, error) {
	// Validate required configuration
	if cfg.Database.Host == "" || cfg.Database.Username == "" || cfg.Database.Password == "" {
		return "", fmt.Errorf("missing required database configuration")
	}

	// Build secure connection string with SSL/TLS enforcement
	var connParams []string

	// Basic connection parameters
	connParams = append(connParams,
		fmt.Sprintf("host=%s", cfg.Database.Host),
		fmt.Sprintf("port=%d", cfg.Database.Port),
		fmt.Sprintf("user=%s", cfg.Database.Username),
		fmt.Sprintf("password=%s", cfg.Database.Password),
		fmt.Sprintf("dbname=%s", cfg.Database.Database),
	)

	// SSL/TLS configuration - enforce secure connections
	sslMode := cfg.Database.SSLMode
	if sslMode == "" || sslMode == "disable" {
		log.Println("⚠️ SSL disabled - enforcing require mode for security")
		sslMode = "require"
	}
	connParams = append(connParams, fmt.Sprintf("sslmode=%s", sslMode))

	// Additional security parameters
	connParams = append(connParams,
		"TimeZone=UTC",
		"connect_timeout=30",
		"statement_timeout=30000",                   // 30 seconds
		"idle_in_transaction_session_timeout=60000", // 1 minute
	)

	// Build final connection string
	connString := "postgres://?" + strings.Join(connParams, "&")

	// Log connection attempt (without password)
	safeConnString := strings.ReplaceAll(connString, cfg.Database.Password, "***")
	log.Printf("🔐 Secure connection string: %s", safeConnString)

	return connString, nil
}

// configureSecurePool sets up secure connection pool configuration
func configureSecurePool(poolConfig *pgxpool.Config, cfg *config.Config) {
	// Connection pool limits for security
	poolConfig.MaxConns = int32(cfg.Database.MaxOpenConns)
	poolConfig.MinConns = int32(cfg.Database.MaxIdleConns)

	// Connection lifecycle for security
	poolConfig.MaxConnLifetime = cfg.Database.ConnMaxLifetime
	poolConfig.MaxConnIdleTime = cfg.Database.ConnMaxIdleTime

	// Health check frequency
	poolConfig.HealthCheckPeriod = time.Minute

	// Connection timeout for security
	poolConfig.ConnConfig.ConnectTimeout = 30 * time.Second

	log.Printf("🔧 Secure pool configured: MaxConns=%d, MinConns=%d, MaxLifetime=%v",
		poolConfig.MaxConns, poolConfig.MinConns, poolConfig.MaxConnLifetime)
}

// configureTLS sets up TLS/SSL configuration for database connections
func configureTLS(poolConfig *pgxpool.Config, cfg *config.Config) error {
	// Configure TLS settings based on SSL mode
	switch cfg.Database.SSLMode {
	case "require", "verify-ca", "verify-full":
		// Configure TLS for secure connections
		tlsConfig := &tls.Config{
			MinVersion: tls.VersionTLS12, // Enforce TLS 1.2 minimum
			MaxVersion: tls.VersionTLS13, // Allow TLS 1.3
		}

		// For production, you might want to configure custom CA certificates
		if cfg.App.Environment == "production" {
			// Load custom CA certificates if available
			if caCertPath := os.Getenv("CARNOW_DB_CA_CERT_PATH"); caCertPath != "" {
				if err := loadCACertificate(tlsConfig, caCertPath); err != nil {
					log.Printf("⚠️ Failed to load CA certificate: %v", err)
				}
			}
		}

		poolConfig.ConnConfig.TLSConfig = tlsConfig
		log.Printf("🔐 TLS configured: MinVersion=TLS1.2, MaxVersion=TLS1.3, SSLMode=%s", cfg.Database.SSLMode)

	case "disable":
		log.Println("⚠️ SSL/TLS disabled - this is not recommended for production")

	default:
		log.Printf("🔧 Using default SSL mode: %s", cfg.Database.SSLMode)
	}

	return nil
}

// loadCACertificate loads a custom CA certificate for database connections
func loadCACertificate(tlsConfig *tls.Config, caCertPath string) error {
	caCert, err := os.ReadFile(caCertPath)
	if err != nil {
		return fmt.Errorf("failed to read CA certificate: %w", err)
	}

	caCertPool := x509.NewCertPool()
	if !caCertPool.AppendCertsFromPEM(caCert) {
		return fmt.Errorf("failed to parse CA certificate")
	}

	tlsConfig.RootCAs = caCertPool
	log.Printf("✅ Custom CA certificate loaded from: %s", caCertPath)

	return nil
}

// testSecureConnection performs comprehensive security tests on the database connection
func testSecureConnection(ctx context.Context, pool *pgxpool.Pool) error {
	// Basic connectivity test
	if err := pool.Ping(ctx); err != nil {
		return fmt.Errorf("basic connectivity test failed: %w", err)
	}

	// Test query execution
	var version string
	err := pool.QueryRow(ctx, "SELECT version()").Scan(&version)
	if err != nil {
		return fmt.Errorf("query execution test failed: %w", err)
	}

	log.Printf("🔍 Database version: %s", version)

	// Test SSL/TLS connection status
	var sslStatus string
	err = pool.QueryRow(ctx, "SHOW ssl").Scan(&sslStatus)
	if err != nil {
		log.Printf("⚠️ Could not check SSL status: %v", err)
	} else {
		log.Printf("🔐 SSL Status: %s", sslStatus)
	}

	return nil
}

// getSecureConnectionInfo retrieves information about the secure connection
func getSecureConnectionInfo(ctx context.Context, pool *pgxpool.Pool, cfg *config.Config) (*SecureConnectionInfo, error) {
	info := &SecureConnectionInfo{
		SSLMode:        cfg.Database.SSLMode,
		ConnectionTime: time.Now(),
		SecurityFeatures: []string{
			"Connection Pooling",
			"Connection Timeout",
			"Statement Timeout",
			"Idle Transaction Timeout",
		},
	}

	// Try to get SSL/TLS information
	var sslVersion, sslCipher string
	err := pool.QueryRow(ctx, "SELECT version, cipher FROM pg_stat_ssl WHERE pid = pg_backend_pid()").Scan(&sslVersion, &sslCipher)
	if err == nil {
		info.TLSVersion = sslVersion
		info.CipherSuite = sslCipher
		info.SecurityFeatures = append(info.SecurityFeatures, "TLS Encryption", "Cipher Suite Protection")
	}

	return info, nil
}

// logSecurityFeatures logs the security features that are active
func logSecurityFeatures(info *SecureConnectionInfo) {
	if info == nil {
		return
	}

	log.Println("🛡️ Active Security Features:")
	for _, feature := range info.SecurityFeatures {
		log.Printf("  ✅ %s", feature)
	}

	if info.TLSVersion != "" {
		log.Printf("  🔐 TLS Version: %s", info.TLSVersion)
	}
	if info.CipherSuite != "" {
		log.Printf("  🔐 Cipher Suite: %s", info.CipherSuite)
	}
}

// Health checks the secure database connection health
func (db *SecureDB) Health() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Update last health check time
	if db.ConnectionInfo != nil {
		db.ConnectionInfo.LastHealthCheck = time.Now()
	}

	// Perform health check
	err := db.Pool.Ping(ctx)

	// Log health check result
	if db.AuditLogger != nil {
		if err != nil {
			db.AuditLogger.LogEvent("HEALTH_CHECK_FAILED", fmt.Sprintf("Health check failed: %v", err))
		} else {
			db.AuditLogger.LogEvent("HEALTH_CHECK_SUCCESS", "Database health check passed")
		}
	}

	return err
}

// Close closes the secure database connection pool
func (db *SecureDB) Close() {
	log.Println("🔄 Closing secure database connection pool...")

	if db.AuditLogger != nil {
		db.AuditLogger.LogConnection("SECURE_CONNECTION_CLOSED", "Database connection closed")
		db.AuditLogger.Close()
	}

	db.Pool.Close()
	log.Println("✅ Secure database connection pool closed")
}

// Query executes a query with audit logging
func (db *SecureDB) Query(ctx context.Context, query string, args ...interface{}) (pgx.Rows, error) {
	// Log query execution for audit
	if db.AuditLogger != nil {
		db.AuditLogger.LogQuery("QUERY_EXECUTED", query, len(args))
	}

	return db.Pool.Query(ctx, query, args...)
}

// QueryRow executes a query that returns a single row with audit logging
func (db *SecureDB) QueryRow(ctx context.Context, query string, args ...interface{}) pgx.Row {
	// Log query execution for audit
	if db.AuditLogger != nil {
		db.AuditLogger.LogQuery("QUERY_ROW_EXECUTED", query, len(args))
	}

	return db.Pool.QueryRow(ctx, query, args...)
}

// Exec executes a query that doesn't return rows with audit logging
func (db *SecureDB) Exec(ctx context.Context, query string, args ...interface{}) error {
	// Log query execution for audit
	if db.AuditLogger != nil {
		db.AuditLogger.LogQuery("EXEC_EXECUTED", query, len(args))
	}

	_, err := db.Pool.Exec(ctx, query, args...)
	return err
}

// Begin starts a new transaction with audit logging
func (db *SecureDB) Begin(ctx context.Context) (pgx.Tx, error) {
	// Log transaction start for audit
	if db.AuditLogger != nil {
		db.AuditLogger.LogEvent("TRANSACTION_STARTED", "Database transaction started")
	}

	return db.Pool.Begin(ctx)
}

// WithTransaction executes a function within a database transaction with audit logging
func (db *SecureDB) WithTransaction(ctx context.Context, fn func(tx pgx.Tx) error) error {
	// Log transaction start
	if db.AuditLogger != nil {
		db.AuditLogger.LogEvent("TRANSACTION_STARTED", "Database transaction started")
	}

	tx, err := db.Begin(ctx)
	if err != nil {
		if db.AuditLogger != nil {
			db.AuditLogger.LogEvent("TRANSACTION_FAILED", fmt.Sprintf("Failed to begin transaction: %v", err))
		}
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	if err := fn(tx); err != nil {
		if db.AuditLogger != nil {
			db.AuditLogger.LogEvent("TRANSACTION_ROLLED_BACK", fmt.Sprintf("Transaction rolled back: %v", err))
		}
		return err
	}

	if err := tx.Commit(ctx); err != nil {
		if db.AuditLogger != nil {
			db.AuditLogger.LogEvent("TRANSACTION_COMMIT_FAILED", fmt.Sprintf("Failed to commit transaction: %v", err))
		}
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	if db.AuditLogger != nil {
		db.AuditLogger.LogEvent("TRANSACTION_COMMITTED", "Transaction committed successfully")
	}

	return nil
}

// Stats returns database connection pool statistics
func (db *SecureDB) Stats() *pgxpool.Stat {
	return db.Pool.Stat()
}

// GetSecurityInfo returns information about the secure connection
func (db *SecureDB) GetSecurityInfo() *SecureConnectionInfo {
	return db.ConnectionInfo
}

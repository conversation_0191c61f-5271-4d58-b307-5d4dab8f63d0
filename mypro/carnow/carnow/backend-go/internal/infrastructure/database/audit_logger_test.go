package database

import (
	"testing"

	"carnow-backend/internal/config"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
)

func TestDatabaseAuditLogger_ConfigSetup(t *testing.T) {
	logger := zaptest.NewLogger(t)
	defer logger.Sync()

	config := &config.Config{
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			Username: "test_user",
			Password: "test_password",
			Database: "test_db",
			SSLMode:  "disable",
		},
	}

	// Test configuration validation
	assert.NotNil(t, config.Database)
	assert.NotEmpty(t, config.Database.Host)
	assert.NotEmpty(t, config.Database.Database)
	assert.NotNil(t, logger)
}

func TestDatabaseAuditLogger_NewDatabaseAuditLogger(t *testing.T) {
	tests := []struct {
		name    string
		config  *config.Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: &config.Config{
				Database: config.DatabaseConfig{
					Host:     "localhost",
					Port:     5432,
					Username: "test_user",
					Password: "test_password",
					Database: "test_db",
					SSLMode:  "disable",
				},
			},
			wantErr: false,
		},
		{
			name:    "nil config",
			config:  nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			auditLogger, err := NewDatabaseAuditLogger(tt.config)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, auditLogger)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, auditLogger)
			}
		})
	}
}

func TestDatabaseAuditLogger_LogQuery(t *testing.T) {
	config := &config.Config{
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			Username: "test_user",
			Password: "test_password",
			Database: "test_db",
			SSLMode:  "disable",
		},
	}

	auditLogger, err := NewDatabaseAuditLogger(config)
	assert.NoError(t, err)
	assert.NotNil(t, auditLogger)

	// Test logging queries
	testCases := []struct {
		name      string
		eventType string
		query     string
		argCount  int
	}{
		{
			name:      "SELECT query",
			eventType: "QUERY_EXECUTED",
			query:     "SELECT * FROM users WHERE id = $1",
			argCount:  1,
		},
		{
			name:      "INSERT query",
			eventType: "QUERY_EXECUTED",
			query:     "INSERT INTO users (name) VALUES ($1)",
			argCount:  1,
		},
		{
			name:      "UPDATE query",
			eventType: "QUERY_EXECUTED",
			query:     "UPDATE users SET name = $1 WHERE id = $2",
			argCount:  2,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test that LogQuery doesn't panic
			assert.NotPanics(t, func() {
				auditLogger.LogQuery(tc.eventType, tc.query, tc.argCount)
			})
		})
	}
}

func TestDatabaseAuditLogger_LogEvent(t *testing.T) {
	config := &config.Config{
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			Username: "test_user",
			Password: "test_password",
			Database: "test_db",
			SSLMode:  "disable",
		},
	}

	auditLogger, err := NewDatabaseAuditLogger(config)
	assert.NoError(t, err)
	assert.NotNil(t, auditLogger)

	// Test logging events
	testEvents := []struct {
		name      string
		eventType string
		message   string
	}{
		{
			name:      "Connection event",
			eventType: "CONNECTION_ESTABLISHED",
			message:   "Database connection established",
		},
		{
			name:      "Transaction event",
			eventType: "TRANSACTION_STARTED",
			message:   "Database transaction started",
		},
		{
			name:      "Error event",
			eventType: "CONNECTION_ERROR",
			message:   "Failed to connect to database",
		},
	}

	for _, te := range testEvents {
		t.Run(te.name, func(t *testing.T) {
			// Test that LogEvent doesn't panic
			assert.NotPanics(t, func() {
				auditLogger.LogEvent(te.eventType, te.message)
			})
		})
	}
}

func TestDatabaseAuditLogger_SecurityValidation(t *testing.T) {
	config := &config.Config{
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			Username: "test_user",
			Password: "test_password",
			Database: "test_db",
			SSLMode:  "require",
		},
	}

	// Test that audit logger can be created with security config
	auditLogger, err := NewDatabaseAuditLogger(config)
	assert.NoError(t, err)
	assert.NotNil(t, auditLogger)

	// Test security settings
	assert.Equal(t, "require", config.Database.SSLMode)
	assert.NotEmpty(t, config.Database.Password)
}

func BenchmarkDatabaseAuditLogger_LogQuery(b *testing.B) {
	config := &config.Config{
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			Username: "test_user",
			Password: "test_password",
			Database: "test_db",
			SSLMode:  "disable",
		},
	}

	auditLogger, err := NewDatabaseAuditLogger(config)
	if err != nil {
		b.Skip("Cannot create audit logger for benchmark")
	}

	query := "SELECT * FROM users WHERE id = $1"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		auditLogger.LogQuery("QUERY_EXECUTED", query, 1)
	}
}

func BenchmarkDatabaseAuditLogger_LogEvent(b *testing.B) {
	config := &config.Config{
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			Username: "test_user",
			Password: "test_password",
			Database: "test_db",
			SSLMode:  "disable",
		},
	}

	auditLogger, err := NewDatabaseAuditLogger(config)
	if err != nil {
		b.Skip("Cannot create audit logger for benchmark")
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		auditLogger.LogEvent("TEST_EVENT", "Benchmark test event")
	}
}

package database

import (
	"fmt"
	"log"
	"strings"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/core/domain"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Database holds the database connection and configuration
type Database struct {
	DB     *gorm.DB
	Config *config.Config
}

// NewDatabase creates a new database connection
func NewDatabase(cfg *config.Config) (*Database, error) {
	// إضافة logging للتشخيص
	log.Println("🔄 إنشاء اتصال بسيط بقاعدة البيانات...")
	log.Printf("Host: %s", cfg.Database.Host)
	log.Printf("Port: %d", cfg.Database.Port)
	log.Printf("Database: %s", cfg.Database.Database)
	log.Printf("SSL Mode: %s", cfg.Database.SSLMode)

	// Build simple DSN with prepared statement cache disabled
	dsn := BuildSimpleDSN(cfg) + " statement_cache_size=0 prefer_simple_protocol=true"

	// إخفاء كلمة المرور من الـ logs
	safeDSN := strings.ReplaceAll(dsn, cfg.Database.Password, "***")
	log.Printf("DSN: %s", safeDSN)

	// Configure GORM with simple default settings
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
		// Disable prepared statement caching to avoid SQLSTATE 42P05 errors
		PrepareStmt: false,
		// Disable foreign key constraints for simplified operations
		DisableForeignKeyConstraintWhenMigrating: true,
		// Skip default transaction for better performance
		SkipDefaultTransaction: true,
	}

	// Open database connection
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		log.Printf("❌ فشل في الاتصال بقاعدة البيانات: %v", err)
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Get underlying SQL DB for basic configuration
	sqlDB, err := db.DB()
	if err != nil {
		log.Printf("❌ فشل في الحصول على SQL DB: %v", err)
		return nil, fmt.Errorf("failed to get underlying SQL DB: %w", err)
	}

	// Simple connection pool settings
	sqlDB.SetMaxOpenConns(10)
	sqlDB.SetMaxIdleConns(5)
	sqlDB.SetConnMaxLifetime(time.Hour)
	sqlDB.SetConnMaxIdleTime(time.Minute * 30)

	// Test connection
	log.Println("🔄 اختبار الاتصال...")
	if err := sqlDB.Ping(); err != nil {
		log.Printf("❌ فشل في ping قاعدة البيانات: %v", err)
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	database := &Database{
		DB:     db,
		Config: cfg,
	}

	// Skip auto-migration for Forever Plan (using existing unified database)
	log.Println("⚠️ Skipping auto-migration (Forever Plan: using existing unified database)")

	log.Println("✅ Database connection established successfully")
	return database, nil
}

// BuildSimpleDSN builds a simple DSN for Supabase
func BuildSimpleDSN(cfg *config.Config) string {
	dsn := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=UTC",
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Database,
		cfg.Database.SSLMode,
	)

	return dsn
}

// AutoMigrate runs auto-migration for all models
func (d *Database) AutoMigrate() error {
	log.Println("🔄 Running database auto-migration...")

	err := d.DB.AutoMigrate(
		&domain.User{},
		&domain.Wallet{},
		&domain.WalletTransaction{},
		&domain.Product{},
		&domain.SellerSubscriptionRequest{},
		&domain.SellerSubscriptionRequestReview{},
	)

	if err != nil {
		return fmt.Errorf("auto-migration failed: %w", err)
	}

	log.Println("✅ Database auto-migration completed successfully")
	return nil
}

// Health checks the database connection health
func (d *Database) Health() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get SQL DB: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

// Close closes the database connection
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get SQL DB for closing: %w", err)
	}

	return sqlDB.Close()
}

// GetDB returns the underlying GORM DB instance
func (d *Database) GetDB() *gorm.DB {
	return d.DB
}

// Transaction executes a function within a database transaction
func (d *Database) Transaction(fn func(tx *gorm.DB) error) error {
	return d.DB.Transaction(fn)
}

// Stats returns database connection statistics
func (d *Database) Stats() map[string]interface{} {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return map[string]interface{}{
			"error": "failed to get SQL DB",
		}
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"open_connections":     stats.OpenConnections,
		"in_use":               stats.InUse,
		"idle":                 stats.Idle,
		"wait_count":           stats.WaitCount,
		"wait_duration":        stats.WaitDuration.String(),
		"max_idle_closed":      stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed":  stats.MaxLifetimeClosed,
	}
}

// Raw executes a raw SQL query
func (d *Database) Raw(sql string, values ...interface{}) *gorm.DB {
	return d.DB.Raw(sql, values...)
}

// Exec executes a raw SQL statement
func (d *Database) Exec(sql string, values ...interface{}) *gorm.DB {
	return d.DB.Exec(sql, values...)
}

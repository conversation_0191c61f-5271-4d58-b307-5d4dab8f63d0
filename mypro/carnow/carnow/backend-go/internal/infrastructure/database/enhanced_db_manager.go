package database

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"carnow-backend/internal/config"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// EnhancedDBManager provides enhanced database management with performance optimization
type EnhancedDBManager struct {
	*SecureDB
	queryOptimizer     *QueryOptimizer
	performanceMonitor *PerformanceMonitor
	readReplicaManager *ReadReplicaManager
	config             *config.Config
	logger             *log.Logger
}

// NewEnhancedDBManager creates a new enhanced database manager
func NewEnhancedDBManager(cfg *config.Config) (*EnhancedDBManager, error) {
	logger := log.New(log.Writer(), "[EnhancedDB] ", log.LstdFlags)
	logger.Printf("🚀 Initializing enhanced database manager")

	// Create secure database connection
	secureDB, err := NewSecureDB(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create secure database: %w", err)
	}

	// Initialize query optimizer
	queryOptimizer := NewQueryOptimizer(secureDB.Pool)

	// Initialize performance monitor with 100ms slow query threshold
	performanceMonitor := NewPerformanceMonitor(secureDB.Pool, 100*time.Millisecond)

	// Initialize read replica manager (if replicas are configured)
	var readReplicaManager *ReadReplicaManager
	if len(cfg.Database.ReadReplicas) > 0 {
		replicaConfigs := make([]ReplicaConfig, len(cfg.Database.ReadReplicas))
		for i, replica := range cfg.Database.ReadReplicas {
			replicaConfigs[i] = ReplicaConfig{
				Host:     replica.Host,
				Port:     replica.Port,
				Database: replica.Database,
				Username: replica.Username,
				Password: replica.Password,
				SSLMode:  replica.SSLMode,
				Weight:   replica.Weight,
			}
		}
		readReplicaManager = NewReadReplicaManager(secureDB.Pool, replicaConfigs)
	}

	enhancedDB := &EnhancedDBManager{
		SecureDB:           secureDB,
		queryOptimizer:     queryOptimizer,
		performanceMonitor: performanceMonitor,
		readReplicaManager: readReplicaManager,
		config:             cfg,
		logger:             logger,
	}

	logger.Printf("✅ Enhanced database manager initialized successfully")
	return enhancedDB, nil
}

// Initialize initializes all components of the enhanced database manager
func (edb *EnhancedDBManager) Initialize(ctx context.Context) error {
	edb.logger.Printf("🔄 Initializing enhanced database components")

	// Initialize read replicas if configured
	if edb.readReplicaManager != nil {
		if err := edb.readReplicaManager.Initialize(ctx); err != nil {
			edb.logger.Printf("⚠️ Failed to initialize read replicas: %v", err)
			// Continue without read replicas
		} else {
			// Start health checks for read replicas
			go edb.readReplicaManager.StartHealthChecks(ctx, 30*time.Second)
		}
	}

	// Start performance monitoring
	go edb.performanceMonitor.StartPeriodicMetricsCollection(ctx, 60*time.Second)

	// Run initial database optimization
	if err := edb.runInitialOptimization(ctx); err != nil {
		edb.logger.Printf("⚠️ Initial optimization failed: %v", err)
	}

	edb.logger.Printf("✅ Enhanced database manager fully initialized")
	return nil
}

// QueryWithOptimization executes a query with performance monitoring and optimization
func (edb *EnhancedDBManager) QueryWithOptimization(ctx context.Context, query string, args ...interface{}) (pgx.Rows, error) {
	startTime := time.Now()

	// Use read replica for SELECT queries if available
	pool := edb.selectPool(query)

	// Execute query
	rows, err := pool.Query(ctx, query, args...)

	// Record performance metrics
	duration := time.Since(startTime)
	edb.performanceMonitor.RecordQuery(query, duration, err)

	// Log slow queries with optimization suggestions
	if duration > edb.performanceMonitor.GetSlowQueryThreshold() {
		go edb.analyzeSlowQuery(context.Background(), query)
	}

	return rows, err
}

// QueryRowWithOptimization executes a single-row query with performance monitoring
func (edb *EnhancedDBManager) QueryRowWithOptimization(ctx context.Context, query string, args ...interface{}) pgx.Row {
	startTime := time.Now()

	// Use read replica for SELECT queries if available
	pool := edb.selectPool(query)

	// Execute query
	row := pool.QueryRow(ctx, query, args...)

	// Record performance metrics (we can't get the actual error here, so assume success)
	duration := time.Since(startTime)
	edb.performanceMonitor.RecordQuery(query, duration, nil)

	// Log slow queries with optimization suggestions
	if duration > edb.performanceMonitor.GetSlowQueryThreshold() {
		go edb.analyzeSlowQuery(context.Background(), query)
	}

	return row
}

// ExecWithOptimization executes a non-query statement with performance monitoring
func (edb *EnhancedDBManager) ExecWithOptimization(ctx context.Context, query string, args ...interface{}) error {
	startTime := time.Now()

	// Write operations always go to primary
	_, err := edb.Pool.Exec(ctx, query, args...)

	// Record performance metrics
	duration := time.Since(startTime)
	edb.performanceMonitor.RecordQuery(query, duration, err)

	// Log slow queries with optimization suggestions
	if duration > edb.performanceMonitor.GetSlowQueryThreshold() {
		go edb.analyzeSlowQuery(context.Background(), query)
	}

	return err
}

// selectPool selects the appropriate connection pool based on query type
func (edb *EnhancedDBManager) selectPool(query string) *pgxpool.Pool {
	// Simple heuristic: if query starts with SELECT and we have read replicas, use them
	if edb.readReplicaManager != nil && isReadQuery(query) {
		if pool, err := edb.readReplicaManager.GetReadPool(); err == nil {
			return pool
		}
	}

	// Fall back to primary pool
	return edb.Pool
}

// isReadQuery determines if a query is a read-only query
func isReadQuery(query string) bool {
	// Simple implementation - in production, you might want more sophisticated parsing
	trimmed := strings.TrimSpace(strings.ToUpper(query))
	return strings.HasPrefix(trimmed, "SELECT") ||
		strings.HasPrefix(trimmed, "WITH") ||
		strings.HasPrefix(trimmed, "EXPLAIN")
}

// analyzeSlowQuery analyzes a slow query and logs optimization suggestions
func (edb *EnhancedDBManager) analyzeSlowQuery(ctx context.Context, query string) {
	edb.logger.Printf("🔍 Analyzing slow query for optimization")

	plan, err := edb.queryOptimizer.AnalyzeQuery(ctx, query)
	if err != nil {
		edb.logger.Printf("⚠️ Failed to analyze query: %v", err)
		return
	}

	if len(plan.Suggestions) > 0 {
		edb.logger.Printf("💡 Optimization suggestions for slow query:")
		for _, suggestion := range plan.Suggestions {
			edb.logger.Printf("  - %s", suggestion)
		}
	}
}

// runInitialOptimization runs initial database optimization tasks
func (edb *EnhancedDBManager) runInitialOptimization(ctx context.Context) error {
	edb.logger.Printf("🔧 Running initial database optimization")

	// Enable pg_stat_statements if not already enabled
	if err := edb.enablePgStatStatements(ctx); err != nil {
		edb.logger.Printf("⚠️ Failed to enable pg_stat_statements: %v", err)
	}

	// Analyze database statistics
	if err := edb.updateTableStatistics(ctx); err != nil {
		edb.logger.Printf("⚠️ Failed to update table statistics: %v", err)
	}

	return nil
}

// enablePgStatStatements enables the pg_stat_statements extension
func (edb *EnhancedDBManager) enablePgStatStatements(ctx context.Context) error {
	// Check if extension exists
	var exists bool
	err := edb.Pool.QueryRow(ctx,
		"SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements')").Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check pg_stat_statements: %w", err)
	}

	if !exists {
		// Try to create the extension (may fail due to permissions)
		_, err = edb.Pool.Exec(ctx, "CREATE EXTENSION IF NOT EXISTS pg_stat_statements")
		if err != nil {
			edb.logger.Printf("⚠️ Could not create pg_stat_statements extension: %v", err)
			return err
		}
		edb.logger.Printf("✅ pg_stat_statements extension enabled")
	}

	return nil
}

// updateTableStatistics updates table statistics for better query planning
func (edb *EnhancedDBManager) updateTableStatistics(ctx context.Context) error {
	edb.logger.Printf("📊 Updating table statistics")

	// Get list of user tables
	rows, err := edb.Pool.Query(ctx,
		"SELECT schemaname, tablename FROM pg_tables WHERE schemaname NOT IN ('information_schema', 'pg_catalog')")
	if err != nil {
		return fmt.Errorf("failed to get table list: %w", err)
	}
	defer rows.Close()

	tableCount := 0
	for rows.Next() {
		var schema, table string
		if err := rows.Scan(&schema, &table); err != nil {
			continue
		}

		// Run ANALYZE on each table
		analyzeQuery := fmt.Sprintf("ANALYZE %s.%s", schema, table)
		if _, err := edb.Pool.Exec(ctx, analyzeQuery); err != nil {
			edb.logger.Printf("⚠️ Failed to analyze table %s.%s: %v", schema, table, err)
			continue
		}

		tableCount++
	}

	edb.logger.Printf("✅ Updated statistics for %d tables", tableCount)
	return nil
}

// GetPerformanceMetrics returns current performance metrics
func (edb *EnhancedDBManager) GetPerformanceMetrics() *PerformanceMetrics {
	return edb.performanceMonitor.GetMetrics()
}

// GetDatabaseStats returns comprehensive database statistics
func (edb *EnhancedDBManager) GetDatabaseStats(ctx context.Context) (map[string]interface{}, error) {
	stats, err := edb.performanceMonitor.GetDatabaseStats(ctx)
	if err != nil {
		return nil, err
	}

	// Add read replica stats if available
	if edb.readReplicaManager != nil {
		stats["read_replicas"] = edb.readReplicaManager.GetStats()
	}

	return stats, nil
}

// OptimizeQuery analyzes and optimizes a query
func (edb *EnhancedDBManager) OptimizeQuery(ctx context.Context, query string) (*QueryPlan, error) {
	return edb.queryOptimizer.AnalyzeQuery(ctx, query)
}

// GetSlowQueries returns slow queries from pg_stat_statements
func (edb *EnhancedDBManager) GetSlowQueries(ctx context.Context, limit int) ([]SlowQuery, error) {
	return edb.queryOptimizer.GetSlowQueries(ctx, limit)
}

// SuggestIndexes suggests indexes for a table based on query patterns
func (edb *EnhancedDBManager) SuggestIndexes(ctx context.Context, tableName string, queries []string) ([]IndexSuggestion, error) {
	return edb.queryOptimizer.SuggestIndexes(ctx, tableName, queries)
}

// Close closes all database connections and stops background processes
func (edb *EnhancedDBManager) Close() {
	edb.logger.Printf("🔄 Closing enhanced database manager")

	// Close read replica manager
	if edb.readReplicaManager != nil {
		edb.readReplicaManager.Close()
	}

	// Close secure database
	edb.SecureDB.Close()

	edb.logger.Printf("✅ Enhanced database manager closed")
}

package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// Redis Cache for Phase 4: Performance Optimization
//
// Features:
// - Set up Redis cluster for caching
// - Implement cache-aside pattern for frequently accessed data
// - Create cache invalidation strategies
// - Add cache performance monitoring
// - Cache warming strategies
// - Cache hit rate monitoring
// - Distributed caching support
// - Cache eviction policies

// RedisCache provides Redis-based caching functionality
type RedisCache struct {
	client   redis.UniversalClient
	config   *RedisCacheConfig
	logger   *zap.Logger
	metrics  *CacheMetrics
	warmer   *CacheWarmer
	patterns *InvalidationPatterns
	mutex    sync.RWMutex
}

// RedisCacheConfig contains Redis cache configuration
type RedisCacheConfig struct {
	// Redis connection configuration
	Addrs        []string      // Redis cluster addresses
	Password     string        // Redis password
	DB           int           // Redis database number
	PoolSize     int           // Connection pool size
	MinIdleConns int           // Minimum idle connections
	MaxRetries   int           // Maximum retry attempts
	RetryDelay   time.Duration // Delay between retries
	DialTimeout  time.Duration // Connection timeout
	ReadTimeout  time.Duration // Read timeout
	WriteTimeout time.Duration // Write timeout
	IdleTimeout  time.Duration // Idle connection timeout

	// Cache behavior configuration
	DefaultTTL           time.Duration // Default cache TTL
	KeyPrefix            string        // Prefix for all cache keys
	EnableCompression    bool          // Enable value compression
	CompressionThreshold int           // Minimum size for compression
	MaxKeyLength         int           // Maximum cache key length
	MaxValueSize         int           // Maximum cache value size

	// Performance configuration
	EnableMetrics      bool          // Enable metrics collection
	MetricsInterval    time.Duration // Metrics collection interval
	EnableCacheWarming bool          // Enable cache warming
	WarmingInterval    time.Duration // Cache warming interval
	BatchSize          int           // Batch size for operations

	// Eviction configuration
	EvictionPolicy  EvictionPolicy // Cache eviction policy
	MaxMemoryPolicy string         // Redis maxmemory policy

	// Monitoring configuration
	HealthCheckInterval time.Duration // Health check interval
	AlertThresholds     CacheAlertThresholds
}

// EvictionPolicy defines cache eviction strategies
type EvictionPolicy string

const (
	EvictionLRU        EvictionPolicy = "allkeys-lru"
	EvictionLFU        EvictionPolicy = "allkeys-lfu"
	EvictionRandom     EvictionPolicy = "allkeys-random"
	EvictionTTL        EvictionPolicy = "volatile-ttl"
	EvictionNoEviction EvictionPolicy = "noeviction"
)

// CacheAlertThresholds defines alert thresholds for cache monitoring
type CacheAlertThresholds struct {
	LowHitRate          float64       // Alert when hit rate is below this
	HighMemoryUsage     float64       // Alert when memory usage exceeds this
	HighConnectionCount int           // Alert when connection count exceeds this
	SlowOperationTime   time.Duration // Alert when operations are slower than this
}

// DefaultRedisCacheConfig returns optimized default configuration
func DefaultRedisCacheConfig() *RedisCacheConfig {
	return &RedisCacheConfig{
		Addrs:        []string{"localhost:6379"},
		Password:     "",
		DB:           0,
		PoolSize:     10,
		MinIdleConns: 5,
		MaxRetries:   3,
		RetryDelay:   time.Second,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		IdleTimeout:  5 * time.Minute,

		DefaultTTL:           time.Hour,
		KeyPrefix:            "carnow:",
		EnableCompression:    true,
		CompressionThreshold: 1024, // 1KB
		MaxKeyLength:         250,
		MaxValueSize:         1024 * 1024, // 1MB

		EnableMetrics:      true,
		MetricsInterval:    30 * time.Second,
		EnableCacheWarming: true,
		WarmingInterval:    5 * time.Minute,
		BatchSize:          100,

		EvictionPolicy:  EvictionLRU,
		MaxMemoryPolicy: "allkeys-lru",

		HealthCheckInterval: time.Minute,
		AlertThresholds: CacheAlertThresholds{
			LowHitRate:          0.7,
			HighMemoryUsage:     0.8,
			HighConnectionCount: 100,
			SlowOperationTime:   100 * time.Millisecond,
		},
	}
}

// CacheMetrics tracks cache performance statistics
type CacheMetrics struct {
	// Hit/Miss statistics
	Hits      int64
	Misses    int64
	Sets      int64
	Deletes   int64
	Evictions int64

	// Performance statistics
	HitRate         float64
	AverageGetTime  time.Duration
	AverageSetTime  time.Duration
	TotalOperations int64
	SlowOperations  int64

	// Memory statistics
	UsedMemory         int64
	MaxMemory          int64
	MemoryUsagePercent float64
	KeyCount           int64

	// Connection statistics
	PoolSize        int
	PoolActiveConns int
	PoolIdleConns   int

	// Error statistics
	ConnectionErrors    int64
	TimeoutErrors       int64
	SerializationErrors int64

	// Timing
	LastUpdated   time.Time
	UptimeSeconds int64
}

// CacheWarmer manages cache warming operations
type CacheWarmer struct {
	cache     *RedisCache
	warmers   map[string]WarmupFunction
	config    *RedisCacheConfig
	logger    *zap.Logger
	isRunning bool
	mutex     sync.RWMutex
}

// WarmupFunction defines a function for cache warming
type WarmupFunction func(ctx context.Context) error

// InvalidationPatterns manages cache invalidation patterns
type InvalidationPatterns struct {
	patterns map[string][]string // Key pattern -> dependent keys
	mutex    sync.RWMutex
}

// CacheEntry represents a cached entry with metadata
type CacheEntry struct {
	Key         string
	Value       interface{}
	TTL         time.Duration
	CreatedAt   time.Time
	AccessedAt  time.Time
	AccessCount int64
	Tags        []string
}

// NewRedisCache creates a new Redis cache instance
func NewRedisCache(config *RedisCacheConfig, logger *zap.Logger) (*RedisCache, error) {
	if config == nil {
		config = DefaultRedisCacheConfig()
	}

	// Create Redis client
	var client redis.UniversalClient
	if len(config.Addrs) > 1 {
		// Cluster configuration
		client = redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:        config.Addrs,
			Password:     config.Password,
			PoolSize:     config.PoolSize,
			MinIdleConns: config.MinIdleConns,
			MaxRetries:   config.MaxRetries,
			DialTimeout:  config.DialTimeout,
			ReadTimeout:  config.ReadTimeout,
			WriteTimeout: config.WriteTimeout,
		})
	} else {
		// Single instance configuration
		client = redis.NewClient(&redis.Options{
			Addr:         config.Addrs[0],
			Password:     config.Password,
			DB:           config.DB,
			PoolSize:     config.PoolSize,
			MinIdleConns: config.MinIdleConns,
			MaxRetries:   config.MaxRetries,
			DialTimeout:  config.DialTimeout,
			ReadTimeout:  config.ReadTimeout,
			WriteTimeout: config.WriteTimeout,
		})
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	cache := &RedisCache{
		client: client,
		config: config,
		logger: logger,
		metrics: &CacheMetrics{
			LastUpdated: time.Now(),
		},
		warmer: &CacheWarmer{
			warmers: make(map[string]WarmupFunction),
			config:  config,
			logger:  logger,
		},
		patterns: &InvalidationPatterns{
			patterns: make(map[string][]string),
		},
	}

	// Set warmer reference
	cache.warmer.cache = cache

	// Start monitoring if enabled
	if config.EnableMetrics {
		go cache.startMetricsCollection()
	}

	// Start cache warming if enabled
	if config.EnableCacheWarming {
		go cache.startCacheWarming()
	}

	// Start health monitoring
	go cache.startHealthMonitoring()

	logger.Info("Redis cache initialized",
		zap.Strings("addrs", config.Addrs),
		zap.String("key_prefix", config.KeyPrefix),
		zap.Duration("default_ttl", config.DefaultTTL),
		zap.Bool("compression_enabled", config.EnableCompression),
	)

	return cache, nil
}

// Get retrieves a value from the cache using cache-aside pattern
func (rc *RedisCache) Get(ctx context.Context, key string, dest interface{}) error {
	start := time.Now()
	defer func() {
		rc.updateGetMetrics(time.Since(start))
	}()

	// Validate and normalize key
	normalizedKey := rc.normalizeKey(key)
	if err := rc.validateKey(normalizedKey); err != nil {
		return fmt.Errorf("invalid cache key: %w", err)
	}

	// Get from Redis
	val, err := rc.client.Get(ctx, normalizedKey).Result()
	if err != nil {
		if err == redis.Nil {
			// Cache miss
			rc.recordMiss()
			return ErrCacheMiss
		}
		rc.recordError("get", err)
		return fmt.Errorf("cache get error: %w", err)
	}

	// Cache hit
	rc.recordHit()

	// Deserialize value
	if err := rc.deserialize([]byte(val), dest); err != nil {
		rc.recordSerializationError()
		return fmt.Errorf("deserialization error: %w", err)
	}

	return nil
}

// Set stores a value in the cache with the specified TTL
func (rc *RedisCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	start := time.Now()
	defer func() {
		rc.updateSetMetrics(time.Since(start))
	}()

	// Validate and normalize key
	normalizedKey := rc.normalizeKey(key)
	if err := rc.validateKey(normalizedKey); err != nil {
		return fmt.Errorf("invalid cache key: %w", err)
	}

	// Serialize value
	data, err := rc.serialize(value)
	if err != nil {
		rc.recordSerializationError()
		return fmt.Errorf("serialization error: %w", err)
	}

	// Validate value size
	if len(data) > rc.config.MaxValueSize {
		return fmt.Errorf("value too large: %d bytes (max: %d)", len(data), rc.config.MaxValueSize)
	}

	// Use default TTL if not specified
	if ttl <= 0 {
		ttl = rc.config.DefaultTTL
	}

	// Set in Redis
	if err := rc.client.Set(ctx, normalizedKey, data, ttl).Err(); err != nil {
		rc.recordError("set", err)
		return fmt.Errorf("cache set error: %w", err)
	}

	rc.recordSet()
	return nil
}

// Delete removes a value from the cache
func (rc *RedisCache) Delete(ctx context.Context, key string) error {
	normalizedKey := rc.normalizeKey(key)
	if err := rc.validateKey(normalizedKey); err != nil {
		return fmt.Errorf("invalid cache key: %w", err)
	}

	deleted, err := rc.client.Del(ctx, normalizedKey).Result()
	if err != nil {
		rc.recordError("delete", err)
		return fmt.Errorf("cache delete error: %w", err)
	}

	if deleted > 0 {
		rc.recordDelete()
	}

	return nil
}

// GetOrSet implements cache-aside pattern: get from cache, or set if not found
func (rc *RedisCache) GetOrSet(ctx context.Context, key string, dest interface{}, fetchFunc func() (interface{}, error), ttl time.Duration) error {
	// Try to get from cache first
	err := rc.Get(ctx, key, dest)
	if err == nil {
		return nil // Cache hit
	}

	if err != ErrCacheMiss {
		// Real error, not just cache miss
		rc.logger.Error("Cache get error in GetOrSet", zap.Error(err))
	}

	// Cache miss - fetch the data
	value, err := fetchFunc()
	if err != nil {
		return fmt.Errorf("fetch function error: %w", err)
	}

	// Set in cache for next time
	if setErr := rc.Set(ctx, key, value, ttl); setErr != nil {
		rc.logger.Error("Failed to set cache in GetOrSet", zap.Error(setErr))
		// Don't fail the request just because cache set failed
	}

	// Copy the fetched value to destination
	if err := rc.copyValue(value, dest); err != nil {
		return fmt.Errorf("failed to copy value: %w", err)
	}

	return nil
}

// SetMany sets multiple key-value pairs in a batch
func (rc *RedisCache) SetMany(ctx context.Context, items map[string]interface{}, ttl time.Duration) error {
	if len(items) == 0 {
		return nil
	}

	pipe := rc.client.Pipeline()

	for key, value := range items {
		normalizedKey := rc.normalizeKey(key)
		if err := rc.validateKey(normalizedKey); err != nil {
			return fmt.Errorf("invalid cache key %s: %w", key, err)
		}

		data, err := rc.serialize(value)
		if err != nil {
			return fmt.Errorf("serialization error for key %s: %w", key, err)
		}

		if ttl <= 0 {
			ttl = rc.config.DefaultTTL
		}

		pipe.Set(ctx, normalizedKey, data, ttl)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		rc.recordError("setmany", err)
		return fmt.Errorf("batch set error: %w", err)
	}

	rc.recordSets(int64(len(items)))
	return nil
}

// DeleteMany deletes multiple keys in a batch
func (rc *RedisCache) DeleteMany(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	normalizedKeys := make([]string, len(keys))
	for i, key := range keys {
		normalizedKeys[i] = rc.normalizeKey(key)
		if err := rc.validateKey(normalizedKeys[i]); err != nil {
			return fmt.Errorf("invalid cache key %s: %w", key, err)
		}
	}

	deleted, err := rc.client.Del(ctx, normalizedKeys...).Result()
	if err != nil {
		rc.recordError("deletemany", err)
		return fmt.Errorf("batch delete error: %w", err)
	}

	rc.recordDeletes(deleted)
	return nil
}

// InvalidatePattern deletes all keys matching a pattern
func (rc *RedisCache) InvalidatePattern(ctx context.Context, pattern string) error {
	normalizedPattern := rc.normalizeKey(pattern)

	// Find all keys matching the pattern
	keys, err := rc.client.Keys(ctx, normalizedPattern).Result()
	if err != nil {
		return fmt.Errorf("pattern scan error: %w", err)
	}

	if len(keys) == 0 {
		return nil
	}

	// Delete all matching keys
	deleted, err := rc.client.Del(ctx, keys...).Result()
	if err != nil {
		rc.recordError("invalidatepattern", err)
		return fmt.Errorf("pattern delete error: %w", err)
	}

	rc.recordDeletes(deleted)
	rc.logger.Info("Cache pattern invalidated",
		zap.String("pattern", normalizedPattern),
		zap.Int64("deleted_keys", deleted),
	)

	return nil
}

// Expire sets a TTL on an existing key
func (rc *RedisCache) Expire(ctx context.Context, key string, ttl time.Duration) error {
	normalizedKey := rc.normalizeKey(key)
	success, err := rc.client.Expire(ctx, normalizedKey, ttl).Result()
	if err != nil {
		return fmt.Errorf("expire error: %w", err)
	}

	if !success {
		return ErrKeyNotFound
	}

	return nil
}

// TTL returns the remaining TTL of a key
func (rc *RedisCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	normalizedKey := rc.normalizeKey(key)
	ttl, err := rc.client.TTL(ctx, normalizedKey).Result()
	if err != nil {
		return 0, fmt.Errorf("ttl error: %w", err)
	}

	return ttl, nil
}

// Exists checks if a key exists in the cache
func (rc *RedisCache) Exists(ctx context.Context, key string) (bool, error) {
	normalizedKey := rc.normalizeKey(key)
	count, err := rc.client.Exists(ctx, normalizedKey).Result()
	if err != nil {
		return false, fmt.Errorf("exists error: %w", err)
	}

	return count > 0, nil
}

// normalizeKey normalizes a cache key with prefix
func (rc *RedisCache) normalizeKey(key string) string {
	if rc.config.KeyPrefix != "" {
		return rc.config.KeyPrefix + key
	}
	return key
}

// validateKey validates a cache key
func (rc *RedisCache) validateKey(key string) error {
	if len(key) == 0 {
		return ErrEmptyKey
	}

	if len(key) > rc.config.MaxKeyLength {
		return ErrKeyTooLong
	}

	// Check for invalid characters
	if strings.Contains(key, " ") || strings.Contains(key, "\n") || strings.Contains(key, "\r") {
		return ErrInvalidKeyChars
	}

	return nil
}

// serialize serializes a value to bytes
func (rc *RedisCache) serialize(value interface{}) ([]byte, error) {
	data, err := json.Marshal(value)
	if err != nil {
		return nil, err
	}

	// Apply compression if enabled and value is large enough
	if rc.config.EnableCompression && len(data) >= rc.config.CompressionThreshold {
		// In production, you would implement compression here
		// For now, just return the raw data
	}

	return data, nil
}

// deserialize deserializes bytes to a value
func (rc *RedisCache) deserialize(data []byte, dest interface{}) error {
	// In production, you would check for compression headers and decompress

	return json.Unmarshal(data, dest)
}

// copyValue copies a value to destination (used in GetOrSet)
func (rc *RedisCache) copyValue(src interface{}, dest interface{}) error {
	// Simple JSON round-trip to copy the value
	data, err := json.Marshal(src)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, dest)
}

// Cache-specific errors
var (
	ErrCacheMiss       = fmt.Errorf("cache miss")
	ErrKeyNotFound     = fmt.Errorf("key not found")
	ErrEmptyKey        = fmt.Errorf("empty key")
	ErrKeyTooLong      = fmt.Errorf("key too long")
	ErrInvalidKeyChars = fmt.Errorf("invalid key characters")
)

// Metrics recording methods
func (rc *RedisCache) recordHit() {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()
	rc.metrics.Hits++
	rc.updateHitRate()
}

func (rc *RedisCache) recordMiss() {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()
	rc.metrics.Misses++
	rc.updateHitRate()
}

func (rc *RedisCache) recordSet() {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()
	rc.metrics.Sets++
}

func (rc *RedisCache) recordSets(count int64) {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()
	rc.metrics.Sets += count
}

func (rc *RedisCache) recordDelete() {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()
	rc.metrics.Deletes++
}

func (rc *RedisCache) recordDeletes(count int64) {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()
	rc.metrics.Deletes += count
}

func (rc *RedisCache) recordError(operation string, err error) {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()

	if strings.Contains(err.Error(), "timeout") {
		rc.metrics.TimeoutErrors++
	} else if strings.Contains(err.Error(), "connection") {
		rc.metrics.ConnectionErrors++
	}

	rc.logger.Error("Cache operation error",
		zap.String("operation", operation),
		zap.Error(err),
	)
}

func (rc *RedisCache) recordSerializationError() {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()
	rc.metrics.SerializationErrors++
}

func (rc *RedisCache) updateHitRate() {
	total := rc.metrics.Hits + rc.metrics.Misses
	if total > 0 {
		rc.metrics.HitRate = float64(rc.metrics.Hits) / float64(total)
	}
}

func (rc *RedisCache) updateGetMetrics(duration time.Duration) {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()

	rc.metrics.TotalOperations++

	// Update average get time
	if rc.metrics.AverageGetTime == 0 {
		rc.metrics.AverageGetTime = duration
	} else {
		rc.metrics.AverageGetTime = (rc.metrics.AverageGetTime + duration) / 2
	}

	// Track slow operations
	if duration > rc.config.AlertThresholds.SlowOperationTime {
		rc.metrics.SlowOperations++
	}
}

func (rc *RedisCache) updateSetMetrics(duration time.Duration) {
	rc.mutex.Lock()
	defer rc.mutex.Unlock()

	rc.metrics.TotalOperations++

	// Update average set time
	if rc.metrics.AverageSetTime == 0 {
		rc.metrics.AverageSetTime = duration
	} else {
		rc.metrics.AverageSetTime = (rc.metrics.AverageSetTime + duration) / 2
	}

	// Track slow operations
	if duration > rc.config.AlertThresholds.SlowOperationTime {
		rc.metrics.SlowOperations++
	}
}

// GetMetrics returns current cache metrics
func (rc *RedisCache) GetMetrics() *CacheMetrics {
	rc.mutex.RLock()
	defer rc.mutex.RUnlock()

	// Return a copy to avoid race conditions
	metricsCopy := *rc.metrics
	return &metricsCopy
}

// Close closes the Redis connection
func (rc *RedisCache) Close() error {
	rc.logger.Info("Closing Redis cache connection")
	return rc.client.Close()
}

// Health checks the cache health
func (rc *RedisCache) Health(ctx context.Context) error {
	return rc.client.Ping(ctx).Err()
}

// startMetricsCollection starts the metrics collection routine
func (rc *RedisCache) startMetricsCollection() {
	ticker := time.NewTicker(rc.config.MetricsInterval)
	defer ticker.Stop()

	for range ticker.C {
		rc.collectAdvancedMetrics()
	}
}

// collectAdvancedMetrics collects advanced metrics from Redis
func (rc *RedisCache) collectAdvancedMetrics() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Get Redis INFO
	info, err := rc.client.Info(ctx, "memory", "stats").Result()
	if err != nil {
		rc.logger.Error("Failed to collect Redis metrics", zap.Error(err))
		return
	}

	// Parse INFO response and update metrics
	rc.parseRedisInfo(info)

	// Update timing
	rc.mutex.Lock()
	rc.metrics.LastUpdated = time.Now()
	rc.metrics.UptimeSeconds = int64(time.Since(rc.metrics.LastUpdated).Seconds())
	rc.mutex.Unlock()
}

// parseRedisInfo parses Redis INFO response
func (rc *RedisCache) parseRedisInfo(info string) {
	// Simple INFO parsing (in production, use a more robust parser)
	lines := strings.Split(info, "\n")

	rc.mutex.Lock()
	defer rc.mutex.Unlock()

	for _, line := range lines {
		if strings.Contains(line, "used_memory:") {
			// Parse memory usage
		} else if strings.Contains(line, "keyspace_hits:") {
			// Parse keyspace hits
		}
		// Add more metric parsing as needed
	}
}

// startCacheWarming starts the cache warming routine
func (rc *RedisCache) startCacheWarming() {
	rc.warmer.Start()
}

// startHealthMonitoring starts health monitoring
func (rc *RedisCache) startHealthMonitoring() {
	ticker := time.NewTicker(rc.config.HealthCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		if err := rc.Health(ctx); err != nil {
			rc.logger.Error("Cache health check failed", zap.Error(err))
		}
		cancel()
	}
}

// RegisterWarmer registers a cache warming function
func (rc *RedisCache) RegisterWarmer(name string, warmer WarmupFunction) {
	rc.warmer.RegisterWarmer(name, warmer)
}

// Start starts the cache warmer
func (cw *CacheWarmer) Start() {
	cw.mutex.Lock()
	defer cw.mutex.Unlock()

	if cw.isRunning {
		return
	}

	cw.isRunning = true
	go cw.warmingLoop()

	cw.logger.Info("Cache warmer started",
		zap.Int("registered_warmers", len(cw.warmers)),
		zap.Duration("warming_interval", cw.config.WarmingInterval),
	)
}

// RegisterWarmer registers a warming function
func (cw *CacheWarmer) RegisterWarmer(name string, warmer WarmupFunction) {
	cw.mutex.Lock()
	defer cw.mutex.Unlock()

	cw.warmers[name] = warmer
	cw.logger.Info("Cache warmer registered", zap.String("name", name))
}

// warmingLoop runs the cache warming loop
func (cw *CacheWarmer) warmingLoop() {
	ticker := time.NewTicker(cw.config.WarmingInterval)
	defer ticker.Stop()

	for range ticker.C {
		cw.performWarming()
	}
}

// performWarming executes all registered warming functions
func (cw *CacheWarmer) performWarming() {
	cw.mutex.RLock()
	warmers := make(map[string]WarmupFunction)
	for k, v := range cw.warmers {
		warmers[k] = v
	}
	cw.mutex.RUnlock()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	for name, warmer := range warmers {
		start := time.Now()
		if err := warmer(ctx); err != nil {
			cw.logger.Error("Cache warming failed",
				zap.String("warmer", name),
				zap.Error(err),
				zap.Duration("duration", time.Since(start)),
			)
		} else {
			cw.logger.Debug("Cache warming completed",
				zap.String("warmer", name),
				zap.Duration("duration", time.Since(start)),
			)
		}
	}
}

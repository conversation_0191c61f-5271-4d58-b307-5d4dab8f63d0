# CarNow Redis Cache Infrastructure

## Overview

This package implements a comprehensive Redis cache infrastructure for the CarNow backend, designed to achieve sub-second response times and optimal performance. The implementation follows Task 2.1 requirements for Redis Cache Infrastructure Setup.

## Features

### 🚀 Multi-Tier Caching
- **L1 Cache**: In-memory cache for ultra-fast access
- **L2 Cache**: Redis cluster for distributed caching
- **HTTP Response Cache**: Automatic response caching middleware
- **Static Data Cache**: Long-term caching for reference data

### 🔥 Cache Warming
- Automatic cache warming on startup
- Scheduled cache warming for frequently accessed data
- Manual cache warming triggers
- Predictive cache warming based on access patterns

### 📊 Performance Monitoring
- Real-time cache hit/miss metrics
- Performance analytics and trending
- Memory usage monitoring
- Connection pool statistics
- Slow operation detection

### 🛡️ Reliability Features
- Automatic failover and recovery
- Circuit breaker pattern for cache operations
- Graceful degradation when cache is unavailable
- Connection pool optimization
- Health checks and monitoring

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│  Cache Provider  │  Cache Middleware  │  Cache Handlers    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Cache Service Layer                      │
├─────────────────────────────────────────────────────────────┤
│  Application Cache  │  HTTP Cache  │  Static Data Cache    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Storage Layer                            │
├─────────────────────────────────────────────────────────────┤
│  L1: Memory Cache  │  L2: Redis Cluster  │  Persistence    │
└─────────────────────────────────────────────────────────────┘
```

## Configuration

### Redis Configuration

```yaml
redis:
  enabled: true
  addrs:
    - "localhost:6379"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"
  idle_timeout: "5m"
  default_ttl: "1h"
  key_prefix: "carnow:"
```

### Environment Variables

```bash
# Redis Configuration
CARNOW_REDIS_ENABLED=true
CARNOW_REDIS_ADDRS=localhost:6379
CARNOW_REDIS_PASSWORD=your_redis_password
CARNOW_REDIS_DB=0
```

## Usage

### Basic Cache Operations

```go
import "carnow-backend/internal/infrastructure/cache"

// Initialize cache provider (done in main.go)
err := cache.InitializeCacheProvider(cfg, logger)

// Use global cache functions
ctx := context.Background()

// Set a value
err = cache.Set(ctx, "product:123", product, time.Hour)

// Get a value
var product Product
err = cache.Get(ctx, "product:123", &product)

// Get or set pattern
err = cache.GetOrSet(ctx, "user:456", &user, func() (interface{}, error) {
    return fetchUserFromDB(456)
}, 30*time.Minute)
```

### HTTP Response Caching

```go
// In your router setup
router.GET("/products", 
    middleware.CacheProductsMiddleware(cacheProvider, logger),
    handlers.GetProducts,
)

// Or with custom configuration
cacheMiddleware := middleware.NewCacheMiddleware(cacheProvider, logger)
router.GET("/categories", 
    cacheMiddleware.CacheAPI(30*time.Minute),
    handlers.GetCategories,
)
```

### Cache Key Helpers

```go
// Use predefined key patterns
productKey := cache.ProductKey("123")        // "product:123"
categoryKey := cache.CategoryKey("auto")     // "category:auto"
userKey := cache.UserKey("user456")          // "user:user456"
sessionKey := cache.SessionKey("sess789")    // "session:sess789"

// Search with filters
searchKey := cache.SearchKey("car parts", map[string]string{
    "category": "automotive",
    "price_max": "100",
})
```

### Cache Invalidation

```go
// Invalidate specific key
err = cache.Delete(ctx, "product:123")

// Invalidate pattern
err = cache.InvalidatePattern(ctx, "products:*")

// Invalidate category
err = cache.InvalidateCategory("products")
```

## Cache Warming

### Automatic Warming

Cache warming happens automatically on startup and at scheduled intervals:

- **Products**: Popular and featured products
- **Categories**: All product categories
- **User Sessions**: Active user session data

### Manual Warming

```go
// Trigger manual cache warming
err = cache.WarmCache(ctx)

// Or via HTTP endpoint
POST /cache/warm
```

### Custom Warming Strategies

```go
// Register custom warmer
cacheProvider.GetService().RegisterWarmer("custom_data", func(ctx context.Context) error {
    // Your custom warming logic
    data := fetchCustomData()
    return cache.Set(ctx, "custom:data", data, time.Hour)
})
```

## Monitoring

### Metrics Endpoint

```bash
# Get cache metrics
GET /cache/metrics

# Response
{
  "cache_metrics": {
    "enabled": true,
    "redis": {
      "hits": 1250,
      "misses": 150,
      "hit_rate": 0.89,
      "memory_usage_mb": 45,
      "connection_count": 8
    },
    "application": {
      "l1_hits": 800,
      "l1_misses": 200,
      "l2_hits": 450,
      "l2_misses": 100,
      "overall_hit_rate": 0.85
    }
  }
}
```

### Health Check

```bash
# Check cache health
GET /cache/health

# Response
{
  "status": "healthy"
}
```

### Performance Metrics

The cache system tracks:
- Hit/miss ratios
- Response times
- Memory usage
- Connection pool statistics
- Error rates
- Slow operations

## Development Setup

### Using Docker Compose

```bash
# Start Redis with Redis Commander
docker-compose -f docker-compose.redis.yml up -d

# Redis will be available at localhost:6379
# Redis Commander UI at http://localhost:8081
```

### Manual Redis Setup

```bash
# Install Redis
brew install redis  # macOS
sudo apt install redis-server  # Ubuntu

# Start Redis
redis-server

# Test connection
redis-cli ping
```

## Testing

### Unit Tests

```bash
# Run cache tests
go test ./internal/infrastructure/cache/...

# Run with coverage
go test -cover ./internal/infrastructure/cache/...

# Run benchmarks
go test -bench=. ./internal/infrastructure/cache/...
```

### Integration Tests

```bash
# Requires Redis running on localhost:6379
go test -tags=integration ./internal/infrastructure/cache/...
```

## Performance Optimization

### Cache Hit Rate Optimization

1. **Appropriate TTL**: Set TTL based on data volatility
2. **Cache Warming**: Preload frequently accessed data
3. **Key Design**: Use consistent, hierarchical key patterns
4. **Batch Operations**: Use batch operations for multiple keys

### Memory Optimization

1. **Compression**: Enable compression for large values
2. **Eviction Policy**: Use LRU eviction for optimal memory usage
3. **Memory Limits**: Set appropriate memory limits
4. **Data Structure**: Use efficient data structures

### Connection Optimization

1. **Pool Size**: Optimize connection pool size
2. **Idle Connections**: Maintain minimum idle connections
3. **Timeouts**: Set appropriate timeouts
4. **Retry Logic**: Implement exponential backoff

## Best Practices

### Cache Key Design

```go
// Good: Hierarchical, descriptive keys
"product:123"
"category:automotive:products"
"user:456:profile"
"search:car_parts:category=auto:page=1"

// Bad: Flat, unclear keys
"p123"
"data"
"temp_key"
```

### TTL Strategy

```go
// Static data: Long TTL
cache.Set(ctx, "categories:all", categories, 24*time.Hour)

// Dynamic data: Short TTL
cache.Set(ctx, "user:profile", profile, 5*time.Minute)

// Search results: Medium TTL
cache.Set(ctx, searchKey, results, 15*time.Minute)
```

### Error Handling

```go
// Always handle cache errors gracefully
var product Product
if err := cache.Get(ctx, key, &product); err != nil {
    if err == cache.ErrCacheMiss {
        // Cache miss - fetch from database
        product = fetchFromDB(productID)
        cache.Set(ctx, key, product, time.Hour) // Best effort
    } else {
        // Cache error - log and continue
        logger.Error("Cache error", zap.Error(err))
        product = fetchFromDB(productID)
    }
}
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check if Redis is running
   - Verify connection configuration
   - Check network connectivity

2. **High Memory Usage**
   - Review TTL settings
   - Check for memory leaks
   - Optimize data structures

3. **Low Hit Rate**
   - Review cache warming strategies
   - Check TTL appropriateness
   - Analyze access patterns

4. **Slow Performance**
   - Check Redis configuration
   - Review connection pool settings
   - Monitor network latency

### Debug Mode

```go
// Enable debug logging
logger := zap.NewDevelopment()

// Check cache status
if cache.IsEnabled() {
    logger.Info("Cache is enabled")
} else {
    logger.Info("Cache is disabled")
}

// Monitor cache operations
metrics := cache.GetMetrics()
logger.Info("Cache metrics", zap.Any("metrics", metrics))
```

## Production Deployment

### Redis Cluster Setup

For production, use Redis Cluster for high availability:

```yaml
redis:
  enabled: true
  addrs:
    - "redis-node1:6379"
    - "redis-node2:6379"
    - "redis-node3:6379"
  password: "secure_password"
  pool_size: 20
  min_idle_conns: 10
```

### Monitoring and Alerting

Set up monitoring for:
- Cache hit rates < 70%
- Memory usage > 80%
- Connection errors
- Slow operations > 100ms

### Backup and Recovery

- Enable Redis persistence (RDB + AOF)
- Regular backup of Redis data
- Test recovery procedures
- Monitor backup integrity

## Contributing

When adding new cache functionality:

1. Follow existing patterns and interfaces
2. Add comprehensive tests
3. Update documentation
4. Consider performance implications
5. Add monitoring metrics

## License

This cache infrastructure is part of the CarNow backend system.
package cache

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// Application-Level Cache for Phase 4: Performance Optimization
//
// Features:
// - Implement in-memory caching for static data
// - Add HTTP response caching for appropriate endpoints
// - Create cache warming strategies
// - Implement cache hit rate monitoring
// - Multi-tier caching (L1: Memory, L2: Redis)
// - Smart cache invalidation
// - Adaptive TTL based on access patterns

// ApplicationCache provides multi-tier application-level caching
type ApplicationCache struct {
	// L1 Cache - In-memory
	memoryCache *MemoryCache

	// L2 Cache - Redis (optional)
	redisCache *RedisCache

	// HTTP Response Cache
	responseCache *HTTPResponseCache

	// Static Data Cache
	staticCache *StaticDataCache

	// Configuration
	config *ApplicationCacheConfig
	logger *zap.Logger

	// Metrics
	metrics *MultiTierCacheMetrics

	// Cache warming
	warmer *ApplicationCacheWarmer

	// State management
	isRunning bool
	mutex     sync.RWMutex
}

// ApplicationCacheConfig contains configuration for application cache
type ApplicationCacheConfig struct {
	// Memory cache configuration
	MemoryCache MemoryCacheConfig

	// HTTP response cache configuration
	ResponseCache ResponseCacheConfig

	// Static data cache configuration
	StaticCache StaticCacheConfig

	// Multi-tier configuration
	EnableRedisL2      bool          // Enable Redis as L2 cache
	L1ToL2PromoteSize  int           // Size threshold to promote from L1 to L2
	L2ToL1DemoteAccess int           // Access count to demote from L2 to L1
	TierSyncInterval   time.Duration // How often to sync between tiers

	// Cache warming configuration
	WarmingStrategies []WarmingStrategy
	WarmingSchedule   WarmingSchedule

	// Monitoring configuration
	MetricsInterval    time.Duration
	EnableAdaptiveTTL  bool
	AdaptiveTTLFactors AdaptiveTTLConfig
}

// MemoryCacheConfig configures the in-memory cache
type MemoryCacheConfig struct {
	MaxSize         int           // Maximum number of entries
	MaxMemoryMB     int           // Maximum memory usage in MB
	DefaultTTL      time.Duration // Default TTL for entries
	CleanupInterval time.Duration // How often to clean expired entries
	EvictionPolicy  string        // LRU, LFU, or FIFO
}

// ResponseCacheConfig configures HTTP response caching
type ResponseCacheConfig struct {
	Enabled            bool
	MaxResponseSize    int               // Maximum response size to cache
	DefaultTTL         time.Duration     // Default TTL for responses
	CacheableStatuses  []int             // HTTP status codes to cache
	CacheableHeaders   map[string]string // Headers that indicate cacheability
	VaryHeaders        []string          // Headers to include in cache key
	CompressionEnabled bool              // Enable response compression
	ETagEnabled        bool              // Enable ETag support
}

// StaticCacheConfig configures static data caching
type StaticCacheConfig struct {
	Categories        map[string]StaticCacheCategory // Cache categories
	RefreshInterval   time.Duration                  // How often to refresh static data
	PreloadOnStartup  bool                           // Preload static data on startup
	BackgroundRefresh bool                           // Refresh in background
}

// StaticCacheCategory defines a category of static data
type StaticCacheCategory struct {
	Name            string
	TTL             time.Duration
	RefreshInterval time.Duration
	DataSource      StaticDataSource
	DependsOn       []string // Other categories this depends on
	Priority        int      // Priority for loading (1-10)
}

// StaticDataSource defines how to fetch static data
type StaticDataSource struct {
	Type       string                 // "sql", "api", "file", "function"
	Source     string                 // SQL query, API endpoint, file path, or function name
	Parameters map[string]interface{} // Additional parameters
}

// WarmingStrategy defines a cache warming strategy
type WarmingStrategy struct {
	Name        string
	Type        string // "scheduled", "on_demand", "predictive"
	Schedule    string // Cron expression for scheduled warming
	DataSources []string
	Enabled     bool
}

// WarmingSchedule defines when cache warming occurs
type WarmingSchedule struct {
	OnStartup     bool
	OffPeakHours  []int // Hours to perform warming (0-23)
	CronSchedules []string
	TriggerEvents []string
}

// AdaptiveTTLConfig configures adaptive TTL calculation
type AdaptiveTTLConfig struct {
	EnableAccessPattern bool // Adjust TTL based on access patterns
	EnableTimeOfDay     bool // Adjust TTL based on time of day
	MinTTL              time.Duration
	MaxTTL              time.Duration
	AccessMultiplier    float64       // Multiplier for frequently accessed items
	UnusedThreshold     time.Duration // Time after which to reduce TTL
}

// MultiTierCacheMetrics tracks metrics across cache tiers
type MultiTierCacheMetrics struct {
	// L1 (Memory) metrics
	L1Hits          int64
	L1Misses        int64
	L1Sets          int64
	L1Evictions     int64
	L1Size          int
	L1MemoryUsageMB int

	// L2 (Redis) metrics
	L2Hits   int64
	L2Misses int64
	L2Sets   int64
	L2Size   int64

	// HTTP Response cache metrics
	ResponseCacheHits   int64
	ResponseCacheMisses int64
	ResponseCacheSize   int
	CachedResponsesSize int64

	// Static data cache metrics
	StaticCacheHits      int64
	StaticCacheMisses    int64
	StaticCacheRefreshes int64
	StaticCacheSize      int

	// Overall metrics
	TotalHits         int64
	TotalMisses       int64
	OverallHitRate    float64
	AverageAccessTime time.Duration

	// Cache warming metrics
	WarmingRuns      int64
	WarmingSuccesses int64
	WarmingFailures  int64
	LastWarmingTime  time.Time

	// Timing
	LastUpdated   time.Time
	UptimeSeconds int64
}

// MemoryCache implements an in-memory LRU cache
type MemoryCache struct {
	items       map[string]*MemoryCacheItem
	lruList     *LRUList
	maxSize     int
	maxMemoryMB int
	currentSize int
	memoryUsage int64
	mutex       sync.RWMutex
	ttl         time.Duration
	logger      *zap.Logger
}

// MemoryCacheItem represents an item in memory cache
type MemoryCacheItem struct {
	Key         string
	Value       interface{}
	Size        int
	ExpiresAt   time.Time
	AccessTime  time.Time
	AccessCount int64
	CreatedAt   time.Time
	Tags        []string
	Priority    int
}

// HTTPResponseCache handles caching of HTTP responses
type HTTPResponseCache struct {
	cache   *MemoryCache
	config  ResponseCacheConfig
	logger  *zap.Logger
	metrics *ResponseCacheMetrics
}

// ResponseCacheMetrics tracks HTTP response cache metrics
type ResponseCacheMetrics struct {
	CachedRequests    int64
	CacheableRequests int64
	ResponsesSaved    int64
	BytesSaved        int64
	AverageHitTime    time.Duration
	AverageMissTime   time.Duration
}

// StaticDataCache handles caching of static/reference data
type StaticDataCache struct {
	cache      *MemoryCache
	categories map[string]*StaticCacheCategory
	config     StaticCacheConfig
	logger     *zap.Logger
	refreshers map[string]StaticDataRefresher
	mutex      sync.RWMutex
}

// StaticDataRefresher defines how to refresh static data
type StaticDataRefresher interface {
	Refresh(ctx context.Context, category string) (interface{}, error)
	Validate(data interface{}) error
	Transform(data interface{}) (interface{}, error)
}

// ApplicationCacheWarmer handles cache warming for application cache
type ApplicationCacheWarmer struct {
	cache      *ApplicationCache
	strategies []WarmingStrategy
	schedule   WarmingSchedule
	logger     *zap.Logger
	isRunning  bool
	mutex      sync.RWMutex
}

// LRUList implements a doubly-linked list for LRU
type LRUList struct {
	head  *LRUNode
	tail  *LRUNode
	size  int
	mutex sync.Mutex
}

// LRUNode represents a node in the LRU list
type LRUNode struct {
	key  string
	prev *LRUNode
	next *LRUNode
}

// NewApplicationCache creates a new application cache
func NewApplicationCache(config *ApplicationCacheConfig, redisCache *RedisCache, logger *zap.Logger) (*ApplicationCache, error) {
	if config == nil {
		config = DefaultApplicationCacheConfig()
	}

	// Create memory cache
	memCache, err := NewMemoryCache(config.MemoryCache, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create memory cache: %w", err)
	}

	// Create HTTP response cache
	respCache, err := NewHTTPResponseCache(config.ResponseCache, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create response cache: %w", err)
	}

	// Create static data cache
	staticCache, err := NewStaticDataCache(config.StaticCache, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create static cache: %w", err)
	}

	cache := &ApplicationCache{
		memoryCache:   memCache,
		redisCache:    redisCache,
		responseCache: respCache,
		staticCache:   staticCache,
		config:        config,
		logger:        logger,
		metrics: &MultiTierCacheMetrics{
			LastUpdated: time.Now(),
		},
	}

	// Create cache warmer
	cache.warmer = &ApplicationCacheWarmer{
		cache:      cache,
		strategies: config.WarmingStrategies,
		schedule:   config.WarmingSchedule,
		logger:     logger,
	}

	// Start monitoring
	if config.MetricsInterval > 0 {
		go cache.startMetricsCollection()
	}

	// Start cache warming if enabled
	if len(config.WarmingStrategies) > 0 {
		go cache.warmer.Start()
	}

	// Start tier synchronization if Redis L2 is enabled
	if config.EnableRedisL2 && redisCache != nil {
		go cache.startTierSynchronization()
	}

	logger.Info("Application cache initialized",
		zap.Int("memory_max_size", config.MemoryCache.MaxSize),
		zap.Int("memory_max_mb", config.MemoryCache.MaxMemoryMB),
		zap.Bool("redis_l2_enabled", config.EnableRedisL2),
		zap.Bool("response_cache_enabled", config.ResponseCache.Enabled),
	)

	return cache, nil
}

// DefaultApplicationCacheConfig returns default configuration
func DefaultApplicationCacheConfig() *ApplicationCacheConfig {
	return &ApplicationCacheConfig{
		MemoryCache: MemoryCacheConfig{
			MaxSize:         10000,
			MaxMemoryMB:     100,
			DefaultTTL:      time.Hour,
			CleanupInterval: 5 * time.Minute,
			EvictionPolicy:  "LRU",
		},
		ResponseCache: ResponseCacheConfig{
			Enabled:            true,
			MaxResponseSize:    1024 * 1024, // 1MB
			DefaultTTL:         5 * time.Minute,
			CacheableStatuses:  []int{200, 301, 302, 404},
			VaryHeaders:        []string{"Accept", "Authorization"},
			CompressionEnabled: true,
			ETagEnabled:        true,
		},
		StaticCache: StaticCacheConfig{
			Categories: map[string]StaticCacheCategory{
				"products": {
					Name:            "products",
					TTL:             time.Hour,
					RefreshInterval: 30 * time.Minute,
					Priority:        5,
				},
				"categories": {
					Name:            "categories",
					TTL:             24 * time.Hour,
					RefreshInterval: 6 * time.Hour,
					Priority:        8,
				},
			},
			RefreshInterval:   time.Hour,
			PreloadOnStartup:  true,
			BackgroundRefresh: true,
		},
		EnableRedisL2:     true,
		L1ToL2PromoteSize: 1024,
		TierSyncInterval:  time.Minute,
		MetricsInterval:   30 * time.Second,
		EnableAdaptiveTTL: true,
		AdaptiveTTLFactors: AdaptiveTTLConfig{
			EnableAccessPattern: true,
			EnableTimeOfDay:     true,
			MinTTL:              time.Minute,
			MaxTTL:              24 * time.Hour,
			AccessMultiplier:    1.5,
			UnusedThreshold:     time.Hour,
		},
	}
}

// Get retrieves a value from the multi-tier cache
func (ac *ApplicationCache) Get(ctx context.Context, key string, dest interface{}) error {
	start := time.Now()
	defer func() {
		ac.recordAccessTime(time.Since(start))
	}()

	// Try L1 (Memory) cache first
	if err := ac.memoryCache.Get(key, dest); err == nil {
		ac.recordL1Hit()
		return nil
	}
	ac.recordL1Miss()

	// Try L2 (Redis) cache if enabled
	if ac.config.EnableRedisL2 && ac.redisCache != nil {
		if err := ac.redisCache.Get(ctx, key, dest); err == nil {
			ac.recordL2Hit()

			// Promote to L1 if frequently accessed
			ac.promoteToL1(key, dest)
			return nil
		}
		ac.recordL2Miss()
	}

	return ErrCacheMiss
}

// Set stores a value in the multi-tier cache
func (ac *ApplicationCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	// Adjust TTL if adaptive TTL is enabled
	if ac.config.EnableAdaptiveTTL {
		ttl = ac.calculateAdaptiveTTL(key, ttl)
	}

	// Set in L1 (Memory) cache
	if err := ac.memoryCache.Set(key, value, ttl); err != nil {
		return fmt.Errorf("L1 cache set error: %w", err)
	}
	ac.recordL1Set()

	// Set in L2 (Redis) cache if enabled and value is large enough
	if ac.config.EnableRedisL2 && ac.redisCache != nil {
		valueSize := ac.estimateValueSize(value)
		if valueSize >= ac.config.L1ToL2PromoteSize {
			if err := ac.redisCache.Set(ctx, key, value, ttl); err != nil {
				ac.logger.Warn("L2 cache set failed", zap.Error(err))
				// Don't fail the request just because L2 set failed
			} else {
				ac.recordL2Set()
			}
		}
	}

	return nil
}

// GetOrSet implements cache-aside pattern across multiple tiers
func (ac *ApplicationCache) GetOrSet(ctx context.Context, key string, dest interface{}, fetchFunc func() (interface{}, error), ttl time.Duration) error {
	// Try to get from cache first
	if err := ac.Get(ctx, key, dest); err == nil {
		return nil
	}

	// Cache miss - fetch the data
	value, err := fetchFunc()
	if err != nil {
		return fmt.Errorf("fetch function error: %w", err)
	}

	// Set in cache for next time
	if err := ac.Set(ctx, key, value, ttl); err != nil {
		ac.logger.Error("Failed to set cache in GetOrSet", zap.Error(err))
	}

	// Copy value to destination
	return ac.copyValue(value, dest)
}

// CacheHTTPResponse caches an HTTP response
func (ac *ApplicationCache) CacheHTTPResponse(r *http.Request, statusCode int, headers http.Header, body []byte) error {
	if !ac.config.ResponseCache.Enabled {
		return nil
	}

	return ac.responseCache.CacheResponse(r, statusCode, headers, body)
}

// GetCachedHTTPResponse retrieves a cached HTTP response
func (ac *ApplicationCache) GetCachedHTTPResponse(r *http.Request) (*CachedResponse, error) {
	if !ac.config.ResponseCache.Enabled {
		return nil, ErrCacheMiss
	}

	return ac.responseCache.GetCachedResponse(r)
}

// GetStaticData retrieves static/reference data
func (ac *ApplicationCache) GetStaticData(category string, dest interface{}) error {
	return ac.staticCache.Get(category, dest)
}

// RefreshStaticData refreshes static data for a category
func (ac *ApplicationCache) RefreshStaticData(ctx context.Context, category string) error {
	return ac.staticCache.Refresh(ctx, category)
}

// InvalidateCategory invalidates all items in a category
func (ac *ApplicationCache) InvalidateCategory(category string) error {
	// Invalidate from memory cache
	ac.memoryCache.InvalidateByTag(category)

	// Invalidate from Redis cache if enabled
	if ac.config.EnableRedisL2 && ac.redisCache != nil {
		pattern := fmt.Sprintf("%s:*", category)
		if err := ac.redisCache.InvalidatePattern(context.Background(), pattern); err != nil {
			ac.logger.Error("L2 cache invalidation failed", zap.Error(err))
		}
	}

	return nil
}

// WarmCache warms the cache with frequently accessed data
func (ac *ApplicationCache) WarmCache(ctx context.Context) error {
	return ac.warmer.WarmCache(ctx)
}

// GetMetrics returns current cache metrics
func (ac *ApplicationCache) GetMetrics() *MultiTierCacheMetrics {
	ac.mutex.RLock()
	defer ac.mutex.RUnlock()

	// Collect metrics from all cache layers
	ac.collectCurrentMetrics()

	// Return a copy
	metricsCopy := *ac.metrics
	return &metricsCopy
}

// Helper methods

func (ac *ApplicationCache) promoteToL1(key string, value interface{}) {
	// Only promote if L1 has space
	if ac.memoryCache.currentSize < ac.memoryCache.maxSize {
		if err := ac.memoryCache.Set(key, value, ac.config.MemoryCache.DefaultTTL); err != nil {
			ac.logger.Debug("Failed to promote to L1", zap.Error(err))
		}
	}
}

func (ac *ApplicationCache) calculateAdaptiveTTL(key string, baseTTL time.Duration) time.Duration {
	config := ac.config.AdaptiveTTLFactors

	// Get access patterns for this key
	item := ac.memoryCache.getItem(key)
	if item == nil {
		return baseTTL
	}

	ttl := baseTTL

	// Adjust based on access patterns
	if config.EnableAccessPattern {
		if item.AccessCount > 10 {
			ttl = time.Duration(float64(ttl) * config.AccessMultiplier)
		}

		// Reduce TTL for rarely accessed items
		if time.Since(item.AccessTime) > config.UnusedThreshold {
			ttl = ttl / 2
		}
	}

	// Adjust based on time of day
	if config.EnableTimeOfDay {
		hour := time.Now().Hour()
		// Increase TTL during business hours (9-17)
		if hour >= 9 && hour <= 17 {
			ttl = time.Duration(float64(ttl) * 1.2)
		}
	}

	// Apply bounds
	if ttl < config.MinTTL {
		ttl = config.MinTTL
	}
	if ttl > config.MaxTTL {
		ttl = config.MaxTTL
	}

	return ttl
}

func (ac *ApplicationCache) estimateValueSize(value interface{}) int {
	// Simple size estimation using JSON marshaling
	data, err := json.Marshal(value)
	if err != nil {
		return 0
	}
	return len(data)
}

func (ac *ApplicationCache) copyValue(src interface{}, dest interface{}) error {
	data, err := json.Marshal(src)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, dest)
}

// Metrics recording methods
func (ac *ApplicationCache) recordL1Hit() {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()
	ac.metrics.L1Hits++
	ac.updateOverallHitRate()
}

func (ac *ApplicationCache) recordL1Miss() {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()
	ac.metrics.L1Misses++
	ac.updateOverallHitRate()
}

func (ac *ApplicationCache) recordL1Set() {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()
	ac.metrics.L1Sets++
}

func (ac *ApplicationCache) recordL2Hit() {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()
	ac.metrics.L2Hits++
	ac.updateOverallHitRate()
}

func (ac *ApplicationCache) recordL2Miss() {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()
	ac.metrics.L2Misses++
	ac.updateOverallHitRate()
}

func (ac *ApplicationCache) recordL2Set() {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()
	ac.metrics.L2Sets++
}

func (ac *ApplicationCache) recordAccessTime(duration time.Duration) {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()

	if ac.metrics.AverageAccessTime == 0 {
		ac.metrics.AverageAccessTime = duration
	} else {
		ac.metrics.AverageAccessTime = (ac.metrics.AverageAccessTime + duration) / 2
	}
}

func (ac *ApplicationCache) updateOverallHitRate() {
	totalHits := ac.metrics.L1Hits + ac.metrics.L2Hits + ac.metrics.ResponseCacheHits + ac.metrics.StaticCacheHits
	totalMisses := ac.metrics.L1Misses + ac.metrics.L2Misses + ac.metrics.ResponseCacheMisses + ac.metrics.StaticCacheMisses

	ac.metrics.TotalHits = totalHits
	ac.metrics.TotalMisses = totalMisses

	if totalHits+totalMisses > 0 {
		ac.metrics.OverallHitRate = float64(totalHits) / float64(totalHits+totalMisses)
	}
}

func (ac *ApplicationCache) collectCurrentMetrics() {
	// Collect L1 metrics
	ac.metrics.L1Size = ac.memoryCache.currentSize
	ac.metrics.L1MemoryUsageMB = int(ac.memoryCache.memoryUsage / 1024 / 1024)

	// Collect L2 metrics if enabled
	if ac.config.EnableRedisL2 && ac.redisCache != nil {
		if redisMetrics := ac.redisCache.GetMetrics(); redisMetrics != nil {
			ac.metrics.L2Size = redisMetrics.KeyCount
		}
	}

	// Update timing
	ac.metrics.LastUpdated = time.Now()
}

func (ac *ApplicationCache) startMetricsCollection() {
	ticker := time.NewTicker(ac.config.MetricsInterval)
	defer ticker.Stop()

	for range ticker.C {
		ac.collectCurrentMetrics()

		ac.logger.Debug("Application cache metrics",
			zap.Float64("overall_hit_rate", ac.metrics.OverallHitRate),
			zap.Int64("total_hits", ac.metrics.TotalHits),
			zap.Int64("total_misses", ac.metrics.TotalMisses),
			zap.Int("l1_size", ac.metrics.L1Size),
			zap.Int("l1_memory_mb", ac.metrics.L1MemoryUsageMB),
		)
	}
}

func (ac *ApplicationCache) startTierSynchronization() {
	ticker := time.NewTicker(ac.config.TierSyncInterval)
	defer ticker.Stop()

	for range ticker.C {
		ac.synchronizeTiers()
	}
}

func (ac *ApplicationCache) synchronizeTiers() {
	// Implement tier synchronization logic
	// This could include promoting frequently accessed L2 items to L1
	// and demoting rarely accessed L1 items to L2

	ac.logger.Debug("Performing tier synchronization")
}

// NewMemoryCache creates a new in-memory cache
func NewMemoryCache(config MemoryCacheConfig, logger *zap.Logger) (*MemoryCache, error) {
	cache := &MemoryCache{
		items:       make(map[string]*MemoryCacheItem),
		lruList:     NewLRUList(),
		maxSize:     config.MaxSize,
		maxMemoryMB: config.MaxMemoryMB,
		ttl:         config.DefaultTTL,
		logger:      logger,
	}

	// Start cleanup routine
	go cache.startCleanup(config.CleanupInterval)

	return cache, nil
}

// Memory cache implementation methods would go here...

// Get retrieves an item from memory cache
func (mc *MemoryCache) Get(key string, dest interface{}) error {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	item, exists := mc.items[key]
	if !exists {
		return ErrCacheMiss
	}

	// Check expiration
	if time.Now().After(item.ExpiresAt) {
		delete(mc.items, key)
		mc.lruList.Remove(key)
		return ErrCacheMiss
	}

	// Update access information
	item.AccessTime = time.Now()
	item.AccessCount++

	// Move to front of LRU list
	mc.lruList.MoveToFront(key)

	// Copy value to destination
	data, err := json.Marshal(item.Value)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, dest)
}

// Set stores an item in memory cache
func (mc *MemoryCache) Set(key string, value interface{}, ttl time.Duration) error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	// Estimate size
	size := mc.estimateSize(value)

	// Check memory limits
	if mc.memoryUsage+int64(size) > int64(mc.maxMemoryMB)*1024*1024 {
		mc.evictLRU()
	}

	// Check size limits
	if mc.currentSize >= mc.maxSize {
		mc.evictLRU()
	}

	if ttl <= 0 {
		ttl = mc.ttl
	}

	item := &MemoryCacheItem{
		Key:         key,
		Value:       value,
		Size:        size,
		ExpiresAt:   time.Now().Add(ttl),
		AccessTime:  time.Now(),
		AccessCount: 1,
		CreatedAt:   time.Now(),
	}

	// Remove existing item if present
	if existingItem, exists := mc.items[key]; exists {
		mc.memoryUsage -= int64(existingItem.Size)
		mc.currentSize--
	}

	mc.items[key] = item
	mc.memoryUsage += int64(size)
	mc.currentSize++

	// Add to LRU list
	mc.lruList.AddToFront(key)

	return nil
}

func (mc *MemoryCache) getItem(key string) *MemoryCacheItem {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	return mc.items[key]
}

func (mc *MemoryCache) estimateSize(value interface{}) int {
	data, err := json.Marshal(value)
	if err != nil {
		return 100 // Default estimate
	}
	return len(data)
}

func (mc *MemoryCache) evictLRU() {
	if tail := mc.lruList.GetTail(); tail != nil {
		if item, exists := mc.items[tail.key]; exists {
			delete(mc.items, tail.key)
			mc.memoryUsage -= int64(item.Size)
			mc.currentSize--
		}
		mc.lruList.Remove(tail.key)
	}
}

func (mc *MemoryCache) InvalidateByTag(tag string) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	for key, item := range mc.items {
		for _, itemTag := range item.Tags {
			if itemTag == tag {
				delete(mc.items, key)
				mc.memoryUsage -= int64(item.Size)
				mc.currentSize--
				mc.lruList.Remove(key)
				break
			}
		}
	}
}

func (mc *MemoryCache) startCleanup(interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for range ticker.C {
		mc.cleanup()
	}
}

func (mc *MemoryCache) cleanup() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	now := time.Now()
	for key, item := range mc.items {
		if now.After(item.ExpiresAt) {
			delete(mc.items, key)
			mc.memoryUsage -= int64(item.Size)
			mc.currentSize--
			mc.lruList.Remove(key)
		}
	}
}

// LRU List implementation
func NewLRUList() *LRUList {
	return &LRUList{}
}

func (lru *LRUList) AddToFront(key string) {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()

	// Remove if exists
	lru.removeNode(key)

	node := &LRUNode{key: key}

	if lru.head == nil {
		lru.head = node
		lru.tail = node
	} else {
		node.next = lru.head
		lru.head.prev = node
		lru.head = node
	}

	lru.size++
}

func (lru *LRUList) MoveToFront(key string) {
	lru.AddToFront(key)
}

func (lru *LRUList) Remove(key string) {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()
	lru.removeNode(key)
}

func (lru *LRUList) GetTail() *LRUNode {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()
	return lru.tail
}

func (lru *LRUList) removeNode(key string) {
	current := lru.head
	for current != nil {
		if current.key == key {
			if current.prev != nil {
				current.prev.next = current.next
			} else {
				lru.head = current.next
			}

			if current.next != nil {
				current.next.prev = current.prev
			} else {
				lru.tail = current.prev
			}

			lru.size--
			break
		}
		current = current.next
	}
}

// HTTP Response Cache implementation stubs
func NewHTTPResponseCache(config ResponseCacheConfig, logger *zap.Logger) (*HTTPResponseCache, error) {
	memConfig := MemoryCacheConfig{
		MaxSize:         1000,
		MaxMemoryMB:     50,
		DefaultTTL:      config.DefaultTTL,
		CleanupInterval: 5 * time.Minute,
		EvictionPolicy:  "LRU",
	}

	memCache, err := NewMemoryCache(memConfig, logger)
	if err != nil {
		return nil, err
	}

	return &HTTPResponseCache{
		cache:   memCache,
		config:  config,
		logger:  logger,
		metrics: &ResponseCacheMetrics{},
	}, nil
}

// CachedResponse represents a cached HTTP response
type CachedResponse struct {
	StatusCode int
	Headers    http.Header
	Body       []byte
	ETag       string
	CachedAt   time.Time
}

func (rc *HTTPResponseCache) CacheResponse(r *http.Request, statusCode int, headers http.Header, body []byte) error {
	if !rc.isCacheable(statusCode, headers) {
		return nil
	}

	if len(body) > rc.config.MaxResponseSize {
		return nil // Don't cache large responses
	}

	cacheKey := rc.generateCacheKey(r)

	response := &CachedResponse{
		StatusCode: statusCode,
		Headers:    headers,
		Body:       body,
		CachedAt:   time.Now(),
	}

	if rc.config.ETagEnabled {
		response.ETag = rc.generateETag(body)
	}

	return rc.cache.Set(cacheKey, response, rc.config.DefaultTTL)
}

func (rc *HTTPResponseCache) GetCachedResponse(r *http.Request) (*CachedResponse, error) {
	cacheKey := rc.generateCacheKey(r)

	var response CachedResponse
	if err := rc.cache.Get(cacheKey, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

func (rc *HTTPResponseCache) isCacheable(statusCode int, headers http.Header) bool {
	// Check if status code is cacheable
	for _, status := range rc.config.CacheableStatuses {
		if statusCode == status {
			return true
		}
	}
	return false
}

func (rc *HTTPResponseCache) generateCacheKey(r *http.Request) string {
	// Create cache key from URL, method, and varying headers
	key := fmt.Sprintf("%s:%s", r.Method, r.URL.String())

	// Add varying headers to key
	var varyParts []string
	for _, header := range rc.config.VaryHeaders {
		if value := r.Header.Get(header); value != "" {
			varyParts = append(varyParts, fmt.Sprintf("%s:%s", header, value))
		}
	}

	if len(varyParts) > 0 {
		sort.Strings(varyParts)
		key += ":" + strings.Join(varyParts, ",")
	}

	// Generate MD5 hash for long keys
	if len(key) > 250 {
		hash := md5.Sum([]byte(key))
		key = hex.EncodeToString(hash[:])
	}

	return key
}

func (rc *HTTPResponseCache) generateETag(body []byte) string {
	hash := md5.Sum(body)
	return fmt.Sprintf(`"%s"`, hex.EncodeToString(hash[:]))
}

// Static Data Cache implementation stubs
func NewStaticDataCache(config StaticCacheConfig, logger *zap.Logger) (*StaticDataCache, error) {
	memConfig := MemoryCacheConfig{
		MaxSize:         5000,
		MaxMemoryMB:     200,
		DefaultTTL:      24 * time.Hour,
		CleanupInterval: time.Hour,
		EvictionPolicy:  "LRU",
	}

	memCache, err := NewMemoryCache(memConfig, logger)
	if err != nil {
		return nil, err
	}

	// Convert categories to the expected type
	categories := make(map[string]*StaticCacheCategory)
	for k, v := range config.Categories {
		category := v // Copy the value
		categories[k] = &category
	}

	cache := &StaticDataCache{
		cache:      memCache,
		categories: categories,
		config:     config,
		logger:     logger,
		refreshers: make(map[string]StaticDataRefresher),
	}

	// Start background refresh if enabled
	if config.BackgroundRefresh {
		go cache.startBackgroundRefresh()
	}

	return cache, nil
}

func (sc *StaticDataCache) Get(category string, dest interface{}) error {
	return sc.cache.Get(category, dest)
}

func (sc *StaticDataCache) Refresh(ctx context.Context, category string) error {
	// Implementation would fetch data from the configured source
	sc.logger.Info("Refreshing static data", zap.String("category", category))
	return nil
}

func (sc *StaticDataCache) startBackgroundRefresh() {
	ticker := time.NewTicker(sc.config.RefreshInterval)
	defer ticker.Stop()

	for range ticker.C {
		sc.refreshAllCategories()
	}
}

func (sc *StaticDataCache) refreshAllCategories() {
	for name := range sc.categories {
		if err := sc.Refresh(context.Background(), name); err != nil {
			sc.logger.Error("Failed to refresh static data",
				zap.String("category", name),
				zap.Error(err),
			)
		}
	}
}

// Cache Warmer implementation
func (cw *ApplicationCacheWarmer) Start() {
	cw.mutex.Lock()
	defer cw.mutex.Unlock()

	if cw.isRunning {
		return
	}

	cw.isRunning = true

	// Warm cache on startup if enabled
	if cw.schedule.OnStartup {
		go cw.performStartupWarming()
	}

	cw.logger.Info("Application cache warmer started")
}

func (cw *ApplicationCacheWarmer) WarmCache(ctx context.Context) error {
	cw.logger.Info("Starting cache warming")

	// Implement cache warming logic
	for _, strategy := range cw.strategies {
		if !strategy.Enabled {
			continue
		}

		if err := cw.executeWarmingStrategy(ctx, strategy); err != nil {
			cw.logger.Error("Cache warming strategy failed",
				zap.String("strategy", strategy.Name),
				zap.Error(err),
			)
		}
	}

	return nil
}

func (cw *ApplicationCacheWarmer) performStartupWarming() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	if err := cw.WarmCache(ctx); err != nil {
		cw.logger.Error("Startup cache warming failed", zap.Error(err))
	}
}

func (cw *ApplicationCacheWarmer) executeWarmingStrategy(ctx context.Context, strategy WarmingStrategy) error {
	cw.logger.Debug("Executing warming strategy", zap.String("strategy", strategy.Name))

	// Implementation would execute the specific warming strategy
	// This could involve:
	// - Pre-loading frequently accessed data
	// - Warming cache with predicted requests
	// - Loading static/reference data

	return nil
}

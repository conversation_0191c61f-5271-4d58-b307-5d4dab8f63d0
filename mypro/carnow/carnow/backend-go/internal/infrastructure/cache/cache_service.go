package cache

import (
	"context"
	"fmt"
	"time"

	"carnow-backend/internal/config"

	"go.uber.org/zap"
)

// CacheService provides a unified interface for all caching operations
type CacheService struct {
	redis       *RedisCache
	application *ApplicationCache
	config      *config.Config
	logger      *zap.Logger
	isEnabled   bool
}

// CacheServiceConfig contains configuration for the cache service
type CacheServiceConfig struct {
	EnableRedis       bool
	EnableApplication bool
	EnableWarming     bool
	WarmingStrategies []string
}

// NewCacheService creates a new cache service instance
func NewCacheService(cfg *config.Config, logger *zap.Logger) (*CacheService, error) {
	service := &CacheService{
		config:    cfg,
		logger:    logger,
		isEnabled: cfg.Redis.Enabled,
	}

	if !cfg.Redis.Enabled {
		logger.Info("Redis cache is disabled")
		return service, nil
	}

	// Initialize Redis cache
	if err := service.initializeRedisCache(); err != nil {
		return nil, fmt.Errorf("failed to initialize Redis cache: %w", err)
	}

	// Initialize Application cache with Redis as L2
	if err := service.initializeApplicationCache(); err != nil {
		return nil, fmt.Errorf("failed to initialize Application cache: %w", err)
	}

	// Register cache warming strategies
	service.registerWarmingStrategies()

	logger.Info("Cache service initialized successfully",
		zap.Bool("redis_enabled", cfg.Redis.Enabled),
		zap.Strings("redis_addrs", cfg.Redis.Addrs),
		zap.String("key_prefix", cfg.Redis.KeyPrefix),
	)

	return service, nil
}

// initializeRedisCache sets up the Redis cache
func (cs *CacheService) initializeRedisCache() error {
	// Convert config to Redis cache config
	redisCfg := &RedisCacheConfig{
		Addrs:        cs.config.Redis.Addrs,
		Password:     cs.config.Redis.Password,
		DB:           cs.config.Redis.DB,
		PoolSize:     cs.config.Redis.PoolSize,
		MinIdleConns: cs.config.Redis.MinIdleConns,
		MaxRetries:   cs.config.Redis.MaxRetries,
		DialTimeout:  cs.config.Redis.DialTimeout,
		ReadTimeout:  cs.config.Redis.ReadTimeout,
		WriteTimeout: cs.config.Redis.WriteTimeout,
		IdleTimeout:  cs.config.Redis.IdleTimeout,
		DefaultTTL:   cs.config.Redis.DefaultTTL,
		KeyPrefix:    cs.config.Redis.KeyPrefix,

		// Performance optimizations
		EnableCompression:    true,
		CompressionThreshold: 1024,
		MaxKeyLength:         250,
		MaxValueSize:         1024 * 1024, // 1MB

		// Monitoring
		EnableMetrics:      true,
		MetricsInterval:    30 * time.Second,
		EnableCacheWarming: true,
		WarmingInterval:    5 * time.Minute,
		BatchSize:          100,

		// Eviction
		EvictionPolicy:  EvictionLRU,
		MaxMemoryPolicy: "allkeys-lru",

		// Health monitoring
		HealthCheckInterval: time.Minute,
		AlertThresholds: CacheAlertThresholds{
			LowHitRate:          0.7,
			HighMemoryUsage:     0.8,
			HighConnectionCount: 100,
			SlowOperationTime:   100 * time.Millisecond,
		},
	}

	var err error
	cs.redis, err = NewRedisCache(redisCfg, cs.logger)
	if err != nil {
		return fmt.Errorf("failed to create Redis cache: %w", err)
	}

	return nil
}

// initializeApplicationCache sets up the multi-tier application cache
func (cs *CacheService) initializeApplicationCache() error {
	appCfg := DefaultApplicationCacheConfig()

	// Customize configuration based on environment
	if cs.config.App.Environment == "production" {
		appCfg.MemoryCache.MaxSize = 20000
		appCfg.MemoryCache.MaxMemoryMB = 200
		appCfg.ResponseCache.DefaultTTL = 10 * time.Minute
	} else {
		appCfg.MemoryCache.MaxSize = 5000
		appCfg.MemoryCache.MaxMemoryMB = 50
		appCfg.ResponseCache.DefaultTTL = 2 * time.Minute
	}

	// Enable Redis L2 if Redis is available
	appCfg.EnableRedisL2 = cs.redis != nil

	var err error
	cs.application, err = NewApplicationCache(appCfg, cs.redis, cs.logger)
	if err != nil {
		return fmt.Errorf("failed to create Application cache: %w", err)
	}

	return nil
}

// registerWarmingStrategies registers cache warming strategies
func (cs *CacheService) registerWarmingStrategies() {
	if cs.redis == nil {
		return
	}

	// Register product cache warmer
	cs.redis.RegisterWarmer("products", cs.warmProductsCache)

	// Register categories cache warmer
	cs.redis.RegisterWarmer("categories", cs.warmCategoriesCache)

	// Register user sessions warmer
	cs.redis.RegisterWarmer("user_sessions", cs.warmUserSessionsCache)

	cs.logger.Info("Cache warming strategies registered")
}

// Cache warming functions

// warmProductsCache warms the product cache with frequently accessed products
func (cs *CacheService) warmProductsCache(ctx context.Context) error {
	cs.logger.Info("Warming products cache")

	// This would typically fetch popular products from the database
	// For now, we'll simulate this
	popularProducts := []string{
		"product:popular:1",
		"product:popular:2",
		"product:popular:3",
		"product:featured:1",
		"product:featured:2",
	}

	for _, productKey := range popularProducts {
		// Simulate product data
		productData := map[string]interface{}{
			"id":          productKey,
			"name":        "Popular Product",
			"price":       99.99,
			"category_id": "automotive",
			"cached_at":   time.Now(),
		}

		if err := cs.redis.Set(ctx, productKey, productData, time.Hour); err != nil {
			cs.logger.Error("Failed to warm product cache",
				zap.String("key", productKey),
				zap.Error(err),
			)
		}
	}

	cs.logger.Info("Products cache warming completed",
		zap.Int("products_warmed", len(popularProducts)),
	)

	return nil
}

// warmCategoriesCache warms the categories cache
func (cs *CacheService) warmCategoriesCache(ctx context.Context) error {
	cs.logger.Info("Warming categories cache")

	// Simulate category data
	categories := []map[string]interface{}{
		{
			"id":   "automotive",
			"name": "Automotive Parts",
			"slug": "automotive",
		},
		{
			"id":   "electronics",
			"name": "Electronics",
			"slug": "electronics",
		},
		{
			"id":   "accessories",
			"name": "Accessories",
			"slug": "accessories",
		},
	}

	// Cache all categories
	if err := cs.redis.Set(ctx, "categories:all", categories, 24*time.Hour); err != nil {
		cs.logger.Error("Failed to warm categories cache", zap.Error(err))
		return err
	}

	// Cache individual categories
	for _, category := range categories {
		key := fmt.Sprintf("category:%s", category["id"])
		if err := cs.redis.Set(ctx, key, category, 24*time.Hour); err != nil {
			cs.logger.Error("Failed to warm category cache",
				zap.String("key", key),
				zap.Error(err),
			)
		}
	}

	cs.logger.Info("Categories cache warming completed",
		zap.Int("categories_warmed", len(categories)),
	)

	return nil
}

// warmUserSessionsCache warms frequently accessed user session data
func (cs *CacheService) warmUserSessionsCache(ctx context.Context) error {
	cs.logger.Info("Warming user sessions cache")

	// This would typically fetch active user sessions
	// For now, we'll just log that it's ready
	cs.logger.Info("User sessions cache warming completed")

	return nil
}

// Public API methods

// Get retrieves a value from the cache
func (cs *CacheService) Get(ctx context.Context, key string, dest interface{}) error {
	if !cs.isEnabled || cs.application == nil {
		return ErrCacheMiss
	}

	return cs.application.Get(ctx, key, dest)
}

// Set stores a value in the cache
func (cs *CacheService) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	if !cs.isEnabled || cs.application == nil {
		return nil // Silently ignore if cache is disabled
	}

	return cs.application.Set(ctx, key, value, ttl)
}

// GetOrSet implements cache-aside pattern
func (cs *CacheService) GetOrSet(ctx context.Context, key string, dest interface{}, fetchFunc func() (interface{}, error), ttl time.Duration) error {
	if !cs.isEnabled || cs.application == nil {
		// If cache is disabled, just fetch the data
		value, err := fetchFunc()
		if err != nil {
			return err
		}
		return cs.copyValue(value, dest)
	}

	return cs.application.GetOrSet(ctx, key, dest, fetchFunc, ttl)
}

// Delete removes a value from the cache
func (cs *CacheService) Delete(ctx context.Context, key string) error {
	if !cs.isEnabled || cs.redis == nil {
		return nil
	}

	return cs.redis.Delete(ctx, key)
}

// InvalidatePattern invalidates all keys matching a pattern
func (cs *CacheService) InvalidatePattern(ctx context.Context, pattern string) error {
	if !cs.isEnabled || cs.redis == nil {
		return nil
	}

	return cs.redis.InvalidatePattern(ctx, pattern)
}

// InvalidateCategory invalidates all items in a category
func (cs *CacheService) InvalidateCategory(category string) error {
	if !cs.isEnabled || cs.application == nil {
		return nil
	}

	return cs.application.InvalidateCategory(category)
}

// GetStaticData retrieves static/reference data
func (cs *CacheService) GetStaticData(category string, dest interface{}) error {
	if !cs.isEnabled || cs.application == nil {
		return ErrCacheMiss
	}

	return cs.application.GetStaticData(category, dest)
}

// RefreshStaticData refreshes static data for a category
func (cs *CacheService) RefreshStaticData(ctx context.Context, category string) error {
	if !cs.isEnabled || cs.application == nil {
		return nil
	}

	return cs.application.RefreshStaticData(ctx, category)
}

// WarmCache manually triggers cache warming
func (cs *CacheService) WarmCache(ctx context.Context) error {
	if !cs.isEnabled {
		return nil
	}

	if cs.application != nil {
		if err := cs.application.WarmCache(ctx); err != nil {
			cs.logger.Error("Application cache warming failed", zap.Error(err))
		}
	}

	return nil
}

// GetMetrics returns cache metrics
func (cs *CacheService) GetMetrics() interface{} {
	if !cs.isEnabled {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	metrics := map[string]interface{}{
		"enabled": true,
	}

	if cs.redis != nil {
		metrics["redis"] = cs.redis.GetMetrics()
	}

	if cs.application != nil {
		metrics["application"] = cs.application.GetMetrics()
	}

	return metrics
}

// Health checks the cache health
func (cs *CacheService) Health(ctx context.Context) error {
	if !cs.isEnabled {
		return nil
	}

	if cs.redis != nil {
		if err := cs.redis.Health(ctx); err != nil {
			return fmt.Errorf("Redis cache health check failed: %w", err)
		}
	}

	return nil
}

// Close closes all cache connections
func (cs *CacheService) Close() error {
	if cs.redis != nil {
		if err := cs.redis.Close(); err != nil {
			cs.logger.Error("Failed to close Redis cache", zap.Error(err))
			return err
		}
	}

	cs.logger.Info("Cache service closed")
	return nil
}

// IsEnabled returns whether caching is enabled
func (cs *CacheService) IsEnabled() bool {
	return cs.isEnabled
}

// Helper methods

func (cs *CacheService) copyValue(src interface{}, dest interface{}) error {
	// This is a simple implementation - in production you might want something more sophisticated
	if cs.application != nil {
		return cs.application.copyValue(src, dest)
	}

	// Fallback implementation
	return fmt.Errorf("cache not available for value copying")
}

// Cache key helpers

// ProductKey generates a cache key for a product
func (cs *CacheService) ProductKey(productID string) string {
	return fmt.Sprintf("product:%s", productID)
}

// CategoryKey generates a cache key for a category
func (cs *CacheService) CategoryKey(categoryID string) string {
	return fmt.Sprintf("category:%s", categoryID)
}

// UserKey generates a cache key for user data
func (cs *CacheService) UserKey(userID string) string {
	return fmt.Sprintf("user:%s", userID)
}

// SessionKey generates a cache key for user session
func (cs *CacheService) SessionKey(sessionID string) string {
	return fmt.Sprintf("session:%s", sessionID)
}

// SearchKey generates a cache key for search results
func (cs *CacheService) SearchKey(query string, filters map[string]string) string {
	key := fmt.Sprintf("search:%s", query)
	if len(filters) > 0 {
		for k, v := range filters {
			key += fmt.Sprintf(":%s=%s", k, v)
		}
	}
	return key
}

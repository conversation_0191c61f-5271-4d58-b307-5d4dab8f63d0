package telemetry

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	otlptracegrpc "go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/exporters/prometheus"
	"go.opentelemetry.io/otel/metric"
	sdkmetric "go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.21.0"
	"go.opentelemetry.io/otel/trace"
)

// TelemetryConfig تحديد إعدادات نظام المراقبة
type TelemetryConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	ServiceName     string `mapstructure:"service_name"`
	OTLPEndpoint    string `mapstructure:"otlp_endpoint"`
	OTLPInsecure    bool   `mapstructure:"otlp_insecure"`
	TracingSampler  string `mapstructure:"tracing_sampler"`
	MetricsEnabled  bool   `mapstructure:"metrics_enabled"`
	MetricsEndpoint string `mapstructure:"metrics_endpoint"`
}

// Initialize يبدأ نظام المراقبة والتتبع
func Initialize(ctx context.Context, serviceName, serviceVersion string, config TelemetryConfig) (func(), error) {
	if !config.Enabled {
		return func() {}, nil
	}

	// إنشاء الموارد
	res, err := resource.New(ctx,
		resource.WithAttributes(
			semconv.ServiceName(serviceName),
			semconv.ServiceVersion(serviceVersion),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create resource: %w", err)
	}

	// إعداد التتبع
	tracerProvider, err := initTracing(ctx, res, config)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize tracing: %w", err)
	}

	// إعداد المقاييس
	meterProvider, err := initMetrics(ctx, res, config)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize metrics: %w", err)
	}

	// تسجيل الموفرين عالمياً
	otel.SetTracerProvider(tracerProvider)
	otel.SetMeterProvider(meterProvider)

	// دالة التنظيف
	cleanup := func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := tracerProvider.Shutdown(ctx); err != nil {
			fmt.Printf("Error shutting down tracer provider: %v\n", err)
		}

		if err := meterProvider.Shutdown(ctx); err != nil {
			fmt.Printf("Error shutting down meter provider: %v\n", err)
		}
	}

	return cleanup, nil
}

// initTracing يبدأ نظام التتبع
func initTracing(ctx context.Context, res *resource.Resource, config TelemetryConfig) (*sdktrace.TracerProvider, error) {
	// إنشاء مُصدر OTLP (gRPC)
	var exporter sdktrace.SpanExporter
	var err error

	if config.OTLPEndpoint != "" {
		opts := []otlptracegrpc.Option{
			otlptracegrpc.WithEndpoint(config.OTLPEndpoint),
		}
		if config.OTLPInsecure {
			opts = append(opts, otlptracegrpc.WithInsecure())
		}

		exporter, err = otlptracegrpc.New(ctx, opts...)
		if err != nil {
			return nil, fmt.Errorf("failed to create OTLP exporter: %w", err)
		}
	} else {
		// استخدام مُصدر وهمي للتطوير
		exporter = &noopExporter{}
	}

	// إنشاء موفر التتبع
	tp := sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
		sdktrace.WithSampler(getSampler(config.TracingSampler)),
	)

	return tp, nil
}

// initMetrics يبدأ نظام المقاييس
func initMetrics(ctx context.Context, res *resource.Resource, config TelemetryConfig) (*sdkmetric.MeterProvider, error) {
	if !config.MetricsEnabled {
		return sdkmetric.NewMeterProvider(), nil
	}

	// إنشاء مُصدر Prometheus
	exporter, err := prometheus.New()
	if err != nil {
		return nil, fmt.Errorf("failed to create prometheus exporter: %w", err)
	}

	// إنشاء موفر المقاييس
	mp := sdkmetric.NewMeterProvider(
		sdkmetric.WithReader(exporter),
		sdkmetric.WithResource(res),
	)

	return mp, nil
}

// getSampler يحدد نوع أخذ العينات
func getSampler(samplerType string) sdktrace.Sampler {
	switch samplerType {
	case "always":
		return sdktrace.AlwaysSample()
	case "never":
		return sdktrace.NeverSample()
	case "ratio":
		return sdktrace.TraceIDRatioBased(0.1) // 10% من الطلبات
	default:
		return sdktrace.AlwaysSample()
	}
}

// noopExporter مُصدر وهمي للتطوير
type noopExporter struct{}

func (n *noopExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	return nil
}

func (n *noopExporter) Shutdown(ctx context.Context) error {
	return nil
}

// Tracer يوفر واجهة للتتبع
type Tracer struct {
	tracer trace.Tracer
}

// NewTracer ينشئ tracer جديد
func NewTracer(name string) *Tracer {
	return &Tracer{
		tracer: otel.Tracer(name),
	}
}

// StartSpan يبدأ span جديد
func (t *Tracer) StartSpan(ctx context.Context, name string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	return t.tracer.Start(ctx, name, opts...)
}

// Meter يوفر واجهة للمقاييس
type Meter struct {
	meter metric.Meter
}

// NewMeter ينشئ meter جديد
func NewMeter(name string) *Meter {
	return &Meter{
		meter: otel.Meter(name),
	}
}

// Counter ينشئ عداد جديد
func (m *Meter) Counter(name, description string) (metric.Int64Counter, error) {
	return m.meter.Int64Counter(name, metric.WithDescription(description))
}

// Histogram ينشئ مدرج تكراري جديد
func (m *Meter) Histogram(name, description string) (metric.Float64Histogram, error) {
	return m.meter.Float64Histogram(name, metric.WithDescription(description))
}

// Gauge ينشئ مقياس جديد (محذوف حالياً لعدم الدعم)
// func (m *Meter) Gauge(name, description string) (metric.Float64Gauge, error) {
// 	return m.meter.Float64Gauge(name, metric.WithDescription(description))
// }

// Metrics يحتوي على جميع المقاييس المطلوبة
type Metrics struct {
	HTTPRequests     metric.Int64Counter
	HTTPDuration     metric.Float64Histogram
	DatabaseQueries  metric.Int64Counter
	DatabaseDuration metric.Float64Histogram
	ErrorCount       metric.Int64Counter
	// ActiveConnections metric.Float64Gauge // محذوف حالياً
}

// NewMetrics ينشئ مجموعة المقاييس
func NewMetrics() (*Metrics, error) {
	meter := NewMeter("carnow-backend")

	httpRequests, err := meter.Counter("http_requests_total", "Total number of HTTP requests")
	if err != nil {
		return nil, err
	}

	httpDuration, err := meter.Histogram("http_request_duration_seconds", "Duration of HTTP requests")
	if err != nil {
		return nil, err
	}

	dbQueries, err := meter.Counter("database_queries_total", "Total number of database queries")
	if err != nil {
		return nil, err
	}

	dbDuration, err := meter.Histogram("database_query_duration_seconds", "Duration of database queries")
	if err != nil {
		return nil, err
	}

	errorCount, err := meter.Counter("errors_total", "Total number of errors")
	if err != nil {
		return nil, err
	}

	// activeConnections, err := meter.Gauge("active_connections", "Number of active connections")
	// if err != nil {
	// 	return nil, err
	// }

	return &Metrics{
		HTTPRequests:     httpRequests,
		HTTPDuration:     httpDuration,
		DatabaseQueries:  dbQueries,
		DatabaseDuration: dbDuration,
		ErrorCount:       errorCount,
		// ActiveConnections: activeConnections,
	}, nil
}

// RecordHTTPRequest يسجل طلب HTTP
func (m *Metrics) RecordHTTPRequest(ctx context.Context, method, path string, statusCode int, duration float64) {
	m.HTTPRequests.Add(ctx, 1, metric.WithAttributes(
		attribute.String("method", method),
		attribute.String("path", path),
		attribute.Int("status_code", statusCode),
	))

	m.HTTPDuration.Record(ctx, duration, metric.WithAttributes(
		attribute.String("method", method),
		attribute.String("path", path),
	))
}

// RecordDatabaseQuery يسجل استعلام قاعدة البيانات
func (m *Metrics) RecordDatabaseQuery(ctx context.Context, operation string, duration float64) {
	m.DatabaseQueries.Add(ctx, 1, metric.WithAttributes(
		attribute.String("operation", operation),
	))

	m.DatabaseDuration.Record(ctx, duration, metric.WithAttributes(
		attribute.String("operation", operation),
	))
}

// RecordError يسجل خطأ
func (m *Metrics) RecordError(ctx context.Context, errorType, operation string) {
	m.ErrorCount.Add(ctx, 1, metric.WithAttributes(
		attribute.String("error_type", errorType),
		attribute.String("operation", operation),
	))
}

// SetActiveConnections يحدد عدد الاتصالات النشطة (محذوف حالياً)
// func (m *Metrics) SetActiveConnections(ctx context.Context, count float64) {
// 	m.ActiveConnections.Record(ctx, count)
// }

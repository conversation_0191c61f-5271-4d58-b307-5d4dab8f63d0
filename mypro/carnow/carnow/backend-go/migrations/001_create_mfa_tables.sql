-- Migration: Create MFA (Multi-Factor Authentication) Tables
-- Created: 2025-07-29
-- Description: Creates tables for MFA challenges, settings, and enhanced session management

-- =============================================================================
-- MFA Settings Table
-- =============================================================================
CREATE TABLE IF NOT EXISTS mfa_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    email_enabled BOOLEAN DEFAULT FALSE,
    totp_enabled BOOLEAN DEFAULT FALSE,
    totp_secret TEXT, -- Encrypted TOTP secret
    totp_secret_hash TEXT, -- Hash of TOTP secret for verification
    backup_codes JSONB, -- Encrypted backup codes array
    backup_codes_hash JSONB, -- Hashes of backup codes
    phone_number TEXT,
    phone_verified BOOLEAN DEFAULT FALSE,
    email_address TEXT,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT unique_user_mfa_settings UNIQUE(user_id)
);

-- Index for faster user lookups
CREATE INDEX IF NOT EXISTS idx_mfa_settings_user_id ON mfa_settings(user_id);

-- =============================================================================
-- MFA Challenges Table
-- =============================================================================
CREATE TABLE IF NOT EXISTS mfa_challenges (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    method TEXT NOT NULL CHECK (method IN ('sms', 'email', 'totp')),
    code TEXT NOT NULL, -- The actual OTP code (should be encrypted)
    code_hash TEXT NOT NULL, -- Hash of the code for verification
    expires_at TIMESTAMPTZ NOT NULL,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_mfa_challenges_user_id ON mfa_challenges(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_challenges_expires_at ON mfa_challenges(expires_at);
CREATE INDEX IF NOT EXISTS idx_mfa_challenges_code_hash ON mfa_challenges(code_hash);

-- =============================================================================
-- Enhanced User Sessions Table
-- =============================================================================
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_token TEXT NOT NULL, -- Encrypted session token
    session_token_hash TEXT NOT NULL UNIQUE, -- Hash for lookup
    refresh_token TEXT, -- Encrypted refresh token
    refresh_token_hash TEXT, -- Hash for lookup
    device_fingerprint TEXT NOT NULL,
    device_type TEXT NOT NULL CHECK (device_type IN ('mobile', 'desktop', 'tablet', 'unknown')),
    device_name TEXT,
    ip_address INET NOT NULL,
    user_agent TEXT,
    location TEXT,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'expired', 'revoked', 'suspended')),
    last_activity TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Security fields
    login_attempts INTEGER DEFAULT 0,
    suspicious_activity BOOLEAN DEFAULT FALSE,
    two_factor_verified BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Indexes for performance and security
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON user_sessions(session_token_hash);
CREATE INDEX IF NOT EXISTS idx_user_sessions_refresh_hash ON user_sessions(refresh_token_hash);
CREATE INDEX IF NOT EXISTS idx_user_sessions_device_fingerprint ON user_sessions(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_user_sessions_status ON user_sessions(status);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity);

-- =============================================================================
-- Session Activity Log Table
-- =============================================================================
CREATE TABLE IF NOT EXISTS session_activity (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES user_sessions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for activity tracking
CREATE INDEX IF NOT EXISTS idx_session_activity_session_id ON session_activity(session_id);
CREATE INDEX IF NOT EXISTS idx_session_activity_user_id ON session_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_session_activity_action ON session_activity(action);
CREATE INDEX IF NOT EXISTS idx_session_activity_created_at ON session_activity(created_at);

-- =============================================================================
-- Security Events Table (for CSRF and other security events)
-- =============================================================================
CREATE TABLE IF NOT EXISTS security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    session_id UUID REFERENCES user_sessions(id) ON DELETE SET NULL,
    event_type TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    ip_address INET,
    user_agent TEXT,
    details JSONB DEFAULT '{}'::jsonb,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for security monitoring
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_session_id ON security_events(session_id);
CREATE INDEX IF NOT EXISTS idx_security_events_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_resolved ON security_events(resolved);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON security_events(created_at);

-- =============================================================================
-- Functions and Triggers
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_mfa_settings_updated_at 
    BEFORE UPDATE ON mfa_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mfa_challenges_updated_at 
    BEFORE UPDATE ON mfa_challenges 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_sessions_updated_at 
    BEFORE UPDATE ON user_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up expired challenges
CREATE OR REPLACE FUNCTION cleanup_expired_mfa_challenges()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM mfa_challenges 
    WHERE expires_at < NOW() - INTERVAL '1 hour' OR used = TRUE;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Mark expired sessions
    UPDATE user_sessions 
    SET status = 'expired' 
    WHERE expires_at < NOW() AND status = 'active';
    
    -- Delete old expired sessions (older than 30 days)
    DELETE FROM user_sessions 
    WHERE status IN ('expired', 'revoked') 
    AND updated_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- Row Level Security (RLS) Policies
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE mfa_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE session_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;

-- MFA Settings policies
CREATE POLICY "Users can view their own MFA settings" ON mfa_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own MFA settings" ON mfa_settings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own MFA settings" ON mfa_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- MFA Challenges policies
CREATE POLICY "Users can view their own MFA challenges" ON mfa_challenges
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can manage MFA challenges" ON mfa_challenges
    FOR ALL USING (true); -- Service role will handle this

-- User Sessions policies
CREATE POLICY "Users can view their own sessions" ON user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can manage user sessions" ON user_sessions
    FOR ALL USING (true); -- Service role will handle this

-- Session Activity policies
CREATE POLICY "Users can view their own session activity" ON session_activity
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can log session activity" ON session_activity
    FOR INSERT WITH CHECK (true); -- Service role will handle this

-- Security Events policies
CREATE POLICY "Admins can view all security events" ON security_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE ur.user_id = auth.uid() AND r.name = 'admin'
        )
    );

CREATE POLICY "Service can manage security events" ON security_events
    FOR ALL USING (true); -- Service role will handle this

-- =============================================================================
-- Comments for documentation
-- =============================================================================

COMMENT ON TABLE mfa_settings IS 'Stores user MFA configuration and settings';
COMMENT ON TABLE mfa_challenges IS 'Temporary storage for MFA challenges (OTP codes)';
COMMENT ON TABLE user_sessions IS 'Enhanced session management with device tracking';
COMMENT ON TABLE session_activity IS 'Audit log for session-related activities';
COMMENT ON TABLE security_events IS 'Security events and incidents tracking';

COMMENT ON FUNCTION cleanup_expired_mfa_challenges() IS 'Cleans up expired MFA challenges';
COMMENT ON FUNCTION cleanup_expired_sessions() IS 'Cleans up expired user sessions';

-- =============================================================================
-- Initial Data
-- =============================================================================

-- Insert default MFA settings for existing users (optional)
-- This can be run separately if needed
/*
INSERT INTO mfa_settings (user_id)
SELECT id FROM auth.users 
WHERE id NOT IN (SELECT user_id FROM mfa_settings);
*/

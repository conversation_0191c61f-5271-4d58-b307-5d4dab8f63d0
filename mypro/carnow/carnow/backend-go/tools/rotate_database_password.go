//go:build rotate_password
// +build rotate_password

package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"carnow-backend/internal/config"

	"github.com/jackc/pgx/v5"
)

const (
	// Default password length
	defaultPasswordLength = 32

	// SQL to update user password
	updatePasswordSQL = "ALTER USER %s WITH PASSWORD '%s'"

	// SQL to check if user exists
	checkUserSQL = "SELECT 1 FROM pg_roles WHERE rolname = $1"
)

func main() {
	// Parse command line flags
	username := flag.String("user", "", "Database user to rotate password for")
	length := flag.Int("length", defaultPasswordLength, "Password length")
	outputFile := flag.String("output", "", "Output file for new password (optional)")
	envVar := flag.String("env-var", "CARNOW_DATABASE_PASSWORD", "Environment variable name for password")
	help := flag.Bool("help", false, "Show help")

	flag.Parse()

	// Show help if requested
	if *help {
		flag.Usage()
		os.Exit(0)
	}

	fmt.Println("🔐 Database Password Rotation Tool")
	fmt.Println("=================================")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Validate username
	if *username == "" {
		*username = cfg.Database.Username
		fmt.Printf("Using database username from config: %s\n", *username)
	}

	if *username == "" {
		log.Fatalf("Database username is required. Use --user flag or set in config.")
	}

	// Generate secure password
	newPassword, err := generateSecurePassword(*length)
	if err != nil {
		log.Fatalf("Failed to generate secure password: %v", err)
	}

	fmt.Printf("✅ Generated secure password (%d characters)\n", len(newPassword))

	// Connect to database
	connString := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Database,
		cfg.Database.SSLMode,
	)

	fmt.Println("Connecting to database...")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	conn, err := pgx.Connect(ctx, connString)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer conn.Close(ctx)

	// Check if user exists
	var exists bool
	err = conn.QueryRow(ctx, checkUserSQL, *username).Scan(&exists)
	if err != nil {
		log.Fatalf("Failed to check if user exists: %v", err)
	}

	if !exists {
		log.Fatalf("User '%s' does not exist in the database", *username)
	}

	// Update password
	fmt.Printf("Updating password for user '%s'...\n", *username)

	// Escape single quotes in password for SQL
	escapedPassword := strings.ReplaceAll(newPassword, "'", "''")

	// Execute password update
	_, err = conn.Exec(ctx, fmt.Sprintf(updatePasswordSQL, *username, escapedPassword))
	if err != nil {
		log.Fatalf("Failed to update password: %v", err)
	}

	fmt.Printf("✅ Password updated successfully for user '%s'\n", *username)

	// Output new password
	if *outputFile != "" {
		// Write to file
		err = os.WriteFile(*outputFile, []byte(newPassword), 0600)
		if err != nil {
			log.Fatalf("Failed to write password to file: %v", err)
		}
		fmt.Printf("✅ Password written to file: %s\n", *outputFile)
	} else {
		// Print to console
		fmt.Println("\n🔐 New Password:")
		fmt.Println(newPassword)
	}

	// Print environment variable export command
	fmt.Println("\n📋 Update your environment with:")
	fmt.Printf("export %s='%s'\n", *envVar, newPassword)

	// Print update instructions
	fmt.Println("\n📝 Next steps:")
	fmt.Println("1. Update your .env file or environment variables")
	fmt.Println("2. Restart your application to use the new password")
	fmt.Println("3. Store the password securely in your password manager")

	fmt.Println("\n✅ Password rotation complete!")
}

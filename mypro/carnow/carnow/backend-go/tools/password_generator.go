package main

import (
	"crypto/rand"
	"fmt"
	"strings"
)

const (
	// Password character set (excluding ambiguous characters)
	charSet = "abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789!@#$%^&*()-_=+[]{}<>:;"
)

// generateSecurePassword generates a cryptographically secure password
func generateSecurePassword(length int) (string, error) {
	// Validate length
	if length < 16 {
		return "", fmt.Errorf("password length must be at least 16 characters")
	}

	// Generate random bytes
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}

	// Map random bytes to character set
	charSetLength := len(charSet)
	for i := 0; i < length; i++ {
		bytes[i] = charSet[int(bytes[i])%charSetLength]
	}

	// Ensure password contains at least one character from each category
	password := string(bytes)
	if err := ensurePasswordComplexity(password); err != nil {
		// If complexity check fails, regenerate
		return generateSecurePassword(length)
	}

	return password, nil
}

// ensurePasswordComplexity ensures the password meets complexity requirements
func ensurePasswordComplexity(password string) error {
	hasLower := false
	hasUpper := false
	hasDigit := false
	hasSpecial := false

	for _, char := range password {
		switch {
		case char >= 'a' && char <= 'z':
			hasLower = true
		case char >= 'A' && char <= 'Z':
			hasUpper = true
		case char >= '0' && char <= '9':
			hasDigit = true
		case strings.ContainsRune("!@#$%^&*()-_=+[]{}<>:;", char):
			hasSpecial = true
		}
	}

	if !hasLower || !hasUpper || !hasDigit || !hasSpecial {
		return fmt.Errorf("password does not meet complexity requirements")
	}

	return nil
}

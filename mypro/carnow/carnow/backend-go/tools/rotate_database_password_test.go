package main

import (
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGenerateSecurePassword(t *testing.T) {
	tests := []struct {
		name    string
		length  int
		wantErr bool
	}{
		{
			name:    "valid length 16",
			length:  16,
			wantErr: false,
		},
		{
			name:    "valid length 32",
			length:  32,
			wantErr: false,
		},
		{
			name:    "valid length 64",
			length:  64,
			wantErr: false,
		},
		{
			name:    "invalid length too short",
			length:  8,
			wantErr: true,
		},
		{
			name:    "invalid length zero",
			length:  0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			password, err := generateSecurePassword(tt.length)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, password)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.length, len(password))

				// Verify password complexity
				err = ensurePasswordComplexity(password)
				assert.NoError(t, err, "Generated password should meet complexity requirements")
			}
		})
	}
}

func TestEnsurePasswordComplexity(t *testing.T) {
	tests := []struct {
		name     string
		password string
		wantErr  bool
	}{
		{
			name:     "valid complex password",
			password: "Abc123!@#def",
			wantErr:  false,
		},
		{
			name:     "missing lowercase",
			password: "ABC123!@#DEF",
			wantErr:  true,
		},
		{
			name:     "missing uppercase",
			password: "abc123!@#def",
			wantErr:  true,
		},
		{
			name:     "missing digits",
			password: "Abc!@#def",
			wantErr:  true,
		},
		{
			name:     "missing special characters",
			password: "Abc123def",
			wantErr:  true,
		},
		{
			name:     "only lowercase",
			password: "abcdefghijk",
			wantErr:  true,
		},
		{
			name:     "only uppercase",
			password: "ABCDEFGHIJK",
			wantErr:  true,
		},
		{
			name:     "only digits",
			password: "1234567890",
			wantErr:  true,
		},
		{
			name:     "only special characters",
			password: "!@#$%^&*()",
			wantErr:  true,
		},
		{
			name:     "empty password",
			password: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ensurePasswordComplexity(tt.password)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestPasswordCharacterSet(t *testing.T) {
	// Test that the character set doesn't contain ambiguous characters
	ambiguousChars := []string{"0", "O", "l", "1", "I"}

	for _, char := range ambiguousChars {
		assert.NotContains(t, charSet, char, "Character set should not contain ambiguous character: %s", char)
	}

	// Test that the character set contains expected character types
	hasLower := false
	hasUpper := false
	hasDigit := false
	hasSpecial := false

	for _, char := range charSet {
		switch {
		case char >= 'a' && char <= 'z':
			hasLower = true
		case char >= 'A' && char <= 'Z':
			hasUpper = true
		case char >= '2' && char <= '9': // Excluding 0 and 1
			hasDigit = true
		case strings.ContainsRune("!@#$%^&*()-_=+[]{}<>:;", char):
			hasSpecial = true
		}
	}

	assert.True(t, hasLower, "Character set should contain lowercase letters")
	assert.True(t, hasUpper, "Character set should contain uppercase letters")
	assert.True(t, hasDigit, "Character set should contain digits")
	assert.True(t, hasSpecial, "Character set should contain special characters")
}

func TestPasswordUniqueness(t *testing.T) {
	// Generate multiple passwords and ensure they're unique
	passwords := make(map[string]bool)
	numPasswords := 100
	passwordLength := 32

	for i := 0; i < numPasswords; i++ {
		password, err := generateSecurePassword(passwordLength)
		require.NoError(t, err)

		// Check if password already exists
		assert.False(t, passwords[password], "Generated password should be unique")
		passwords[password] = true
	}

	assert.Equal(t, numPasswords, len(passwords), "All generated passwords should be unique")
}

func TestPasswordDistribution(t *testing.T) {
	// Test that generated passwords have good character distribution
	password, err := generateSecurePassword(1000) // Large password for statistical analysis
	require.NoError(t, err)

	charCounts := make(map[rune]int)
	for _, char := range password {
		charCounts[char]++
	}

	// Check that we're using a reasonable variety of characters
	uniqueChars := len(charCounts)
	minExpectedUnique := len(charSet) / 2 // At least half of available characters

	assert.GreaterOrEqual(t, uniqueChars, minExpectedUnique,
		"Password should use a good variety of characters")
}

// Benchmark tests
func BenchmarkGenerateSecurePassword(b *testing.B) {
	lengths := []int{16, 32, 64, 128}

	for _, length := range lengths {
		b.Run(fmt.Sprintf("length_%d", length), func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_, err := generateSecurePassword(length)
				if err != nil {
					b.Fatal(err)
				}
			}
		})
	}
}

func BenchmarkEnsurePasswordComplexity(b *testing.B) {
	// Generate a test password once
	password, err := generateSecurePassword(32)
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ensurePasswordComplexity(password)
	}
}

// Test helper functions
func TestPasswordMeetsRequirements(t *testing.T) {
	// Generate passwords of different lengths and verify they all meet requirements
	lengths := []int{16, 24, 32, 48, 64}

	for _, length := range lengths {
		t.Run(fmt.Sprintf("length_%d", length), func(t *testing.T) {
			password, err := generateSecurePassword(length)
			require.NoError(t, err)

			// Check length
			assert.Equal(t, length, len(password))

			// Check complexity
			err = ensurePasswordComplexity(password)
			assert.NoError(t, err)

			// Check that all characters are from our character set
			for _, char := range password {
				assert.Contains(t, charSet, string(char),
					"Password character should be from allowed character set")
			}
		})
	}
}

func TestPasswordSecurityProperties(t *testing.T) {
	password, err := generateSecurePassword(32)
	require.NoError(t, err)

	// Test that password doesn't contain common patterns
	commonPatterns := []string{
		"123", "abc", "ABC", "password", "admin", "user",
		"qwerty", "asdf", "zxcv", "!@#$%^&*()", "abcdefg",
	}

	lowerPassword := strings.ToLower(password)
	for _, pattern := range commonPatterns {
		assert.NotContains(t, lowerPassword, strings.ToLower(pattern),
			"Password should not contain common pattern: %s", pattern)
	}

	// Test that password doesn't have obvious repetitions
	for i := 0; i < len(password)-2; i++ {
		substr := password[i : i+3]
		restOfPassword := password[i+3:]
		assert.NotContains(t, restOfPassword, substr,
			"Password should not have obvious repetitions")
	}
}

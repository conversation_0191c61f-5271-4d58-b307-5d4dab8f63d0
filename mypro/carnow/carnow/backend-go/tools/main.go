package main

import (
	"context"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"

	"carnow-backend/internal/config"
	"carnow-backend/internal/infrastructure/database"

	"database/sql"
	"github.com/jackc/pgx/v5"
)

func runMigrations() {
	// Parse command line flags
	flag.CommandLine = flag.NewFlagSet("migrate", flag.ExitOnError)
	migrationDir := flag.String("dir", "./internal/infrastructure/database/migrations", "Migration directory path")
	flag.CommandLine.Parse(os.Args[2:])

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	fmt.Printf("Connecting to database: %s\n", cfg.Database.Host)

	// Create direct database connection to avoid prepared statement caching
	dsn := database.BuildSimpleDSN(cfg) + " statement_cache_size=0 prefer_simple_protocol=true"
	sqlDB, err := sql.Open("pgx", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer sqlDB.Close()

	// Test connection
	if err := sqlDB.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	// Read migration files
	files, err := ioutil.ReadDir(*migrationDir)
	if err != nil {
		log.Fatalf("Failed to read migration directory: %v", err)
	}

	fmt.Printf("Found %d migration files\n", len(files))

	// Apply migrations using direct SQL execution
	// This avoids prepared statement caching issues
	for _, file := range files {
		if filepath.Ext(file.Name()) == ".sql" {
			fmt.Printf("Applying migration: %s\n", file.Name())
			
			// Read migration file
			filePath := filepath.Join(*migrationDir, file.Name())
			content, err := ioutil.ReadFile(filePath)
			if err != nil {
				log.Fatalf("Failed to read migration file %s: %v", file.Name(), err)
			}

			// Execute migration using direct SQL execution
			// This avoids prepared statement caching issues
			_, err = sqlDB.ExecContext(context.Background(), string(content))
			if err != nil {
				log.Fatalf("Failed to apply migration %s: %v", file.Name(), err)
			}

			fmt.Printf("✅ Migration %s applied successfully\n", file.Name())
		}
	}

	fmt.Println("🎉 All migrations applied successfully!")
}

func printUsage() {
	fmt.Println("CarNow Backend Tools")
	fmt.Println("Usage: go run tools/main.go <command>")
	fmt.Println()
	fmt.Println("Available commands:")
	fmt.Println("  assign-admin <user_email>    Assign admin role to a user")
	fmt.Println("  migrate                     Apply database migrations")
	fmt.Println("  check-db                    Check database tables and structure")
	fmt.Println()
}

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]

	switch command {
	case "migrate":
		runMigrations()
	case "apply-007":
		runApply007Migration()
	case "assign-admin":
		if len(os.Args) < 3 {
			fmt.Println("Error: email is required")
			printUsage()
			os.Exit(1)
		}
		runAssignAdminRole(os.Args[2])
	case "check-db":
		checkDatabase()
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
		os.Exit(1)
	}
}

func runApply007Migration() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	fmt.Printf("Connecting to database: %s\n", cfg.Database.Host)

	// Create direct database connection to avoid prepared statement caching
	dsn := database.BuildSimpleDSN(cfg) + " statement_cache_size=0 prefer_simple_protocol=true"
	sqlDB, err := sql.Open("pgx", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer sqlDB.Close()

	// Path to the specific migration file
	migrationFile := "./internal/infrastructure/database/migrations/007_seller_subscription_requests.sql"
	
	// Check if file exists
	if _, err := os.Stat(migrationFile); os.IsNotExist(err) {
		log.Fatalf("Migration file does not exist: %s", migrationFile)
	}

	fmt.Printf("Applying migration: %s\n", migrationFile)
	
	// Read migration file
	content, err := ioutil.ReadFile(migrationFile)
	if err != nil {
		log.Fatalf("Failed to read migration file %s: %v", migrationFile, err)
	}

	// Execute migration using direct SQL execution
	// This avoids prepared statement caching issues
	_, err = sqlDB.ExecContext(context.Background(), string(content))
	if err != nil {
		log.Fatalf("Failed to apply migration %s: %v", migrationFile, err)
	}

	fmt.Printf("✅ Migration %s applied successfully\n", migrationFile)
	fmt.Println("🎉 Seller subscription requests table created successfully!")
}

func checkDatabase() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	fmt.Printf("Connecting to database: %s\n", cfg.Database.Host)

	// Create direct database connection to avoid prepared statement caching
	dsn := database.BuildSimpleDSN(cfg) + " statement_cache_size=0 prefer_simple_protocol=true"

	// Use pgx directly with simple protocol
	pgConfig, err := pgx.ParseConfig(dsn)
	if err != nil {
		log.Fatalf("Failed to parse DSN: %v", err)
	}
	pgConfig.DefaultQueryExecMode = pgx.QueryExecModeSimpleProtocol

	conn, err := pgx.ConnectConfig(context.Background(), pgConfig)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer conn.Close(context.Background())

	// Check database connectivity using raw SQL
	fmt.Println("Checking database connectivity...")
	var version string
	err = conn.QueryRow(context.Background(), "SELECT version()").Scan(&version)
	if err != nil {
		log.Fatalf("Failed to query database version: %v", err)
	}
	fmt.Printf("✅ Database version: %s\n", version)

	// Check if required tables exist
	tables := []string{"users", "user_profiles", "user_sessions", "auth_audit_log", "seller_subscription_requests"}
	fmt.Println("Checking required tables...")
	for _, table := range tables {
		var exists bool
		err = conn.QueryRow(context.Background(), "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = $1)", table).Scan(&exists)
		if err != nil {
			log.Fatalf("Failed to check table %s: %v", table, err)
		}
		if exists {
			fmt.Printf("✅ Table '%s' exists\n", table)
		} else {
			fmt.Printf("❌ Table '%s' does not exist\n", table)
		}
	}

	fmt.Println("✅ Database connectivity check completed successfully!")
}

func runAssignAdminRole(email string) {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	fmt.Printf("Connecting to database: %s\n", cfg.Database.Host)

	// Create direct database connection to avoid prepared statement caching
	dsn := database.BuildSimpleDSN(cfg) + " statement_cache_size=0 prefer_simple_protocol=true"
	sqlDB, err := sql.Open("pgx", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer sqlDB.Close()

	// Test connection
	if err := sqlDB.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	// First, find the user ID from auth.users table
	var userID string
	err = sqlDB.QueryRowContext(context.Background(), `
		SELECT id 
		FROM auth.users 
		WHERE email = $1
	`, email).Scan(&userID)

	if err != nil {
		log.Fatalf("Failed to find user: %v", err)
	}

	fmt.Printf("Found user ID: %s\n", userID)

	// Check if user exists in public.profiles table
	var exists bool
	err = sqlDB.QueryRowContext(context.Background(), `
		SELECT EXISTS(
			SELECT 1 FROM public.profiles WHERE auth_id = $1
		)
	`, userID).Scan(&exists)

	if err != nil {
		log.Fatalf("Failed to check if user exists in public.profiles: %v", err)
	}

	if !exists {
		// Create user in public.profiles table
		fmt.Printf("Creating user in public.profiles table...\n")
		_, err = sqlDB.ExecContext(context.Background(), `
			INSERT INTO public.profiles (id, auth_id, email, role, created_at, updated_at)
			VALUES (gen_random_uuid(), $1, $2, 'admin', NOW(), NOW())
		`, userID, email)
		if err != nil {
			log.Fatalf("Failed to create user in public.profiles: %v", err)
		}
		fmt.Printf("✅ User created in public.profiles table with admin role\n")
	} else {
		// Update existing user to admin role
		fmt.Printf("Updating user role to admin...\n")
		_, err = sqlDB.ExecContext(context.Background(), `
			UPDATE public.profiles 
			SET role = 'admin', updated_at = NOW()
			WHERE auth_id = $1
		`, userID)
		if err != nil {
			log.Fatalf("Failed to update user role: %v", err)
		}
		fmt.Printf("✅ User role updated to admin\n")
	}

	// Verify the change
	var role string
	err = sqlDB.QueryRowContext(context.Background(), `
		SELECT role FROM public.profiles WHERE auth_id = $1
	`, userID).Scan(&role)

	if err != nil {
		log.Fatalf("Failed to verify user role: %v", err)
	}

	fmt.Printf("✅ Verification: User %s now has role '%s'\n", email, role)
	fmt.Printf("🎉 Admin role assigned successfully!\n")
}

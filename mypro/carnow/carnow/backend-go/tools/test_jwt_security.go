//go:build test_jwt
// +build test_jwt

package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/services"
)

func main() {
	fmt.Println("🔐 JWT Security Implementation Test")
	fmt.Println("==================================")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize JWT service
	jwtService, err := services.NewJWTService(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize JWT service: %v", err)
	}

	fmt.Println("✅ JWT Service initialized successfully")

	// Test token generation
	testUserID := "550e8400-e29b-41d4-a716-446655440000"
	testEmail := "<EMAIL>"
	testRole := "authenticated"

	fmt.Printf("🧪 Testing token generation for user: %s\n", testEmail)

	// Generate token pair
	tokenPair, err := jwtService.GenerateTokenPair(testUserID, testEmail, testRole)
	if err != nil {
		log.Fatalf("Failed to generate token pair: %v", err)
	}

	fmt.Printf("✅ Token pair generated successfully\n")
	fmt.Printf("   Access Token Length: %d characters\n", len(tokenPair.AccessToken))
	fmt.Printf("   Refresh Token Length: %d characters\n", len(tokenPair.RefreshToken))
	fmt.Printf("   Expires At: %s\n", tokenPair.ExpiresAt.Format(time.RFC3339))
	fmt.Printf("   Token Type: %s\n", tokenPair.TokenType)

	// Test token validation
	fmt.Println("\n🔍 Testing token validation...")

	claims, err := jwtService.ValidateToken(tokenPair.AccessToken)
	if err != nil {
		log.Fatalf("Failed to validate access token: %v", err)
	}

	fmt.Printf("✅ Access token validated successfully\n")
	fmt.Printf("   User ID: %s\n", claims.UserID)
	fmt.Printf("   Email: %s\n", claims.Email)
	fmt.Printf("   Role: %s\n", claims.Role)
	fmt.Printf("   Token Type: %s\n", claims.TokenType)
	fmt.Printf("   Expires At: %s\n", claims.ExpiresAt.Time.Format(time.RFC3339))

	// Test refresh token validation
	refreshClaims, err := jwtService.ValidateToken(tokenPair.RefreshToken)
	if err != nil {
		log.Fatalf("Failed to validate refresh token: %v", err)
	}

	fmt.Printf("✅ Refresh token validated successfully\n")
	fmt.Printf("   Token Type: %s\n", refreshClaims.TokenType)

	// Test token refresh
	fmt.Println("\n🔄 Testing token refresh...")

	newTokenPair, err := jwtService.RefreshToken(tokenPair.RefreshToken)
	if err != nil {
		log.Fatalf("Failed to refresh token: %v", err)
	}

	fmt.Printf("✅ Token refresh successful\n")
	fmt.Printf("   New Access Token Length: %d characters\n", len(newTokenPair.AccessToken))
	fmt.Printf("   New Refresh Token Length: %d characters\n", len(newTokenPair.RefreshToken))

	// Test public key retrieval
	fmt.Println("\n🔑 Testing public key retrieval...")

	publicKeyPEM, err := jwtService.GetPublicKeyPEM()
	if err != nil {
		log.Fatalf("Failed to get public key: %v", err)
	}

	fmt.Printf("✅ Public key retrieved successfully\n")
	fmt.Printf("   Public Key Length: %d characters\n", len(publicKeyPEM))

	// Test invalid token
	fmt.Println("\n❌ Testing invalid token handling...")

	_, err = jwtService.ValidateToken("invalid.token.here")
	if err != nil {
		fmt.Printf("✅ Invalid token correctly rejected: %v\n", err)
	} else {
		fmt.Println("❌ Invalid token was incorrectly accepted!")
		os.Exit(1)
	}

	fmt.Println("\n🎉 All JWT security tests passed!")
	fmt.Println("✅ JWT implementation is working correctly")
	fmt.Println("✅ Tokens are properly signed and validated")
	fmt.Println("✅ Token refresh mechanism works")
	fmt.Println("✅ Invalid tokens are rejected")
	fmt.Println("✅ Public key can be retrieved for external validation")
}

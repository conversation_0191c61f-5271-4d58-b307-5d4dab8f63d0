services:
  - type: web
    name: carnow-backend
    env: go
    plan: free
    region: frankfurt
    dockerfilePath: ./Dockerfile
    envVars:
      - key: GO_ENV
        value: production
      - key: GIN_MODE
        value: release
      - key: PORT
        value: 8080
      - key: CARNOW_APP_ENVIRONMENT
        value: production
      - key: CARNOW_APP_DEBUG
        value: false
      
      # SECURITY: Database credentials from Render environment variables
      # Set these in Render dashboard under Environment tab
      - key: CARNOW_DATABASE_HOST
        fromDatabase:
          name: carnow-db
          property: host
      - key: CARNOW_DATABASE_PORT
        fromDatabase:
          name: carnow-db
          property: port
      - key: CARNOW_DATABASE_USERNAME
        fromDatabase:
          name: carnow-db
          property: user
      - key: CARNOW_DATABASE_PASSWORD
        fromDatabase:
          name: carnow-db
          property: password
      - key: CARNOW_DATABASE_DATABASE
        fromDatabase:
          name: carnow-db
          property: database
      
      # SECURITY: Supabase configuration from Render environment variables
      # Set these manually in Render dashboard - DO NOT put real values here
      - key: CARNOW_SUPABASE_URL
        sync: false  # Must be set manually in dashboard
      - key: CARNOW_SUPABASE_ANON_KEY
        sync: false  # Must be set manually in dashboard
      - key: CARNOW_SUPABASE_SERVICE_ROLE_KEY
        sync: false  # Must be set manually in dashboard
      - key: CARNOW_SUPABASE_JWT_SECRET
        sync: false  # Must be set manually in dashboard
      
      # SECURITY: JWT Configuration - set in Render dashboard
      - key: CARNOW_JWT_SECRET
        sync: false  # Must be set manually in dashboard
      - key: CARNOW_JWT_ISSUER
        value: carnow-backend
      - key: CARNOW_JWT_AUDIENCE
        value: carnow-app
      
      # SECURITY: Generate secure keys and set in Render dashboard
      - key: CARNOW_SECURITY_ENCRYPTION_KEY
        sync: false  # Must be set manually in dashboard
      
      # Force redeploy trigger (update timestamp to trigger redeploy)
      - key: FORCE_REDEPLOY
        value: "2025-01-18T10:00:00Z"
    
    buildFilter:
      paths:
        - backend-go/**
      ignoredPaths:
        - "*.md"
        - "docs/**"
    
    healthCheckPath: "/api/v1/health"

# Database service (if using Render PostgreSQL)
databases:
  - name: carnow-db
    databaseName: carnow
    user: carnow_user
    plan: free

# SECURITY NOTES:
# 1. This file is safe to commit - it contains no secrets
# 2. Real secrets must be set in Render dashboard under Environment tab
# 3. Use sync: false for sensitive environment variables
# 4. Database credentials are automatically injected by Render
# 5. Generate secure secrets using: openssl rand -base64 64
#!/bin/bash

# CarNow Backend Startup Script
# This script starts the CarNow Go backend with proper environment variables

echo "🚀 Starting CarNow Backend..."

# Load environment variables
export CARNOW_DATABASE_PASSWORD=9uS2LhnExynEJNWR
export CARNOW_GOOGLE_CLIENT_ID=630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com
export CARNOW_GOOGLE_CLIENT_SECRET=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
export CARNOW_GOOGLE_ANDROID_CLIENT_ID=630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com

echo "✅ Environment variables loaded"

# Start the backend
echo "🌐 Starting server on port 8080..."
go run cmd/main.go

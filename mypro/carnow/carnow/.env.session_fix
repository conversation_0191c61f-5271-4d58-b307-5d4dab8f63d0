# CarNow Session Fix Configuration
# Extended token expiry settings for better user experience

# JWT Configuration - Extended for better UX
JWT_EXPIRES_IN=168h
JWT_REFRESH_EXPIRES_IN=720h

# Session Management - Extended timeouts
SESSION_TIMEOUT=604800  # 7 days in seconds
HEARTBEAT_INTERVAL=3600  # 1 hour in seconds
INACTIVITY_WARNING=604200  # 6 days 23 hours in seconds

# Token Storage Configuration
TOKEN_STORAGE_ENCRYPTION=true
TOKEN_STORAGE_BIOMETRIC_REQUIRED=false

# Authentication Configuration
AUTH_REMEMBER_ME_DURATION=604800  # 7 days in seconds
AUTH_AUTO_REFRESH_ENABLED=true
AUTH_REFRESH_THRESHOLD=86400  # 1 day before expiry

# Backend Configuration
BACKEND_URL=https://backend-go-8klm.onrender.com
API_TIMEOUT=30000  # 30 seconds

# Debug Configuration
DEBUG_AUTH_PERSISTENCE=true
DEBUG_TOKEN_LIFECYCLE=true

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// شاشة اختبار بسيطة لاختبار مسار الإعدادات
class SettingsTestScreen extends StatelessWidget {
  const SettingsTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار مسار الإعدادات'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'اختبار التنقل إلى الإعدادات',
              style: TextStyle(fontSize: 24),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                debugPrint('🔍 Attempting to navigate to /settings');
                try {
                  context.push('/settings');
                  debugPrint('✅ Navigation to /settings successful');
                } catch (e) {
                  debugPrint('❌ Navigation to /settings failed: $e');
                }
              },
              child: const Text('الذهاب إلى الإعدادات'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                debugPrint('🔍 Attempting to navigate to /account');
                try {
                  context.push('/account');
                  debugPrint('✅ Navigation to /account successful');
                } catch (e) {
                  debugPrint('❌ Navigation to /account failed: $e');
                }
              },
              child: const Text('الذهاب إلى الحساب'),
            ),
            const SizedBox(height: 20),
            const Text(
              'تحقق من الـ console للمعلومات التشخيصية',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
} 
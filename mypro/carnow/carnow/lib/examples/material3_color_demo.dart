import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import '../core/theme/carnow_color_system.dart';

/// مثال عملي لاستخدام نظام Material 3 Dynamic Color في CarNow
class Material3ColorDemo extends StatefulWidget {
  const Material3ColorDemo({super.key});

  @override
  State<Material3ColorDemo> createState() => _Material3ColorDemoState();
}

class _Material3ColorDemoState extends State<Material3ColorDemo> {
  bool _useDynamicColor = true;
  ThemeMode _themeMode = ThemeMode.system;
  Color? _customSeedColor;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ColorScheme>(
      future: _generateColorScheme(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        final colorScheme = snapshot.data!;
        
        return Theme(
          data: _buildTheme(colorScheme),
          child: Scaffold(
            appBar: AppBar(
              title: const Text('معرض نظام الألوان الجديد'),
              centerTitle: true,
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildControlsSection(context),
                  const Gap(24),
                  _buildColorPaletteDemo(context, colorScheme),
                  const Gap(24),
                  _buildAutomotiveColorsDemo(context),
                  const Gap(24),
                  _buildAccessibilityDemo(context),
                  const Gap(24),
                  _buildComponentsDemo(context),
                  const Gap(24),
                  _buildCarPartsDemo(context),
                ],
              ),
            ),
            floatingActionButton: FloatingActionButton.extended(
              onPressed: () => _showColorGeneratorDialog(context),
              icon: const Icon(Icons.palette),
              label: const Text('منشئ الألوان'),
            ),
          ),
        );
      },
    );
  }

  Future<ColorScheme> _generateColorScheme() async {
    return CarNowColorSystem.generateColorScheme(
      brightness: _themeMode == ThemeMode.light 
          ? Brightness.light 
          : _themeMode == ThemeMode.dark 
              ? Brightness.dark 
              : MediaQuery.platformBrightnessOf(context),
      useDynamicColor: _useDynamicColor,
      customSeedColor: _customSeedColor,
    );
  }

  ThemeData _buildTheme(ColorScheme colorScheme) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      fontFamily: 'Cairo',
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
      ),
      cardTheme: CardThemeData(
        color: colorScheme.surfaceContainerLow,
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildControlsSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات النظام',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(16),
            
            // Dynamic Color Toggle
            SwitchListTile(
              title: const Text('استخدام الألوان الديناميكية'),
              subtitle: const Text('يتكيف مع ألوان النظام'),
              value: _useDynamicColor,
              onChanged: (value) {
                setState(() {
                  _useDynamicColor = value;
                });
              },
            ),
            
            // Theme Mode Selection
            const Gap(16),
            Text(
              'وضع التصميم',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Wrap(
              spacing: 8,
              children: [
                ChoiceChip(
                  label: const Text('فاتح'),
                  selected: _themeMode == ThemeMode.light,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _themeMode = ThemeMode.light;
                      });
                    }
                  },
                ),
                ChoiceChip(
                  label: const Text('داكن'),
                  selected: _themeMode == ThemeMode.dark,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _themeMode = ThemeMode.dark;
                      });
                    }
                  },
                ),
                ChoiceChip(
                  label: const Text('تلقائي'),
                  selected: _themeMode == ThemeMode.system,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _themeMode = ThemeMode.system;
                      });
                    }
                  },
                ),
              ],
            ),
            
            // Custom Color Selection
            if (!_useDynamicColor) ...[
              const Gap(16),
              Text(
                'اللون المخصص',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const Gap(8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildColorChip(
                    context,
                    'أخضر CarNow',
                    CarNowColorSystem.primaryBrand,
                    _customSeedColor == CarNowColorSystem.primaryBrand,
                  ),
                  _buildColorChip(
                    context,
                    'أزرق',
                    CarNowColorSystem.secondaryBrand,
                    _customSeedColor == CarNowColorSystem.secondaryBrand,
                  ),
                  _buildColorChip(
                    context,
                    'برتقالي',
                    CarNowColorSystem.accentBrand,
                    _customSeedColor == CarNowColorSystem.accentBrand,
                  ),
                  ...Colors.primaries.take(6).map((color) => _buildColorChip(
                    context,
                    '',
                    color,
                    _customSeedColor == color,
                  )),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildColorChip(BuildContext context, String label, Color color, bool isSelected) {
    return FilterChip(
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _customSeedColor = selected ? color : null;
        });
      },
      avatar: CircleAvatar(
        backgroundColor: color,
        radius: 8,
      ),
      label: Text(label.isNotEmpty ? label : '#${color.value.toRadixString(16).substring(2).toUpperCase()}'),
    );
  }

  Widget _buildColorPaletteDemo(BuildContext context, ColorScheme colorScheme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'لوحة الألوان Material 3',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(16),
            
            // Primary Colors
            _buildColorRow('الألوان الأساسية', [
              ('Primary', colorScheme.primary, colorScheme.onPrimary),
              ('Secondary', colorScheme.secondary, colorScheme.onSecondary),
              ('Tertiary', colorScheme.tertiary, colorScheme.onTertiary),
            ]),
            
            const Gap(16),
            
            // Surface Colors
            _buildColorRow('ألوان السطح', [
              ('Surface', colorScheme.surface, colorScheme.onSurface),
              ('Surface Variant', colorScheme.surfaceContainerHighest, colorScheme.onSurfaceVariant),
              ('Inverse Surface', colorScheme.inverseSurface, colorScheme.onInverseSurface),
            ]),
            
            const Gap(16),
            
            // Container Colors
            _buildColorRow('ألوان الحاويات', [
              ('Primary Container', colorScheme.primaryContainer, colorScheme.onPrimaryContainer),
              ('Secondary Container', colorScheme.secondaryContainer, colorScheme.onSecondaryContainer),
              ('Tertiary Container', colorScheme.tertiaryContainer, colorScheme.onTertiaryContainer),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildColorRow(String title, List<(String, Color, Color)> colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const Gap(8),
        Row(
          children: colors.map((colorData) {
            return Expanded(
              child: Container(
                height: 80,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: colorData.$2,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Theme.of(context).colorScheme.outline),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      colorData.$1,
                      style: TextStyle(
                        color: colorData.$3,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const Gap(4),
                    Text(
                      '#${colorData.$2.value.toRadixString(16).substring(2).toUpperCase()}',
                      style: TextStyle(
                        color: colorData.$3.withOpacity(0.7),
                        fontSize: 10,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildAutomotiveColorsDemo(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ألوان صناعة السيارات',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(16),
            
            // Category Colors
            Text(
              'فئات قطع الغيار',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: CarNowExpressiveColors.categories.entries.map((entry) {
                final color = entry.value;
                final contrastColor = AccessibilityColorSystem.ensureContrast(
                  Colors.white,
                  color,
                );
                
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getCategoryIcon(entry.key),
                        color: contrastColor,
                        size: 16,
                      ),
                      const Gap(4),
                      Text(
                        _getArabicCategoryName(entry.key),
                        style: TextStyle(
                          color: contrastColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
            
            const Gap(16),
            
            // Status Colors
            Text(
              'حالة المنتجات',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: CarNowExpressiveColors.status.entries.map((entry) {
                final color = entry.value;
                final contrastColor = AccessibilityColorSystem.ensureContrast(
                  Colors.white,
                  color,
                );
                
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getStatusIcon(entry.key),
                        color: contrastColor,
                        size: 16,
                      ),
                      const Gap(4),
                      Text(
                        _getArabicStatusName(entry.key),
                        style: TextStyle(
                          color: contrastColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccessibilityDemo(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختبار إمكانية الوصول',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(16),
            
            ...CarNowExpressiveColors.categories.entries.take(3).map((entry) {
              final backgroundColor = entry.value;
              final textColor = Colors.white;
              final contrastRatio = AccessibilityColorSystem.calculateContrast(
                textColor,
                backgroundColor,
              );
              final isAccessible = contrastRatio >= 4.5;
              
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isAccessible ? Colors.green : Colors.red,
                    width: 2,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      isAccessible ? Icons.check_circle : Icons.error,
                      color: isAccessible ? Colors.green : Colors.red,
                    ),
                    const Gap(8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getArabicCategoryName(entry.key),
                            style: TextStyle(
                              color: AccessibilityColorSystem.ensureContrast(
                                textColor,
                                backgroundColor,
                              ),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            'نسبة التباين: ${contrastRatio.toStringAsFixed(2)}:1',
                            style: TextStyle(
                              color: AccessibilityColorSystem.ensureContrast(
                                textColor,
                                backgroundColor,
                              ).withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildComponentsDemo(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مكونات Material 3',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(16),
            
            // Buttons
            Text(
              'الأزرار',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('زر مرفوع'),
                ),
                FilledButton(
                  onPressed: () {},
                  child: const Text('زر ممتلئ'),
                ),
                OutlinedButton(
                  onPressed: () {},
                  child: const Text('زر محدود'),
                ),
                TextButton(
                  onPressed: () {},
                  child: const Text('زر نصي'),
                ),
              ],
            ),
            
            const Gap(16),
            
            // Form Elements
            Text(
              'عناصر النموذج',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            const TextField(
              decoration: InputDecoration(
                labelText: 'البحث عن قطع الغيار',
                hintText: 'اكتب اسم القطعة...',
                prefixIcon: Icon(Icons.search),
              ),
            ),
            
            const Gap(16),
            
            // Switch and Checkbox
            SwitchListTile(
              title: const Text('تفعيل الإشعارات'),
              value: true,
              onChanged: (value) {},
            ),
            
            CheckboxListTile(
              title: const Text('أوافق على الشروط والأحكام'),
              value: true,
              onChanged: (value) {},
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCarPartsDemo(BuildContext context) {
    final sampleParts = [
      ('فلتر هواء', 'engine', 'available', '150 ريال'),
      ('مصباح أمامي', 'exterior', 'pending', '300 ريال'),
      ('غطاء مقعد', 'interior', 'sold', '200 ريال'),
      ('بطارية', 'electrical', 'maintenance', '450 ريال'),
      ('أقراص فرامل', 'brakes', 'reserved', '350 ريال'),
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مثال قطع الغيار',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Gap(16),
            
            ...sampleParts.map((part) {
              final categoryColor = CarNowExpressiveColors.getCategoryColor(part.$2);
              final statusColor = CarNowExpressiveColors.getStatusColor(part.$3);
              
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: categoryColor,
                    child: Icon(
                      _getCategoryIcon(part.$2),
                      color: AccessibilityColorSystem.ensureContrast(
                        Colors.white,
                        categoryColor,
                      ),
                      size: 20,
                    ),
                  ),
                  title: Text(part.$1),
                  subtitle: Text(_getArabicCategoryName(part.$2)),
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: statusColor),
                        ),
                        child: Text(
                          _getArabicStatusName(part.$3),
                          style: TextStyle(
                            color: statusColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const Gap(4),
                      Text(
                        part.$4,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  void _showColorGeneratorDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('منشئ الألوان التلقائي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('سيتم توليد مجموعة ألوان جديدة بناءً على اللون المحدد'),
            const Gap(16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _customSeedColor = Colors.primaries[
                    DateTime.now().millisecond % Colors.primaries.length
                  ];
                });
                Navigator.of(context).pop();
              },
              child: const Text('توليد ألوان عشوائية'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    const categoryIcons = {
      'engine': Icons.settings,
      'exterior': Icons.directions_car,
      'interior': Icons.airline_seat_recline_normal,
      'electrical': Icons.electrical_services,
      'brakes': Icons.speed,
      'transmission': Icons.auto_mode,
      'suspension': Icons.height,
      'tires': Icons.circle,
      'accessories': Icons.star,
      'tools': Icons.build,
    };
    return categoryIcons[category] ?? Icons.help;
  }

  IconData _getStatusIcon(String status) {
    const statusIcons = {
      'available': Icons.check_circle,
      'pending': Icons.schedule,
      'sold': Icons.sell,
      'reserved': Icons.bookmark,
      'maintenance': Icons.build_circle,
      'shipping': Icons.local_shipping,
      'delivered': Icons.done_all,
      'cancelled': Icons.cancel,
    };
    return statusIcons[status] ?? Icons.help;
  }

  String _getArabicCategoryName(String category) {
    const categoryNames = {
      'engine': 'محرك',
      'exterior': 'خارجي',
      'interior': 'داخلي', 
      'electrical': 'كهربائي',
      'brakes': 'فرامل',
      'transmission': 'ناقل الحركة',
      'suspension': 'تعليق',
      'tires': 'إطارات',
      'accessories': 'إكسسوارات',
      'tools': 'أدوات',
    };
    return categoryNames[category] ?? category;
  }

  String _getArabicStatusName(String status) {
    const statusNames = {
      'available': 'متوفر',
      'pending': 'معلق',
      'sold': 'مباع',
      'reserved': 'محجوز',
      'maintenance': 'صيانة',
      'shipping': 'شحن',
      'delivered': 'تم التسليم',
      'cancelled': 'ملغي',
    };
    return statusNames[status] ?? status;
  }
} 
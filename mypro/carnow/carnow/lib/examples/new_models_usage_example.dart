import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../features/parts/models/models.dart';
import '../features/cars/providers/engine_specifications_provider.dart';
import '../features/admin_tools/vehicles/providers/vehicle_providers.dart';

/// مثال عملي لاستخدام النماذج والمقدمات الجديدة
/// Practical example for using new models and providers
class NewModelsUsageExample extends ConsumerWidget {
  const NewModelsUsageExample({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('مثال النماذج الجديدة')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildVehicleMakesSection(ref),
            const SizedBox(height: 24),
            _buildEngineSpecsSection(ref),
            const SizedBox(height: 24),
            _buildPartsSection(ref),
            const SizedBox(height: 24),
            _buildUsageExamples(),
          ],
        ),
      ),
    );
  }

  /// قسم ماركات المركبات
  Widget _buildVehicleMakesSection(WidgetRef ref) {
    final vehicleMakesAsync = ref.watch(vehicleMakesProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ماركات المركبات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            vehicleMakesAsync.when(
              data: (makes) => Column(
                children: makes
                    .take(3)
                    .map(
                      (make) => ListTile(
                        leading: make.logoUrl != null
                            ? Image.network(
                                make.logoUrl!,
                                width: 40,
                                height: 40,
                              )
                            : const Icon(Icons.directions_car),
                        title: Text(make.name),
                        subtitle: Text(make.nameAr ?? ''),
                        trailing: Text(make.countryOfOrigin ?? ''),
                      ),
                    )
                    .toList(),
              ),
              loading: () => const CircularProgressIndicator(),
              error: (error, _) => Text('خطأ: $error'),
            ),
          ],
        ),
      ),
    );
  }

  /// قسم مواصفات المحركات
  Widget _buildEngineSpecsSection(WidgetRef ref) {
    final engineSpecsAsync = ref.watch(engineSpecificationsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مواصفات المحركات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            engineSpecsAsync.when(
              data: (engines) => Column(
                children: engines
                    .take(3)
                    .map(
                      (engine) => ListTile(
                        title: Text(engine.name),
                        subtitle: Text(
                          '${engine.displacement}L ${engine.fuelType}',
                        ),
                        trailing: Text('${engine.horsepower} HP'),
                      ),
                    )
                    .toList(),
              ),
              loading: () => const CircularProgressIndicator(),
              error: (error, _) => Text('خطأ: $error'),
            ),
          ],
        ),
      ),
    );
  }

  /// قسم القطع
  Widget _buildPartsSection(WidgetRef ref) {
    final partsAsync = ref.watch(partsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'القطع المتوفرة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            partsAsync.when(
              data: (parts) => Column(
                children: parts
                    .take(3)
                    .map(
                      (part) => ListTile(
                        leading: part.imageUrl != null
                            ? Image.network(
                                part.imageUrl!,
                                width: 40,
                                height: 40,
                              )
                            : const Icon(Icons.build),
                        title: Text(part.name),
                        subtitle: Text(part.brand ?? 'غير محدد'),
                        trailing: Text(
                          '${part.priceLyd.toStringAsFixed(2)} د.ل',
                        ),
                      ),
                    )
                    .toList(),
              ),
              loading: () => const CircularProgressIndicator(),
              error: (error, _) => Text('خطأ: $error'),
            ),
          ],
        ),
      ),
    );
  }

  /// أمثلة الاستخدام
  Widget _buildUsageExamples() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أمثلة الاستخدام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildCodeExample('البحث في ماركات المركبات', '''
// البحث في الماركات
final makes = await ref
    .read(vehicleMakesProvider.notifier)
    .search('تويوتا');

// الحصول على موديلات ماركة معينة  
final models = await ref
    .read(vehicleModelsProvider.notifier)
    .getByMakeId('toyota_id');
'''),
            const SizedBox(height: 16),
            _buildCodeExample('إدارة القطع والتوافق', '''
// البحث في القطع
final parts = await ref
    .read(partsProvider.notifier)
    .search('فلتر زيت');

// الحصول على قطعة كاملة مع التوافق
final completePart = await ref
    .read(completePartProvider('part_id').future);

// القطع المتوافقة مع مركبة معينة
final compatibleParts = await ref
    .read(partCompatibilityProvider.notifier)
    .getByVehicle(
      makeId: 'toyota',
      modelId: 'camry',
      year: 2020,
    );
'''),
            const SizedBox(height: 16),
            _buildCodeExample('استخدام مواصفات المحركات', '''
// البحث في المحركات
final engines = await ref
    .read(engineSpecificationsProvider.notifier)
    .search('V6');

// تصفية حسب نوع الوقود
final dieselEngines = await ref
    .read(engineSpecificationsProvider.notifier)
    .filterByFuelType('diesel');

// الحصول على محرك كامل مع الأداء
final completeEngine = await ref
    .read(completeEngineProvider('engine_id').future);
'''),
          ],
        ),
      ),
    );
  }

  Widget _buildCodeExample(String title, String code) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Text(
            code,
            style: const TextStyle(
              fontFamily: 'monospace',
              fontSize: 12,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }
}

/// شاشة عرض المثال
///
/// تُقدّم نموذجاً عملياً لكيفية استخدام النماذج الجديدة ومزودي Riverpod.
class ExampleDisplayScreen extends StatelessWidget {
  const ExampleDisplayScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProviderScope(child: NewModelsUsageExample());
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/utils/user_display_utils.dart';
import '../features/account/models/user_model.dart';
import '../l10n/app_localizations.dart';

/// Example screen to test OAuth name extraction
class OAuthNameExtractionTestScreen extends ConsumerWidget {
  const OAuthNameExtractionTestScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    
    // Create example OAuth user metadata scenarios
    final oauthScenarios = [
      {
        'provider': 'Google',
        'metadata': {
          'full_name': 'أحمد محمد علي',
          'email': '<EMAIL>',
          'avatar_url': 'https://lh3.googleusercontent.com/a/default-user=s96-c',
        },
        'description': 'Google user with Arabic name'
      },
      {
        'provider': 'Google',
        'metadata': {
          'name': '<PERSON>',
          'email': '<EMAIL>',
          'avatar_url': 'https://lh3.googleusercontent.com/a/default-user=s96-c',
        },
        'description': 'Google user with English name'
      },
      {
        'provider': 'Facebook',
        'metadata': {
          'full_name': 'سارة أحمد محمد',
          'email': '<EMAIL>',
          'picture': 'https://graph.facebook.com/12345/picture',
        },
        'description': 'Facebook user with Arabic name'
      },
      {
        'provider': 'Email',
        'metadata': {
          'email': '<EMAIL>',
        },
        'description': 'Email registration - should extract from email'
      },
      {
        'provider': 'Email',
        'metadata': {
          'email': '<EMAIL>',
        },
        'description': 'Email registration - should use default name'
      },
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار استخراج الأسماء من OAuth'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: oauthScenarios.length,
        itemBuilder: (context, index) {
          final scenario = oauthScenarios[index];
          final provider = scenario['provider'] as String;
          final metadata = scenario['metadata'] as Map<String, dynamic>;
          final description = scenario['description'] as String;
          
          // Simulate name extraction logic
          final extractedName = _extractNameFromMetadata(metadata);
          
          // Create a mock user model
          final mockUser = UserModel(
            id: 'test_$index',
            authId: 'auth_$index',
            name: extractedName,
            email: metadata['email'] as String?,
            profileImageUrl: metadata['avatar_url'] as String? ?? 
                           metadata['picture'] as String?,
          );

          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Provider info
                  Row(
                    children: [
                      Icon(
                        _getProviderIcon(provider),
                        color: _getProviderColor(provider),
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        provider,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: _getProviderColor(provider),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  
                  const Divider(height: 24),
                  
                  // Original metadata
                  Text(
                    'البيانات الأصلية:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: metadata.entries.map((entry) {
                        return Text(
                          '${entry.key}: ${entry.value}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontFamily: 'monospace',
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Processed result
                  Text(
                    'النتيجة المعالجة:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        backgroundImage: mockUser.profileImageUrl != null
                            ? NetworkImage(mockUser.profileImageUrl!)
                            : null,
                        child: mockUser.profileImageUrl == null
                            ? Text(
                                UserDisplayUtils.getAvatarLetter(mockUser, context),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : null,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              UserDisplayUtils.getDisplayName(mockUser, context),
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              mockUser.email ?? '',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  UserDisplayUtils.isProfileComplete(mockUser)
                                      ? Icons.check_circle
                                      : Icons.warning,
                                  size: 16,
                                  color: UserDisplayUtils.isProfileComplete(mockUser)
                                      ? Colors.green
                                      : Colors.orange,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  UserDisplayUtils.isProfileComplete(mockUser)
                                      ? 'الملف الشخصي مكتمل'
                                      : 'الملف الشخصي غير مكتمل',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: UserDisplayUtils.isProfileComplete(mockUser)
                                        ? Colors.green
                                        : Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Extract name from OAuth metadata (simulating the actual extraction logic)
  String? _extractNameFromMetadata(Map<String, dynamic> metadata) {
    // This mirrors the logic in ProfileSyncService
    final nameFromOAuth = metadata['full_name'] ?? 
                         metadata['name'] ?? 
                         metadata['display_name'];
    
    if (nameFromOAuth != null && nameFromOAuth.isNotEmpty) {
      return nameFromOAuth;
    }
    
    // Try to extract from email
    final email = metadata['email'] as String?;
    if (email != null && email.contains('@')) {
      final prefix = email.split('@').first;
      
      // Clean up the email prefix
      String cleaned = prefix.replaceAll(RegExp(r'[0-9]+$'), '');
      cleaned = cleaned.replaceAll(RegExp('[._-]'), ' ');
      
      final words = cleaned.split(' ')
          .where((word) => word.isNotEmpty)
          .map((word) => word[0].toUpperCase() + word.substring(1).toLowerCase())
          .toList();
      
      final result = words.join(' ');
      
      if (result.length >= 3) {
        return result;
      }
    }
    
    return 'عضو جديد';
  }

  IconData _getProviderIcon(String provider) {
    switch (provider) {
      case 'Google':
        return Icons.g_mobiledata;
      case 'Facebook':
        return Icons.facebook;
      case 'Email':
        return Icons.email;
      default:
        return Icons.person;
    }
  }

  Color _getProviderColor(String provider) {
    switch (provider) {
      case 'Google':
        return Colors.red;
      case 'Facebook':
        return Colors.blue;
      case 'Email':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
} 
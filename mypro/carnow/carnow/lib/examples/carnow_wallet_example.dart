import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../core/providers/carnow_providers.dart';

/// مثال على كيفية استخدام النظام المالي الجديد (Carnow)
class CarnowWalletExample extends HookConsumerWidget {
  const CarnowWalletExample({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final healthCheck = ref.watch(carnowHealthCheckProvider);
    final userAsync = ref.watch(carnowUserNotifierProvider);
    final walletAsync = ref.watch(carnowWalletNotifierProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال على النظام المالي الجديد'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Health Check Card
            Card(
              child: ListTile(
                title: const Text('حالة النظام المالي'),
                subtitle: healthCheck.when(
                  data: (isHealthy) => Text(
                    isHealthy ? 'النظام يعمل بشكل طبيعي' : 'مشكلة في النظام',
                    style: TextStyle(
                      color: isHealthy ? Colors.green : Colors.red,
                    ),
                  ),
                  loading: () => const Text('جاري التحقق...'),
                  error: (error, _) => Text('خطأ: $error'),
                ),
                leading: healthCheck.maybeWhen(
                  data: (isHealthy) => Icon(
                    isHealthy ? Icons.check_circle : Icons.error,
                    color: isHealthy ? Colors.green : Colors.red,
                  ),
                  orElse: () => const CircularProgressIndicator(),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // User Info Card
            Card(
              child: ListTile(
                title: const Text('معلومات المستخدم'),
                subtitle: userAsync.when(
                  data: (user) => user != null
                      ? Text('${user.firstName} ${user.lastName}')
                      : const Text('لم يتم تسجيل الدخول'),
                  loading: () => const Text('جاري التحميل...'),
                  error: (error, _) => Text('خطأ: $error'),
                ),
                leading: const Icon(Icons.person),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Wallet Info Card
            Card(
              child: ListTile(
                title: const Text('معلومات المحفظة'),
                subtitle: walletAsync.when(
                  data: (wallet) => wallet != null
                      ? Text('الرصيد: ${wallet.balance.toStringAsFixed(2)} ر.س')
                      : const Text('لا توجد محفظة'),
                  loading: () => const Text('جاري التحميل...'),
                  error: (error, _) => Text('خطأ: $error'),
                ),
                leading: const Icon(Icons.account_balance_wallet),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            const Text(
              'الإجراءات المتاحة:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            
            const SizedBox(height: 16),
            
            // Open Financial Wallet Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  context.push('/wallet/financial');
                },
                icon: const Icon(Icons.account_balance_wallet),
                label: const Text('فتح المحفظة المالية'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Quick Deposit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: walletAsync.value != null
                    ? () => _showQuickDepositDialog(context, ref)
                    : null,
                icon: const Icon(Icons.add_circle),
                label: const Text('إيداع سريع'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Refresh Data Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  ref.invalidate(carnowUserNotifierProvider);
                  ref.invalidate(carnowWalletNotifierProvider);
                },
                icon: const Icon(Icons.refresh),
                label: const Text('تحديث البيانات'),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // API Information Card
            Card(
              color: Colors.grey.shade50,
              child: const Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات التكامل:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('• النظام المالي: Carnow Backend (Go)'),
                    Text('• الرابط: https://backend-go-8klm.onrender.com'),
                    Text('• قاعدة البيانات: PostgreSQL'),
                    Text('• نوع التكامل: REST API'),
                    Text('• الأمان: JWT Token'),
                    SizedBox(height: 8),
                    Text(
                      'الميزات المتاحة:',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    Text('✓ إنشاء المحفظة تلقائياً'),
                    Text('✓ الإيداع والسحب'),
                    Text('✓ تتبع المعاملات'),
                    Text('✓ إحصائيات المحفظة'),
                    Text('✓ تحديث فوري للبيانات'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showQuickDepositDialog(BuildContext context, WidgetRef ref) {
    final amountController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إيداع سريع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'المبلغ',
                prefixText: 'ر.س ',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'سيتم إيداع المبلغ فوراً في محفظتك',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final amount = double.tryParse(amountController.text);
              if (amount != null && amount > 0) {
                try {
                  await ref.read(walletActionsProvider.notifier).addFunds(amount);
                  
                  if (context.mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم الإيداع بنجاح')),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ: ${e.toString()}')),
                    );
                  }
                }
              }
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/utils/user_display_utils.dart';
import '../features/account/models/user_model.dart';
import '../l10n/app_localizations.dart';

/// Example screen to demonstrate the new user display system
class UserDisplayExampleScreen extends ConsumerWidget {
  const UserDisplayExampleScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    
    // Create example users with different profile completion states
    final users = [
      // Complete profile
      const UserModel(
        id: '1',
        authId: '1',
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        phone: '+************',
      ),
      
      // Incomplete profile - has name but no phone
      const UserModel(
        id: '2',
        authId: '2',
        name: 'سارة أحمد',
        email: '<EMAIL>',
      ),
      
      // New user with default name
      const UserModel(
        id: '3',
        authId: '3',
        name: 'مستخدم',
        email: '<EMAIL>',
      ),
      
      // New user with email prefix as name
      const UserModel(
        id: '4',
        authId: '4',
        name: 'john.doe',
        email: '<EMAIL>',
      ),
      
      // User with cleaned email name
      const UserModel(
        id: '5',
        authId: '5',
        name: 'user_name_123',
        email: '<EMAIL>',
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام عرض المستخدمين الجديد'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: users.length,
        itemBuilder: (context, index) {
          final user = users[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Original data
                  Text(
                    'البيانات الأصلية:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('الاسم: ${user.name}'),
                  Text('الإيميل: ${user.email}'),
                  Text('الهاتف: ${user.phone ?? 'غير متوفر'}'),
                  
                  const Divider(height: 24),
                  
                  // Processed display
                  Text(
                    'العرض المحسن:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        child: Text(
                          UserDisplayUtils.getAvatarLetter(user, context),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              UserDisplayUtils.getDisplayName(user, context),
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              user.email ?? '',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                            
                            // Profile status
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Icon(
                                  UserDisplayUtils.isProfileComplete(user)
                                      ? Icons.check_circle
                                      : Icons.warning,
                                  size: 16,
                                  color: UserDisplayUtils.isProfileComplete(user)
                                      ? Colors.green
                                      : Colors.orange,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  UserDisplayUtils.isProfileComplete(user)
                                      ? 'الملف الشخصي مكتمل'
                                      : 'الملف الشخصي غير مكتمل',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: UserDisplayUtils.isProfileComplete(user)
                                        ? Colors.green
                                        : Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                            
                                                         // Status message
                             if (UserDisplayUtils.getProfileStatusMessage(user, context).isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.secondary.withAlpha((0.2 * 255).toInt()),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                                                 child: Text(
                                   UserDisplayUtils.getProfileStatusMessage(user, context),
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).colorScheme.secondary,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
} 
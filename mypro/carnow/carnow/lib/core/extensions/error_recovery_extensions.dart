import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../errors/result.dart';
import '../errors/app_error.dart';
import '../services/error_recovery_service.dart';

/// Extensions لتسهيل استخدام Error Recovery
/// Extensions to simplify Error Recovery usage

/// Extension على Future لإضافة إعادة المحاولة التلقائية
extension FutureRetryExtension<T> on Future<T> {
  /// إضافة إعادة المحاولة التلقائية للـ Future
  Future<Result<T>> withRetry({
    String? operationName,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    Duration maxDelay = const Duration(seconds: 30),
    bool Function(dynamic error)? shouldRetry,
    Map<String, dynamic>? metadata,
  }) async {
    // نحتاج للحصول على ErrorRecoveryService
    // هذا مبسط، في التطبيق الحقيقي نحتاج dependency injection
    throw UnimplementedError(
      'Use executeWithRetry from ErrorRecoveryService instead',
    );
  }
}

/// Extension على Ref لتسهيل استخدام Error Recovery
extension ErrorRecoveryRef on Ref {
  /// تنفيذ عملية مع إعادة المحاولة التلقائية
  Future<Result<T>> executeWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    Duration maxDelay = const Duration(seconds: 30),
    bool Function(dynamic error)? shouldRetry,
    Map<String, dynamic>? metadata,
  }) async {
    final recoveryService = await read(errorRecoveryServiceProvider.future);
    return recoveryService.executeWithRetry(
      operation,
      operationName: operationName,
      maxRetries: maxRetries,
      initialDelay: initialDelay,
      backoffMultiplier: backoffMultiplier,
      maxDelay: maxDelay,
      shouldRetry: shouldRetry,
      metadata: metadata,
    );
  }

  /// تنفيذ عملية مع تخزين مؤقت
  Future<Result<T>> executeWithCache<T>(
    Future<T> Function() operation, {
    required String cacheKey,
    Duration cacheDuration = const Duration(minutes: 5),
    String? operationName,
    T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    final recoveryService = await read(errorRecoveryServiceProvider.future);
    return recoveryService.executeWithCache(
      operation,
      cacheKey: cacheKey,
      cacheDuration: cacheDuration,
      operationName: operationName,
      fromJson: fromJson,
      toJson: toJson,
    );
  }

  /// تنفيذ عملية مع إعادة المحاولة والتخزين المؤقت
  Future<Result<T>> executeWithRetryAndCache<T>(
    Future<T> Function() operation, {
    required String cacheKey,
    String? operationName,
    Duration cacheDuration = const Duration(minutes: 5),
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    Duration maxDelay = const Duration(seconds: 30),
    bool Function(dynamic error)? shouldRetry,
    T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic> Function(T)? toJson,
    Map<String, dynamic>? metadata,
  }) async {
    final recoveryService = await read(errorRecoveryServiceProvider.future);

    // أولاً نحاول التخزين المؤقت
    final cacheResult = await recoveryService.executeWithCache(
      operation,
      cacheKey: cacheKey,
      cacheDuration: cacheDuration,
      operationName: operationName,
      fromJson: fromJson,
      toJson: toJson,
    );

    // إذا نجح التخزين المؤقت، نرجع النتيجة
    if (cacheResult.isSuccess) {
      return cacheResult;
    }

    // إذا فشل، نحاول مع إعادة المحاولة
    return recoveryService.executeWithRetry(
      operation,
      operationName: operationName,
      maxRetries: maxRetries,
      initialDelay: initialDelay,
      backoffMultiplier: backoffMultiplier,
      maxDelay: maxDelay,
      shouldRetry: shouldRetry,
      metadata: metadata,
    );
  }
}

/// Extension على AsyncNotifier لإضافة Error Recovery
/// Note: This extension provides helper methods but cannot directly modify state
/// due to Riverpod's protection. Use these methods within your AsyncNotifier classes.
extension AsyncNotifierErrorRecovery<T> on AsyncNotifier<T> {
  /// تحديث الحالة مع إعادة المحاولة التلقائية
  /// This method returns the result instead of directly setting state
  Future<Result<T>> executeWithRetry(
    Future<T> Function() operation, {
    String? operationName,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    bool Function(dynamic error)? shouldRetry,
    Map<String, dynamic>? metadata,
  }) async {
    return ref.executeWithRetry(
      operation,
      operationName: operationName,
      maxRetries: maxRetries,
      initialDelay: initialDelay,
      shouldRetry: shouldRetry,
      metadata: metadata,
    );
  }

  /// تحديث الحالة مع تخزين مؤقت
  /// This method returns the result instead of directly setting state
  Future<Result<T>> executeWithCache(
    Future<T> Function() operation, {
    required String cacheKey,
    Duration cacheDuration = const Duration(minutes: 5),
    String? operationName,
    T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    return ref.executeWithCache(
      operation,
      cacheKey: cacheKey,
      cacheDuration: cacheDuration,
      operationName: operationName,
      fromJson: fromJson,
      toJson: toJson,
    );
  }

  /// تحديث الحالة مع إعادة المحاولة والتخزين المؤقت
  /// This method returns the result instead of directly setting state
  Future<Result<T>> executeWithRetryAndCache(
    Future<T> Function() operation, {
    required String cacheKey,
    String? operationName,
    Duration cacheDuration = const Duration(minutes: 5),
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    bool Function(dynamic error)? shouldRetry,
    T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic> Function(T)? toJson,
    Map<String, dynamic>? metadata,
  }) async {
    return ref.executeWithRetryAndCache(
      operation,
      cacheKey: cacheKey,
      operationName: operationName,
      cacheDuration: cacheDuration,
      maxRetries: maxRetries,
      initialDelay: initialDelay,
      shouldRetry: shouldRetry,
      fromJson: fromJson,
      toJson: toJson,
      metadata: metadata,
    );
  }
}

/// Mixin لإضافة Error Recovery للـ Providers
mixin ErrorRecoveryMixin<T> on AsyncNotifier<T> {
  /// تنفيذ عملية مع معالجة شاملة للأخطاء
  Future<void> executeOperation(
    Future<T> Function() operation, {
    String? operationName,
    bool useCache = false,
    String? cacheKey,
    Duration cacheDuration = const Duration(minutes: 5),
    bool useRetry = true,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    bool Function(dynamic error)? shouldRetry,
    T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic> Function(T)? toJson,
    Map<String, dynamic>? metadata,
  }) async {
    state = const AsyncValue.loading();

    Result<T> result;

    if (useCache && cacheKey != null && useRetry) {
      result = await executeWithRetryAndCache(
        operation,
        cacheKey: cacheKey,
        operationName: operationName,
        cacheDuration: cacheDuration,
        maxRetries: maxRetries,
        initialDelay: initialDelay,
        shouldRetry: shouldRetry,
        fromJson: fromJson,
        toJson: toJson,
        metadata: metadata,
      );
    } else if (useCache && cacheKey != null) {
      result = await executeWithCache(
        operation,
        cacheKey: cacheKey,
        cacheDuration: cacheDuration,
        operationName: operationName,
        fromJson: fromJson,
        toJson: toJson,
      );
    } else if (useRetry) {
      result = await executeWithRetry(
        operation,
        operationName: operationName,
        maxRetries: maxRetries,
        initialDelay: initialDelay,
        shouldRetry: shouldRetry,
        metadata: metadata,
      );
    } else {
      // تنفيذ عادي بدون recovery
      try {
        final data = await operation();
        result = Result.success(data);
      } catch (error, stackTrace) {
        result = Result.failure(
          AppErrorFactory.fromError(error, stackTrace: stackTrace),
        );
      }
    }

    // تحديث الحالة بناءً على النتيجة
    result.when(
      success: (data) => state = AsyncValue.data(data),
      failure: (error) => state = AsyncValue.error(error, StackTrace.current),
    );
  }
}

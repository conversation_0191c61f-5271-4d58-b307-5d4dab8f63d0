/// ============================================================================
/// ADVANCED LOADING STATES - Enhanced Loading and Progress System
/// ============================================================================
/// 
/// Advanced loading states with progress indicators and user feedback
/// Task 21: Enhance error handling and user feedback - Loading states
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Enhanced loading state types
enum LoadingStateType {
  idle,
  loading,
  success,
  error,
  retrying,
  offline,
}

/// Advanced loading state with progress and context
class AdvancedLoadingState {
  const AdvancedLoadingState({
    this.type = LoadingStateType.idle,
    this.progress = 0.0,
    this.message,
    this.operation,
    this.startTime,
    this.estimatedDuration,
    this.canCancel = false,
    this.retryCount = 0,
    this.maxRetries = 3,
    this.error,
  });

  final LoadingStateType type;
  final double progress; // 0.0 to 1.0
  final String? message;
  final String? operation;
  final DateTime? startTime;
  final Duration? estimatedDuration;
  final bool canCancel;
  final int retryCount;
  final int maxRetries;
  final Object? error;

  /// Check if currently loading
  bool get isLoading => type == LoadingStateType.loading || type == LoadingStateType.retrying;

  /// Check if can retry
  bool get canRetry => type == LoadingStateType.error && retryCount < maxRetries;

  /// Get elapsed time
  Duration? get elapsedTime {
    if (startTime == null) return null;
    return DateTime.now().difference(startTime!);
  }

  /// Get estimated remaining time
  Duration? get estimatedRemainingTime {
    if (estimatedDuration == null || progress <= 0) return null;
    final elapsed = elapsedTime;
    if (elapsed == null) return null;
    
    final totalEstimated = Duration(
      milliseconds: (elapsed.inMilliseconds / progress).round(),
    );
    return totalEstimated - elapsed;
  }

  /// Create copy with new values
  AdvancedLoadingState copyWith({
    LoadingStateType? type,
    double? progress,
    String? message,
    String? operation,
    DateTime? startTime,
    Duration? estimatedDuration,
    bool? canCancel,
    int? retryCount,
    int? maxRetries,
    Object? error,
  }) {
    return AdvancedLoadingState(
      type: type ?? this.type,
      progress: progress ?? this.progress,
      message: message ?? this.message,
      operation: operation ?? this.operation,
      startTime: startTime ?? this.startTime,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      canCancel: canCancel ?? this.canCancel,
      retryCount: retryCount ?? this.retryCount,
      maxRetries: maxRetries ?? this.maxRetries,
      error: error ?? this.error,
    );
  }

  /// Create loading state
  factory AdvancedLoadingState.loading({
    String? message,
    String? operation,
    Duration? estimatedDuration,
    bool canCancel = false,
  }) {
    return AdvancedLoadingState(
      type: LoadingStateType.loading,
      message: message,
      operation: operation,
      startTime: DateTime.now(),
      estimatedDuration: estimatedDuration,
      canCancel: canCancel,
    );
  }

  /// Create retrying state
  factory AdvancedLoadingState.retrying({
    required int retryCount,
    String? message,
    String? operation,
    Object? error,
    int maxRetries = 3,
  }) {
    return AdvancedLoadingState(
      type: LoadingStateType.retrying,
      message: message,
      operation: operation,
      startTime: DateTime.now(),
      retryCount: retryCount,
      maxRetries: maxRetries,
      error: error,
    );
  }

  /// Create success state
  factory AdvancedLoadingState.success({
    String? message,
    String? operation,
  }) {
    return AdvancedLoadingState(
      type: LoadingStateType.success,
      message: message,
      operation: operation,
      progress: 1.0,
    );
  }

  /// Create error state
  factory AdvancedLoadingState.error({
    required Object error,
    String? message,
    String? operation,
    int retryCount = 0,
    int maxRetries = 3,
  }) {
    return AdvancedLoadingState(
      type: LoadingStateType.error,
      message: message,
      operation: operation,
      retryCount: retryCount,
      maxRetries: maxRetries,
      error: error,
    );
  }

  /// Create offline state
  factory AdvancedLoadingState.offline({
    String? message,
    String? operation,
  }) {
    return AdvancedLoadingState(
      type: LoadingStateType.offline,
      message: message,
      operation: operation,
    );
  }

  @override
  String toString() {
    return 'AdvancedLoadingState(type: $type, progress: $progress, message: $message)';
  }
}

/// Advanced loading state notifier
class AdvancedLoadingNotifier extends StateNotifier<AdvancedLoadingState> {
  AdvancedLoadingNotifier() : super(const AdvancedLoadingState());

  /// Start loading operation
  void startLoading({
    String? message,
    String? operation,
    Duration? estimatedDuration,
    bool canCancel = false,
  }) {
    state = AdvancedLoadingState.loading(
      message: message,
      operation: operation,
      estimatedDuration: estimatedDuration,
      canCancel: canCancel,
    );
  }

  /// Update progress
  void updateProgress(double progress, {String? message}) {
    if (!state.isLoading) return;
    
    state = state.copyWith(
      progress: progress.clamp(0.0, 1.0),
      message: message ?? state.message,
    );
  }

  /// Complete loading with success
  void completeSuccess({String? message}) {
    state = AdvancedLoadingState.success(
      message: message,
      operation: state.operation,
    );
  }

  /// Complete loading with error
  void completeError(Object error, {String? message}) {
    state = AdvancedLoadingState.error(
      error: error,
      message: message,
      operation: state.operation,
      retryCount: state.retryCount,
      maxRetries: state.maxRetries,
    );
  }

  /// Start retry attempt
  void startRetry({String? message}) {
    state = AdvancedLoadingState.retrying(
      retryCount: state.retryCount + 1,
      message: message,
      operation: state.operation,
      error: state.error,
      maxRetries: state.maxRetries,
    );
  }

  /// Set offline state
  void setOffline({String? message}) {
    state = AdvancedLoadingState.offline(
      message: message,
      operation: state.operation,
    );
  }

  /// Reset to idle
  void reset() {
    state = const AdvancedLoadingState();
  }

  /// Cancel current operation
  void cancel() {
    if (state.canCancel) {
      state = const AdvancedLoadingState();
    }
  }
}

/// Provider for advanced loading state
final advancedLoadingProvider = StateNotifierProvider.autoDispose<AdvancedLoadingNotifier, AdvancedLoadingState>((ref) {
  return AdvancedLoadingNotifier();
});

/// Advanced loading indicator widget
class AdvancedLoadingIndicator extends ConsumerWidget {
  const AdvancedLoadingIndicator({
    super.key,
    this.size = 40,
    this.strokeWidth = 4,
    this.showProgress = true,
    this.showMessage = true,
    this.showElapsedTime = false,
    this.showEstimatedTime = false,
    this.variant = LoadingIndicatorVariant.circular,
  });

  final double size;
  final double strokeWidth;
  final bool showProgress;
  final bool showMessage;
  final bool showElapsedTime;
  final bool showEstimatedTime;
  final LoadingIndicatorVariant variant;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loadingState = ref.watch(advancedLoadingProvider);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (!loadingState.isLoading) {
      return const SizedBox.shrink();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Loading indicator
        _buildLoadingIndicator(colorScheme, loadingState),
        
        if (showMessage && loadingState.message != null) ...[
          const SizedBox(height: 12),
          Text(
            loadingState.message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
        
        if (showProgress && loadingState.progress > 0) ...[
          const SizedBox(height: 8),
          Text(
            '${(loadingState.progress * 100).round()}%',
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
        
        if (showElapsedTime && loadingState.elapsedTime != null) ...[
          const SizedBox(height: 4),
          Text(
            'وقت منقضي: ${_formatDuration(loadingState.elapsedTime!)}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
        
        if (showEstimatedTime && loadingState.estimatedRemainingTime != null) ...[
          const SizedBox(height: 4),
          Text(
            'وقت متبقي: ${_formatDuration(loadingState.estimatedRemainingTime!)}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildLoadingIndicator(ColorScheme colorScheme, AdvancedLoadingState state) {
    switch (variant) {
      case LoadingIndicatorVariant.circular:
        return SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            value: showProgress && state.progress > 0 ? state.progress : null,
            strokeWidth: strokeWidth,
            valueColor: AlwaysStoppedAnimation<Color>(
              state.type == LoadingStateType.retrying 
                  ? Colors.orange 
                  : colorScheme.primary,
            ),
          ),
        );
        
      case LoadingIndicatorVariant.linear:
        return SizedBox(
          width: size * 3,
          child: LinearProgressIndicator(
            value: showProgress && state.progress > 0 ? state.progress : null,
            valueColor: AlwaysStoppedAnimation<Color>(
              state.type == LoadingStateType.retrying 
                  ? Colors.orange 
                  : colorScheme.primary,
            ),
            backgroundColor: colorScheme.surfaceContainerHighest,
          ),
        );
        
      case LoadingIndicatorVariant.dots:
        return _buildDotsIndicator(colorScheme, state);
    }
  }

  Widget _buildDotsIndicator(ColorScheme colorScheme, AdvancedLoadingState state) {
    return SizedBox(
      width: size,
      height: size / 4,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(3, (index) {
          return AnimatedContainer(
            duration: Duration(milliseconds: 600 + (index * 200)),
            width: size / 8,
            height: size / 8,
            decoration: BoxDecoration(
              color: state.type == LoadingStateType.retrying 
                  ? Colors.orange 
                  : colorScheme.primary,
              shape: BoxShape.circle,
            ),
          );
        }),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    
    if (minutes > 0) {
      return '${minutes}د ${seconds}ث';
    } else {
      return '${seconds}ث';
    }
  }
}

/// Loading indicator variants
enum LoadingIndicatorVariant {
  circular,
  linear,
  dots,
}

/// Advanced loading overlay
class AdvancedLoadingOverlay extends ConsumerWidget {
  const AdvancedLoadingOverlay({
    super.key,
    required this.child,
    this.showOverlay = true,
    this.overlayColor,
    this.canDismiss = false,
  });

  final Widget child;
  final bool showOverlay;
  final Color? overlayColor;
  final bool canDismiss;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loadingState = ref.watch(advancedLoadingProvider);
    final colorScheme = Theme.of(context).colorScheme;

    return Stack(
      children: [
        child,
        
        if (showOverlay && loadingState.isLoading)
          Positioned.fill(
            child: Container(
              color: overlayColor ?? colorScheme.surface.withOpacity(0.8),
              child: Center(
                child: Card(
                  elevation: 8,
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const AdvancedLoadingIndicator(
                          showMessage: true,
                          showProgress: true,
                          showElapsedTime: true,
                        ),
                        
                        if (loadingState.canCancel) ...[
                          const SizedBox(height: 16),
                          TextButton(
                            onPressed: () {
                              ref.read(advancedLoadingProvider.notifier).cancel();
                            },
                            child: const Text('إلغاء'),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// Loading button with advanced states
class AdvancedLoadingButton extends ConsumerWidget {
  const AdvancedLoadingButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.icon,
    this.width,
    this.height = 56,
    this.style,
    this.showProgress = true,
  });

  final VoidCallback? onPressed;
  final String text;
  final Widget? icon;
  final double? width;
  final double height;
  final ButtonStyle? style;
  final bool showProgress;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loadingState = ref.watch(advancedLoadingProvider);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final isLoading = loadingState.isLoading;
    final isEnabled = !isLoading && onPressed != null;

    return SizedBox(
      width: width ?? double.infinity,
      height: height,
      child: ElevatedButton(
        onPressed: isEnabled ? onPressed : null,
        style: style ?? ElevatedButton.styleFrom(
          backgroundColor: _getButtonColor(colorScheme, loadingState.type),
          foregroundColor: colorScheme.onPrimary,
          disabledBackgroundColor: colorScheme.surfaceContainerHighest,
          disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        ),
        child: _buildButtonContent(theme, colorScheme, loadingState),
      ),
    );
  }

  Widget _buildButtonContent(ThemeData theme, ColorScheme colorScheme, AdvancedLoadingState state) {
    if (state.isLoading) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              value: showProgress && state.progress > 0 ? state.progress : null,
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.onPrimary),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            _getLoadingText(state.type),
            style: theme.textTheme.labelLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    if (icon != null) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          const SizedBox(width: 8),
          Text(
            text,
            style: theme.textTheme.labelLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: theme.textTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Color _getButtonColor(ColorScheme colorScheme, LoadingStateType type) {
    switch (type) {
      case LoadingStateType.retrying:
        return Colors.orange;
      case LoadingStateType.error:
        return colorScheme.error;
      case LoadingStateType.success:
        return Colors.green;
      case LoadingStateType.offline:
        return Colors.grey;
      default:
        return colorScheme.primary;
    }
  }

  String _getLoadingText(LoadingStateType type) {
    switch (type) {
      case LoadingStateType.loading:
        return 'جاري المعالجة...';
      case LoadingStateType.retrying:
        return 'إعادة المحاولة...';
      default:
        return 'جاري المعالجة...';
    }
  }
}

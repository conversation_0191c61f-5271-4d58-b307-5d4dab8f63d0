// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_bottom_navigation.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bottomNavigationIndexHash() =>
    r'1fd9aab44ff7fe01707b6036fa4a350dd3469b22';

/// Provider لإدارة حالة التنقل السفلي
///
/// Copied from [BottomNavigationIndex].
@ProviderFor(BottomNavigationIndex)
final bottomNavigationIndexProvider =
    AutoDisposeNotifierProvider<BottomNavigationIndex, int>.internal(
      BottomNavigationIndex.new,
      name: r'bottomNavigationIndexProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bottomNavigationIndexHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BottomNavigationIndex = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:go_router/go_router.dart';
import 'unified_navigation_system.dart';
import '../../features/cart/providers/cart_provider.dart';
import '../../features/notifications/services/notification_service.dart';

part 'unified_bottom_navigation.g.dart';

/// شريط التنقل السفلي الموحد لتطبيق CarNow
///
/// يوفر هذا المكون:
/// - تنقل سريع بين الأقسام الرئيسية
/// - مؤشرات بصرية للحالة النشطة
/// - دعم للشارات (badges) للإشعارات
/// - تصميم متسق مع هوية التطبيق
class UnifiedBottomNavigation extends ConsumerStatefulWidget {
  const UnifiedBottomNavigation({super.key, required this.child});

  final Widget child;

  @override
  ConsumerState<UnifiedBottomNavigation> createState() =>
      _UnifiedBottomNavigationState();
}

class _UnifiedBottomNavigationState
    extends ConsumerState<UnifiedBottomNavigation> {
  late int _selectedIndex;

  /// قائمة عناصر التنقل الرئيسية
  final List<BottomNavigationItem> _navigationItems = [
    const BottomNavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'الرئيسية',
      route: UnifiedNavigationSystem.home,
    ),
    const BottomNavigationItem(
      icon: Icons.search_outlined,
      activeIcon: Icons.search,
      label: 'البحث',
      route: UnifiedNavigationSystem.search,
    ),
    const BottomNavigationItem(
      icon: Icons.garage_outlined,
      activeIcon: Icons.garage,
      label: 'المرآب',
      route: UnifiedNavigationSystem.garage,
    ),
    const BottomNavigationItem(
      icon: Icons.shopping_cart_outlined,
      activeIcon: Icons.shopping_cart,
      label: 'السلة',
      route: UnifiedNavigationSystem.cart,
      showBadge: true, // سيعرض شارة عدد العناصر
    ),
    const BottomNavigationItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'الحساب',
      route: UnifiedNavigationSystem.profile,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _selectedIndex = _getCurrentIndex();
  }

  /// تحديد الفهرس الحالي بناءً على الرابط المُفعل
  int _getCurrentIndex() {
    final location = GoRouterState.of(context).uri.path;

    for (int i = 0; i < _navigationItems.length; i++) {
      final item = _navigationItems[i];
      if (location.startsWith(item.route) ||
          (item.route == UnifiedNavigationSystem.home && location == '/')) {
        return i;
      }
    }

    return 0; // افتراضي للصفحة الرئيسية
  }

  /// التعامل مع ضغطة عنصر التنقل
  void _onItemTapped(int index) {
    if (_selectedIndex == index) {
      // إذا كان العنصر نشط بالفعل، التمرير إلى الأعلى أو تحديث
      _handleDoubleTab(index);
      return;
    }

    setState(() {
      _selectedIndex = index;
    });

    final item = _navigationItems[index];
    context.go(item.route);

    // تحديث حالة التنقل في Provider
    ref.read(navigationStateProvider.notifier).setRoute(item.route);
  }

  /// التعامل مع الضغط المزدوج على نفس العنصر
  void _handleDoubleTab(int index) {
    final item = _navigationItems[index];

    switch (item.route) {
      case UnifiedNavigationSystem.home:
        // التمرير إلى الأعلى في الصفحة الرئيسية
        _scrollToTop();
        break;
      case UnifiedNavigationSystem.search:
        // مسح نتائج البحث والعودة للبحث الفارغ
        context.go(UnifiedNavigationSystem.search);
        break;
      case UnifiedNavigationSystem.garage:
        // تحديث قائمة السيارات
        _refreshCurrentPage();
        break;
      default:
        _scrollToTop();
    }
  }

  /// التمرير إلى أعلى الصفحة الحالية
  void _scrollToTop() => Scrollable.maybeOf(context)?.position.animateTo(
    0,
    duration: const Duration(milliseconds: 500),
    curve: Curves.easeInOut,
  );

  /// تحديث الصفحة الحالية
  void _refreshCurrentPage() {
    // يمكن إضافة منطق التحديث هنا
    // مثل استدعاء Provider للتحديث
  }

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;

    return Scaffold(
      body: widget.child,
      bottomNavigationBar: Material(
        elevation: 3,
        color: cs.surface,
        child: NavigationBar(
          backgroundColor: cs.surface,
          surfaceTintColor: cs.surfaceTint,
          indicatorColor: cs.secondaryContainer,
          labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
          selectedIndex: _selectedIndex,
          onDestinationSelected: _onItemTapped,
          destinations: _navigationItems
              .map((item) => _buildNavigationDestination(item, cs))
              .toList(),
        ),
      ),
    );
  }

  NavigationDestination _buildNavigationDestination(
    BottomNavigationItem item,
    ColorScheme cs,
  ) {
    final iconWidget = Icon(item.icon);
    final selectedIconWidget = Icon(item.activeIcon, color: cs.primary);

    Widget finalIcon = iconWidget;
    Widget finalSelectedIcon = selectedIconWidget;

    if (item.showBadge) {
      finalIcon = _buildBadgedIcon(iconWidget, item);
      finalSelectedIcon = _buildBadgedIcon(selectedIconWidget, item);
    }

    return NavigationDestination(
      icon: finalIcon,
      selectedIcon: finalSelectedIcon,
      label: item.label,
    );
  }

  /// بناء أيقونة التنقل مع دعم الشارات
  Widget _buildBadgedIcon(Widget iconWidget, BottomNavigationItem item) {
    return Consumer(
      builder: (context, ref, child) {
        // هنا يمكن ربط البيانات الفعلية للشارات
        // مثل عدد عناصر السلة أو الإشعارات غير المقروءة
        final badgeCount = _getBadgeCount(item);

        if (badgeCount == 0) {
          return iconWidget;
        }

        return Badge(
          label: Text(
            badgeCount > 99 ? '99+' : badgeCount.toString(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Colors.red,
          child: iconWidget,
        );
      },
    );
  }

  /// الحصول على عدد الشارة للعنصر المحدد
  int _getBadgeCount(BottomNavigationItem item) {
    switch (item.route) {
      case UnifiedNavigationSystem.cart:
        // إرجاع عدد العناصر في السلة
        final cartAsync = ref.watch(cartProvider);
        return cartAsync.maybeWhen(
          data: (items) => items.length,
          orElse: () => 0,
        );
      case UnifiedNavigationSystem.profile:
        // إرجاع عدد الإشعارات غير المقروءة
        final unreadAsync = ref.watch(unreadNotificationCountProvider);
        return unreadAsync.maybeWhen(data: (count) => count, orElse: () => 0);
      default:
        return 0;
    }
  }
}

/// نموذج لعنصر التنقل السفلي
class BottomNavigationItem {
  const BottomNavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.route,
    this.showBadge = false,
  });

  final IconData icon;
  final IconData activeIcon;
  final String label;
  final String route;
  final bool showBadge;
}

/// Provider لإدارة حالة التنقل السفلي
@riverpod
class BottomNavigationIndex extends _$BottomNavigationIndex {
  @override
  int build() {
    return 0;
  }

  void setIndex(int index) {
    state = index;
  }

  void reset() {
    state = 0;
  }
}

/// Extension لإضافة أيقونات مخصصة للمرآب
extension CustomIcons on Icons {
  static const IconData garageOutlined = Icons.directions_car_outlined;
  static const IconData garage = Icons.directions_car;
}

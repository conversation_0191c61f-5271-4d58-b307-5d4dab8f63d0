import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// Import all the existing screens that need routes
import '../../../features/history/screens/history_screen.dart';
import '../../../features/favorites/screens/favorites_screen.dart';
import '../../../features/cart/screens/cart_screen.dart';
import '../../../features/notifications/screens/notifications_screen.dart';
import '../../../features/auction/screens/auction_list_screen.dart';
import '../../../features/auction/screens/auction_detail_screen.dart';

/// طرق للشاشات الموجودة التي كانت مفقودة من الراوتر
final missingRoutes = [
  // طرق التاريخ والمحفوظات
  GoRoute(
    path: '/history/viewed',
    builder: (context, state) => const HistoryScreen(),
  ),
  GoRoute(
    path: '/history/purchases',
    builder: (context, state) =>
        const HistoryScreen(), // يمكن إنشاء شاشة منفصلة للمشتريات
  ),

  // طرق المفضلة والمحفوظات
  GoRoute(
    path: '/favorites',
    builder: (context, state) => const FavoritesScreen(),
  ),
  GoRoute(
    path: '/saved',
    builder: (context, state) => const FavoritesScreen(), // نفس شاشة المفضلة
  ),

  // طريق سلة التسوق
  GoRoute(path: '/cart', builder: (context, state) => const CartScreen()),

  // طريق الإشعارات
  GoRoute(
    path: '/notifications',
    builder: (context, state) => const NotificationsScreen(),
  ),

  // طرق المزادات
  GoRoute(
    path: '/auctions',
    builder: (context, state) => const AuctionListScreen(),
  ),
  GoRoute(
    path: '/auction/:id',
    builder: (context, state) {
      final auctionId = state.pathParameters['id']!;
      return AuctionDetailScreen(partId: auctionId);
    },
  ),

  // طرق المنتجات والفئات
  GoRoute(
    path: '/product/:id',
    builder: (context, state) {
      final productId = state.pathParameters['id']!;
      return ProductDetailsScreen(productId: productId);
    },
  ),
  GoRoute(
    path: '/products/:id',
    builder: (context, state) {
      final productId = state.pathParameters['id']!;
      return ProductDetailsScreen(productId: productId);
    },
  ),
  GoRoute(
    path: '/category/:name',
    builder: (context, state) {
      final categoryName = state.pathParameters['name']!;
      return CategoryProductsScreen(categoryName: categoryName);
    },
  ),
  GoRoute(
    path: '/categories/:name',
    builder: (context, state) {
      final categoryName = state.pathParameters['name']!;
      return CategoryProductsScreen(categoryName: categoryName);
    },
  ),

  // طرق البحث
  GoRoute(
    path: '/search/parts',
    builder: (context, state) {
      final searchQuery = state.uri.queryParameters['q'] ?? '';
      return SearchPartsScreen(initialQuery: searchQuery);
    },
  ),

  // طرق السيارات والجراج
  GoRoute(path: '/add-car', builder: (context, state) => const AddCarScreen()),
  GoRoute(
    path: '/garage/add-vehicle',
    builder: (context, state) => const AddVehicleScreen(),
  ),

  // طرق الدعم
  GoRoute(
    path: '/support/tickets/:id',
    builder: (context, state) {
      final ticketId = state.pathParameters['id']!;
      return SupportTicketDetailsScreen(ticketId: ticketId);
    },
  ),

  // طريق "حول التطبيق"
  GoRoute(
    path: '/about',
    builder: (context, state) => const AboutScreen(),
  ),

  // طريق "الشروط والأحكام"
  GoRoute(
    path: '/terms',
    builder: (context, state) => const TermsScreen(),
  ),

  // طريق "سياسة الخصوصية"
  GoRoute(
    path: '/privacy',
    builder: (context, state) => const PrivacyScreen(),
  ),

  // طريق المحادثات
  GoRoute(
    path: '/conversations/:conversationId/details',
    builder: (context, state) {
      final conversationId = state.pathParameters['conversationId']!;
      return ConversationDetailsScreen(conversationId: conversationId);
    },
  ),
  GoRoute(
    path: '/users/:userId/profile',
    builder: (context, state) {
      final userId = state.pathParameters['userId']!;
      return UserProfileScreen(userId: userId);
    },
  ),

  // طرق البائع المتقدمة - تم نقلها إلى seller_routes.dart
  GoRoute(
    path: '/seller/products/:id',
    builder: (context, state) {
      final productId = state.pathParameters['id']!;
      return SellerProductDetailsScreen(productId: productId);
    },
  ),
  GoRoute(
    path: '/seller/products/:id/edit',
    builder: (context, state) {
      final productId = state.pathParameters['id']!;
      return SellerProductFormScreen(productId: productId);
    },
  ),
  GoRoute(
    path: '/seller/products/manage',
    builder: (context, state) => const ManageProductsScreen(),
  ),
  GoRoute(
    path: '/seller/orders/manage',
    builder: (context, state) => const SellerOrdersManagementScreen(),
  ),
  GoRoute(
    path: '/seller/orders/:id',
    builder: (context, state) {
      final orderId = state.pathParameters['id']!;
      return SellerOrderDetailsScreen(orderId: orderId);
    },
  ),
  GoRoute(
    path: '/seller/offers/create',
    builder: (context, state) => const CreateOfferScreen(),
  ),
  GoRoute(
    path: '/seller/reports',
    builder: (context, state) => const SellerReportsScreen(),
  ),
];

// شاشات placeholder للطرق التي لا تحتوي على شاشات جاهزة
class ProductDetailsScreen extends StatelessWidget {
  const ProductDetailsScreen({super.key, required this.productId});
  final String productId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تفاصيل المنتج')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.shopping_bag, size: 64),
            const SizedBox(height: 16),
            Text('تفاصيل المنتج: $productId'),
            const SizedBox(height: 8),
            const Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class CategoryProductsScreen extends StatelessWidget {
  const CategoryProductsScreen({super.key, required this.categoryName});
  final String categoryName;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('فئة: $categoryName')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.category, size: 64),
            const SizedBox(height: 16),
            Text('منتجات فئة: $categoryName'),
            const SizedBox(height: 8),
            const Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class SearchPartsScreen extends StatelessWidget {
  const SearchPartsScreen({super.key, required this.initialQuery});
  final String initialQuery;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('البحث في قطع الغيار')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.search, size: 64),
            const SizedBox(height: 16),
            Text('البحث عن: $initialQuery'),
            const SizedBox(height: 8),
            const Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class AddCarScreen extends StatelessWidget {
  const AddCarScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إضافة سيارة')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.directions_car, size: 64),
            SizedBox(height: 16),
            Text('إضافة سيارة جديدة'),
            SizedBox(height: 8),
            Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class AddVehicleScreen extends StatelessWidget {
  const AddVehicleScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إضافة مركبة للجراج')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.garage, size: 64),
            SizedBox(height: 16),
            Text('إضافة مركبة للجراج'),
            SizedBox(height: 8),
            Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class SupportTicketDetailsScreen extends StatelessWidget {
  const SupportTicketDetailsScreen({super.key, required this.ticketId});
  final String ticketId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تفاصيل التذكرة')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.support_agent, size: 64),
            const SizedBox(height: 16),
            Text('تذكرة دعم: $ticketId'),
            const SizedBox(height: 8),
            const Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class ConversationDetailsScreen extends StatelessWidget {
  const ConversationDetailsScreen({super.key, required this.conversationId});
  final String conversationId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تفاصيل المحادثة')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.chat, size: 64),
            const SizedBox(height: 16),
            Text('محادثة: $conversationId'),
            const SizedBox(height: 8),
            const Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class UserProfileScreen extends StatelessWidget {
  const UserProfileScreen({super.key, required this.userId});
  final String userId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الملف الشخصي')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.person, size: 64),
            const SizedBox(height: 16),
            Text('ملف المستخدم: $userId'),
            const SizedBox(height: 8),
            const Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

// شاشات البائع
class SellerProductFormScreen extends StatelessWidget {
  const SellerProductFormScreen({super.key, this.productId});
  final String? productId;

  @override
  Widget build(BuildContext context) {
    final isEditing = productId != null;
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'تعديل المنتج' : 'إضافة منتج جديد'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(isEditing ? Icons.edit : Icons.add_shopping_cart, size: 64),
            const SizedBox(height: 16),
            Text(isEditing ? 'تعديل المنتج: $productId' : 'إضافة منتج جديد'),
            const SizedBox(height: 8),
            const Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class SellerProductDetailsScreen extends StatelessWidget {
  const SellerProductDetailsScreen({super.key, required this.productId});
  final String productId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تفاصيل المنتج')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.inventory, size: 64),
            const SizedBox(height: 16),
            Text('منتج البائع: $productId'),
            const SizedBox(height: 8),
            const Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class ManageProductsScreen extends StatelessWidget {
  const ManageProductsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إدارة المنتجات')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2, size: 64),
            SizedBox(height: 16),
            Text('إدارة منتجات البائع'),
            SizedBox(height: 8),
            Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class SellerOrdersManagementScreen extends StatelessWidget {
  const SellerOrdersManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إدارة الطلبات')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assignment, size: 64),
            SizedBox(height: 16),
            Text('إدارة طلبات البائع'),
            SizedBox(height: 8),
            Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class SellerOrderDetailsScreen extends StatelessWidget {
  const SellerOrderDetailsScreen({super.key, required this.orderId});
  final String orderId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تفاصيل الطلب')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.receipt_long, size: 64),
            const SizedBox(height: 16),
            Text('طلب البائع: $orderId'),
            const SizedBox(height: 8),
            const Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class CreateOfferScreen extends StatelessWidget {
  const CreateOfferScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إنشاء عرض')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.local_offer, size: 64),
            SizedBox(height: 16),
            Text('إنشاء عرض جديد'),
            SizedBox(height: 8),
            Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class SellerReportsScreen extends StatelessWidget {
  const SellerReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تقارير البائع')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.analytics, size: 64),
            SizedBox(height: 16),
            Text('تقارير ومبيعات البائع'),
            SizedBox(height: 8),
            Text('هذه الشاشة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

// شاشات الإعدادات والمعلومات
class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('حول التطبيق')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info, size: 64),
            SizedBox(height: 16),
            Text('حول تطبيق CarNow'),
            SizedBox(height: 8),
            Text('منصة شراء وبيع قطع غيار السيارات في ليبيا'),
            SizedBox(height: 16),
            Text('الإصدار 1.0.0'),
          ],
        ),
      ),
    );
  }
}

class TermsScreen extends StatelessWidget {
  const TermsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الشروط والأحكام')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.description, size: 64),
            SizedBox(height: 16),
            Text('الشروط والأحكام'),
            SizedBox(height: 8),
            Text('شروط استخدام تطبيق CarNow'),
            SizedBox(height: 8),
            Text('هذه الصفحة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

class PrivacyScreen extends StatelessWidget {
  const PrivacyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('سياسة الخصوصية')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.privacy_tip, size: 64),
            SizedBox(height: 16),
            Text('سياسة الخصوصية'),
            SizedBox(height: 8),
            Text('كيف نحمي بياناتك الشخصية'),
            SizedBox(height: 8),
            Text('هذه الصفحة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../features/seller/screens/subscription_request_screen.dart';
import '../../../features/settings/screens/developer_tools_screen.dart';
import '../../../features/admin_tools/screens/admin_subscription_requests_screen.dart';
import '../../../features/admin_tools/screens/admin_financial_dashboard_screen.dart';
import '../../../features/inventory/screens/inventory_management_screen.dart';
import '../../../features/analytics/screens/analytics_dashboard_screen.dart';
import '../../../features/reports/screens/sales_reports_screen.dart';

/// Developer routes configuration following Forever Plan Architecture
/// All routes follow Flutter (UI Only) → Go API → Supabase (Data Only)
List<RouteBase> developerRoutes = [
  GoRoute(
    path: '/developer-tools',
    builder: (context, state) => const DeveloperToolsScreen(),
  ),
  
  // Analytics and Reports
  GoRoute(
    path: '/dev/analytics',
    builder: (context, state) => const AnalyticsDashboardScreen(),
  ),
  GoRoute(
    path: '/dev/sales-reports',
    builder: (context, state) => const SalesReportsScreen(),
  ),
  
  // Inventory and Management
  GoRoute(
    path: '/dev/inventory',
    builder: (context, state) => const InventoryManagementScreen(),
  ),
  GoRoute(
    path: '/dev/advanced-dashboard',
    builder: (context, state) => const AdminFinancialDashboardScreen(),
  ),
  
  // Admin Tools
  GoRoute(
    path: '/dev/admin-subscription-requests',
    builder: (context, state) => const AdminSubscriptionRequestsScreen(),
  ),
  
  // Subscription Management (screens defined in developer_tools_screen.dart)
  GoRoute(
    path: '/dev/subscription-discount',
    builder: (context, state) => const SubscriptionDiscountSettingsScreen(),
  ),
  GoRoute(
    path: '/dev/subscription-pricing',
    builder: (context, state) => const SubscriptionPricingManagementScreen(),
  ),
  
  // Store and Warranty Management
  GoRoute(
    path: '/dev/stores-listing',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('قائمة المتاجر')),
      body: const Center(
        child: Text('شاشة قائمة المتاجر - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  GoRoute(
    path: '/dev/warranty',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('قائمة الضمانات')),
      body: const Center(
        child: Text('شاشة قائمة الضمانات - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  
  // Chat Management
  GoRoute(
    path: '/dev/archived-chats',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('تدوين المحادثات')),
      body: const Center(
        child: Text('شاشة تدوين المحادثات - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  GoRoute(
    path: '/dev/chat-storage',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('إدارة أسعار البيانات والخصوص')),
      body: const Center(
        child: Text('شاشة إدارة أسعار البيانات والخصوص - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  GoRoute(
    path: '/dev/conversations',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('المحادثات')),
      body: const Center(
        child: Text('شاشة المحادثات - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  
  // Search and Parts
  GoRoute(
    path: '/dev/enhanced-search',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('اختبار القطع المتوافقة')),
      body: const Center(
        child: Text('شاشة اختبار القطع المتوافقة - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  GoRoute(
    path: '/dev/compatible-parts',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('إعدادات خدمة الاشتراكات السنوي')),
      body: const Center(
        child: Text('شاشة إعدادات خدمة الاشتراكات السنوي - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  
  // Developer Tools
  GoRoute(
    path: '/dev/database-tools',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('أدوات قاعدة البيانات')),
      body: const Center(
        child: Text('أدوات قاعدة البيانات - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  GoRoute(
    path: '/dev/performance-test',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('اختبار الأداء')),
      body: const Center(
        child: Text('شاشة اختبار الأداء - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  GoRoute(
    path: '/dev/error-simulator',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('محاكي الأخطاء')),
      body: const Center(
        child: Text('شاشة محاكي الأخطاء - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  GoRoute(
    path: '/dev/debugging-tools',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('أدوات التصحيح')),
      body: const Center(
        child: Text('شاشة أدوات التصحيح - قيد التطوير\nسيتم ربطها بـ Go API حسب Forever Plan Architecture'),
      ),
    ),
  ),
  
  // Legacy routes for backward compatibility
  GoRoute(
    path: '/developer/system-logs',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('سجل النظام')),
      body: const Center(
        child: Text('شاشة سجل النظام - قيد التطوير'),
      ),
    ),
  ),
  GoRoute(
    path: '/developer/testing-tools',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('أدوات الاختبار')),
      body: const Center(
        child: Text('شاشة أدوات الاختبار - قيد التطوير'),
      ),
    ),
  ),
  GoRoute(
    path: '/developer/auth-test',
    builder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Auth Test')),
      body: const Center(
        child: Text('Developer Auth Test Screen'),
      ),
    ),
  ),
  GoRoute(
    path: '/developer/subscription-requests',
    builder: (context, state) => const SubscriptionRequestScreen(),
  ),
];

// Example of a simple developer menu that could be shown on the DeveloperToolsScreen
class DeveloperMenu extends StatelessWidget {
  const DeveloperMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        ListTile(
          title: const Text('Storybook'),
          onTap: () => context.push('/developer/storybook'),
        ),
        ListTile(
          title: const Text('Logs'),
          onTap: () => context.push('/developer/logs'),
        ),
        ListTile(
          title: const Text('Feature Flags'),
          onTap: () => context.push('/developer/feature-flags'),
        ),
      ],
    );
  }
}

import 'package:go_router/go_router.dart';

import '../../../shared/screens/error_screen.dart';
import '../../../shared/screens/main_screen.dart';
import '../../../features/auth/screens/splash_screen.dart';

// Re-exporting the routes directly for cleaner imports
final List<GoRoute> mainRoutes = buildMainRoutes();

class RouteNames {
  static const String splash = 'splash';
  static const String main = 'main';
  static const String error = 'error';
}

List<GoRoute> buildMainRoutes() {
  return [
    GoRoute(
      path: '/',
      name: RouteNames.splash,
      builder: (_, _) => const SplashScreen(),
    ),
    GoRoute(
      path: '/main',
      name: RouteNames.main,
      builder: (_, _) => const MainScreen(),
      // Removed circular references to other route collections
      // They will be added by the parent router
    ),
    GoRoute(
      path: '/error',
      name: RouteNames.error,
      builder: (_, state) {
        final Map<String, dynamic> params =
            state.extra as Map<String, dynamic>? ?? {};
        return ErrorScreen(
          title: params['title'] as String? ?? 'Error',
          message: params['message'] as String? ?? 'An error occurred.',
        );
      },
    ),
  ];
}

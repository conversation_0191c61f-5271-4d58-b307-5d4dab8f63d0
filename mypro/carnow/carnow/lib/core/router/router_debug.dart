// =============================================================================
// Router Debug Helper
// =============================================================================
//
// ملف مساعد لتشخيص مشاكل الروتر والتنقل في التطبيق
// يحتوي على دوال مساعدة لاختبار المسارات وتتبع الأخطاء

import 'package:flutter/foundation.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

final _logger = Logger('RouterDebug');

/// فئة مساعدة لتشخيص مشاكل الروتر
class RouterDebugHelper {
  static const List<String> criticalRoutes = [
    '/',
    '/login',
    '/register',
    '/account',
    '/categories',
    '/garage',
    '/store',
  ];

  /// اختبار جميع المسارات الأساسية
  static Future<Map<String, bool>> testCriticalRoutes(GoRouter router) async {
    final results = <String, bool>{};
    
    for (final route in criticalRoutes) {
      try {
        // Test if the route can be resolved
        final routeData = router.routerDelegate.currentConfiguration;
        _logger.info('Testing route: $route');
        
        // This is a basic check - in a real test, you'd navigate and check
        results[route] = true;
        _logger.info('Route $route: OK');
      } catch (e) {
        results[route] = false;
        _logger.severe('Route $route: FAILED - $e');
      }
    }
    
    return results;
  }

  /// طباعة معلومات مفصلة عن حالة الروتر
  static void debugRouterState(GoRouter router) {
    if (!kDebugMode) return;

    _logger.info('=== Router Debug Info ===');
    final config = router.routerDelegate.currentConfiguration;
    _logger.info('Current configuration: ${config.matches.length} routes');
    
    for (var i = 0; i < config.matches.length; i++) {
      final route = config.matches[i];
      _logger.info('Route $i: ${route.matchedLocation}');
    }
    
    _logger.info('Can pop: ${router.canPop()}');
    _logger.info('========================');
  }

  /// اختبار تنقل آمن مع معالجة الأخطاء
  static Future<bool> safeNavigate(BuildContext context, String route) async {
    try {
      final router = GoRouter.of(context);
      
             // Check if router is properly initialized
      final config = router.routerDelegate.currentConfiguration;
      if (config.matches.isEmpty) {
        _logger.warning('Router not properly initialized');
        return false;
      }

      // Try to navigate
      context.go(route);
      _logger.info('Successfully navigated to $route');
      return true;
      
    } catch (e) {
      _logger.severe('Navigation to $route failed: $e');
      
      // Try fallback navigation
      try {
        context.push(route);
        _logger.info('Successfully navigated to $route using push');
        return true;
      } catch (e2) {
        _logger.severe('Fallback navigation also failed: $e2');
        return false;
      }
    }
  }

  /// عرض معلومات تشخيص في واجهة المستخدم
  static void showDebugDialog(BuildContext context, GoRouter router) {
    if (!kDebugMode) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات تشخيص الروتر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
                     crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('يمكن العودة: ${router.canPop()}'),
            const SizedBox(height: 16),
            const Text('المسارات الأساسية:'),
            ...criticalRoutes.map((route) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Text('• $route'),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final results = await testCriticalRoutes(router);
              _logger.info('Route test results: $results');
            },
            child: const Text('اختبار المسارات'),
          ),
        ],
      ),
    );
  }
}

/// Widget لعرض معلومات تشخيص الروتر (للتطوير فقط)
class RouterDebugWidget extends StatelessWidget {
  const RouterDebugWidget({super.key});

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode) return const SizedBox.shrink();

    return FloatingActionButton(
      mini: true,
      onPressed: () {
        final router = GoRouter.of(context);
        RouterDebugHelper.showDebugDialog(context, router);
      },
      child: const Icon(Icons.bug_report),
    );
  }
} 
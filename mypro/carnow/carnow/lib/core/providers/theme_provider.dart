import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'theme_provider.g.dart';

final _logger = Logger('ThemeProvider');

const String _themePrefsKey = 'app_theme_mode';

@riverpod
class AppThemeMode extends _$AppThemeMode {
  // تجنب التغييرات المفاجئة للثيم باستخدام متغير داخلي لحالة التحميل
  bool _isLoading = true;
  late ThemeMode _cachedThemeMode;

  @override
  Future<ThemeMode> build() async {
    _logger.info('Building theme mode provider');

    // إنشاء صورة مؤقتة لمنع وميض الثيم (تحديد قيمة مبدئية)
    if (_isLoading) {
      // جلب حالة الثيم من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      final themeString = prefs.getString(_themePrefsKey);

      if (themeString != null) {
        // تفسير القيمة المحفوظة
        switch (themeString) {
          case 'light':
            _cachedThemeMode = ThemeMode.light;
            break;
          case 'dark':
            _cachedThemeMode = ThemeMode.dark;
            break;
          default:
            _cachedThemeMode = ThemeMode.system;
        }
      } else {
        // استخدام القيمة المبدئية إذا لم يتم حفظ شيء سابقًا
        _cachedThemeMode = ThemeMode.system;
      }

      _isLoading = false;
      _logger.info('Theme mode loaded: $_cachedThemeMode');
    }

    return _cachedThemeMode;
  }

  /// تغيير وضع الثيم
  Future<void> setThemeMode(ThemeMode mode) async {
    _logger.info('Setting theme mode to: $mode');

    // تحديث حالة الثيم في الذاكرة
    _cachedThemeMode = mode;

    // حفظ الثيم في التخزين المحلي
    final prefs = await SharedPreferences.getInstance();
    String themeString;

    switch (mode) {
      case ThemeMode.light:
        themeString = 'light';
        break;
      case ThemeMode.dark:
        themeString = 'dark';
        break;
      default:
        themeString = 'system';
    }

    await prefs.setString(_themePrefsKey, themeString);

    // تحديث حالة المزود
    state = AsyncData(mode);
    _logger.info('Theme mode updated and saved');
  }

  /// طلب وضع الثيم الحالي للنظام
  Future<void> useSystemTheme() => setThemeMode(ThemeMode.system);

  /// طلب وضع النهار
  Future<void> useLightTheme() => setThemeMode(ThemeMode.light);

  /// طلب وضع الليل
  Future<void> useDarkTheme() => setThemeMode(ThemeMode.dark);
}

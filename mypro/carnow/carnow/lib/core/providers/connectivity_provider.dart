import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'connectivity_provider.g.dart';

@Riverpod(keepAlive: true)
Stream<List<ConnectivityResult>> connectivity(Ref ref) {
  // onConnectivityChanged now emits a list to support multi-interface devices.
  return Connectivity().onConnectivityChanged;
}

/// Exposes a simple boolean representing whether the device currently has
/// any form of connectivity.
final isConnectedProvider = Provider.autoDispose<bool>((ref) {
  final asyncConnectivity = ref.watch(connectivityProvider);

  return asyncConnectivity.when(
    data: (results) =>
        results.isNotEmpty && results.any((r) => r != ConnectivityResult.none),
    loading: () => false,
    error: (_, stackTrace) => false,
  );
});

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:flutter/foundation.dart';

/// Global Provider Observer لتتبع جميع التغييرات والأخطاء في Providers
class GlobalProviderObserver extends ProviderObserver {
  static final _logger = Logger();

  // Enhanced provider tracking with categorization
  static int _providerCount = 0;
  static int _errorCount = 0;
  static final Map<String, int> _providerCategories = {};
  static final Map<String, DateTime> _providerInitTimes = {};
  
  // Performance thresholds - adjusted for larger apps
  static const int _warningThreshold = 35; // Increased from 20
  static const int _criticalThreshold = 50;
  static const int _maxExpectedProviders = 40; // Expected count for this app size

  @override
  void didAddProvider(
    ProviderBase<Object?> provider,
    Object? value,
    ProviderContainer container,
  ) {
    _providerCount++;
    final providerName = provider.name ?? provider.runtimeType.toString();
    final category = _categorizeProvider(providerName);
    
    // Track provider by category
    _providerCategories[category] = (_providerCategories[category] ?? 0) + 1;
    _providerInitTimes[providerName] = DateTime.now();

    // Intelligent logging - only log important providers or in specific scenarios
    if (kDebugMode && _shouldLogProvider(providerName)) {
      _logger.d('🐛 Provider initialized: $providerName');
    }

    // Enhanced performance monitoring with contextual warnings
    if (kDebugMode) {
      if (_providerCount == _warningThreshold) {
        _logger.w(
          '⚠️ ⚠️ High provider count detected: $_providerCount providers initialized\n'
          '${_getProviderSummary()}',
        );
      } else if (_providerCount == _criticalThreshold) {
        _logger.e(
          '🚨 CRITICAL: Very high provider count: $_providerCount providers!\n'
          '${_getDetailedProviderSummary()}\n'
          'Consider implementing lazy loading for non-critical providers.',
        );
      } else if (_providerCount > _maxExpectedProviders) {
        // Only warn every 5 providers after the max expected
        if ((_providerCount - _maxExpectedProviders) % 5 == 0) {
          _logger.w(
            '📊 Provider count update: $_providerCount providers (${_providerCount - _maxExpectedProviders} over expected)\n'
            '${_getProviderSummary()}',
          );
        }
      }
    }
  }

  /// Categorize providers for better tracking
  String _categorizeProvider(String providerName) {
    final name = providerName.toLowerCase();
    
    if (name.contains('auth') || name.contains('session') || name.contains('user')) {
      return 'Authentication';
    } else if (name.contains('supabase') || name.contains('database') || name.contains('client')) {
      return 'Database/API';
    } else if (name.contains('repository') || name.contains('service')) {
      return 'Business Logic';
    } else if (name.contains('theme') || name.contains('router') || name.contains('navigation')) {
      return 'UI/Navigation';
    } else if (name.contains('connection') || name.contains('network')) {
      return 'Connectivity';
    } else if (name.contains('provider<') && (name.contains('bool') || name.contains('string') || name.contains('int'))) {
      return 'Simple Values';
    } else if (name.contains('future') || name.contains('stream')) {
      return 'Async Operations';
    } else {
      return 'Other';
    }
  }

  /// Get a summary of providers by category
  String _getProviderSummary() {
    final summary = StringBuffer('Provider breakdown by category:\n');
    _providerCategories.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value))
        ..forEach((entry) {
          summary.writeln('  ${entry.key}: ${entry.value}');
        });
    return summary.toString();
  }

  /// Get detailed provider summary for critical situations
  String _getDetailedProviderSummary() {
    final summary = StringBuffer(_getProviderSummary());
    summary.writeln('\nOptimization suggestions:');
    
    final authCount = _providerCategories['Authentication'] ?? 0;
    final dbCount = _providerCategories['Database/API'] ?? 0;
    final businessCount = _providerCategories['Business Logic'] ?? 0;
    
    if (authCount > 5) {
      summary.writeln('  • Too many auth providers ($authCount) - consider consolidation');
    }
    if (dbCount > 8) {
      summary.writeln('  • Too many database providers ($dbCount) - implement lazy loading');
    }
    if (businessCount > 15) {
      summary.writeln('  • Too many business logic providers ($businessCount) - use lazy initialization');
    }
    
    return summary.toString();
  }

  @override
  void didUpdateProvider(
    ProviderBase<Object?> provider,
    Object? previousValue,
    Object? newValue,
    ProviderContainer container,
  ) {
    // تتبع التحديثات المهمة فقط
    if (provider.name != null && provider.name!.contains('auth')) {
      _logger.i('Auth provider updated: ${provider.name}');
    }

    // معالجة AsyncValue errors
    if (newValue is AsyncValue) {
      newValue.whenOrNull(
        error: (error, stackTrace) {
          _handleProviderError(provider, error, stackTrace);
        },
      );
    }
  }

  @override
  void didDisposeProvider(
    ProviderBase<Object?> provider,
    ProviderContainer container,
  ) {
    final providerName = provider.name ?? provider.runtimeType.toString();
    final category = _categorizeProvider(providerName);
    
    // Check if this is a critical provider that shouldn't be disposed
    if (_isCriticalProvider(providerName)) {
      _logger.w('⚠️ CRITICAL PROVIDER DISPOSED: $providerName - This may cause ValueNotifier disposal errors!');
      
      // Provide more specific guidance
      if (providerName.toLowerCase().contains('user')) {
        _logger.i('💡 Tip: Add ref.keepAlive() to prevent premature disposal of user-related providers');
      }
    }
    
    // Update category count
    if (_providerCategories.containsKey(category)) {
      _providerCategories[category] = _providerCategories[category]! - 1;
      if (_providerCategories[category]! <= 0) {
        _providerCategories.remove(category);
      }
    }
    
    // Remove from tracking
    _providerInitTimes.remove(providerName);

    // Only log disposal of important providers
    if (kDebugMode && _shouldLogProvider(providerName)) {
      _logger.d('🐛 Provider disposed: $providerName');
    }
  }

  @override
  void providerDidFail(
    ProviderBase<Object?> provider,
    Object error,
    StackTrace stackTrace,
    ProviderContainer container,
  ) {
    _errorCount++;
    _handleProviderError(provider, error, stackTrace);
  }

  /// Check if a provider is critical and shouldn't be disposed
  bool _isCriticalProvider(String providerName) {
    final name = providerName.toLowerCase();
    
    // Skip deprecated providers from warnings
    if (name.contains('deprecated') || name.contains('legacy')) {
      return false;
    }
    
    // Critical providers that should use keepAlive()
    return name.contains('auth') ||
           name.contains('session') ||
           name.contains('user') ||
           name.contains('supabase') ||
           name.contains('connection') ||
           name.contains('theme') ||
           name.contains('router') ||
           name.contains('unified');
  }

  /// Determine if a provider should be logged (reduce noise)
  bool _shouldLogProvider(String providerName) {
    final name = providerName.toLowerCase();
    
    // Always log auth-related providers
    if (name.contains('auth') ||
        name.contains('session') ||
        name.contains('user')) {
      return true;
    }

    // Always log error-prone providers
    if (name.contains('supabase') ||
        name.contains('connection')) {
      return true;
    }

    // Always log important business logic
    if (name.contains('repository') ||
        name.contains('service')) {
      return true;
    }

    // Skip deprecated providers from logging
    if (name.contains('deprecated') || name.contains('legacy')) {
      return false;
    }

    // Skip common/frequent providers to reduce noise
    if (providerName.contains('Provider<bool>') ||
        providerName.contains('Provider<String>') ||
        providerName.contains('Provider<int>') ||
        providerName.contains('FutureProvider<')) {
      return false;
    }

    return false; // Changed to false to reduce noise by default
  }

  /// Get enhanced startup performance summary
  static Map<String, dynamic> getStartupSummary() {
    return {
      'providerCount': _providerCount,
      'errorCount': _errorCount,
      'categorySummary': Map.from(_providerCategories),
      'isHealthy': _providerCount <= _maxExpectedProviders,
      'performanceStatus': _getPerformanceStatus(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Get performance status description
  static String _getPerformanceStatus() {
    if (_providerCount <= _maxExpectedProviders) {
      return 'optimal';
    } else if (_providerCount <= _warningThreshold) {
      return 'acceptable';
    } else if (_providerCount <= _criticalThreshold) {
      return 'concerning';
    } else {
      return 'critical';
    }
  }

  /// Get providers by category for debugging
  static Map<String, int> getProvidersByCategory() {
    return Map.from(_providerCategories);
  }

  /// Reset counters (useful for testing)
  static void resetCounters() {
    _providerCount = 0;
    _errorCount = 0;
    _providerCategories.clear();
    _providerInitTimes.clear();
  }

  /// معالجة أخطاء Provider بشكل موحد
  void _handleProviderError(
    ProviderBase<Object?> provider,
    Object error,
    StackTrace stackTrace,
  ) {
    final providerName = provider.name ?? provider.runtimeType.toString();

    _logger.e(
      'Provider error in $providerName',
      error: error,
      stackTrace: stackTrace,
    );

    // إرسال الأخطاء الحرجة إلى Sentry
    if (shouldReportToSentry(error)) {
      Sentry.captureException(
        error,
        stackTrace: stackTrace,
        withScope: (scope) {
          scope.setTag('provider_name', providerName);
          scope.setTag('provider_category', _categorizeProvider(providerName));
          scope.setTag('error_type', error.runtimeType.toString());
          scope.level = SentryLevel.error;
        },
      );
    }
  }

  /// تحديد ما إذا كان يجب إرسال الخطأ إلى Sentry
  bool shouldReportToSentry(Object error) {
    // لا نرسل أخطاء الشبكة العادية أو الإلغاءات
    if (error.toString().contains('cancelled') ||
        error.toString().contains('SocketException') ||
        error.toString().contains('Connection closed')) {
      return false;
    }

    // نرسل الأخطاء الحرجة فقط في production
    return !kDebugMode; // Only report in production
  }
}

/// Extension لسهولة استخدام Observer
extension ProviderContainerExtension on ProviderContainer {
  /// إنشاء ProviderContainer مع Observer
  static ProviderContainer withObserver() {
    return ProviderContainer(observers: [GlobalProviderObserver()]);
  }
}

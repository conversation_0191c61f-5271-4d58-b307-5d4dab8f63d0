import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';
import '../../features/categories/models/category_model.dart';

part 'clean_categories_provider.g.dart';

final _logger = Logger('CleanCategoriesProvider');

/// Clean Categories Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// Simple providers that use SimpleApiClient ONLY
/// No direct Supabase calls, no complex state management

/// All categories provider with fallback
@riverpod
Future<List<CategoryModel>> allCategories(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching all categories from Go backend');
    
    final response = await apiClient.get<Map<String, dynamic>>(
      '/categories',
    );
    
    if (!response.isSuccess || response.data == null) {
      // Provide more detailed error information
      final errorMessage = response.error ?? response.message ?? 'Unknown error';
      _logger.warning('Backend response: Success=${response.isSuccess}, Data=${response.data != null}, Error=$errorMessage');
      
      // Check if it's a network connectivity issue
      if (errorMessage.contains('Network request failed') || 
          errorMessage.contains('timeout') ||
          errorMessage.contains('connection')) {
        _logger.warning('Backend connectivity issue detected, returning fallback data');
        return _getFallbackCategories();
      }
      
      throw Exception('Failed to fetch categories: $errorMessage');
    }

    final data = response.data!;
    
    // Handle different response formats from Go backend
    final List<dynamic> categoriesJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : throw Exception('Invalid categories response format');

    final categories = categoriesJson
        .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
        .toList();
        
    _logger.info('Fetched ${categories.length} categories successfully');
    return categories;
  } catch (e) {
    _logger.severe('Error fetching categories: $e');
    
    // Return fallback data for network issues
    if (e.toString().contains('Network request failed') ||
        e.toString().contains('timeout') ||
        e.toString().contains('connection')) {
      _logger.info('Returning fallback categories due to network issues');
      return _getFallbackCategories();
    }
    
    rethrow;
  }
}

/// Fallback categories when backend is unavailable
List<CategoryModel> _getFallbackCategories() {
  return [
    CategoryModel(
      id: 'cat-1',
      name: 'Auto Parts',
      nameAr: 'قطع غيار السيارات',
      description: 'Automotive parts and components',
      isActive: true,
      sortOrder: 1,
      type: 'part',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    CategoryModel(
      id: 'cat-2',
      name: 'Car Accessories',
      nameAr: 'إكسسوارات السيارات',
      description: 'Car accessories and add-ons',
      isActive: true,
      sortOrder: 2,
      type: 'accessory',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    CategoryModel(
      id: 'cat-3',
      name: 'Electronics',
      nameAr: 'الإلكترونيات',
      description: 'Car electronics and gadgets',
      isActive: true,
      sortOrder: 3,
      type: 'electronic',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    CategoryModel(
      id: 'cat-4',
      name: 'Maintenance',
      nameAr: 'الصيانة',
      description: 'Maintenance and service parts',
      isActive: true,
      sortOrder: 4,
      type: 'maintenance',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];
}

/// Parent categories provider (categories with no parent)
@riverpod
Future<List<CategoryModel>> parentCategories(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching parent categories from Go backend');
    
    final response = await apiClient.get<Map<String, dynamic>>(
      '/categories/parents',
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to fetch parent categories: ${response.error}');
    }

    final data = response.data!;
    final List<dynamic> categoriesJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    final categories = categoriesJson
        .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
        .toList();
        
    _logger.info('Fetched ${categories.length} parent categories successfully');
    return categories;
  } catch (e) {
    _logger.severe('Error fetching parent categories: $e');
    return [];
  }
}

/// Subcategories provider for a given parent category
@riverpod
Future<List<CategoryModel>> subcategories(Ref ref, String parentId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching subcategories for parent: $parentId');
    
    final response = await apiClient.get<Map<String, dynamic>>(
      '/categories/$parentId/subcategories',
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to fetch subcategories: ${response.error}');
    }

    final data = response.data!;
    final List<dynamic> categoriesJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    final categories = categoriesJson
        .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
        .toList();
        
    _logger.info('Fetched ${categories.length} subcategories for parent $parentId');
    return categories;
  } catch (e) {
    _logger.severe('Error fetching subcategories for $parentId: $e');
    return [];
  }
}

/// Popular categories provider (most used categories)
@riverpod
Future<List<CategoryModel>> popularCategories(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching popular categories from Go backend');
    
    final response = await apiClient.get<Map<String, dynamic>>(
      '/categories/popular',
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    final List<dynamic> categoriesJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    final categories = categoriesJson
        .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
        .toList();
        
    _logger.info('Fetched ${categories.length} popular categories successfully');
    return categories;
  } catch (e) {
    _logger.warning('Error fetching popular categories: $e');
    return [];
  }
}

/// Single category by ID provider
@riverpod
Future<CategoryModel?> categoryById(Ref ref, String categoryId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching category with ID: $categoryId');
    
    final response = await apiClient.get<Map<String, dynamic>>(
      '/categories/$categoryId',
    );
    
    if (!response.isSuccess || response.data == null) {
      return null;
    }

    final data = response.data!;
    final categoryData = data.containsKey('data') ? data['data'] : data;
    
    if (categoryData == null) return null;
    
    final category = CategoryModel.fromJson(categoryData as Map<String, dynamic>);
    _logger.info('Fetched category: ${category.name}');
    return category;
  } catch (e) {
    _logger.warning('Error fetching category $categoryId: $e');
    return null;
  }
}

/// Create new category
@riverpod
Future<CategoryModel?> createCategory(Ref ref, Map<String, dynamic> categoryData) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Creating new category');
    
    final response = await apiClient.post<Map<String, dynamic>>(
      '/categories',
      data: categoryData,
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to create category: ${response.error}');
    }

    final data = response.data!;
    final newCategoryData = data.containsKey('data') ? data['data'] : data;
    
    if (newCategoryData == null) return null;
    
    final category = CategoryModel.fromJson(newCategoryData as Map<String, dynamic>);
    _logger.info('Created category: ${category.name}');
    
    // Invalidate categories cache
    ref.invalidate(allCategoriesProvider);
    ref.invalidate(parentCategoriesProvider);
    
    return category;
  } catch (e) {
    _logger.severe('Error creating category: $e');
    return null;
  }
}

/// Update existing category
@riverpod
Future<CategoryModel?> updateCategory(Ref ref, String categoryId, Map<String, dynamic> updateData) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Updating category: $categoryId');
    
    final response = await apiClient.put<Map<String, dynamic>>(
      '/categories/$categoryId',
      data: updateData,
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to update category: ${response.error}');
    }

    final data = response.data!;
    final updatedCategoryData = data.containsKey('data') ? data['data'] : data;
    
    if (updatedCategoryData == null) return null;
    
    final category = CategoryModel.fromJson(updatedCategoryData as Map<String, dynamic>);
    _logger.info('Updated category: ${category.name}');
    
    // Invalidate categories cache
    ref.invalidate(allCategoriesProvider);
    ref.invalidate(parentCategoriesProvider);
    ref.invalidate(categoryByIdProvider(categoryId));
    
    return category;
  } catch (e) {
    _logger.severe('Error updating category $categoryId: $e');
    return null;
  }
}

/// Delete category
@riverpod
Future<bool> deleteCategory(Ref ref, String categoryId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Deleting category: $categoryId');
    
    final response = await apiClient.delete<Map<String, dynamic>>(
      '/categories/$categoryId',
    );
    
    if (!response.isSuccess) {
      throw Exception('Failed to delete category: ${response.error}');
    }

    _logger.info('Deleted category: $categoryId');
    
    // Invalidate categories cache
    ref.invalidate(allCategoriesProvider);
    ref.invalidate(parentCategoriesProvider);
    ref.invalidate(categoryByIdProvider(categoryId));
    
    return true;
  } catch (e) {
    _logger.severe('Error deleting category $categoryId: $e');
    return false;
  }
} 
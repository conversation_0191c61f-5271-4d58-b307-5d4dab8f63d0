import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

// part 'lazy_provider_system.g.dart';  // Commented out until generated

/// Lazy Provider System for optimized startup performance
/// 
/// This system defers initialization of non-critical providers to reduce
/// startup time and initial provider count.
class LazyProviderSystem {
  static final _logger = Logger();
  static final Set<String> _deferredProviders = {};
  static bool _startupPhaseCompleted = false;
  
  /// Mark startup phase as completed (typically after 3-5 seconds)
  static void completeStartupPhase() {
    _startupPhaseCompleted = true;
    _logger.i('Startup phase completed, lazy providers can now initialize');
  }
  
  /// Check if we're still in startup phase
  static bool get isStartupPhase => !_startupPhaseCompleted;
  
  /// Register a provider as deferred
  static void registerDeferredProvider(String name) {
    _deferredProviders.add(name);
  }
  
  /// Get count of deferred providers
  static int get deferredProviderCount => _deferredProviders.length;
}

/// Mixin for providers that should be loaded lazily after startup
mixin LazyLoadingProvider {
  bool get shouldDeferInitialization => LazyProviderSystem.isStartupPhase;
}

/// Provider that manages startup completion timing
final startupPhaseManagerProvider = StateNotifierProvider<StartupPhaseNotifier, bool>((ref) {
  return StartupPhaseNotifier();
});

/// State notifier for startup phase management
class StartupPhaseNotifier extends StateNotifier<bool> {
  StartupPhaseNotifier() : super(false) {
    // Complete startup phase after a delay
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) {
        LazyProviderSystem.completeStartupPhase();
        state = true;
      }
    });
  }
}

/// Lazy provider for non-critical services
final lazyServiceManagerProvider = StateNotifierProvider<LazyServiceManager, Map<String, bool>>((ref) {
  return LazyServiceManager();
});

/// Service manager for lazy initialization
class LazyServiceManager extends StateNotifier<Map<String, bool>> {
  LazyServiceManager() : super({});
  
  /// Initialize a service lazily
  Future<void> initializeService(String serviceName, Future<void> Function() initializer) async {
    if (state[serviceName] == true) return; // Already initialized
    
    LazyProviderSystem.registerDeferredProvider(serviceName);
    
    // Wait for startup if needed
    if (LazyProviderSystem.isStartupPhase) {
      // Wait for startup phase to complete
      await Future.delayed(const Duration(seconds: 4));
    }
    
    try {
      await initializer();
      state = {...state, serviceName: true};
      LazyProviderSystem._logger.d('Lazy service initialized: $serviceName');
    } catch (e) {
      LazyProviderSystem._logger.e('Failed to initialize lazy service $serviceName: $e');
    }
  }
}

/// Lazy wrapper for repository providers
class LazyRepositoryProvider<T> {
  
  LazyRepositoryProvider(this._provider, this._name);
  final Provider<T> _provider;
  final String _name;
  bool _initialized = false;
  
  Provider<T> get provider {
    if (!_initialized && LazyProviderSystem.isStartupPhase) {
      LazyProviderSystem.registerDeferredProvider(_name);
    }
    _initialized = true;
    return _provider;
  }
}

/// Factory for creating lazy providers
class LazyProviderFactory {
  /// Create a lazy version of a repository provider
  static LazyRepositoryProvider<T> repository<T>(
    Provider<T> provider,
    String name,
  ) {
    return LazyRepositoryProvider(provider, name);
  }
  
  /// Create a lazy FutureProvider that waits for startup completion
  static Provider<AsyncValue<T>> future<T>(
    FutureProvider<T> originalProvider,
    String name,
  ) {
    return Provider<AsyncValue<T>>((ref) {
      // Register as deferred
      LazyProviderSystem.registerDeferredProvider(name);
      
      // If we're in startup phase, return loading state
      if (LazyProviderSystem.isStartupPhase) {
        return const AsyncValue.loading();
      }
      
      // Otherwise, watch the original provider
      return ref.watch(originalProvider);
    });
  }
  
  /// Create a lazy StreamProvider
  static Provider<AsyncValue<T>> stream<T>(
    StreamProvider<T> originalProvider,
    String name,
  ) {
    return Provider<AsyncValue<T>>((ref) {
      LazyProviderSystem.registerDeferredProvider(name);
      
      if (LazyProviderSystem.isStartupPhase) {
        return const AsyncValue.loading();
      }
      
      return ref.watch(originalProvider);
    });
  }
}

/// Utility for creating deferred providers that only initialize when accessed
class DeferredProvider {
  /// Create a provider that only initializes when first accessed
  static Provider<T> create<T>(
    T Function() create,
    String name,
  ) {
    bool isInitialized = false;
    late T instance;
    
    return Provider<T>((ref) {
      if (!isInitialized) {
        LazyProviderSystem.registerDeferredProvider(name);
        instance = create();
        isInitialized = true;
        LazyProviderSystem._logger.d('Deferred provider initialized: $name');
      }
      return instance;
    });
  }
  
  /// Create an async provider that defers initialization
  static FutureProvider<T> createAsync<T>(
    Future<T> Function() create,
    String name,
  ) {
    return FutureProvider<T>((ref) async {
      // Wait for startup completion if needed
      if (LazyProviderSystem.isStartupPhase) {
        // Wait for startup to complete
        await Future.delayed(const Duration(seconds: 4));
      }
      
      LazyProviderSystem.registerDeferredProvider(name);
      final result = await create();
      LazyProviderSystem._logger.d('Deferred async provider initialized: $name');
      return result;
    });
  }
}

/// Provider to track lazy loading statistics
final lazyLoadingStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final startupCompleted = ref.watch(startupPhaseManagerProvider);
  
  return {
    'startupCompleted': startupCompleted,
    'deferredProviderCount': LazyProviderSystem.deferredProviderCount,
    'isStartupPhase': LazyProviderSystem.isStartupPhase,
  };
}); 
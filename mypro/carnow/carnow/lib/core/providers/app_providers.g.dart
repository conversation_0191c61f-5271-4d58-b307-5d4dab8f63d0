// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productsHash() => r'7c22e5686943b5ddd3a80d81545b32741c5c4fda';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Products provider with proper error handling
///
/// Copied from [products].
@ProviderFor(products)
const productsProvider = ProductsFamily();

/// Products provider with proper error handling
///
/// Copied from [products].
class ProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// Products provider with proper error handling
  ///
  /// Copied from [products].
  const ProductsFamily();

  /// Products provider with proper error handling
  ///
  /// Copied from [products].
  ProductsProvider call({
    String? category,
    String? brand,
    double? minPrice,
    double? maxPrice,
    String? search,
  }) {
    return ProductsProvider(
      category: category,
      brand: brand,
      minPrice: minPrice,
      maxPrice: maxPrice,
      search: search,
    );
  }

  @override
  ProductsProvider getProviderOverride(covariant ProductsProvider provider) {
    return call(
      category: provider.category,
      brand: provider.brand,
      minPrice: provider.minPrice,
      maxPrice: provider.maxPrice,
      search: provider.search,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productsProvider';
}

/// Products provider with proper error handling
///
/// Copied from [products].
class ProductsProvider extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// Products provider with proper error handling
  ///
  /// Copied from [products].
  ProductsProvider({
    String? category,
    String? brand,
    double? minPrice,
    double? maxPrice,
    String? search,
  }) : this._internal(
         (ref) => products(
           ref as ProductsRef,
           category: category,
           brand: brand,
           minPrice: minPrice,
           maxPrice: maxPrice,
           search: search,
         ),
         from: productsProvider,
         name: r'productsProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$productsHash,
         dependencies: ProductsFamily._dependencies,
         allTransitiveDependencies: ProductsFamily._allTransitiveDependencies,
         category: category,
         brand: brand,
         minPrice: minPrice,
         maxPrice: maxPrice,
         search: search,
       );

  ProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.category,
    required this.brand,
    required this.minPrice,
    required this.maxPrice,
    required this.search,
  }) : super.internal();

  final String? category;
  final String? brand;
  final double? minPrice;
  final double? maxPrice;
  final String? search;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(ProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductsProvider._internal(
        (ref) => create(ref as ProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        category: category,
        brand: brand,
        minPrice: minPrice,
        maxPrice: maxPrice,
        search: search,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _ProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductsProvider &&
        other.category == category &&
        other.brand == brand &&
        other.minPrice == minPrice &&
        other.maxPrice == maxPrice &&
        other.search == search;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, category.hashCode);
    hash = _SystemHash.combine(hash, brand.hashCode);
    hash = _SystemHash.combine(hash, minPrice.hashCode);
    hash = _SystemHash.combine(hash, maxPrice.hashCode);
    hash = _SystemHash.combine(hash, search.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `category` of this provider.
  String? get category;

  /// The parameter `brand` of this provider.
  String? get brand;

  /// The parameter `minPrice` of this provider.
  double? get minPrice;

  /// The parameter `maxPrice` of this provider.
  double? get maxPrice;

  /// The parameter `search` of this provider.
  String? get search;
}

class _ProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with ProductsRef {
  _ProductsProviderElement(super.provider);

  @override
  String? get category => (origin as ProductsProvider).category;
  @override
  String? get brand => (origin as ProductsProvider).brand;
  @override
  double? get minPrice => (origin as ProductsProvider).minPrice;
  @override
  double? get maxPrice => (origin as ProductsProvider).maxPrice;
  @override
  String? get search => (origin as ProductsProvider).search;
}

String _$categoriesHash() => r'da07edbfed3264f8a25fcde354ad66cc49a05f92';

/// Categories provider
///
/// Copied from [categories].
@ProviderFor(categories)
final categoriesProvider = AutoDisposeFutureProvider<List<String>>.internal(
  categories,
  name: r'categoriesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$categoriesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CategoriesRef = AutoDisposeFutureProviderRef<List<String>>;
String _$brandsHash() => r'b092307f40fad6ff048a3e3856823efff1a31bdd';

/// Brands provider
///
/// Copied from [brands].
@ProviderFor(brands)
final brandsProvider = AutoDisposeFutureProvider<List<String>>.internal(
  brands,
  name: r'brandsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$brandsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BrandsRef = AutoDisposeFutureProviderRef<List<String>>;
String _$productByIdHash() => r'23bb801d419da4bbd6cb26d33eda13ac8df7a266';

/// Product by ID provider
///
/// Copied from [productById].
@ProviderFor(productById)
const productByIdProvider = ProductByIdFamily();

/// Product by ID provider
///
/// Copied from [productById].
class ProductByIdFamily extends Family<AsyncValue<ProductModel?>> {
  /// Product by ID provider
  ///
  /// Copied from [productById].
  const ProductByIdFamily();

  /// Product by ID provider
  ///
  /// Copied from [productById].
  ProductByIdProvider call(String id) {
    return ProductByIdProvider(id);
  }

  @override
  ProductByIdProvider getProviderOverride(
    covariant ProductByIdProvider provider,
  ) {
    return call(provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productByIdProvider';
}

/// Product by ID provider
///
/// Copied from [productById].
class ProductByIdProvider extends AutoDisposeFutureProvider<ProductModel?> {
  /// Product by ID provider
  ///
  /// Copied from [productById].
  ProductByIdProvider(String id)
    : this._internal(
        (ref) => productById(ref as ProductByIdRef, id),
        from: productByIdProvider,
        name: r'productByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productByIdHash,
        dependencies: ProductByIdFamily._dependencies,
        allTransitiveDependencies: ProductByIdFamily._allTransitiveDependencies,
        id: id,
      );

  ProductByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<ProductModel?> Function(ProductByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductByIdProvider._internal(
        (ref) => create(ref as ProductByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductModel?> createElement() {
    return _ProductByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductByIdProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductByIdRef on AutoDisposeFutureProviderRef<ProductModel?> {
  /// The parameter `id` of this provider.
  String get id;
}

class _ProductByIdProviderElement
    extends AutoDisposeFutureProviderElement<ProductModel?>
    with ProductByIdRef {
  _ProductByIdProviderElement(super.provider);

  @override
  String get id => (origin as ProductByIdProvider).id;
}

String _$featuredProductsHash() => r'569a20c78e848316dbfe3ca6689b8d7391ed689d';

/// Featured products provider
///
/// Copied from [featuredProducts].
@ProviderFor(featuredProducts)
final featuredProductsProvider =
    AutoDisposeFutureProvider<List<ProductModel>>.internal(
      featuredProducts,
      name: r'featuredProductsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$featuredProductsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FeaturedProductsRef = AutoDisposeFutureProviderRef<List<ProductModel>>;
String _$userOrdersHash() => r'7a207de7ab2ebc1d3bcbfd4d47be05ca7e6b2d96';

/// User orders provider
///
/// Copied from [userOrders].
@ProviderFor(userOrders)
final userOrdersProvider = AutoDisposeFutureProvider<List<OrderModel>>.internal(
  userOrders,
  name: r'userOrdersProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userOrdersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserOrdersRef = AutoDisposeFutureProviderRef<List<OrderModel>>;
String _$orderStatusHash() => r'1f6fe670f82d612fffc9d9bfcf6085326e5144aa';

/// Order status provider
///
/// Copied from [orderStatus].
@ProviderFor(orderStatus)
const orderStatusProvider = OrderStatusFamily();

/// Order status provider
///
/// Copied from [orderStatus].
class OrderStatusFamily extends Family<AsyncValue<String>> {
  /// Order status provider
  ///
  /// Copied from [orderStatus].
  const OrderStatusFamily();

  /// Order status provider
  ///
  /// Copied from [orderStatus].
  OrderStatusProvider call(String orderId) {
    return OrderStatusProvider(orderId);
  }

  @override
  OrderStatusProvider getProviderOverride(
    covariant OrderStatusProvider provider,
  ) {
    return call(provider.orderId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'orderStatusProvider';
}

/// Order status provider
///
/// Copied from [orderStatus].
class OrderStatusProvider extends AutoDisposeFutureProvider<String> {
  /// Order status provider
  ///
  /// Copied from [orderStatus].
  OrderStatusProvider(String orderId)
    : this._internal(
        (ref) => orderStatus(ref as OrderStatusRef, orderId),
        from: orderStatusProvider,
        name: r'orderStatusProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$orderStatusHash,
        dependencies: OrderStatusFamily._dependencies,
        allTransitiveDependencies: OrderStatusFamily._allTransitiveDependencies,
        orderId: orderId,
      );

  OrderStatusProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.orderId,
  }) : super.internal();

  final String orderId;

  @override
  Override overrideWith(
    FutureOr<String> Function(OrderStatusRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: OrderStatusProvider._internal(
        (ref) => create(ref as OrderStatusRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        orderId: orderId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<String> createElement() {
    return _OrderStatusProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OrderStatusProvider && other.orderId == orderId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, orderId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OrderStatusRef on AutoDisposeFutureProviderRef<String> {
  /// The parameter `orderId` of this provider.
  String get orderId;
}

class _OrderStatusProviderElement
    extends AutoDisposeFutureProviderElement<String>
    with OrderStatusRef {
  _OrderStatusProviderElement(super.provider);

  @override
  String get orderId => (origin as OrderStatusProvider).orderId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

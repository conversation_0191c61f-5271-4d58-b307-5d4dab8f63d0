import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../errors/app_error.dart';
import '../errors/result.dart';

/// حالات النموذج الممكنة
/// Possible form states
enum FormStatus {
  /// نموذج فارغ - الحالة الافتراضية
  /// Initial empty form - default state
  initial,

  /// في حالة التحميل أثناء حفظ النموذج
  /// Loading state during form submission
  submitting,

  /// تم حفظ النموذج بنجاح
  /// Form was submitted successfully
  success,

  /// حدث خطأ أثناء حفظ النموذج
  /// An error occurred during submission
  error,
}

/// حالة النموذج الأساسية
/// Base form state
abstract class BaseFormState {
  BaseFormState({
    this.status = FormStatus.initial,
    this.errorMessage,
    this.isValid = false,
  });

  /// حالة النموذج الحالية
  /// Current form status
  final FormStatus status;

  /// رسالة الخطأ إن وجدت
  /// Error message if any
  final String? errorMessage;

  /// هل النموذج صالح للإرسال
  /// Whether form is valid for submission
  final bool isValid;

  /// هل النموذج في حالة تحميل
  /// Whether form is in loading state
  bool get isSubmitting => status == FormStatus.submitting;

  /// هل تم إرسال النموذج بنجاح
  /// Whether form was submitted successfully
  bool get isSuccess => status == FormStatus.success;

  /// هل حدث خطأ في النموذج
  /// Whether form has an error
  bool get hasError => status == FormStatus.error;
}

/// مزود أساسي للنماذج
/// Base form provider
abstract class BaseFormNotifier<T extends BaseFormState> extends Notifier<T> {
  BaseFormNotifier() {
    _logger = Logger(runtimeType.toString());
  }

  late final Logger _logger;

  /// إعداد الحالة الأولية للنموذج
  /// Setup initial state for the form
  @override
  T build();

  /// تحديث حالة النموذج بقيم جديدة
  /// Update form state with new values
  T updateState(T Function(T currentState) update) {
    state = update(state);
    return state;
  }

  /// تعيين حالة التحميل
  /// Set loading state
  void setLoading() {
    updateState(
      (currentState) => _copyWithStatus(currentState, FormStatus.submitting),
    );
  }

  /// تعيين حالة النجاح
  /// Set success state
  void setSuccess() {
    updateState(
      (currentState) => _copyWithStatus(currentState, FormStatus.success),
    );
  }

  /// تعيين حالة الخطأ
  /// Set error state
  void setError(String message) {
    updateState(
      (currentState) => _copyWithStatus(
        currentState,
        FormStatus.error,
        errorMessage: message,
      ),
    );
  }

  /// نسخة من الحالة الحالية بحالة نموذج جديدة
  /// Copy of current state with a new form status
  T _copyWithStatus(T currentState, FormStatus status, {String? errorMessage}) {
    // هذه الدالة سيتم تنفيذها بواسطة الفئات المشتقة
    // This should be implemented by derived classes
    throw UnimplementedError(
      'copyWithStatus should be implemented by derived classes',
    );
  }

  /// إعادة تعيين النموذج إلى الحالة الأولية
  /// Reset form to initial state
  void resetForm() {
    state = build();
  }

  /// إرسال النموذج
  /// Submit the form
  Future<Result<R>> submitForm<R>(Future<R> Function() onSubmit) async {
    try {
      // تعيين حالة التحميل
      setLoading();

      // تحقق من صلاحية النموذج
      if (!state.isValid) {
        setError('النموذج غير صالح. الرجاء التحقق من البيانات المدخلة.');
        return Result.failure(
          const AppError.validation(message: 'Form validation failed'),
        );
      }

      // تنفيذ عملية الإرسال
      final result = await onSubmit();

      // تعيين حالة النجاح
      setSuccess();

      return Result.success(result);
    } catch (e, stackTrace) {
      _logger.severe('Error submitting form', e, stackTrace);
      final error = e is AppError ? e : AppErrorFactory.fromError(e);
      setError(error.message);
      return Result.failure(error);
    }
  }

  /// التحقق من صلاحية النموذج
  /// Validate the form
  void validateForm();
}

/// قالب مزود نموذج بسيط مع دعم Riverpod
/// Simple form provider template with Riverpod support
abstract class SimpleFormNotifier<T> extends AutoDisposeNotifier<T> {
  late final Logger logger = Logger(runtimeType.toString());

  Future<void> submit() async {
    try {
      await onSubmit();
    } catch (e, stackTrace) {
      logger.severe('Error submitting form', e, stackTrace);
      onError(e);
    }
  }

  /// تنفيذ عند الإرسال - يتم تجاوزه بواسطة الفئات المشتقة
  /// Execute on submit - overridden by derived classes
  Future<void> onSubmit();

  /// معالجة الأخطاء - يتم تجاوزه بواسطة الفئات المشتقة
  /// Handle errors - overridden by derived classes
  void onError(dynamic error) {
    // Default implementation
    debugPrint('Form error: $error');
  }

  /// إعادة تعيين النموذج
  /// Reset form
  void reset() {
    ref.invalidateSelf();
  }
}

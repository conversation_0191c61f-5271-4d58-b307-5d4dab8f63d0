// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_session_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userSessionStateHash() => r'dd091d74e1be2652426278dd42474d7da03b8a90';

/// See also [userSessionState].
@ProviderFor(userSessionState)
final userSessionStateProvider = AutoDisposeProvider<UserSessionState>.internal(
  userSessionState,
  name: r'userSessionStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userSessionStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserSessionStateRef = AutoDisposeProviderRef<UserSessionState>;
String _$isUserAuthenticatedHash() =>
    r'1198af315a3b005483de6994ebd77e2ff5bfd975';

/// Quick check if user is authenticated
///
/// Copied from [isUserAuthenticated].
@ProviderFor(isUserAuthenticated)
final isUserAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isUserAuthenticated,
  name: r'isUserAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isUserAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsUserAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$hasCompleteProfileHash() =>
    r'1c9ac1ea357a6543b7c5ee43091f688e25cc1596';

/// Check if user has complete profile
///
/// Copied from [hasCompleteProfile].
@ProviderFor(hasCompleteProfile)
final hasCompleteProfileProvider = AutoDisposeProvider<bool>.internal(
  hasCompleteProfile,
  name: r'hasCompleteProfileProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasCompleteProfileHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HasCompleteProfileRef = AutoDisposeProviderRef<bool>;
String _$sessionStateDescriptionHash() =>
    r'71e676522d3a8554f9396229a346112f19f39a5a';

/// Get session state description
///
/// Copied from [sessionStateDescription].
@ProviderFor(sessionStateDescription)
final sessionStateDescriptionProvider = AutoDisposeProvider<String>.internal(
  sessionStateDescription,
  name: r'sessionStateDescriptionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sessionStateDescriptionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SessionStateDescriptionRef = AutoDisposeProviderRef<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

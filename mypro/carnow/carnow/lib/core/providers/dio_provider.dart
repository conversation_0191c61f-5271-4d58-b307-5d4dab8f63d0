import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../interceptors/error_interceptor.dart';
import '../../../core/auth/unified_auth_provider.dart';

part 'dio_provider.g.dart';

/// موفر Dio للطلبات HTTP - Forever Plan Architecture
/// Uses SimpleAuthSystem for authentication headers ONLY
@riverpod
Dio dio(Ref ref) {
  final dio = Dio();

  // إعداد البيانات الأساسية
  dio.options = BaseOptions(
    baseUrl: 'https://backend-go-8klm.onrender.com',
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    headers: {'Content-Type': 'application/json', 'Accept': 'application/json'},
  );

  // إضافة Interceptors
  dio.interceptors.add(UnifiedErrorInterceptor());

  // إضافة Authorization interceptor using SimpleAuthSystem
  dio.interceptors.add(InterceptorsWrapper(
    onRequest: (options, handler) async {
      try {
        // Get auth token from convenience provider
        final token = ref.read(currentAccessTokenProvider);
        
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
      } catch (e) {
        // If token retrieval fails, continue without auth header
        print('⚠️ Failed to get auth token for request: $e');
      }
      
      handler.next(options);
    },
    onError: (error, handler) async {
      // Handle 401 errors - SimpleAuthSystem manages token refresh internally
      if (error.response?.statusCode == 401) {
        print('⚠️ 401 error - authentication required or token expired');
        // SimpleAuthSystem will handle token refresh automatically on next request
        // For now, just continue with the error to let the app handle the auth flow
      }
      
      handler.next(error);
    },
  ));

  return dio;
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_flow_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isCoreSubscriptionFormValidHash() =>
    r'e4f47f42b856b5c1f66439b337df528b251c1408';

/// Provider for current form validation state
///
/// Copied from [isCoreSubscriptionFormValid].
@ProviderFor(isCoreSubscriptionFormValid)
final isCoreSubscriptionFormValidProvider = AutoDisposeProvider<bool>.internal(
  isCoreSubscriptionFormValid,
  name: r'isCoreSubscriptionFormValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isCoreSubscriptionFormValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsCoreSubscriptionFormValidRef = AutoDisposeProviderRef<bool>;
String _$coreSubscriptionProgressHash() =>
    r'df507a038ffa1ade72c88a44617dd85c80db1a2c';

/// Provider for current step progress
///
/// Copied from [coreSubscriptionProgress].
@ProviderFor(coreSubscriptionProgress)
final coreSubscriptionProgressProvider = AutoDisposeProvider<double>.internal(
  coreSubscriptionProgress,
  name: r'coreSubscriptionProgressProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$coreSubscriptionProgressHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CoreSubscriptionProgressRef = AutoDisposeProviderRef<double>;
String _$isCoreSubscriptionLoadingHash() =>
    r'863199d97bef953630e0d7fb95b28a5f29ee5d3f';

/// Provider for loading state
///
/// Copied from [isCoreSubscriptionLoading].
@ProviderFor(isCoreSubscriptionLoading)
final isCoreSubscriptionLoadingProvider = AutoDisposeProvider<bool>.internal(
  isCoreSubscriptionLoading,
  name: r'isCoreSubscriptionLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isCoreSubscriptionLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsCoreSubscriptionLoadingRef = AutoDisposeProviderRef<bool>;
String _$coreSubscriptionErrorHash() =>
    r'02698c04f72dad20d355cbcf4b9897695fe9687b';

/// Provider for error state
///
/// Copied from [coreSubscriptionError].
@ProviderFor(coreSubscriptionError)
final coreSubscriptionErrorProvider =
    AutoDisposeProvider<SubscriptionError?>.internal(
      coreSubscriptionError,
      name: r'coreSubscriptionErrorProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$coreSubscriptionErrorHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CoreSubscriptionErrorRef = AutoDisposeProviderRef<SubscriptionError?>;
String _$coreSubscriptionErrorArabicHash() =>
    r'22f6f89b61b76f9b5a34613473c74c6ec235151b';

/// Provider for Arabic error message
///
/// Copied from [coreSubscriptionErrorArabic].
@ProviderFor(coreSubscriptionErrorArabic)
final coreSubscriptionErrorArabicProvider =
    AutoDisposeProvider<String?>.internal(
      coreSubscriptionErrorArabic,
      name: r'coreSubscriptionErrorArabicProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$coreSubscriptionErrorArabicHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CoreSubscriptionErrorArabicRef = AutoDisposeProviderRef<String?>;
String _$coreSubscriptionSuccessHash() =>
    r'cfdcf24c7201ceea7b8e5bf7f240c6ef803edecf';

/// Provider for success state
///
/// Copied from [coreSubscriptionSuccess].
@ProviderFor(coreSubscriptionSuccess)
final coreSubscriptionSuccessProvider = AutoDisposeProvider<String?>.internal(
  coreSubscriptionSuccess,
  name: r'coreSubscriptionSuccessProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$coreSubscriptionSuccessHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CoreSubscriptionSuccessRef = AutoDisposeProviderRef<String?>;
String _$coreSubscriptionSuccessArabicHash() =>
    r'4f23adc1138f501de6f863e33de41b4f244d774b';

/// Provider for Arabic success state
///
/// Copied from [coreSubscriptionSuccessArabic].
@ProviderFor(coreSubscriptionSuccessArabic)
final coreSubscriptionSuccessArabicProvider =
    AutoDisposeProvider<String?>.internal(
      coreSubscriptionSuccessArabic,
      name: r'coreSubscriptionSuccessArabicProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$coreSubscriptionSuccessArabicHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CoreSubscriptionSuccessArabicRef = AutoDisposeProviderRef<String?>;
String _$coreSubscriptionResponseHash() =>
    r'6bcb718f275c82cd25eeaddd5f192604870eb31e';

/// Provider for subscription response
///
/// Copied from [coreSubscriptionResponse].
@ProviderFor(coreSubscriptionResponse)
final coreSubscriptionResponseProvider =
    AutoDisposeProvider<SubscriptionResponse?>.internal(
      coreSubscriptionResponse,
      name: r'coreSubscriptionResponseProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$coreSubscriptionResponseHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CoreSubscriptionResponseRef =
    AutoDisposeProviderRef<SubscriptionResponse?>;
String _$coreSubscriptionFormDataHash() =>
    r'021d21ed814f14fb9ba776e1da1d4130abb7c040';

/// Provider for form data
///
/// Copied from [coreSubscriptionFormData].
@ProviderFor(coreSubscriptionFormData)
final coreSubscriptionFormDataProvider =
    AutoDisposeProvider<CoreSubscriptionFormData>.internal(
      coreSubscriptionFormData,
      name: r'coreSubscriptionFormDataProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$coreSubscriptionFormDataHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CoreSubscriptionFormDataRef =
    AutoDisposeProviderRef<CoreSubscriptionFormData>;
String _$coreSubscriptionFlowStatusHash() =>
    r'4393c12b6a665ac166ae5e9a57a28dc2a3a37bf7';

/// Provider for flow status
///
/// Copied from [coreSubscriptionFlowStatus].
@ProviderFor(coreSubscriptionFlowStatus)
final coreSubscriptionFlowStatusProvider =
    AutoDisposeProvider<CoreSubscriptionFlowStatus>.internal(
      coreSubscriptionFlowStatus,
      name: r'coreSubscriptionFlowStatusProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$coreSubscriptionFlowStatusHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CoreSubscriptionFlowStatusRef =
    AutoDisposeProviderRef<CoreSubscriptionFlowStatus>;
String _$coreSubscriptionValidationErrorsHash() =>
    r'8e2153067db05f458b4c1f5cacccc8342ee39269';

/// Provider for validation errors
///
/// Copied from [coreSubscriptionValidationErrors].
@ProviderFor(coreSubscriptionValidationErrors)
final coreSubscriptionValidationErrorsProvider =
    AutoDisposeProvider<Map<String, String>>.internal(
      coreSubscriptionValidationErrors,
      name: r'coreSubscriptionValidationErrorsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$coreSubscriptionValidationErrorsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CoreSubscriptionValidationErrorsRef =
    AutoDisposeProviderRef<Map<String, String>>;
String _$canSubmitCoreSubscriptionHash() =>
    r'65e459b1bafac22529f3681feb2819f5a873d9e5';

/// Provider for can submit state
///
/// Copied from [canSubmitCoreSubscription].
@ProviderFor(canSubmitCoreSubscription)
final canSubmitCoreSubscriptionProvider = AutoDisposeProvider<bool>.internal(
  canSubmitCoreSubscription,
  name: r'canSubmitCoreSubscriptionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$canSubmitCoreSubscriptionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CanSubmitCoreSubscriptionRef = AutoDisposeProviderRef<bool>;
String _$coreSubscriptionFlowProviderHash() =>
    r'136bab5ebe41fc18bba655cda8106bd2c76cdae2';

/// Main core subscription flow provider with comprehensive state management
///
/// Copied from [CoreSubscriptionFlowProvider].
@ProviderFor(CoreSubscriptionFlowProvider)
final coreSubscriptionFlowProviderProvider =
    AutoDisposeNotifierProvider<
      CoreSubscriptionFlowProvider,
      CoreSubscriptionFlowState
    >.internal(
      CoreSubscriptionFlowProvider.new,
      name: r'coreSubscriptionFlowProviderProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$coreSubscriptionFlowProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CoreSubscriptionFlowProvider =
    AutoDisposeNotifier<CoreSubscriptionFlowState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

import 'dart:async';
import 'package:logger/logger.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/cache_service.dart';

final _logger = Logger();

/// حالة الـ Infinite Scroll مع البيانات والتحكم في التحميل
class InfiniteScrollState<T> {
  const InfiniteScrollState({
    this.items = const [],
    this.isLoading = false,
    this.isLoadingMore = false,
    this.hasMore = true,
    this.error,
    this.currentPage = 1,
    this.isRefreshing = false,
    this.totalItems = 0,
  });
  final List<T> items;
  final bool isLoading;
  final bool isLoadingMore;
  final bool hasMore;
  final String? error;
  final int currentPage;
  final bool isRefreshing;
  final int totalItems;

  InfiniteScrollState<T> copyWith({
    List<T>? items,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasMore,
    String? error,
    int? currentPage,
    bool? isRefreshing,
    int? totalItems,
  }) => InfiniteScrollState<T>(
    items: items ?? this.items,
    isLoading: isLoading ?? this.isLoading,
    isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    hasMore: hasMore ?? this.hasMore,
    error: error,
    currentPage: currentPage ?? this.currentPage,
    isRefreshing: isRefreshing ?? this.isRefreshing,
    totalItems: totalItems ?? this.totalItems,
  );

  @override
  String toString() =>
      'InfiniteScrollState(items: ${items.length}, isLoading: $isLoading, '
      'hasMore: $hasMore)';
}

/// نتيجة دالة جلب البيانات
class FetchResult<T> {
  const FetchResult({
    required this.items,
    required this.totalCount,
    required this.hasMore,
  });
  final List<T> items;
  final int totalCount;
  final bool hasMore;
}

/// Generic Infinite Scroll Notifier يمكن استخدامه مع أي نوع بيانات
class InfiniteScrollNotifier<T> extends StateNotifier<InfiniteScrollState<T>> {
  InfiniteScrollNotifier() : super(const InfiniteScrollState()) {
    _initCacheService();
  }
  static const int defaultPageSize = 20;
  Timer? _debounceTimer;

  // دالة لجلب البيانات
  Future<FetchResult<T>> Function(
    int page,
    int limit,
    Map<String, dynamic>? filters,
  )?
  _fetchFunction;

  // فلاتر البحث الحالية
  Map<String, dynamic>? _currentFilters;

  // مفتاح الكاش
  String? _cacheKey;

  // خدمة الكاش
  CacheService? _cacheService;

  Future<void> _initCacheService() async {
    try {
      _cacheService = await CacheService.getInstance();
    } catch (e) {
      _logger.e('Failed to initialize cache service: $e');
    }
  }

  /// تهيئة الـ Provider مع دالة جلب البيانات
  void initialize({
    required Future<FetchResult<T>> Function(
      int page,
      int limit,
      Map<String, dynamic>? filters,
    )
    fetchFunction,
    String? cacheKey,
  }) {
    _fetchFunction = fetchFunction;
    _cacheKey = cacheKey;
  }

  /// تحميل الصفحة الأولى من البيانات
  Future<void> loadInitial({
    Map<String, dynamic>? filters,
    bool useCache = true,
  }) async {
    if (state.isLoading || _fetchFunction == null) {
      return;
    }

    try {
      state = state.copyWith(
        isLoading: true,
        error: null,
        items: [],
        currentPage: 1,
        hasMore: true,
      );

      _currentFilters = filters;

      // محاولة الحصول على البيانات من الكاش أولاً
      List<Map<String, dynamic>>? cachedData;
      if (useCache && _cacheKey != null && _cacheService != null) {
        cachedData = await _cacheService!.getList(_cacheKey!);
      }

      if (cachedData != null && cachedData.isNotEmpty) {
        // نحول البيانات المحفوظة إلى النوع المطلوب (هذا يحتاج تحسين حسب النوع)
        state = state.copyWith(
          items: [], // سنتعامل مع هذا لاحقاً
          isLoading: false,
          totalItems: cachedData.length,
          hasMore: false,
        );
        return;
      }

      // جلب البيانات من المصدر
      final result = await _fetchFunction!(1, defaultPageSize, filters);

      // حفظ في الكاش إذا كان متاحاً
      if (_cacheKey != null && _cacheService != null) {
        // تحويل البيانات لـ Map للحفظ في الكاش
        final dataToCache = result.items
            .map((item) => <String, dynamic>{})
            .toList();

        await _cacheService!.setList(key: _cacheKey!, list: dataToCache);
      }

      state = state.copyWith(
        items: result.items,
        isLoading: false,
        hasMore: result.hasMore,
        totalItems: result.totalCount,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// تحميل الصفحة التالية من البيانات
  Future<void> loadMore() async {
    if (state.isLoadingMore ||
        !state.hasMore ||
        state.isLoading ||
        _fetchFunction == null) {
      return;
    }

    try {
      state = state.copyWith(isLoadingMore: true, error: null);

      final nextPage = state.currentPage + 1;
      final result = await _fetchFunction!(
        nextPage,
        defaultPageSize,
        _currentFilters,
      );

      final newItems = [...state.items, ...result.items];

      state = state.copyWith(
        items: newItems,
        isLoadingMore: false,
        hasMore: result.hasMore,
        currentPage: nextPage,
        totalItems: result.totalCount,
      );
    } catch (e) {
      state = state.copyWith(isLoadingMore: false, error: e.toString());
    }
  }

  /// تحديث البيانات (Pull to Refresh)
  Future<void> refresh({Map<String, dynamic>? filters}) async {
    if (state.isRefreshing || _fetchFunction == null) {
      return;
    }

    try {
      state = state.copyWith(isRefreshing: true, error: null);

      // مسح الكاش القديم
      if (_cacheKey != null && _cacheService != null) {
        await _cacheService!.remove(_cacheKey!);
      }

      _currentFilters = filters;
      final result = await _fetchFunction!(1, defaultPageSize, filters);

      state = state.copyWith(
        items: result.items,
        isRefreshing: false,
        hasMore: result.hasMore,
        currentPage: 1,
        totalItems: result.totalCount,
      );
    } catch (e) {
      state = state.copyWith(isRefreshing: false, error: e.toString());
    }
  }

  /// البحث مع Debouncing
  void searchWithDebounce(
    String query, {
    Map<String, dynamic>? additionalFilters,
    Duration debounceTime = const Duration(milliseconds: 500),
  }) {
    _debounceTimer?.cancel();

    _debounceTimer = Timer(debounceTime, () {
      final filters = {
        if (additionalFilters != null) ...additionalFilters,
        'search_query': query.trim().isEmpty ? null : query.trim(),
      };

      loadInitial(filters: filters, useCache: false);
    });
  }

  /// مسح جميع البيانات والعودة للحالة الأولية
  void clear() {
    _debounceTimer?.cancel();
    state = const InfiniteScrollState();
  }

  /// إضافة عنصر جديد للقائمة
  void addItem(T item, {bool atBeginning = true}) {
    final newItems = atBeginning
        ? [item, ...state.items]
        : [...state.items, item];

    state = state.copyWith(items: newItems, totalItems: state.totalItems + 1);
  }

  /// تحديث عنصر موجود في القائمة
  void updateItem(T item, bool Function(T) finder) {
    final itemIndex = state.items.indexWhere(finder);
    if (itemIndex != -1) {
      final newItems = [...state.items];
      newItems[itemIndex] = item;

      state = state.copyWith(items: newItems);
    }
  }

  /// حذف عنصر من القائمة
  void removeItem(bool Function(T) finder) {
    final newItems = state.items.where((item) => !finder(item)).toList();

    state = state.copyWith(
      items: newItems,
      totalItems: state.totalItems - (state.items.length - newItems.length),
    );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
}

/// مساعد لإنشاء Provider خاص بنوع معين من البيانات
typedef InfiniteScrollProvider<T> =
    StateNotifierProvider<InfiniteScrollNotifier<T>, InfiniteScrollState<T>>;

/// Factory لإنشاء Providers للأنواع المختلفة
abstract class InfiniteScrollProviders {
  /// إنشاء Provider للمنتجات
  static final products =
      StateNotifierProvider<
        InfiniteScrollNotifier<dynamic>,
        InfiniteScrollState<dynamic>
      >(
        (ref) => InfiniteScrollNotifier<dynamic>(),
        name: 'productsInfiniteScroll',
      );

  /// إنشاء Provider للفئات
  static final categories =
      StateNotifierProvider<
        InfiniteScrollNotifier<dynamic>,
        InfiniteScrollState<dynamic>
      >(
        (ref) => InfiniteScrollNotifier<dynamic>(),
        name: 'categoriesInfiniteScroll',
      );

  /// إنشاء Provider للطلبات
  static final orders =
      StateNotifierProvider<
        InfiniteScrollNotifier<dynamic>,
        InfiniteScrollState<dynamic>
      >(
        (ref) => InfiniteScrollNotifier<dynamic>(),
        name: 'ordersInfiniteScroll',
      );
}

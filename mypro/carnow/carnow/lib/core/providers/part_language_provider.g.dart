// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'part_language_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$partLanguageNotifierHash() =>
    r'ffca0c0e858c56ecddb6ed443b05c57bbb1e4a73';

/// See also [PartLanguageNotifier].
@ProviderFor(PartLanguageNotifier)
final partLanguageNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      PartLanguageNotifier,
      PartLanguage
    >.internal(
      PartLanguageNotifier.new,
      name: r'partLanguageNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$partLanguageNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$PartLanguageNotifier = AutoDisposeAsyncNotifier<PartLanguage>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

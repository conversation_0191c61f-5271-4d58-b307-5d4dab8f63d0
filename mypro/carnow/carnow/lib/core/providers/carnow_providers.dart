import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../networking/simple_api_client.dart';
import '../models/carnow_user.dart';
import '../models/carnow_wallet.dart';
import '../models/carnow_transaction.dart';
import '../../../core/auth/unified_auth_provider.dart';


part 'carnow_providers.g.dart';

/// Clean CarNow Providers - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// FIXED: Removed all direct Supabase calls
/// ✅ Uses SimpleApiClient ONLY
/// ✅ Uses SimpleAuthSystem for auth
/// ✅ Simple @riverpod patterns only

// =============================================================================
// TASK 9: Clean System Health and User Providers
// =============================================================================

/// CarNow system health check provider
@riverpod
Future<bool> carnowHealthCheck(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/health',
    );
    
    return response.isSuccess;
  } catch (e) {
    return false;
  }
}

/// CarNow user notifier provider (alias for currentCarnowUser)
@riverpod
Future<CarnowUser?> carnowUserNotifier(Ref ref) async {
  return await ref.watch(currentCarnowUserProvider.future);
}

/// CarNow wallet notifier provider (alias for userWallet)
@riverpod
Future<CarnowWallet?> carnowWalletNotifier(Ref ref) async {
  return await ref.watch(userWalletProvider.future);
}

/// Current CarNow user provider using Go backend API ONLY
@riverpod
Future<CarnowUser?> currentCarnowUser(Ref ref) async {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  final currentUser = ref.watch(currentUserProvider);
  
  // Check if user is authenticated
  if (!isAuthenticated || currentUser?.email == null) {
    return null;
  }

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    // Get user profile from Go backend
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/user/profile',
    );
    
    if (!response.isSuccess || response.data == null) {
      // User profile doesn't exist, this is normal for new users
      return null;
    }

    return CarnowUser.fromJson(response.data!);
  } catch (e) {
    // Handle errors gracefully
    if (e.toString().contains('not found') || e.toString().contains('404')) {
      return null; // User doesn't exist yet
    }
    rethrow;
  }
}

/// Create or update CarNow user provider
@riverpod
class CarnowUserActions extends _$CarnowUserActions {
  @override
  Future<CarnowUser?> build() async {
    // Initial state is the current user
    return await ref.read(currentCarnowUserProvider.future);
  }

  /// Create a new CarNow user
  Future<CarnowUser> createUser({
    required String email,
    String? name,
    String? phone,
  }) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.post<Map<String, dynamic>>(
      '/user/profile',
      data: {
        'email': email,
        if (name != null) 'name': name,
        if (phone != null) 'phone': phone,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to create user: ${response.message}');
    }

    final user = CarnowUser.fromJson(response.data!);
    
    // Update the state
    state = AsyncValue.data(user);
    
    return user;
  }

  /// Update CarNow user profile
  Future<CarnowUser> updateUser({
    String? name,
    String? phone,
  }) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.put<Map<String, dynamic>>(
      '/user/profile',
      data: {
        if (name != null) 'name': name,
        if (phone != null) 'phone': phone,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to update user: ${response.message}');
    }

    final user = CarnowUser.fromJson(response.data!);
    
    // Update the state
    state = AsyncValue.data(user);
    
    return user;
  }
}

// =============================================================================
// TASK 9: Clean Wallet Providers
// =============================================================================

/// User wallet provider using Go backend API ONLY
@riverpod
Future<CarnowWallet?> userWallet(Ref ref) async {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  
  // Check if user is authenticated
  if (!isAuthenticated) {
    return null;
  }

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/wallet',
    );
    
    if (!response.isSuccess || response.data == null) {
      return null;
    }

    return CarnowWallet.fromJson(response.data!);
  } catch (e) {
    // Handle errors gracefully
    return null;
  }
}

/// Wallet transactions provider
@riverpod
Future<List<CarnowTransaction>> walletTransactions(Ref ref) async {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  
  // Check if user is authenticated
  if (!isAuthenticated) {
    return [];
  }

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/wallet/transactions',
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    final List<dynamic> transactionsJson = data.containsKey('transactions') && data['transactions'] is List
        ? data['transactions'] as List<dynamic>
        : data.containsKey('data') && data['data'] is List
            ? data['data'] as List<dynamic>
            : [];

    return transactionsJson
        .map((json) => CarnowTransaction.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    return [];
  }
}

/// Wallet actions provider for transactions
@riverpod
class WalletActions extends _$WalletActions {
  @override
  Future<CarnowWallet?> build() async {
    return await ref.read(userWalletProvider.future);
  }

  /// Add funds to wallet
  Future<void> addFunds(double amount) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.post<Map<String, dynamic>>(
      '/wallet/transaction',
      data: {
        'amount': amount,
        'type': 'deposit',
        'description': 'Add funds to wallet',
      },
    );
    
    if (!response.isSuccess) {
      throw Exception('Failed to add funds: ${response.message}');
    }

    // Refresh wallet data
    ref.invalidate(userWalletProvider);
    ref.invalidate(walletTransactionsProvider);
  }

  /// Withdraw funds from wallet
  Future<void> withdrawFunds(double amount) async {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.post<Map<String, dynamic>>(
      '/wallet/transaction',
      data: {
        'amount': amount,
        'type': 'withdraw',
        'description': 'Withdraw funds from wallet',
      },
    );
    
    if (!response.isSuccess) {
      throw Exception('Failed to withdraw funds: ${response.message}');
    }

    // Refresh wallet data
    ref.invalidate(userWalletProvider);
    ref.invalidate(walletTransactionsProvider);
  }
}

// =============================================================================
// DEPRECATED: Legacy Complex Providers - Mark for Removal
// =============================================================================

/// DEPRECATED: Use currentCarnowUserProvider instead
@Deprecated('Use currentCarnowUserProvider instead - this had direct Supabase violations')
final legacyCarnowUserProvider = Provider<CarnowUser?>((ref) => null);

/// DEPRECATED: Use userWalletProvider instead
@Deprecated('Use userWalletProvider instead - this had complex state management')
final legacyWalletProvider = Provider<CarnowWallet?>((ref) => null);

// =============================================================================
// REMOVED: Architectural Violations
// =============================================================================
// The following violations have been REMOVED:
// ❌ Direct Supabase.instance.client.auth.currentUser calls
// ❌ Complex StateNotifierProvider chains
// ❌ Enhanced/Complex service integrations
// ❌ Multiple auth system dependencies
// ❌ Complex error handling with custom exceptions
//
// Replaced with:
// ✅ SimpleApiClient for ALL data operations
// ✅ SimpleAuthSystem for auth state
// ✅ Simple @riverpod patterns
// ✅ Clean error handling
// ✅ Forever Plan compliance
import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/unified_logger.dart';
import '../utils/resource_manager.dart';

/// Enum representing the network connection status
enum NetworkStatus { online, offline, unknown }

/// Extension methods for NetworkStatus
extension NetworkStatusX on NetworkStatus {
  bool get isOnline => this == NetworkStatus.online;
  bool get isOffline => this == NetworkStatus.offline;
}

/// A provider that streams the current network status
final networkStatusProvider = StreamProvider<NetworkStatus>((ref) {
  final connectivity = Connectivity();
  final controller = ResourceManager.registerController(
    StreamController<NetworkStatus>(),
  );

  /// Helper to determine network status from connectivity results
  NetworkStatus determineNetworkStatus(List<ConnectivityResult> results) {
    if (results.isEmpty) {
      return NetworkStatus.offline;
    }

    // If any connection type indicates online, consider the device online
    for (final result in results) {
      if (result == ConnectivityResult.mobile ||
          result == ConnectivityResult.wifi ||
          result == ConnectivityResult.ethernet ||
          result == ConnectivityResult.vpn) {
        return NetworkStatus.online;
      }
    }

    return NetworkStatus.offline;
  }

  // Check initial connection status
  connectivity.checkConnectivity().then((results) {
    final status = determineNetworkStatus(results);
    UnifiedLogger.info('🌐 نظام مراقبة الشبكة مُفعل', tag: 'NetworkStatus');
    if (!controller.isClosed) {
      controller.add(status);
    }
  });

  // Listen for connection changes
  final subscription = connectivity.onConnectivityChanged.listen((results) {
    final status = determineNetworkStatus(results);
    UnifiedLogger.info('🌐 تم استعادة اتصال الإنترنت', tag: 'NetworkStatus');
    if (!controller.isClosed) {
      controller.add(status);
    }
  }).register();

  // Cleanup
  ref.onDispose(() {
    UnifiedLogger.info('Disposing network status provider', tag: 'NetworkStatus');
    subscription.dispose();
    ResourceManager.closeController(controller);
  });

  return controller.stream;
});

/// Utility function to execute operations with smart retry for network errors
Future<T> executeWithConnectivityAwareness<T>({
  required Future<T> Function() operation,
  required Ref ref,
  int maxAttempts = 3,
  Duration initialDelay = const Duration(seconds: 1),
  Duration maxBackoffDelay = const Duration(seconds: 15),
  String operationName = 'Network Operation',
}) async {
  var attempts = 0;
  var currentDelay = initialDelay;

  while (attempts < maxAttempts) {
    attempts++;

    try {
      UnifiedLogger.info('Attempting $operationName (attempt $attempts/$maxAttempts)', tag: 'NetworkStatus');
      return await operation();
    } catch (e) {
      final isNetworkError = _isNetworkRelatedError(e.toString());
      final isLastAttempt = attempts >= maxAttempts;

      if (!isNetworkError || isLastAttempt) {
        UnifiedLogger.error('خطأ في تسجيل حالة الشبكة: $e', error: e, tag: 'NetworkStatus');
        UnifiedLogger.warning('تم تجاهل خطأ تسجيل حالة الشبكة', tag: 'NetworkStatus');
        rethrow;
      }

      UnifiedLogger.warning(
        '$operationName failed (attempt $attempts/$maxAttempts): $e',
      );
      await Future<void>.delayed(currentDelay);

      // Exponential backoff
      currentDelay = Duration(
        milliseconds: (currentDelay.inMilliseconds * 2).clamp(
          0,
          maxBackoffDelay.inMilliseconds,
        ),
      );
    }
  }

  throw Exception('$operationName failed after $maxAttempts attempts');
}

/// Helper to identify network-related errors
bool _isNetworkRelatedError(String errorMessage) {
  final message = errorMessage.toLowerCase();
  return message.contains('network') ||
      message.contains('connection') ||
      message.contains('timeout') ||
      message.contains('socket') ||
      message.contains('offline') ||
      message.contains('unreachable');
}

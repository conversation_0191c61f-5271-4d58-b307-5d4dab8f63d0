// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$productsHash() => r'291f285c8114d71971f8363f85b778f11d16c00e';

/// See also [products].
@ProviderFor(products)
final productsProvider = AutoDisposeFutureProvider<List<ProductModel>>.internal(
  products,
  name: r'productsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$productsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ProductsRef = AutoDisposeFutureProviderRef<List<ProductModel>>;
String _$paginatedProductsHash() => r'eed0a23009a25061146c087b34c1fb25b677e1dc';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [paginatedProducts].
@ProviderFor(paginatedProducts)
const paginatedProductsProvider = PaginatedProductsFamily();

/// See also [paginatedProducts].
class PaginatedProductsFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// See also [paginatedProducts].
  const PaginatedProductsFamily();

  /// See also [paginatedProducts].
  PaginatedProductsProvider call(Map<String, int> params) {
    return PaginatedProductsProvider(params);
  }

  @override
  PaginatedProductsProvider getProviderOverride(
    covariant PaginatedProductsProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'paginatedProductsProvider';
}

/// See also [paginatedProducts].
class PaginatedProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// See also [paginatedProducts].
  PaginatedProductsProvider(Map<String, int> params)
    : this._internal(
        (ref) => paginatedProducts(ref as PaginatedProductsRef, params),
        from: paginatedProductsProvider,
        name: r'paginatedProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$paginatedProductsHash,
        dependencies: PaginatedProductsFamily._dependencies,
        allTransitiveDependencies:
            PaginatedProductsFamily._allTransitiveDependencies,
        params: params,
      );

  PaginatedProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final Map<String, int> params;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(PaginatedProductsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PaginatedProductsProvider._internal(
        (ref) => create(ref as PaginatedProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _PaginatedProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PaginatedProductsProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PaginatedProductsRef on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `params` of this provider.
  Map<String, int> get params;
}

class _PaginatedProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with PaginatedProductsRef {
  _PaginatedProductsProviderElement(super.provider);

  @override
  Map<String, int> get params => (origin as PaginatedProductsProvider).params;
}

String _$productHash() => r'a4f8858f16a20967375ddb438c69ba55e5cbf22c';

/// See also [product].
@ProviderFor(product)
const productProvider = ProductFamily();

/// See also [product].
class ProductFamily extends Family<AsyncValue<ProductModel>> {
  /// See also [product].
  const ProductFamily();

  /// See also [product].
  ProductProvider call(String productId) {
    return ProductProvider(productId);
  }

  @override
  ProductProvider getProviderOverride(covariant ProductProvider provider) {
    return call(provider.productId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productProvider';
}

/// See also [product].
class ProductProvider extends AutoDisposeFutureProvider<ProductModel> {
  /// See also [product].
  ProductProvider(String productId)
    : this._internal(
        (ref) => product(ref as ProductRef, productId),
        from: productProvider,
        name: r'productProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productHash,
        dependencies: ProductFamily._dependencies,
        allTransitiveDependencies: ProductFamily._allTransitiveDependencies,
        productId: productId,
      );

  ProductProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.productId,
  }) : super.internal();

  final String productId;

  @override
  Override overrideWith(
    FutureOr<ProductModel> Function(ProductRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductProvider._internal(
        (ref) => create(ref as ProductRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        productId: productId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ProductModel> createElement() {
    return _ProductProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductProvider && other.productId == productId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, productId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductRef on AutoDisposeFutureProviderRef<ProductModel> {
  /// The parameter `productId` of this provider.
  String get productId;
}

class _ProductProviderElement
    extends AutoDisposeFutureProviderElement<ProductModel>
    with ProductRef {
  _ProductProviderElement(super.provider);

  @override
  String get productId => (origin as ProductProvider).productId;
}

String _$auctionProductsHash() => r'9b5fad3b5a81d106249903ac81137eccd49361e8';

/// See also [auctionProducts].
@ProviderFor(auctionProducts)
final auctionProductsProvider =
    AutoDisposeFutureProvider<List<ProductModel>>.internal(
      auctionProducts,
      name: r'auctionProductsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$auctionProductsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuctionProductsRef = AutoDisposeFutureProviderRef<List<ProductModel>>;
String _$paginatedAuctionProductsHash() =>
    r'3656097b7b85584badcdb55f7344ed79bfb6c3f2';

/// See also [paginatedAuctionProducts].
@ProviderFor(paginatedAuctionProducts)
const paginatedAuctionProductsProvider = PaginatedAuctionProductsFamily();

/// See also [paginatedAuctionProducts].
class PaginatedAuctionProductsFamily
    extends Family<AsyncValue<List<ProductModel>>> {
  /// See also [paginatedAuctionProducts].
  const PaginatedAuctionProductsFamily();

  /// See also [paginatedAuctionProducts].
  PaginatedAuctionProductsProvider call(Map<String, int> params) {
    return PaginatedAuctionProductsProvider(params);
  }

  @override
  PaginatedAuctionProductsProvider getProviderOverride(
    covariant PaginatedAuctionProductsProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'paginatedAuctionProductsProvider';
}

/// See also [paginatedAuctionProducts].
class PaginatedAuctionProductsProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// See also [paginatedAuctionProducts].
  PaginatedAuctionProductsProvider(Map<String, int> params)
    : this._internal(
        (ref) => paginatedAuctionProducts(
          ref as PaginatedAuctionProductsRef,
          params,
        ),
        from: paginatedAuctionProductsProvider,
        name: r'paginatedAuctionProductsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$paginatedAuctionProductsHash,
        dependencies: PaginatedAuctionProductsFamily._dependencies,
        allTransitiveDependencies:
            PaginatedAuctionProductsFamily._allTransitiveDependencies,
        params: params,
      );

  PaginatedAuctionProductsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final Map<String, int> params;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(PaginatedAuctionProductsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PaginatedAuctionProductsProvider._internal(
        (ref) => create(ref as PaginatedAuctionProductsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _PaginatedAuctionProductsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PaginatedAuctionProductsProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PaginatedAuctionProductsRef
    on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `params` of this provider.
  Map<String, int> get params;
}

class _PaginatedAuctionProductsProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with PaginatedAuctionProductsRef {
  _PaginatedAuctionProductsProviderElement(super.provider);

  @override
  Map<String, int> get params =>
      (origin as PaginatedAuctionProductsProvider).params;
}

String _$productsByCategoryHash() =>
    r'21e4b6985ce3a3a7486a5296f10de2d7e8ff378d';

/// See also [productsByCategory].
@ProviderFor(productsByCategory)
const productsByCategoryProvider = ProductsByCategoryFamily();

/// See also [productsByCategory].
class ProductsByCategoryFamily extends Family<AsyncValue<List<ProductModel>>> {
  /// See also [productsByCategory].
  const ProductsByCategoryFamily();

  /// See also [productsByCategory].
  ProductsByCategoryProvider call(String categoryId) {
    return ProductsByCategoryProvider(categoryId);
  }

  @override
  ProductsByCategoryProvider getProviderOverride(
    covariant ProductsByCategoryProvider provider,
  ) {
    return call(provider.categoryId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'productsByCategoryProvider';
}

/// See also [productsByCategory].
class ProductsByCategoryProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// See also [productsByCategory].
  ProductsByCategoryProvider(String categoryId)
    : this._internal(
        (ref) => productsByCategory(ref as ProductsByCategoryRef, categoryId),
        from: productsByCategoryProvider,
        name: r'productsByCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$productsByCategoryHash,
        dependencies: ProductsByCategoryFamily._dependencies,
        allTransitiveDependencies:
            ProductsByCategoryFamily._allTransitiveDependencies,
        categoryId: categoryId,
      );

  ProductsByCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(ProductsByCategoryRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ProductsByCategoryProvider._internal(
        (ref) => create(ref as ProductsByCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _ProductsByCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ProductsByCategoryProvider &&
        other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ProductsByCategoryRef
    on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _ProductsByCategoryProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with ProductsByCategoryRef {
  _ProductsByCategoryProviderElement(super.provider);

  @override
  String get categoryId => (origin as ProductsByCategoryProvider).categoryId;
}

String _$paginatedProductsByCategoryHash() =>
    r'30e5eded505baeb0d13632137bcb96ec9aa54e7c';

/// See also [paginatedProductsByCategory].
@ProviderFor(paginatedProductsByCategory)
const paginatedProductsByCategoryProvider = PaginatedProductsByCategoryFamily();

/// See also [paginatedProductsByCategory].
class PaginatedProductsByCategoryFamily
    extends Family<AsyncValue<List<ProductModel>>> {
  /// See also [paginatedProductsByCategory].
  const PaginatedProductsByCategoryFamily();

  /// See also [paginatedProductsByCategory].
  PaginatedProductsByCategoryProvider call(Map<String, dynamic> params) {
    return PaginatedProductsByCategoryProvider(params);
  }

  @override
  PaginatedProductsByCategoryProvider getProviderOverride(
    covariant PaginatedProductsByCategoryProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'paginatedProductsByCategoryProvider';
}

/// See also [paginatedProductsByCategory].
class PaginatedProductsByCategoryProvider
    extends AutoDisposeFutureProvider<List<ProductModel>> {
  /// See also [paginatedProductsByCategory].
  PaginatedProductsByCategoryProvider(Map<String, dynamic> params)
    : this._internal(
        (ref) => paginatedProductsByCategory(
          ref as PaginatedProductsByCategoryRef,
          params,
        ),
        from: paginatedProductsByCategoryProvider,
        name: r'paginatedProductsByCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$paginatedProductsByCategoryHash,
        dependencies: PaginatedProductsByCategoryFamily._dependencies,
        allTransitiveDependencies:
            PaginatedProductsByCategoryFamily._allTransitiveDependencies,
        params: params,
      );

  PaginatedProductsByCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final Map<String, dynamic> params;

  @override
  Override overrideWith(
    FutureOr<List<ProductModel>> Function(
      PaginatedProductsByCategoryRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: PaginatedProductsByCategoryProvider._internal(
        (ref) => create(ref as PaginatedProductsByCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<ProductModel>> createElement() {
    return _PaginatedProductsByCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PaginatedProductsByCategoryProvider &&
        other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PaginatedProductsByCategoryRef
    on AutoDisposeFutureProviderRef<List<ProductModel>> {
  /// The parameter `params` of this provider.
  Map<String, dynamic> get params;
}

class _PaginatedProductsByCategoryProviderElement
    extends AutoDisposeFutureProviderElement<List<ProductModel>>
    with PaginatedProductsByCategoryRef {
  _PaginatedProductsByCategoryProviderElement(super.provider);

  @override
  Map<String, dynamic> get params =>
      (origin as PaginatedProductsByCategoryProvider).params;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

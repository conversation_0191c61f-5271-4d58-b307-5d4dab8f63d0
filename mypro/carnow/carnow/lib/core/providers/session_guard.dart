import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/auth/unified_auth_provider.dart';

part 'session_guard.g.dart';

/// DEPRECATED: Session Guard - Forever Plan Architecture Migration
/// This provider has been replaced by UnifiedAuthProvider session management
/// 
/// Forever Plan: Use UnifiedAuthProvider for all auth operations
/// The session validation is now handled automatically by UnifiedAuthProvider

@Deprecated('Session management is now handled by UnifiedAuthProvider automatically')
@Riverpod(keepAlive: true)
class SessionGuard extends _$SessionGuard {
  @override
  FutureOr<void> build() async {
    // This provider is deprecated - UnifiedAuthProvider handles session management
    // Session validation and cleanup is now automatic in UnifiedAuthProvider
    
    // Watch authentication state to ensure it's initialized and handling sessions
    ref.watch(isAuthenticatedProvider);
    
    // Log deprecation warning
    print('⚠️ SessionGuard is deprecated - UnifiedAuthProvider now handles session management automatically');
    
    return;
  }
  
  /// DEPRECATED: Use UnifiedAuthProvider.signOut() instead
  @Deprecated('Use UnifiedAuthProvider.signOut() instead')
  Future<void> forceSignOut() async {
    final authSystem = ref.read(unifiedAuthProviderProvider.notifier);
    await authSystem.signOut();
  }
}

// ================================================================================
// MIGRATION GUIDE:
// ================================================================================
// 
// OLD: ref.read(sessionGuardProvider);
// NEW: ref.read(unifiedAuthProviderProvider); // Session management is automatic
//
// OLD: ref.read(sessionGuardProvider.notifier).forceSignOut();
// NEW: ref.read(unifiedAuthProviderProvider.notifier).signOut();
//
// Session validation, refresh token management, and cleanup are now handled
// automatically by UnifiedAuthProvider - no manual session guard needed.
// ================================================================================

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';
import '../models/product_model.dart';

part 'simple_product_provider.g.dart';

final _logger = Logger('SimpleProductProvider');

/// Simple product providers using Go backend API
/// Follows Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)

/// All products provider with simple AsyncValue error handling
final allProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching all products from Go backend');
    
    final response = await apiClient.getApi<Map<String, dynamic>>('/api/v1/products');
    
    if (!response.isSuccess || response.data == null) {
      throw Exception('Failed to fetch products: ${response.message}');
    }

    final data = response.data!;
    final productsJson = data['data'] as List<dynamic>;
    final products = productsJson
        .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
        .toList();
        
    _logger.info('Fetched ${products.length} products successfully');
    return products;
  } catch (e) {
    _logger.severe('Error fetching products: $e');
    rethrow;
  }
});

/// Featured products provider
final featuredProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/products/featured',
      queryParameters: {'limit': 10},
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    final productsJson = data['data'] as List<dynamic>;
    return productsJson
        .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    _logger.warning('Error fetching featured products: $e');
    return [];
  }
});

/// Single product by ID provider - FIXED: Converted to @riverpod pattern
@riverpod
Future<ProductModel?> productById(Ref ref, String productId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching product: $productId');
    
    final response = await apiClient.getApi<Map<String, dynamic>>('/products/$productId');
    
    if (!response.isSuccess || response.data == null) {
      _logger.warning('Product not found: $productId');
      return null;
    }

    final data = response.data!;
    final productJson = data['data'] as Map<String, dynamic>;
    return ProductModel.fromJson(productJson);
  } catch (e) {
    _logger.severe('Error fetching product $productId: $e');
    rethrow;
  }
}

/// Products by category provider - FIXED: Converted to @riverpod pattern
@riverpod
Future<List<ProductModel>> productsByCategory(Ref ref, String categoryId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>('/products/category/$categoryId');
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    final productsJson = data['data'] as List<dynamic>;
    return productsJson
        .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    _logger.warning('Error fetching products for category $categoryId: $e');
    return [];
  }
}

/// User's products provider (for sellers)
final userProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>('/products/my-products');
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    final productsJson = data['data'] as List<dynamic>;
    return productsJson
        .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    _logger.warning('Error fetching user products: $e');
    return [];
  }
});

/// Product actions provider for CRUD operations
final productActionsProvider = Provider<ProductActions>((ref) {
  return ProductActions(ref);
});

/// Product actions class with clean error handling
class ProductActions {
  ProductActions(this._ref);
  
  final Ref _ref;
  
  /// Create a new product
  Future<ProductModel> createProduct(Map<String, dynamic> productData) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      _logger.info('Creating new product');
      
      final response = await apiClient.post<Map<String, dynamic>>(
        '/products',
        data: productData,
      );
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to create product: ${response.message}');
      }

      // Refresh relevant providers
      _ref.invalidate(allProductsProvider);
      _ref.invalidate(userProductsProvider);
      
      final data = response.data!;
      final productJson = data['data'] as Map<String, dynamic>;
      final createdProduct = ProductModel.fromJson(productJson);
      _logger.info('Successfully created product: ${createdProduct.id}');
      return createdProduct;
    } catch (e) {
      _logger.severe('Error creating product: $e');
      rethrow;
    }
  }
  
  /// Update an existing product
  Future<ProductModel> updateProduct(String productId, Map<String, dynamic> updateData) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      _logger.info('Updating product: $productId');
      
      final response = await apiClient.put<Map<String, dynamic>>(
        '/products/$productId',
        data: updateData,
      );
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to update product: ${response.message}');
      }

      // Refresh relevant providers
      _ref.invalidate(allProductsProvider);
      _ref.invalidate(userProductsProvider);
      _ref.invalidate(productByIdProvider(productId));
      
      final data = response.data!;
      final productJson = data['data'] as Map<String, dynamic>;
      final updatedProduct = ProductModel.fromJson(productJson);
      _logger.info('Successfully updated product: $productId');
      return updatedProduct;
    } catch (e) {
      _logger.severe('Error updating product $productId: $e');
      rethrow;
    }
  }
  
  /// Delete a product
  Future<void> deleteProduct(String productId) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      _logger.info('Deleting product: $productId');
      
      final response = await apiClient.delete('/products/$productId');
      
      if (!response.isSuccess) {
        throw Exception('Failed to delete product: ${response.message}');
      }

      // Refresh relevant providers
      _ref.invalidate(allProductsProvider);
      _ref.invalidate(userProductsProvider);
      _ref.invalidate(productByIdProvider(productId));
      
      _logger.info('Successfully deleted product: $productId');
    } catch (e) {
      _logger.severe('Error deleting product $productId: $e');
      rethrow;
    }
  }
  
  /// Toggle product favorite status
  Future<void> toggleFavorite(String productId) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.post<Map<String, dynamic>>('/products/$productId/toggle-favorite');
      
      if (!response.isSuccess) {
        throw Exception('Failed to toggle favorite: ${response.message}');
      }

      // Refresh product details
      _ref.invalidate(productByIdProvider(productId));
      
      _logger.info('Toggled favorite for product: $productId');
    } catch (e) {
      _logger.warning('Error toggling favorite for product $productId: $e');
      rethrow;
    }
  }
  
  /// Get product analytics (for sellers)
  Future<Map<String, dynamic>> getProductAnalytics(String productId) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.getApi<Map<String, dynamic>>('/products/$productId/analytics');
      
      if (!response.isSuccess || response.data == null) {
        return {};
      }

      return response.data!;
    } catch (e) {
      _logger.warning('Error fetching analytics for product $productId: $e');
      return {};
    }
  }
} 
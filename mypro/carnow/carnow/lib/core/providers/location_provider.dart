import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../core/services/location_service.dart';
import '../networking/simple_api_client.dart';
import '../models/city_model.dart';

part 'location_provider.g.dart';

@riverpod
LocationService locationService(Ref ref) {
  return LocationService(ref.watch(simpleApiClientProvider));
}

@riverpod
Future<List<City>> cities(Ref ref) {
  return ref.watch(locationServiceProvider).getCities();
}

@riverpod
Future<City?> cityById(Ref ref, int id) async {
  final citiesList = await ref.watch(citiesProvider.future);
  try {
    return citiesList.firstWhere((city) => city.id == id);
  } catch (e) {
    // Return null if no city is found, instead of throwing an error
    return null;
  }
}

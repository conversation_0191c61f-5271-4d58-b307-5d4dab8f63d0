// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'seller_request_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerRequestNotifierHash() =>
    r'00bc730db2c27c1eb3f6d3e1b495bbd899b12f34';

/// Clean Seller Request Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// FIXED: Removed direct Supabase calls
/// ✅ Uses SimpleApiClient ONLY
/// ✅ Uses SimpleAuthSystem for auth
///
/// Copied from [SellerRequestNotifier].
@ProviderFor(SellerRequestNotifier)
final sellerRequestNotifierProvider =
    AutoDisposeNotifierProvider<SellerRequestNotifier, bool>.internal(
      SellerRequestNotifier.new,
      name: r'sellerRequestNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerRequestNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SellerRequestNotifier = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';
import '../models/product_model.dart';

part 'clean_products_provider.g.dart';

final _logger = Logger('CleanProductsProvider');

/// Clean Products Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// Simple providers that use SimpleApiClient ONLY
/// No direct Supabase calls, no complex state management

/// All products provider with pagination support and fallback
@riverpod
Future<List<ProductModel>> allProducts(
  Ref ref, {
  int page = 1,
  int limit = 20,
}) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching products: page $page, limit $limit');
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/products',
      queryParameters: {
        'page': page,
        'limit': limit,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      // Provide more detailed error information
      final errorMessage = response.message ?? response.error ?? 'Unknown error';
      _logger.warning('Backend response: Success=${response.isSuccess}, Data=${response.data != null}, Error=$errorMessage');
      
      // Check if it's a network connectivity issue
      if (errorMessage.contains('Network request failed') || 
          errorMessage.contains('timeout') ||
          errorMessage.contains('connection')) {
        _logger.warning('Backend connectivity issue detected, returning fallback data');
        return _getFallbackProducts();
      }
      
      throw Exception('Failed to fetch products: $errorMessage');
    }

    final data = response.data!;
    
    // Handle different response formats from Go backend
    final List<dynamic> productsJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : throw Exception('Invalid response format');

    final products = productsJson
        .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
        .toList();
        
    _logger.info('Fetched ${products.length} products successfully');
    return products;
  } catch (e) {
    _logger.severe('Error fetching products: $e');
    
    // Return fallback data for network issues
    if (e.toString().contains('Network request failed') ||
        e.toString().contains('timeout') ||
        e.toString().contains('connection')) {
      _logger.info('Returning fallback products due to network issues');
      return _getFallbackProducts();
    }
    
    rethrow;
  }
}

/// Fallback products when backend is unavailable
List<ProductModel> _getFallbackProducts() {
  return [
    ProductModel(
      id: 'fallback-1',
      name: 'Premium Auto Part',
      description: 'High-quality automotive component for optimal performance',
      price: 99.99,
      sellerId: 'sample-seller',
      categoryId: 'auto-parts',
      images: ['https://via.placeholder.com/300x200?text=Auto+Part'],
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    ProductModel(
      id: 'fallback-2',
      name: 'Car Accessory Kit',
      description: 'Complete car accessory kit with premium materials',
      price: 149.99,
      sellerId: 'sample-seller-2',
      categoryId: 'accessories',
      images: ['https://via.placeholder.com/300x200?text=Accessory+Kit'],
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    ProductModel(
      id: 'fallback-3',
      name: 'Engine Maintenance Kit',
      description: 'Professional engine maintenance kit for all vehicle types',
      price: 199.99,
      sellerId: 'sample-seller-3',
      categoryId: 'maintenance',
      images: ['https://via.placeholder.com/300x200?text=Maintenance+Kit'],
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];
}

/// Single product by ID provider
@riverpod
Future<ProductModel?> productById(Ref ref, String productId) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching product: $productId');
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/products/$productId',
    );
    
    if (!response.isSuccess || response.data == null) {
      _logger.warning('Product not found: $productId');
      return null;
    }

    final data = response.data!;
    
    // Handle different response formats
    Map<String, dynamic> productJson;
    if (data.containsKey('data') && data['data'] is Map) {
      productJson = data['data'] as Map<String, dynamic>;
    } else    productJson = data;
  

    return ProductModel.fromJson(productJson);
  } catch (e) {
    _logger.severe('Error fetching product $productId: $e');
    rethrow;
  }
}

/// Products by category provider
@riverpod
Future<List<ProductModel>> productsByCategory(
  Ref ref,
  String categoryId, {
  int page = 1,
  int limit = 20,
}) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    _logger.info('Fetching products for category: $categoryId');
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/categories/$categoryId/products',
      queryParameters: {
        'page': page,
        'limit': limit,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      _logger.warning('No products found for category: $categoryId');
      return [];
    }

    final data = response.data!;
    
    final List<dynamic> productsJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    return productsJson
        .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    _logger.warning('Error fetching products for category $categoryId: $e');
    return [];
  }
}

/// Featured products provider
@riverpod
Future<List<ProductModel>> featuredProducts(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/products/featured',
      queryParameters: {'limit': 10},
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    
    final List<dynamic> productsJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    return productsJson
        .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    _logger.warning('Error fetching featured products: $e');
    return [];
  }
}

/// User's products provider (for sellers)
@riverpod
Future<List<ProductModel>> userProducts(Ref ref) async {
  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/user/products',
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    
    final List<dynamic> productsJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    return productsJson
        .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    _logger.warning('Error fetching user products: $e');
    return [];
  }
}

/// Search products provider
@riverpod
Future<List<ProductModel>> searchProducts(
  Ref ref,
  String query, {
  int page = 1,
  int limit = 20,
}) async {
  if (query.trim().isEmpty) {
    return [];
  }

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    
    final response = await apiClient.getApi<Map<String, dynamic>>(
      '/products/search',
      queryParameters: {
        'q': query,
        'page': page,
        'limit': limit,
      },
    );
    
    if (!response.isSuccess || response.data == null) {
      return [];
    }

    final data = response.data!;
    
    final List<dynamic> productsJson = data.containsKey('data') && data['data'] is List
        ? data['data'] as List<dynamic>
        : data is List
            ? data as List<dynamic>
            : [];

    return productsJson
        .map((json) => ProductModel.fromJson(json as Map<String, dynamic>))
        .toList();
  } catch (e) {
    _logger.warning('Error searching products: $e');
    return [];
  }
}

/// Product actions provider for CRUD operations
final productActionsProvider = Provider<ProductActions>((ref) {
  return ProductActions(ref);
});

/// Product actions class with clean error handling
class ProductActions {
  ProductActions(this._ref);
  
  final Ref _ref;
  
  /// Create a new product
  Future<ProductModel> createProduct(Map<String, dynamic> productData) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      _logger.info('Creating new product');
      
      final response = await apiClient.post<Map<String, dynamic>>(
        '/products',
        data: productData,
      );
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to create product: ${response.message}');
      }

      final data = response.data!;
      
      Map<String, dynamic> productJson;
      if (data.containsKey('data') && data['data'] is Map) {
        productJson = data['data'] as Map<String, dynamic>;
      } else      productJson = data;
    

      // Refresh relevant providers
      // Note: Provider invalidation will be added after code generation
      
      final createdProduct = ProductModel.fromJson(productJson);
      _logger.info('Successfully created product: ${createdProduct.id}');
      return createdProduct;
    } catch (e) {
      _logger.severe('Error creating product: $e');
      rethrow;
    }
  }
  
  /// Update an existing product
  Future<ProductModel> updateProduct(
    String productId,
    Map<String, dynamic> updateData,
  ) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.put<Map<String, dynamic>>(
        '/products/$productId',
        data: updateData,
      );
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to update product: ${response.message}');
      }

      final data = response.data!;
      
      Map<String, dynamic> productJson;
      if (data.containsKey('data') && data['data'] is Map) {
        productJson = Map<String, dynamic>.from(data['data'] as Map);
      } else      productJson = Map<String, dynamic>.from(data);
    

      // Refresh relevant providers
      // Note: Provider invalidation will be added after code generation
      
      return ProductModel.fromJson(productJson);
    } catch (e) {
      _logger.severe('Error updating product $productId: $e');
      rethrow;
    }
  }
  
  /// Delete a product
  Future<void> deleteProduct(String productId) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);
      
      final response = await apiClient.delete('/products/$productId');
      
      if (!response.isSuccess) {
        throw Exception('Failed to delete product: ${response.message}');
      }

      // Refresh relevant providers
      // Note: Provider invalidation will be added after code generation
      
      _logger.info('Successfully deleted product: $productId');
    } catch (e) {
      _logger.severe('Error deleting product $productId: $e');
      rethrow;
    }
  }
} 
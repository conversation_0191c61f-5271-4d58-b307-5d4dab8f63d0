// ignore_for_file: sort_constructors_first

import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'global_provider_observer.dart';
import 'lazy_provider_system.dart';

part 'provider_performance_monitor.g.dart';

/// ================================================================================================
/// PROVIDER PERFORMANCE MONITORING SYSTEM
/// ================================================================================================
/// 
/// Comprehensive monitoring system for provider performance, startup analytics,
/// and optimization metrics.
/// ================================================================================================

final _logger = Logger();

/// Provider performance metrics
class ProviderPerformanceMetrics {
  const ProviderPerformanceMetrics({
    required this.totalProviders,
    required this.criticalProviders,
    required this.lazyProviders,
    required this.deferredProviders,
    required this.initializationTime,
    required this.startupCompleted,
    required this.performanceScore,
    required this.optimizationLevel,
    required this.categoryBreakdown,
    this.recommendations = const [],
  });

  final int totalProviders;
  final int criticalProviders;
  final int lazyProviders;
  final int deferredProviders;
  final Duration initializationTime;
  final bool startupCompleted;
  final double performanceScore; // 0-100
  final String optimizationLevel; // 'excellent', 'good', 'fair', 'poor'
  final Map<String, int> categoryBreakdown;
  final List<String> recommendations;

  Map<String, dynamic> toJson() {
    return {
      'totalProviders': totalProviders,
      'criticalProviders': criticalProviders,
      'lazyProviders': lazyProviders,
      'deferredProviders': deferredProviders,
      'initializationTimeMs': initializationTime.inMilliseconds,
      'startupCompleted': startupCompleted,
      'performanceScore': performanceScore,
      'optimizationLevel': optimizationLevel,
      'categoryBreakdown': categoryBreakdown,
      'recommendations': recommendations,
    };
  }
}

/// Performance monitoring service
class ProviderPerformanceMonitor {

  factory ProviderPerformanceMonitor() => _instance;
  ProviderPerformanceMonitor._internal();

  static final _instance = ProviderPerformanceMonitor._internal();

  final Stopwatch _initStopwatch = Stopwatch();
  final List<String> _performanceEvents = [];
  bool _isMonitoring = false;

  /// Start performance monitoring
  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    _initStopwatch.start();
    _logEvent('performance_monitoring_started');
    
    if (kDebugMode) {
      _logger.i('📊 Provider performance monitoring started');
    }
  }

  /// Stop performance monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    _initStopwatch.stop();
    _logEvent('performance_monitoring_stopped');
    _isMonitoring = false;
    
    if (kDebugMode) {
      _logger.i('📊 Provider performance monitoring stopped after ${_initStopwatch.elapsedMilliseconds}ms');
    }
  }

  /// Log a performance event
  void _logEvent(String event) {
    _performanceEvents.add('${DateTime.now().millisecondsSinceEpoch}: $event');
  }

  /// Calculate performance metrics
  ProviderPerformanceMetrics calculateMetrics() {
    final summary = GlobalProviderObserver.getStartupSummary();
    final lazyStats = LazyProviderSystem.deferredProviderCount;
    final categories = GlobalProviderObserver.getProvidersByCategory();
    
    final totalProviders = summary['providerCount'] as int;
    final criticalProviders = _calculateCriticalProviders(categories);
    final initTime = _initStopwatch.elapsed;
    
    final performanceScore = _calculatePerformanceScore(
      totalProviders,
      criticalProviders,
      lazyStats,
      initTime,
    );
    
    final optimizationLevel = _getOptimizationLevel(performanceScore);
    final recommendations = _generateRecommendations(totalProviders, categories, performanceScore);

    return ProviderPerformanceMetrics(
      totalProviders: totalProviders,
      criticalProviders: criticalProviders,
      lazyProviders: lazyStats,
      deferredProviders: lazyStats,
      initializationTime: initTime,
      startupCompleted: !LazyProviderSystem.isStartupPhase,
      performanceScore: performanceScore,
      optimizationLevel: optimizationLevel,
      categoryBreakdown: Map.from(categories),
      recommendations: recommendations,
    );
  }

  /// Calculate critical provider count
  int _calculateCriticalProviders(Map<String, int> categories) {
    final authCount = categories['Authentication'] ?? 0;
    final dbCount = categories['Database/API'] ?? 0;
    final uiCount = categories['UI/Navigation'] ?? 0;
    return authCount + dbCount + uiCount;
  }

  /// Calculate performance score (0-100)
  double _calculatePerformanceScore(
    int totalProviders,
    int criticalProviders,
    int lazyProviders,
    Duration initTime,
  ) {
    double score = 100;

    // Penalize high total provider count
    if (totalProviders > 40) {
      score -= (totalProviders - 40) * 2; // -2 points per provider over 40
    }

    // Penalize too many critical providers
    if (criticalProviders > 15) {
      score -= (criticalProviders - 15) * 3; // -3 points per critical provider over 15
    }

    // Reward lazy loading implementation
    if (lazyProviders > 0) {
      score += lazyProviders * 1.5; // **** points per lazy provider
    }

    // Penalize slow initialization
    if (initTime.inMilliseconds > 2000) {
      score -= (initTime.inMilliseconds - 2000) / 100; // -1 point per 100ms over 2s
    }

    return score.clamp(0.0, 100.0);
  }

  /// Get optimization level based on score
  String _getOptimizationLevel(double score) {
    if (score >= 90) return 'excellent';
    if (score >= 75) return 'good';
    if (score >= 60) return 'fair';
    return 'poor';
  }

  /// Generate optimization recommendations
  List<String> _generateRecommendations(
    int totalProviders,
    Map<String, int> categories,
    double score,
  ) {
    final recommendations = <String>[];

    if (totalProviders > 35) {
      recommendations.add('Consider implementing more lazy loading for non-critical providers');
    }

    final authCount = categories['Authentication'] ?? 0;
    if (authCount > 5) {
      recommendations.add('Too many auth providers detected - consolidate auth system');
    }

    final businessCount = categories['Business Logic'] ?? 0;
    if (businessCount > 12) {
      recommendations.add('High business logic provider count - implement lazy initialization');
    }

    if (score < 70) {
      recommendations.add('Overall performance needs improvement - review provider architecture');
    }

    if (LazyProviderSystem.deferredProviderCount == 0) {
      recommendations.add('No lazy providers detected - implement lazy loading for better startup performance');
    }

    return recommendations;
  }

  /// Get performance events
  List<String> get performanceEvents => List.unmodifiable(_performanceEvents);

  /// Get current monitoring status
  bool get isMonitoring => _isMonitoring;

  /// Get initialization time
  Duration get initializationTime => _initStopwatch.elapsed;
}

/// ================================================================================================
/// PROVIDER PERFORMANCE PROVIDERS
/// ================================================================================================

/// Performance monitor provider
final performanceMonitorProvider = Provider<ProviderPerformanceMonitor>((ref) {
  return ProviderPerformanceMonitor();
});

/// Current performance metrics provider
final currentPerformanceMetricsProvider = Provider<ProviderPerformanceMetrics>((ref) {
  final monitor = ref.watch(performanceMonitorProvider);
  return monitor.calculateMetrics();
});

/// Performance analytics provider
final performanceAnalyticsProvider = Provider<Map<String, dynamic>>((ref) {
  final metrics = ref.watch(currentPerformanceMetricsProvider);
  final startupStats = ref.watch(lazyLoadingStatsProvider);
  
  return {
    'metrics': metrics.toJson(),
    'lazyLoading': startupStats,
    'optimization': {
      'score': metrics.performanceScore,
      'level': metrics.optimizationLevel,
      'recommendations': metrics.recommendations,
    },
    'summary': {
      'isOptimal': metrics.performanceScore >= 85,
      'needsImprovement': metrics.performanceScore < 70,
      'startupCompleted': metrics.startupCompleted,
    },
  };
});

/// Startup performance tracker - simplified
@riverpod
class StartupPerformanceTracker extends _$StartupPerformanceTracker {
  @override
  Map<String, dynamic> build() {
    _initializeTracking();
    return {};
  }

  void _initializeTracking() {
    final stopwatch = Stopwatch()..start();
    
    // Simple one-time tracking instead of periodic updates
    Timer(const Duration(seconds: 3), () {
      final summary = GlobalProviderObserver.getStartupSummary();
      final categories = GlobalProviderObserver.getProvidersByCategory();
      
      state = {
        'finalStartupTimeMs': stopwatch.elapsedMilliseconds,
        'startupCompleted': true,
        'providerCount': summary['providerCount'],
        'errorCount': summary['errorCount'],
        'categories': categories,
        'performanceGrade': _calculateStartupGrade(stopwatch.elapsedMilliseconds, summary['providerCount'] as int? ?? 0),
      };
      
      if (kDebugMode) {
        _logger.i('🏁 Startup performance: ${stopwatch.elapsedMilliseconds}ms, ${summary['providerCount']} providers');
      }
    });
  }

  String _calculateStartupGrade(int timeMs, int providerCount) {
    if (timeMs < 1500 && providerCount <= 30) return 'A+';
    if (timeMs < 2000 && providerCount <= 35) return 'A';
    if (timeMs < 3000 && providerCount <= 40) return 'B';
    if (timeMs < 4000 && providerCount <= 50) return 'C';
    return 'D';
  }
}



/// ================================================================================================
/// PERFORMANCE REPORTING
/// ================================================================================================

/// Generate performance report
String generatePerformanceReport(ProviderPerformanceMetrics metrics) {
  final buffer = StringBuffer();
  
  buffer.writeln('📊 PROVIDER PERFORMANCE REPORT');
  buffer.writeln('=' * 50);
  buffer.writeln('🏆 Performance Score: ${metrics.performanceScore.toStringAsFixed(1)}/100');
  buffer.writeln('📈 Optimization Level: ${metrics.optimizationLevel.toUpperCase()}');
  buffer.writeln('⏱️  Initialization Time: ${metrics.initializationTime.inMilliseconds}ms');
  buffer.writeln();
  
  buffer.writeln('📦 Provider Breakdown:');
  buffer.writeln('  • Total Providers: ${metrics.totalProviders}');
  buffer.writeln('  • Critical Providers: ${metrics.criticalProviders}');
  buffer.writeln('  • Lazy Providers: ${metrics.lazyProviders}');
  buffer.writeln('  • Deferred Providers: ${metrics.deferredProviders}');
  buffer.writeln();
  
  if (metrics.categoryBreakdown.isNotEmpty) {
    buffer.writeln('📂 Category Breakdown:');
    final sortedCategories = metrics.categoryBreakdown.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    for (final category in sortedCategories) {
      buffer.writeln('  • ${category.key}: ${category.value}');
    }
    buffer.writeln();
  }
  
  if (metrics.recommendations.isNotEmpty) {
    buffer.writeln('💡 Optimization Recommendations:');
    for (final recommendation in metrics.recommendations) {
      buffer.writeln('  • $recommendation');
    }
    buffer.writeln();
  }
  
  buffer.writeln('✅ Startup Completed: ${metrics.startupCompleted ? "Yes" : "No"}');
  buffer.writeln('=' * 50);
  
  return buffer.toString();
}
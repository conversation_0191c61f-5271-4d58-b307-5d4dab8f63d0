import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/part_language_model.dart';

part 'part_language_provider.g.dart';

const String _partLanguagePrefKey = 'partNameLanguage';

@riverpod
class PartLanguageNotifier extends _$PartLanguageNotifier {
  late SharedPreferences _prefs;

  @override
  Future<PartLanguage> build() async {
    _prefs = await SharedPreferences.getInstance();
    final langCode = _prefs.getString(_partLanguagePrefKey);
    return PartLanguage.fromCode(langCode ?? 'ar'); // Default to Arabic
  }

  Future<void> setLanguage(PartLanguage newLanguage) async {
    state = AsyncData(newLanguage);
    await _prefs.setString(_partLanguagePrefKey, newLanguage.code);
  }
}

import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

final _logger = Logger(
  printer: PrettyPrinter(methodCount: 0, errorMethodCount: 5, lineLength: 50),
);

/// نموذج عنصر التخزين المؤقت
class CacheItem {
  const CacheItem({
    required this.data,
    required this.timestamp,
    required this.ttl,
  });

  factory CacheItem.fromJson(Map<String, dynamic> json) => CacheItem(
    data: json['data'],
    timestamp: DateTime.parse(json['timestamp'] as String),
    ttl: Duration(milliseconds: json['ttl'] as int),
  );
  final dynamic data;
  final DateTime timestamp;
  final Duration ttl;

  bool get isExpired => DateTime.now().difference(timestamp) > ttl;

  Map<String, dynamic> toJson() => {
    'data': data,
    'timestamp': timestamp.toIso8601String(),
    'ttl': ttl.inMilliseconds,
  };
}

/// مدير التخزين المؤقت
class CacheManager {
  static const String _keyPrefix = 'carnow_cache_';
  static const Duration _defaultTtl = Duration(minutes: 5);

  SharedPreferences? _prefs;
  final Map<String, CacheItem> _memoryCache = {};

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _logger.i('CacheManager initialized');
  }

  /// حفظ البيانات في التخزين المؤقت
  Future<void> set(
    String key,
    dynamic data, {
    Duration? ttl,
    bool persistToDisk = false,
  }) async {
    final cacheKey = _keyPrefix + key;
    final item = CacheItem(
      data: data,
      timestamp: DateTime.now(),
      ttl: ttl ?? _defaultTtl,
    );

    // حفظ في الذاكرة
    _memoryCache[cacheKey] = item;

    // حفظ في التخزين الدائم إذا طُلب
    if (persistToDisk && _prefs != null) {
      try {
        final jsonString = jsonEncode(item.toJson());
        await _prefs!.setString(cacheKey, jsonString);
        _logger.d('Cached to disk: $key');
      } catch (e) {
        _logger.w('Failed to cache to disk: $key', error: e);
      }
    }

    _logger.d('Cached in memory: $key');
  }

  /// استرجاع البيانات من التخزين المؤقت
  Future<T?> get<T>(String key) async {
    final cacheKey = _keyPrefix + key;

    // البحث في الذاكرة أولاً
    if (_memoryCache.containsKey(cacheKey)) {
      final item = _memoryCache[cacheKey]!;
      if (!item.isExpired) {
        _logger.d('Cache hit (memory): $key');
        return item.data as T?;
      } else {
        _memoryCache.remove(cacheKey);
        _logger.d('Cache expired (memory): $key');
      }
    }

    // البحث في التخزين الدائم
    if (_prefs != null) {
      try {
        final jsonString = _prefs!.getString(cacheKey);
        if (jsonString != null) {
          final json = jsonDecode(jsonString);
          final item = CacheItem.fromJson(json as Map<String, dynamic>);

          if (!item.isExpired) {
            // إعادة تحميل في الذاكرة
            _memoryCache[cacheKey] = item;
            _logger.d('Cache hit (disk): $key');
            return item.data as T?;
          } else {
            await _prefs!.remove(cacheKey);
            _logger.d('Cache expired (disk): $key');
          }
        }
      } catch (e) {
        _logger.w('Failed to read from disk cache: $key', error: e);
      }
    }

    _logger.d('Cache miss: $key');
    return null;
  }

  /// حذف عنصر من التخزين المؤقت
  Future<void> remove(String key) async {
    final cacheKey = _keyPrefix + key;

    _memoryCache.remove(cacheKey);

    if (_prefs != null) {
      await _prefs!.remove(cacheKey);
    }

    _logger.d('Cache removed: $key');
  }

  /// مسح جميع البيانات المؤقتة
  Future<void> clear() async {
    _memoryCache.clear();

    if (_prefs != null) {
      final keys = _prefs!
          .getKeys()
          .where((key) => key.startsWith(_keyPrefix))
          .toList();

      for (final key in keys) {
        await _prefs!.remove(key);
      }
    }

    _logger.i('All cache cleared');
  }

  /// مسح البيانات المنتهية الصلاحية
  Future<void> cleanExpired() async {
    final expiredKeys = <String>[];

    // تنظيف الذاكرة
    _memoryCache.removeWhere((key, item) {
      if (item.isExpired) {
        expiredKeys.add(key);
        return true;
      }
      return false;
    });

    // تنظيف التخزين الدائم
    if (_prefs != null) {
      final keys = _prefs!
          .getKeys()
          .where((key) => key.startsWith(_keyPrefix))
          .toList();

      for (final key in keys) {
        try {
          final jsonString = _prefs!.getString(key);
          if (jsonString != null) {
            final json = jsonDecode(jsonString);
            final timestamp = DateTime.parse(json['timestamp'] as String);
            final ttl = Duration(milliseconds: json['ttl'] as int);

            if (DateTime.now().difference(timestamp) > ttl) {
              await _prefs!.remove(key);
              expiredKeys.add(key);
            }
          }
        } catch (e) {
          // إزالة البيانات التالفة
          await _prefs!.remove(key);
          expiredKeys.add(key);
        }
      }
    }

    if (expiredKeys.isNotEmpty) {
      _logger.i('Cleaned ${expiredKeys.length} expired cache items');
    }
  }

  /// الحصول على إحصائيات التخزين المؤقت
  Map<String, dynamic> getStats() {
    final memoryCount = _memoryCache.length;
    final memorySize = _memoryCache.values
        .map((item) => jsonEncode(item.toJson()).length)
        .fold(0, (a, b) => a + b);

    return {
      'memory_items': memoryCount,
      'memory_size_bytes': memorySize,
      'memory_size_kb': (memorySize / 1024).toStringAsFixed(2),
    };
  }
}

/// مزود مدير التخزين المؤقت
final cacheManagerProvider = Provider<CacheManager>((ref) {
  final manager = CacheManager();
  manager.init();
  return manager;
});

/// مزود لتنظيف التخزين المؤقت دورياً
final cacheCleanerProvider = Provider<Timer>((ref) {
  final cacheManager = ref.watch(cacheManagerProvider);

  // تنظيف كل 30 دقيقة
  final timer = Timer.periodic(const Duration(minutes: 30), (timer) async {
    try {
      await cacheManager.cleanExpired();
      _logger.i('Periodic cache cleanup completed');
    } catch (e) {
      _logger.e('Periodic cache cleanup failed', error: e);
    }
  });

  ref.onDispose(timer.cancel);

  return timer;
});

/// مزود للبيانات المؤقتة مع جلب تلقائي
/// Factory function to create a cached data provider using composition instead of inheritance
FutureProvider<T> createCachedDataProvider<T>({
  required String cacheKey,
  required Future<T> Function() fetcher,
  Duration ttl = const Duration(minutes: 5),
}) => FutureProvider<T>((ref) async {
  final cacheManager = ref.watch(cacheManagerProvider);

  // محاولة الحصول على البيانات من التخزين المؤقت
  final cached = await cacheManager.get<T>(cacheKey);

  if (cached != null) {
    return cached;
  }

  // جلب البيانات الجديدة
  try {
    final data = await fetcher();
    await cacheManager.set(cacheKey, data, ttl: ttl);
    return data;
  } catch (e) {
    _logger.e('Failed to fetch data for key: $cacheKey', error: e);
    rethrow;
  }
});

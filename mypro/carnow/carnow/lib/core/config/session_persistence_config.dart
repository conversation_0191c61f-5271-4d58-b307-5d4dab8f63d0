// =============================================================================
// Session Persistence Configuration - CarNow
// إعدادات استمرارية الجلسة - كار ناو
// =============================================================================

/// Configuration class for session persistence settings
/// فئة التكوين لإعدادات استمرارية الجلسة
class SessionPersistenceConfig {
  // Token Expiry Settings - إعدادات انتهاء صلاحية التوكن
  static const Duration accessTokenExpiry = Duration(days: 7);
  static const Duration refreshTokenExpiry = Duration(days: 30);
  
  // Session Management Settings - إعدادات إدارة الجلسة
  static const Duration sessionTimeout = Duration(days: 7);
  static const Duration heartbeatInterval = Duration(hours: 1);
  static const Duration inactivityWarning = Duration(days: 6, hours: 23);
  
  // Token Refresh Settings - إعدادات تحديث التوكن
  static const Duration tokenRefreshInterval = Duration(days: 6);
  static const Duration tokenRefreshThreshold = Duration(days: 1);
  
  // Storage Settings - إعدادات التخزين
  static const bool enableTokenEncryption = true;
  static const bool requireBiometricAuth = false;
  static const bool enableSessionExtension = true;
  
  // Monitoring Settings - إعدادات المراقبة
  static const Duration sessionCheckInterval = Duration(hours: 1);
  static const bool enableSessionLogging = true;
  static const bool enableTokenLifecycleLogging = true;
  
  /// Get token expiry date from now
  /// الحصول على تاريخ انتهاء صلاحية التوكن من الآن
  static DateTime getAccessTokenExpiryFromNow() {
    return DateTime.now().add(accessTokenExpiry);
  }
  
  /// Get refresh token expiry date from now
  /// الحصول على تاريخ انتهاء صلاحية توكن التحديث من الآن
  static DateTime getRefreshTokenExpiryFromNow() {
    return DateTime.now().add(refreshTokenExpiry);
  }
  
  /// Check if token should be refreshed
  /// التحقق من ضرورة تحديث التوكن
  static bool shouldRefreshToken(DateTime tokenExpiry) {
    final now = DateTime.now();
    final timeUntilExpiry = tokenExpiry.difference(now);
    return timeUntilExpiry <= tokenRefreshThreshold;
  }
  
  /// Get session expiry date from last activity
  /// الحصول على تاريخ انتهاء الجلسة من آخر نشاط
  static DateTime getSessionExpiryFromActivity(DateTime lastActivity) {
    return lastActivity.add(sessionTimeout);
  }
  
  /// Check if session is expired
  /// التحقق من انتهاء صلاحية الجلسة
  static bool isSessionExpired(DateTime lastActivity) {
    final now = DateTime.now();
    final sessionExpiry = getSessionExpiryFromActivity(lastActivity);
    return now.isAfter(sessionExpiry);
  }
  
  /// Get time until session expires
  /// الحصول على الوقت المتبقي حتى انتهاء الجلسة
  static Duration? getTimeUntilSessionExpiry(DateTime lastActivity) {
    final now = DateTime.now();
    final sessionExpiry = getSessionExpiryFromActivity(lastActivity);
    
    if (now.isAfter(sessionExpiry)) {
      return Duration.zero;
    }
    
    return sessionExpiry.difference(now);
  }
  
  /// Check if session warning should be shown
  /// التحقق من ضرورة إظهار تحذير الجلسة
  static bool shouldShowSessionWarning(DateTime lastActivity) {
    final timeUntilExpiry = getTimeUntilSessionExpiry(lastActivity);
    if (timeUntilExpiry == null) return false;
    
    return timeUntilExpiry <= const Duration(hours: 1);
  }
  
  /// Get configuration summary for debugging
  /// الحصول على ملخص التكوين للتصحيح
  static Map<String, dynamic> getConfigSummary() {
    return {
      'access_token_expiry_days': accessTokenExpiry.inDays,
      'refresh_token_expiry_days': refreshTokenExpiry.inDays,
      'session_timeout_days': sessionTimeout.inDays,
      'heartbeat_interval_hours': heartbeatInterval.inHours,
      'token_refresh_interval_days': tokenRefreshInterval.inDays,
      'token_refresh_threshold_days': tokenRefreshThreshold.inDays,
      'enable_token_encryption': enableTokenEncryption,
      'require_biometric_auth': requireBiometricAuth,
      'enable_session_extension': enableSessionExtension,
      'session_check_interval_hours': sessionCheckInterval.inHours,
      'enable_session_logging': enableSessionLogging,
      'enable_token_lifecycle_logging': enableTokenLifecycleLogging,
    };
  }
  
  /// Print configuration summary
  /// طباعة ملخص التكوين
  static void printConfigSummary() {
    final config = getConfigSummary();
    print('=== CarNow Session Persistence Configuration ===');
    config.forEach((key, value) {
      print('$key: $value');
    });
    print('===============================================');
  }
}

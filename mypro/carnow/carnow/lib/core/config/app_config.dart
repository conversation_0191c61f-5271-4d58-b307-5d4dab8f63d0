class AppConfig {
  // Performance Configuration
  static const int cacheMaxSize = 100 * 1024 * 1024; // 100MB
  static const Duration cacheDuration = Duration(hours: 24);
  static const int maxConcurrentRequests = 10;
  static const Duration requestTimeout = Duration(seconds: 30);

  // Analytics Configuration
  static const bool analyticsEnabled = true;
  static const Duration analyticsFlushInterval = Duration(minutes: 5);
  static const int maxAnalyticsEvents = 1000;

  // Database Configuration
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  static const Duration dbConnectionTimeout = Duration(seconds: 10);

  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageFormats = [
    'jpg',
    'jpeg',
    'png',
    'webp',
  ];
  static const int thumbnailSize = 300;

  // Search Configuration
  static const int searchDebounceMs = 500;
  static const int maxSearchHistory = 10;
  static const int searchResultsLimit = 50;

  // Initialization flag to ensure the initializer runs only once
  static bool _initialized = false;

  /// Initializes global application configuration.
  /// Currently this is a lightweight placeholder but allows asynchronous
  /// setup (e.g. remote config, Sentry) without blocking startup.
  static Future<void> initialize() async {
    if (_initialized) return;
    // TODO: Add actual initialization logic here when needed.
    _initialized = true;
  }
}

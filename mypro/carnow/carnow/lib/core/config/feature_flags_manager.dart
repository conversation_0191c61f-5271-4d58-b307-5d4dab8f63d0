import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'environment_config.dart';

/// Feature flag types for type-safe feature flag management
enum FeatureFlag {
  enhancedSecurity('enhanced_security'),
  rateLimiting('rate_limiting'),
  advancedMonitoring('advanced_monitoring'),
  arabicLocalization('arabic_localization'),
  accessibilityFeatures('accessibility_features'),
  biometricAuth('biometric_auth'),
  googleAuth('google_auth'),
  emailAuth('email_auth'),
  debugLogging('debug_logging'),
  analytics('analytics'),
  crashReporting('crash_reporting'),
  offlineMode('offline_mode'),
  darkMode('dark_mode'),
  hapticFeedback('haptic_feedback'),
  pushNotifications('push_notifications'),
  autoBackup('auto_backup'),
  advancedValidation('advanced_validation'),
  performanceOptimizations('performance_optimizations'),
  experimentalFeatures('experimental_features'),
  betaFeatures('beta_features');

  const FeatureFlag(this.key);
  final String key;
}

/// Feature flag configuration with metadata
class FeatureFlagConfig {
  final String key;
  final bool defaultValue;
  final String description;
  final List<Environment> enabledEnvironments;
  final String? rolloutPercentage;
  final DateTime? expiryDate;
  final Map<String, dynamic> metadata;

  const FeatureFlagConfig({
    required this.key,
    required this.defaultValue,
    required this.description,
    this.enabledEnvironments = const [],
    this.rolloutPercentage,
    this.expiryDate,
    this.metadata = const {},
  });

  /// Check if feature is enabled for current environment
  bool isEnabledForEnvironment(Environment environment) {
    if (enabledEnvironments.isEmpty) return true;
    return enabledEnvironments.contains(environment);
  }

  /// Check if feature has expired
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'defaultValue': defaultValue,
      'description': description,
      'enabledEnvironments': enabledEnvironments.map((e) => e.name).toList(),
      'rolloutPercentage': rolloutPercentage,
      'expiryDate': expiryDate?.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory FeatureFlagConfig.fromJson(Map<String, dynamic> json) {
    return FeatureFlagConfig(
      key: json['key'] ?? '',
      defaultValue: json['defaultValue'] ?? false,
      description: json['description'] ?? '',
      enabledEnvironments: (json['enabledEnvironments'] as List<dynamic>?)
          ?.map((e) => Environment.values.firstWhere((env) => env.name == e))
          .toList() ?? [],
      rolloutPercentage: json['rolloutPercentage'],
      expiryDate: json['expiryDate'] != null 
          ? DateTime.parse(json['expiryDate'])
          : null,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }
}

/// Feature flags manager for CarNow authentication system
class FeatureFlagsManager {
  static const String _prefsKey = 'carnow_feature_flags';
  static const String _overridesKey = 'carnow_feature_flag_overrides';
  
  static FeatureFlagsManager? _instance;
  static FeatureFlagsManager get instance => _instance ??= FeatureFlagsManager._();

  FeatureFlagsManager._();

  SharedPreferences? _prefs;
  final Map<String, bool> _overrides = {};
  final Map<String, FeatureFlagConfig> _configs = {};
  bool _isInitialized = false;

  /// Initialize feature flags manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    _prefs = await SharedPreferences.getInstance();
    await _loadOverrides();
    _initializeDefaultConfigs();
    _isInitialized = true;
  }

  /// Initialize default feature flag configurations
  void _initializeDefaultConfigs() {
    final configs = [
      FeatureFlagConfig(
        key: FeatureFlag.enhancedSecurity.key,
        defaultValue: true,
        description: 'Enhanced security features including advanced validation and monitoring',
        enabledEnvironments: [Environment.staging, Environment.production],
      ),
      FeatureFlagConfig(
        key: FeatureFlag.rateLimiting.key,
        defaultValue: false,
        description: 'Rate limiting for API requests and authentication attempts',
        enabledEnvironments: [Environment.staging, Environment.production],
      ),
      FeatureFlagConfig(
        key: FeatureFlag.advancedMonitoring.key,
        defaultValue: true,
        description: 'Advanced monitoring and analytics for authentication flows',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.arabicLocalization.key,
        defaultValue: true,
        description: 'Arabic language support and RTL layout',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.accessibilityFeatures.key,
        defaultValue: true,
        description: 'Accessibility features including screen reader support and high contrast mode',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.biometricAuth.key,
        defaultValue: false,
        description: 'Biometric authentication (fingerprint, face recognition)',
        enabledEnvironments: [Environment.development, Environment.staging],
      ),
      FeatureFlagConfig(
        key: FeatureFlag.googleAuth.key,
        defaultValue: true,
        description: 'Google OAuth authentication',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.emailAuth.key,
        defaultValue: true,
        description: 'Email and password authentication',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.debugLogging.key,
        defaultValue: kDebugMode,
        description: 'Debug logging and detailed error reporting',
        enabledEnvironments: [Environment.development, Environment.testing],
      ),
      FeatureFlagConfig(
        key: FeatureFlag.analytics.key,
        defaultValue: true,
        description: 'Analytics and usage tracking',
        enabledEnvironments: [Environment.staging, Environment.production],
      ),
      FeatureFlagConfig(
        key: FeatureFlag.crashReporting.key,
        defaultValue: true,
        description: 'Crash reporting and error tracking',
        enabledEnvironments: [Environment.staging, Environment.production],
      ),
      FeatureFlagConfig(
        key: FeatureFlag.offlineMode.key,
        defaultValue: false,
        description: 'Offline mode support with local caching',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.darkMode.key,
        defaultValue: true,
        description: 'Dark mode theme support',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.hapticFeedback.key,
        defaultValue: true,
        description: 'Haptic feedback for user interactions',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.pushNotifications.key,
        defaultValue: false,
        description: 'Push notifications for authentication events',
        enabledEnvironments: [Environment.production],
      ),
      FeatureFlagConfig(
        key: FeatureFlag.autoBackup.key,
        defaultValue: false,
        description: 'Automatic backup of user preferences and settings',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.advancedValidation.key,
        defaultValue: true,
        description: 'Advanced form validation with real-time feedback',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.performanceOptimizations.key,
        defaultValue: true,
        description: 'Performance optimizations and caching',
      ),
      FeatureFlagConfig(
        key: FeatureFlag.experimentalFeatures.key,
        defaultValue: false,
        description: 'Experimental features for testing',
        enabledEnvironments: [Environment.development],
        expiryDate: DateTime.now().add(const Duration(days: 30)),
      ),
      FeatureFlagConfig(
        key: FeatureFlag.betaFeatures.key,
        defaultValue: false,
        description: 'Beta features for early access users',
        enabledEnvironments: [Environment.development, Environment.staging],
      ),
    ];

    for (final config in configs) {
      _configs[config.key] = config;
    }
  }

  /// Load feature flag overrides from preferences
  Future<void> _loadOverrides() async {
    final overridesJson = _prefs?.getString(_overridesKey);
    if (overridesJson != null) {
      try {
        final overridesMap = Map<String, dynamic>.from(jsonDecode(overridesJson));
        _overrides.addAll(overridesMap.cast<String, bool>());
      } catch (e) {
        if (kDebugMode) {
          print('Failed to load feature flag overrides: $e');
        }
      }
    }
  }

  /// Save feature flag overrides to preferences
  Future<void> _saveOverrides() async {
    try {
      await _prefs?.setString(_overridesKey, jsonEncode(_overrides));
    } catch (e) {
      if (kDebugMode) {
        print('Failed to save feature flag overrides: $e');
      }
    }
  }

  /// Check if a feature flag is enabled
  bool isEnabled(FeatureFlag flag) {
    _ensureInitialized();
    
    final config = _configs[flag.key];
    if (config == null) {
      if (kDebugMode) {
        print('Feature flag config not found: ${flag.key}');
      }
      return false;
    }

    // Check if feature has expired
    if (config.isExpired) {
      return false;
    }

    // Check environment restrictions
    final currentEnv = ConfigurationManager.current.environment;
    if (!config.isEnabledForEnvironment(currentEnv)) {
      return false;
    }

    // Check for override first
    if (_overrides.containsKey(flag.key)) {
      return _overrides[flag.key]!;
    }

    // Check environment config
    final envConfig = ConfigurationManager.current;
    if (envConfig.featureFlags.containsKey(flag.key)) {
      return envConfig.featureFlags[flag.key] as bool;
    }

    // Return default value
    return config.defaultValue;
  }

  /// Set feature flag override
  Future<void> setOverride(FeatureFlag flag, bool value) async {
    _ensureInitialized();
    
    if (kDebugMode || ConfigurationManager.current.environment == Environment.testing) {
      _overrides[flag.key] = value;
      await _saveOverrides();
    } else {
      throw UnsupportedError('Feature flag overrides are only allowed in debug or testing mode');
    }
  }

  /// Remove feature flag override
  Future<void> removeOverride(FeatureFlag flag) async {
    _ensureInitialized();
    
    if (kDebugMode || ConfigurationManager.current.environment == Environment.testing) {
      _overrides.remove(flag.key);
      await _saveOverrides();
    }
  }

  /// Clear all overrides
  Future<void> clearAllOverrides() async {
    _ensureInitialized();
    
    if (kDebugMode || ConfigurationManager.current.environment == Environment.testing) {
      _overrides.clear();
      await _saveOverrides();
    }
  }

  /// Get all feature flags with their current values
  Map<String, bool> getAllFlags() {
    _ensureInitialized();
    
    final result = <String, bool>{};
    for (final flag in FeatureFlag.values) {
      result[flag.key] = isEnabled(flag);
    }
    return result;
  }

  /// Get feature flag configuration
  FeatureFlagConfig? getConfig(FeatureFlag flag) {
    _ensureInitialized();
    return _configs[flag.key];
  }

  /// Get all feature flag configurations
  Map<String, FeatureFlagConfig> getAllConfigs() {
    _ensureInitialized();
    return Map.unmodifiable(_configs);
  }

  /// Get feature flags status for debugging
  Map<String, dynamic> getStatus() {
    _ensureInitialized();
    
    final currentEnv = ConfigurationManager.current.environment;
    final flags = <String, Map<String, dynamic>>{};
    
    for (final flag in FeatureFlag.values) {
      final config = _configs[flag.key];
      if (config != null) {
        flags[flag.key] = {
          'enabled': isEnabled(flag),
          'default_value': config.defaultValue,
          'has_override': _overrides.containsKey(flag.key),
          'override_value': _overrides[flag.key],
          'environment_enabled': config.isEnabledForEnvironment(currentEnv),
          'expired': config.isExpired,
          'description': config.description,
        };
      }
    }
    
    return {
      'environment': currentEnv.name,
      'total_flags': FeatureFlag.values.length,
      'enabled_flags': getAllFlags().values.where((v) => v).length,
      'overrides_count': _overrides.length,
      'flags': flags,
    };
  }

  /// Refresh feature flags from remote configuration (placeholder for future implementation)
  Future<void> refresh() async {
    _ensureInitialized();
    
    // In the future, this could fetch feature flags from a remote service
    // For now, we just reload from local preferences
    await _loadOverrides();
  }

  /// Export feature flag configuration
  Map<String, dynamic> exportConfiguration() {
    _ensureInitialized();
    
    return {
      'configs': _configs.map((key, config) => MapEntry(key, config.toJson())),
      'overrides': Map<String, bool>.from(_overrides),
      'exported_at': DateTime.now().toIso8601String(),
      'environment': ConfigurationManager.current.environment.name,
    };
  }

  /// Import feature flag configuration
  Future<void> importConfiguration(Map<String, dynamic> data) async {
    if (kDebugMode || ConfigurationManager.current.environment == Environment.testing) {
      if (data['configs'] != null) {
        final configsData = Map<String, dynamic>.from(data['configs']);
        for (final entry in configsData.entries) {
          _configs[entry.key] = FeatureFlagConfig.fromJson(entry.value);
        }
      }
      
      if (data['overrides'] != null) {
        final overridesData = Map<String, bool>.from(data['overrides']);
        _overrides.addAll(overridesData);
        await _saveOverrides();
      }
    }
  }

  /// Reset to default configuration
  Future<void> reset() async {
    _overrides.clear();
    _configs.clear();
    await _prefs?.remove(_overridesKey);
    _initializeDefaultConfigs();
  }

  /// Ensure manager is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('FeatureFlagsManager not initialized. Call initialize() first.');
    }
  }
}

/// Feature flag helper extensions
extension FeatureFlagExtensions on FeatureFlag {
  /// Check if this feature flag is enabled
  bool get isEnabled => FeatureFlagsManager.instance.isEnabled(this);
  
  /// Get configuration for this feature flag
  FeatureFlagConfig? get config => FeatureFlagsManager.instance.getConfig(this);
}

/// Authentication-specific feature flag helpers
class AuthFeatureFlags {
  static bool get isGoogleAuthEnabled => FeatureFlag.googleAuth.isEnabled;
  static bool get isEmailAuthEnabled => FeatureFlag.emailAuth.isEnabled;
  static bool get isBiometricAuthEnabled => FeatureFlag.biometricAuth.isEnabled;
  static bool get isEnhancedSecurityEnabled => FeatureFlag.enhancedSecurity.isEnabled;
  static bool get isRateLimitingEnabled => FeatureFlag.rateLimiting.isEnabled;
  static bool get isAdvancedMonitoringEnabled => FeatureFlag.advancedMonitoring.isEnabled;
  static bool get isArabicLocalizationEnabled => FeatureFlag.arabicLocalization.isEnabled;
  static bool get areAccessibilityFeaturesEnabled => FeatureFlag.accessibilityFeatures.isEnabled;
  static bool get isDebugLoggingEnabled => FeatureFlag.debugLogging.isEnabled;
  static bool get isAnalyticsEnabled => FeatureFlag.analytics.isEnabled;
  static bool get isCrashReportingEnabled => FeatureFlag.crashReporting.isEnabled;
  static bool get isHapticFeedbackEnabled => FeatureFlag.hapticFeedback.isEnabled;
  static bool get isAdvancedValidationEnabled => FeatureFlag.advancedValidation.isEnabled;
  static bool get arePerformanceOptimizationsEnabled => FeatureFlag.performanceOptimizations.isEnabled;

  /// Get all authentication-related feature flags
  static Map<String, bool> getAllAuthFlags() {
    return {
      'google_auth': isGoogleAuthEnabled,
      'email_auth': isEmailAuthEnabled,
      'biometric_auth': isBiometricAuthEnabled,
      'enhanced_security': isEnhancedSecurityEnabled,
      'rate_limiting': isRateLimitingEnabled,
      'advanced_monitoring': isAdvancedMonitoringEnabled,
      'arabic_localization': isArabicLocalizationEnabled,
      'accessibility_features': areAccessibilityFeaturesEnabled,
      'debug_logging': isDebugLoggingEnabled,
      'analytics': isAnalyticsEnabled,
      'crash_reporting': isCrashReportingEnabled,
      'haptic_feedback': isHapticFeedbackEnabled,
      'advanced_validation': isAdvancedValidationEnabled,
      'performance_optimizations': arePerformanceOptimizationsEnabled,
    };
  }
}

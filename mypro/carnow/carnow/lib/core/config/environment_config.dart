import 'dart:io';
import 'package:flutter/foundation.dart';

/// Environment types for the CarNow application
enum Environment {
  development,
  staging,
  production,
  testing,
}

/// Environment-specific configuration for CarNow authentication system
class EnvironmentConfig {
  final Environment environment;
  final String apiBaseUrl;
  final String supabaseUrl;
  final String supabaseAnonKey;
  final String googleClientId;
  final bool enableGoogleAuth;
  final bool enableEmailAuth;
  final bool enableBiometricAuth;
  final bool enableDebugLogging;
  final bool enableAnalytics;
  final bool enableCrashReporting;
  final int apiTimeoutSeconds;
  final int tokenRefreshThresholdMinutes;
  final String encryptionKey;
  final Map<String, dynamic> featureFlags;

  const EnvironmentConfig({
    required this.environment,
    required this.apiBaseUrl,
    required this.supabaseUrl,
    required this.supabaseAnonKey,
    required this.googleClientId,
    this.enableGoogleAuth = true,
    this.enableEmailAuth = true,
    this.enableBiometricAuth = false,
    this.enableDebugLogging = false,
    this.enableAnalytics = true,
    this.enableCrashReporting = true,
    this.apiTimeoutSeconds = 30,
    this.tokenRefreshThresholdMinutes = 5,
    required this.encryptionKey,
    this.featureFlags = const {},
  });

  /// Creates configuration for development environment
  static const EnvironmentConfig development = EnvironmentConfig(
    environment: Environment.development,
    apiBaseUrl: 'https://backend-go-8klm.onrender.com/api/v1',
    supabaseUrl: 'https://lpxtghyvxuenyyisrrro.supabase.co',
    supabaseAnonKey: 'DEV_ANON_KEY_PLACEHOLDER',
    googleClientId: 'DEV_GOOGLE_CLIENT_ID_PLACEHOLDER',
    enableGoogleAuth: true,
    enableEmailAuth: true,
    enableBiometricAuth: true,
    enableDebugLogging: true,
    enableAnalytics: false,
    enableCrashReporting: false,
    apiTimeoutSeconds: 60,
    tokenRefreshThresholdMinutes: 10,
    encryptionKey: 'DEV_ENCRYPTION_KEY_PLACEHOLDER',
    featureFlags: {
      'enhanced_security': true,
      'rate_limiting': false,
      'advanced_monitoring': true,
      'arabic_localization': true,
      'accessibility_features': true,
    },
  );

  /// Creates configuration for staging environment
  static const EnvironmentConfig staging = EnvironmentConfig(
    environment: Environment.staging,
    apiBaseUrl: 'https://staging-backend-go-8klm.onrender.com/api/v1',
    supabaseUrl: 'https://lpxtghyvxuenyyisrrro.supabase.co',
    supabaseAnonKey: 'STAGING_ANON_KEY_PLACEHOLDER',
    googleClientId: 'STAGING_GOOGLE_CLIENT_ID_PLACEHOLDER',
    enableGoogleAuth: true,
    enableEmailAuth: true,
    enableBiometricAuth: true,
    enableDebugLogging: true,
    enableAnalytics: true,
    enableCrashReporting: true,
    apiTimeoutSeconds: 45,
    tokenRefreshThresholdMinutes: 5,
    encryptionKey: 'STAGING_ENCRYPTION_KEY_PLACEHOLDER',
    featureFlags: {
      'enhanced_security': true,
      'rate_limiting': true,
      'advanced_monitoring': true,
      'arabic_localization': true,
      'accessibility_features': true,
    },
  );

  /// Creates configuration for production environment
  static const EnvironmentConfig production = EnvironmentConfig(
    environment: Environment.production,
    apiBaseUrl: 'https://backend-go-8klm.onrender.com/api/v1',
    supabaseUrl: 'https://lpxtghyvxuenyyisrrro.supabase.co',
    supabaseAnonKey: 'PRODUCTION_ANON_KEY_PLACEHOLDER',
    googleClientId: 'PRODUCTION_GOOGLE_CLIENT_ID_PLACEHOLDER',
    enableGoogleAuth: true,
    enableEmailAuth: true,
    enableBiometricAuth: true,
    enableDebugLogging: false,
    enableAnalytics: true,
    enableCrashReporting: true,
    apiTimeoutSeconds: 30,
    tokenRefreshThresholdMinutes: 5,
    encryptionKey: 'PRODUCTION_ENCRYPTION_KEY_PLACEHOLDER',
    featureFlags: {
      'enhanced_security': true,
      'rate_limiting': true,
      'advanced_monitoring': true,
      'arabic_localization': true,
      'accessibility_features': true,
    },
  );

  /// Creates configuration for testing environment
  static const EnvironmentConfig testing = EnvironmentConfig(
    environment: Environment.testing,
    apiBaseUrl: 'https://backend-go-8klm.onrender.com/api/v1',
    supabaseUrl: 'https://test.supabase.co',
    supabaseAnonKey: 'TEST_ANON_KEY_PLACEHOLDER',
    googleClientId: 'TEST_GOOGLE_CLIENT_ID_PLACEHOLDER',
    enableGoogleAuth: false,
    enableEmailAuth: true,
    enableBiometricAuth: false,
    enableDebugLogging: true,
    enableAnalytics: false,
    enableCrashReporting: false,
    apiTimeoutSeconds: 10,
    tokenRefreshThresholdMinutes: 1,
    encryptionKey: 'TEST_ENCRYPTION_KEY_PLACEHOLDER',
    featureFlags: {
      'enhanced_security': false,
      'rate_limiting': false,
      'advanced_monitoring': false,
      'arabic_localization': true,
      'accessibility_features': true,
    },
  );

  /// Gets the current environment configuration
  static EnvironmentConfig get current {
    if (kDebugMode) {
      return development;
    }
    
    // Check for environment variables or build configuration
    final envString = Platform.environment['CARNOW_ENV'] ?? 'production';
    
    switch (envString.toLowerCase()) {
      case 'development':
      case 'dev':
        return development;
      case 'staging':
      case 'stage':
        return staging;
      case 'testing':
      case 'test':
        return testing;
      case 'production':
      case 'prod':
      default:
        return production;
    }
  }

  /// Checks if a feature flag is enabled
  bool isFeatureEnabled(String featureName) {
    return featureFlags[featureName] as bool? ?? false;
  }

  /// Gets the timeout duration for API calls
  Duration get apiTimeout => Duration(seconds: apiTimeoutSeconds);

  /// Gets the token refresh threshold duration
  Duration get tokenRefreshThreshold => Duration(minutes: tokenRefreshThresholdMinutes);

  /// Validates the configuration
  List<String> validate() {
    final errors = <String>[];

    if (apiBaseUrl.isEmpty) {
      errors.add('API base URL is required');
    }

    if (supabaseUrl.isEmpty) {
      errors.add('Supabase URL is required');
    }

    if (supabaseAnonKey.isEmpty || supabaseAnonKey.contains('PLACEHOLDER')) {
      errors.add('Valid Supabase anonymous key is required');
    }

    if (enableGoogleAuth && (googleClientId.isEmpty || googleClientId.contains('PLACEHOLDER'))) {
      errors.add('Valid Google client ID is required when Google auth is enabled');
    }

    if (encryptionKey.isEmpty || encryptionKey.contains('PLACEHOLDER')) {
      errors.add('Valid encryption key is required');
    }

    if (apiTimeoutSeconds <= 0) {
      errors.add('API timeout must be positive');
    }

    if (tokenRefreshThresholdMinutes <= 0) {
      errors.add('Token refresh threshold must be positive');
    }

    // Validate URLs
    try {
      Uri.parse(apiBaseUrl);
    } catch (e) {
      errors.add('Invalid API base URL format');
    }

    try {
      Uri.parse(supabaseUrl);
    } catch (e) {
      errors.add('Invalid Supabase URL format');
    }

    return errors;
  }

  /// Creates a copy with updated values
  EnvironmentConfig copyWith({
    Environment? environment,
    String? apiBaseUrl,
    String? supabaseUrl,
    String? supabaseAnonKey,
    String? googleClientId,
    bool? enableGoogleAuth,
    bool? enableEmailAuth,
    bool? enableBiometricAuth,
    bool? enableDebugLogging,
    bool? enableAnalytics,
    bool? enableCrashReporting,
    int? apiTimeoutSeconds,
    int? tokenRefreshThresholdMinutes,
    String? encryptionKey,
    Map<String, dynamic>? featureFlags,
  }) {
    return EnvironmentConfig(
      environment: environment ?? this.environment,
      apiBaseUrl: apiBaseUrl ?? this.apiBaseUrl,
      supabaseUrl: supabaseUrl ?? this.supabaseUrl,
      supabaseAnonKey: supabaseAnonKey ?? this.supabaseAnonKey,
      googleClientId: googleClientId ?? this.googleClientId,
      enableGoogleAuth: enableGoogleAuth ?? this.enableGoogleAuth,
      enableEmailAuth: enableEmailAuth ?? this.enableEmailAuth,
      enableBiometricAuth: enableBiometricAuth ?? this.enableBiometricAuth,
      enableDebugLogging: enableDebugLogging ?? this.enableDebugLogging,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
      enableCrashReporting: enableCrashReporting ?? this.enableCrashReporting,
      apiTimeoutSeconds: apiTimeoutSeconds ?? this.apiTimeoutSeconds,
      tokenRefreshThresholdMinutes: tokenRefreshThresholdMinutes ?? this.tokenRefreshThresholdMinutes,
      encryptionKey: encryptionKey ?? this.encryptionKey,
      featureFlags: featureFlags ?? this.featureFlags,
    );
  }

  /// Converts to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'environment': environment.name,
      'apiBaseUrl': apiBaseUrl,
      'supabaseUrl': supabaseUrl,
      'enableGoogleAuth': enableGoogleAuth,
      'enableEmailAuth': enableEmailAuth,
      'enableBiometricAuth': enableBiometricAuth,
      'enableDebugLogging': enableDebugLogging,
      'enableAnalytics': enableAnalytics,
      'enableCrashReporting': enableCrashReporting,
      'apiTimeoutSeconds': apiTimeoutSeconds,
      'tokenRefreshThresholdMinutes': tokenRefreshThresholdMinutes,
      'featureFlags': featureFlags,
    };
  }

  /// Creates from JSON
  factory EnvironmentConfig.fromJson(Map<String, dynamic> json) {
    return EnvironmentConfig(
      environment: Environment.values.firstWhere(
        (e) => e.name == json['environment'],
        orElse: () => Environment.production,
      ),
      apiBaseUrl: json['apiBaseUrl'] ?? '',
      supabaseUrl: json['supabaseUrl'] ?? '',
      supabaseAnonKey: json['supabaseAnonKey'] ?? '',
      googleClientId: json['googleClientId'] ?? '',
      enableGoogleAuth: json['enableGoogleAuth'] ?? true,
      enableEmailAuth: json['enableEmailAuth'] ?? true,
      enableBiometricAuth: json['enableBiometricAuth'] ?? false,
      enableDebugLogging: json['enableDebugLogging'] ?? false,
      enableAnalytics: json['enableAnalytics'] ?? true,
      enableCrashReporting: json['enableCrashReporting'] ?? true,
      apiTimeoutSeconds: json['apiTimeoutSeconds'] ?? 30,
      tokenRefreshThresholdMinutes: json['tokenRefreshThresholdMinutes'] ?? 5,
      encryptionKey: json['encryptionKey'] ?? '',
      featureFlags: Map<String, dynamic>.from(json['featureFlags'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'EnvironmentConfig(environment: $environment, apiBaseUrl: $apiBaseUrl, enableGoogleAuth: $enableGoogleAuth, enableEmailAuth: $enableEmailAuth)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnvironmentConfig &&
        other.environment == environment &&
        other.apiBaseUrl == apiBaseUrl &&
        other.supabaseUrl == supabaseUrl &&
        other.enableGoogleAuth == enableGoogleAuth &&
        other.enableEmailAuth == enableEmailAuth;
  }

  @override
  int get hashCode {
    return Object.hash(
      environment,
      apiBaseUrl,
      supabaseUrl,
      enableGoogleAuth,
      enableEmailAuth,
    );
  }
}

/// Configuration manager for secure handling of sensitive configuration
class ConfigurationManager {
  static EnvironmentConfig? _currentConfig;
  static final List<String> _validationErrors = [];

  /// Gets the current configuration
  static EnvironmentConfig get current {
    _currentConfig ??= _loadConfiguration();
    return _currentConfig!;
  }

  /// Loads configuration from environment
  static EnvironmentConfig _loadConfiguration() {
    final config = EnvironmentConfig.current;
    
    // Validate configuration
    _validationErrors.clear();
    _validationErrors.addAll(config.validate());
    
    if (_validationErrors.isNotEmpty && !kDebugMode) {
      throw ConfigurationException(
        'Configuration validation failed: ${_validationErrors.join(', ')}',
      );
    }
    
    return config;
  }

  /// Gets validation errors
  static List<String> get validationErrors => List.unmodifiable(_validationErrors);

  /// Checks if configuration is valid
  static bool get isValid => _validationErrors.isEmpty;

  /// Reloads configuration
  static void reload() {
    _currentConfig = null;
    _currentConfig = _loadConfiguration();
  }

  /// Updates configuration (for testing purposes)
  static void updateConfiguration(EnvironmentConfig config) {
    if (kDebugMode || current.environment == Environment.testing) {
      _currentConfig = config;
    } else {
      throw UnsupportedError('Configuration updates are only allowed in debug or testing mode');
    }
  }

  /// Resets to default configuration
  static void reset() {
    _currentConfig = null;
    _validationErrors.clear();
  }
}

/// Exception thrown when configuration is invalid
class ConfigurationException implements Exception {
  final String message;
  
  const ConfigurationException(this.message);
  
  @override
  String toString() => 'ConfigurationException: $message';
}

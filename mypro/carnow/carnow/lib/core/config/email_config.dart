import 'package:flutter_dotenv/flutter_dotenv.dart';

/// إعدادات الإيميل للتطبيق
class EmailConfig {
  // إعدادات SMTP
  static String get smtpHost => dotenv.env['SMTP_HOST'] ?? 'smtp.gmail.com';
  static int get smtpPort => int.tryParse(dotenv.env['SMTP_PORT'] ?? '') ?? 587;

  // بيانات الشركة
  static String get companyEmail =>
      dotenv.env['COMPANY_EMAIL'] ?? '<EMAIL>';
  static String get companyPassword =>
      dotenv.env['COMPANY_EMAIL_PASSWORD'] ?? '';
  static String get companyName =>
      dotenv.env['COMPANY_NAME'] ?? 'CarNow - كارناو';

  // معلومات الشركة للفاتورة
  static String get companyPhone =>
      dotenv.env['COMPANY_PHONE'] ?? '+218 21 123 4567';
  static String get companyAddress =>
      dotenv.env['COMPANY_ADDRESS'] ?? 'طرابلس، ليبيا';

  /// التحقق من إعدادات الإيميل
  static bool get isConfigured =>
      companyEmail.isNotEmpty && companyPassword.isNotEmpty;

  /// إعدادات COD
  static const int estimatedDeliveryDays = 3;
  static const String defaultCurrency = 'LYD';
}

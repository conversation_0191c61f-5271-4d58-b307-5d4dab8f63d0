import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';

import 'environment_config.dart';

/// Secure configuration manager for handling sensitive API keys and secrets
class SecureConfigManager {
  static const String _configPrefix = 'carnow_config_';
  static const String _apiKeysKey = '${_configPrefix}api_keys';
  static const String _secretsKey = '${_configPrefix}secrets';
  static const String _encryptionKeyKey = '${_configPrefix}encryption_key';
  static const String _configHashKey = '${_configPrefix}config_hash';

  static final FlutterSecureStorage _secureStorage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_PKCS1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
      synchronizable: false,
    ),
  );

  static SecureConfigManager? _instance;
  static SecureConfigManager get instance => _instance ??= SecureConfigManager._();

  SecureConfigManager._();

  /// Secure API keys storage
  final Map<String, String> _apiKeys = {};
  final Map<String, String> _secrets = {};
  bool _isInitialized = false;

  /// Initialize secure configuration
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadSecureConfiguration();
      await _validateConfigurationIntegrity();
      _isInitialized = true;
    } catch (e) {
      if (kDebugMode) {
        print('SecureConfigManager initialization failed: $e');
      }
      // In production, we might want to use fallback configuration
      await _initializeWithDefaults();
    }
  }

  /// Load secure configuration from storage
  Future<void> _loadSecureConfiguration() async {
    // Load API keys
    final apiKeysJson = await _secureStorage.read(key: _apiKeysKey);
    if (apiKeysJson != null) {
      final apiKeysMap = Map<String, dynamic>.from(jsonDecode(apiKeysJson));
      _apiKeys.addAll(apiKeysMap.cast<String, String>());
    }

    // Load secrets
    final secretsJson = await _secureStorage.read(key: _secretsKey);
    if (secretsJson != null) {
      final secretsMap = Map<String, dynamic>.from(jsonDecode(secretsJson));
      _secrets.addAll(secretsMap.cast<String, String>());
    }

    // Load from environment variables if not in secure storage
    await _loadFromEnvironmentVariables();
  }

  /// Load configuration from environment variables
  Future<void> _loadFromEnvironmentVariables() async {
    final env = Platform.environment;

    // Supabase configuration
    final supabaseAnonKey = env['SUPABASE_ANON_KEY'];
    if (supabaseAnonKey != null && supabaseAnonKey.isNotEmpty) {
      _apiKeys['supabase_anon_key'] = supabaseAnonKey;
    }

    final supabaseServiceKey = env['SUPABASE_SERVICE_KEY'];
    if (supabaseServiceKey != null && supabaseServiceKey.isNotEmpty) {
      _secrets['supabase_service_key'] = supabaseServiceKey;
    }

    // Google OAuth configuration
    final googleClientId = env['GOOGLE_CLIENT_ID'];
    if (googleClientId != null && googleClientId.isNotEmpty) {
      _apiKeys['google_client_id'] = googleClientId;
    }

    final googleClientSecret = env['GOOGLE_CLIENT_SECRET'];
    if (googleClientSecret != null && googleClientSecret.isNotEmpty) {
      _secrets['google_client_secret'] = googleClientSecret;
    }

    // Encryption keys
    final encryptionKey = env['ENCRYPTION_KEY'];
    if (encryptionKey != null && encryptionKey.isNotEmpty) {
      _secrets['encryption_key'] = encryptionKey;
    }

    // JWT secrets
    final jwtSecret = env['JWT_SECRET'];
    if (jwtSecret != null && jwtSecret.isNotEmpty) {
      _secrets['jwt_secret'] = jwtSecret;
    }

    // API keys for external services
    final slackWebhookUrl = env['SLACK_WEBHOOK_URL'];
    if (slackWebhookUrl != null && slackWebhookUrl.isNotEmpty) {
      _secrets['slack_webhook_url'] = slackWebhookUrl;
    }

    // Save to secure storage if loaded from environment
    if (_apiKeys.isNotEmpty || _secrets.isNotEmpty) {
      await _saveSecureConfiguration();
    }
  }

  /// Initialize with default configuration for development
  Future<void> _initializeWithDefaults() async {
    if (kDebugMode) {
      // Development defaults - these should be replaced with real values
      _apiKeys.addAll({
        'supabase_anon_key': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQzNzQ0NTYsImV4cCI6MjA0OTk1MDQ1Nn0.aJvKWyTHhYhJrSdLGLOGlHJYgBHNdgzWxJZFqKqZqKs',
        'google_client_id': '123456789-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
      });

      _secrets.addAll({
        'encryption_key': 'dev_encryption_key_32_characters_long',
        'jwt_secret': 'dev_jwt_secret_for_development_only',
        'supabase_service_key': 'dev_service_key_placeholder',
      });

      await _saveSecureConfiguration();
    }
    _isInitialized = true;
  }

  /// Save secure configuration to storage
  Future<void> _saveSecureConfiguration() async {
    try {
      // Save API keys
      if (_apiKeys.isNotEmpty) {
        await _secureStorage.write(
          key: _apiKeysKey,
          value: jsonEncode(_apiKeys),
        );
      }

      // Save secrets
      if (_secrets.isNotEmpty) {
        await _secureStorage.write(
          key: _secretsKey,
          value: jsonEncode(_secrets),
        );
      }

      // Save configuration hash for integrity validation
      await _saveConfigurationHash();
    } catch (e) {
      if (kDebugMode) {
        print('Failed to save secure configuration: $e');
      }
      rethrow;
    }
  }

  /// Save configuration hash for integrity validation
  Future<void> _saveConfigurationHash() async {
    final configData = {
      'api_keys': _apiKeys,
      'secrets': _secrets.keys.toList(), // Only store keys, not values
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    final configJson = jsonEncode(configData);
    final configHash = sha256.convert(utf8.encode(configJson)).toString();

    await _secureStorage.write(key: _configHashKey, value: configHash);
  }

  /// Validate configuration integrity
  Future<void> _validateConfigurationIntegrity() async {
    try {
      final storedHash = await _secureStorage.read(key: _configHashKey);
      if (storedHash == null) {
        // First time setup, save current hash
        await _saveConfigurationHash();
        return;
      }

      final configData = {
        'api_keys': _apiKeys,
        'secrets': _secrets.keys.toList(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      final configJson = jsonEncode(configData);
      final currentHash = sha256.convert(utf8.encode(configJson)).toString();

      // Note: In production, you might want stricter validation
      // For now, we just log if hashes don't match
      if (storedHash != currentHash && kDebugMode) {
        print('Configuration integrity check: hashes differ, configuration may have been modified');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Configuration integrity validation failed: $e');
      }
    }
  }

  /// Get API key securely
  String? getApiKey(String key) {
    _ensureInitialized();
    return _apiKeys[key];
  }

  /// Get secret securely
  String? getSecret(String key) {
    _ensureInitialized();
    return _secrets[key];
  }

  /// Set API key securely
  Future<void> setApiKey(String key, String value) async {
    _ensureInitialized();
    _apiKeys[key] = value;
    await _saveSecureConfiguration();
  }

  /// Set secret securely
  Future<void> setSecret(String key, String value) async {
    _ensureInitialized();
    _secrets[key] = value;
    await _saveSecureConfiguration();
  }

  /// Remove API key
  Future<void> removeApiKey(String key) async {
    _ensureInitialized();
    _apiKeys.remove(key);
    await _saveSecureConfiguration();
  }

  /// Remove secret
  Future<void> removeSecret(String key) async {
    _ensureInitialized();
    _secrets.remove(key);
    await _saveSecureConfiguration();
  }

  /// Get Supabase anonymous key
  String? get supabaseAnonKey => getApiKey('supabase_anon_key');

  /// Get Supabase service key
  String? get supabaseServiceKey => getSecret('supabase_service_key');

  /// Get Google client ID
  String? get googleClientId => getApiKey('google_client_id');

  /// Get Google client secret
  String? get googleClientSecret => getSecret('google_client_secret');

  /// Get encryption key
  String? get encryptionKey => getSecret('encryption_key');

  /// Get JWT secret
  String? get jwtSecret => getSecret('jwt_secret');

  /// Get Slack webhook URL
  String? get slackWebhookUrl => getSecret('slack_webhook_url');

  /// Check if configuration is complete
  bool get isConfigurationComplete {
    _ensureInitialized();
    
    final requiredApiKeys = ['supabase_anon_key'];
    final requiredSecrets = ['encryption_key'];
    
    final hasRequiredApiKeys = requiredApiKeys.every(_apiKeys.containsKey);
    final hasRequiredSecrets = requiredSecrets.every(_secrets.containsKey);
    
    return hasRequiredApiKeys && hasRequiredSecrets;
  }

  /// Get configuration status
  Map<String, dynamic> getConfigurationStatus() {
    _ensureInitialized();
    
    return {
      'initialized': _isInitialized,
      'complete': isConfigurationComplete,
      'api_keys_count': _apiKeys.length,
      'secrets_count': _secrets.length,
      'available_api_keys': _apiKeys.keys.toList(),
      'available_secrets': _secrets.keys.toList(),
    };
  }

  /// Clear all secure configuration
  Future<void> clearAll() async {
    _apiKeys.clear();
    _secrets.clear();
    
    await _secureStorage.delete(key: _apiKeysKey);
    await _secureStorage.delete(key: _secretsKey);
    await _secureStorage.delete(key: _configHashKey);
    
    _isInitialized = false;
  }

  /// Ensure manager is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('SecureConfigManager not initialized. Call initialize() first.');
    }
  }

  /// Export configuration for backup (excludes secrets)
  Map<String, dynamic> exportConfiguration() {
    _ensureInitialized();
    
    return {
      'api_keys': Map<String, String>.from(_apiKeys),
      'secret_keys': _secrets.keys.toList(), // Only export keys, not values
      'exported_at': DateTime.now().toIso8601String(),
      'environment': ConfigurationManager.current.environment.name,
    };
  }

  /// Import configuration from backup
  Future<void> importConfiguration(Map<String, dynamic> config) async {
    if (config['api_keys'] != null) {
      final apiKeys = Map<String, String>.from(config['api_keys']);
      _apiKeys.addAll(apiKeys);
    }
    
    await _saveSecureConfiguration();
    _isInitialized = true;
  }

  /// Validate all required configuration is present
  List<String> validateConfiguration() {
    final errors = <String>[];
    
    if (!_isInitialized) {
      errors.add('Configuration manager not initialized');
      return errors;
    }
    
    // Check required API keys
    if (supabaseAnonKey == null || supabaseAnonKey!.isEmpty) {
      errors.add('Supabase anonymous key is missing');
    }
    
    if (ConfigurationManager.current.enableGoogleAuth) {
      if (googleClientId == null || googleClientId!.isEmpty) {
        errors.add('Google client ID is missing');
      }
    }
    
    // Check required secrets
    if (encryptionKey == null || encryptionKey!.isEmpty) {
      errors.add('Encryption key is missing');
    }
    
    // Validate key formats
    if (supabaseAnonKey != null && !supabaseAnonKey!.startsWith('eyJ')) {
      errors.add('Supabase anonymous key format is invalid');
    }
    
    if (googleClientId != null && !googleClientId!.contains('.apps.googleusercontent.com')) {
      errors.add('Google client ID format is invalid');
    }
    
    return errors;
  }
}

/// Configuration validation result
class ConfigValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ConfigValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });

  factory ConfigValidationResult.valid() {
    return const ConfigValidationResult(isValid: true);
  }

  factory ConfigValidationResult.invalid(List<String> errors, [List<String>? warnings]) {
    return ConfigValidationResult(
      isValid: false,
      errors: errors,
      warnings: warnings ?? [],
    );
  }

  @override
  String toString() {
    if (isValid) return 'Configuration is valid';
    
    final buffer = StringBuffer('Configuration validation failed:\n');
    for (final error in errors) {
      buffer.writeln('  Error: $error');
    }
    for (final warning in warnings) {
      buffer.writeln('  Warning: $warning');
    }
    return buffer.toString();
  }
}

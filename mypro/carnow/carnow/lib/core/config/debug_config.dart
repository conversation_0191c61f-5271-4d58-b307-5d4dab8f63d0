import 'package:flutter/foundation.dart';

/// Configuration class for debug settings
class DebugConfig {
  /// Whether debug prints are enabled
  static bool get debugPrintsEnabled => kDebugMode;
  
  /// Whether verbose logging is enabled
  static bool get verboseLoggingEnabled => kDebugMode;
  
  /// Whether performance monitoring is enabled
  static bool get performanceMonitoringEnabled => true;
  
  /// Whether analytics are enabled
  static bool get analyticsEnabled => !kDebugMode;
  
  /// Whether error reporting is enabled
  static bool get errorReportingEnabled => !kDebugMode;
  
  /// Maximum log level for production
  static String get logLevel => kDebugMode ? 'debug' : 'error';
  
  /// Whether to show debug overlays
  static bool get showDebugOverlays => kDebugMode;
  
  /// Whether to enable debug banners
  static bool get showDebugBanner => kDebugMode;
}

/// Safe print function that only prints in debug mode
void safePrint(Object? object) {
  if (DebugConfig.debugPrintsEnabled) {
    // ignore: avoid_print
    print(object);
  }
}

/// Safe debug print function with additional context
void safeDebugPrint(String message, {String? tag}) {
  if (DebugConfig.debugPrintsEnabled) {
    final tagPrefix = tag != null ? '[$tag] ' : '';
    // ignore: avoid_print
    print('$tagPrefix$message');
  }
}
/// ============================================================================
/// SERVER URLS CONFIGURATION - Centralized URL Management
/// ============================================================================
///
/// This file contains all server URLs in one place for easy management.
/// When hosting changes, update only this file.
/// ============================================================================

/// Centralized server URL configuration
class ServerUrls {
  // ==================== HOSTED SERVERS ====================
  
  /// Main production backend server
  /// Change this when hosting provider changes
  static const String productionBackend = 'https://backend-go-8klm.onrender.com';
  
  /// Staging backend server (if different from production)
  static const String stagingBackend = 'https://backend-go-8klm.onrender.com';
  
  /// Development backend server (if different)
  static const String developmentBackend = 'https://backend-go-8klm.onrender.com';
  
  /// Supabase project URL
  static const String supabaseUrl = 'https://lpxtghyvxuenyyisrrro.supabase.co';
  
  // ==================== LOCAL DEVELOPMENT SERVERS ====================
  
  /// Local development server (localhost)
  static const String localBackend = 'http://localhost:8080';
  
  /// Android emulator local server
  static const String androidEmulatorBackend = 'http://********:8080';
  
  /// iOS simulator local server (if different)
  static const String iosSimulatorBackend = 'http://localhost:8080';
  
  // ==================== API VERSIONS ====================
  
  /// Current API version
  static const String apiVersion = 'v1';
  
  /// API base path
  static const String apiBasePath = '/api/v1';
  
  // ==================== HEALTH CHECK ENDPOINTS ====================
  
  /// Health check endpoint
  static const String healthEndpoint = '/health';
  
  /// Ready check endpoint
  static const String readyEndpoint = '/ready';
  
  // ==================== URL BUILDERS ====================
  
  /// Build full API URL for a server
  static String buildApiUrl(String serverUrl, String endpoint) {
    if (endpoint.startsWith('http')) {
      return endpoint;
    }
    if (endpoint.startsWith('/')) {
      return '$serverUrl$apiBasePath$endpoint';
    }
    return '$serverUrl$apiBasePath/$endpoint';
  }
  
  /// Build health check URL for a server
  static String buildHealthUrl(String serverUrl) {
    return '$serverUrl$healthEndpoint';
  }
  
  /// Build ready check URL for a server
  static String buildReadyUrl(String serverUrl) {
    return '$serverUrl$readyEndpoint';
  }
  
  // ==================== ENVIRONMENT HELPERS ====================
  
  /// Get the appropriate hosted server URL based on environment
  static String getHostedServerUrl(String environment) {
    switch (environment.toLowerCase()) {
      case 'production':
        return productionBackend;
      case 'staging':
        return stagingBackend;
      case 'development':
        return developmentBackend;
      default:
        return productionBackend;
    }
  }
  
  /// Get local server URL based on platform
  static String getLocalServerUrl(String platform) {
    switch (platform.toLowerCase()) {
      case 'android':
        return androidEmulatorBackend;
      case 'ios':
        return iosSimulatorBackend;
      default:
        return localBackend;
    }
  }
  
  // ==================== CONFIGURATION INFO ====================
  
  /// Get all server URLs for debugging
  static Map<String, dynamic> getAllUrls() {
    return {
      'hosted': {
        'production': productionBackend,
        'staging': stagingBackend,
        'development': developmentBackend,
      },
      'local': {
        'localhost': localBackend,
        'android_emulator': androidEmulatorBackend,
        'ios_simulator': iosSimulatorBackend,
      },
      'supabase': supabaseUrl,
      'api': {
        'version': apiVersion,
        'base_path': apiBasePath,
      },
      'endpoints': {
        'health': healthEndpoint,
        'ready': readyEndpoint,
      },
    };
  }
  
  /// Print server configuration for debugging
  static void printConfiguration() {
    print('🔗 Server URLs Configuration:');
    print('📡 Hosted Servers:');
    print('   Production: $productionBackend');
    print('   Staging: $stagingBackend');
    print('   Development: $developmentBackend');
    print('🏠 Local Servers:');
    print('   Localhost: $localBackend');
    print('   Android Emulator: $androidEmulatorBackend');
    print('   iOS Simulator: $iosSimulatorBackend');
    print('🗄️ Supabase: $supabaseUrl');
    print('🔧 API Version: $apiVersion');
  }
}

/// Quick access to server URLs
class ServerConfig {
  /// Get production backend URL
  static String get productionBackend => ServerUrls.productionBackend;
  
  /// Get local backend URL
  static String get localBackend => ServerUrls.localBackend;
  
  /// Get Android emulator backend URL
  static String get androidEmulatorBackend => ServerUrls.androidEmulatorBackend;
  
  /// Get Supabase URL
  static String get supabaseUrl => ServerUrls.supabaseUrl;
  
  /// Get API version
  static String get apiVersion => ServerUrls.apiVersion;
} 
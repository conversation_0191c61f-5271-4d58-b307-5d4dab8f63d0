// ============================================================================
// CarNow Subscription Error Handler Usage Examples
// ============================================================================
// File: subscription_error_handler_usage_example.dart
// Description: Examples showing how to use SubscriptionErrorHandler
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../error/subscription_error_handler.dart';
import '../models/subscription_error.dart';
import '../models/subscription_request.dart';
import '../models/subscription_response.dart';
import '../services/subscription_service.dart';

/// Example screen showing how to use SubscriptionErrorHandler
class SubscriptionErrorHandlerExampleScreen extends ConsumerStatefulWidget {
  const SubscriptionErrorHandlerExampleScreen({super.key});

  @override
  ConsumerState<SubscriptionErrorHandlerExampleScreen> createState() =>
      _SubscriptionErrorHandlerExampleScreenState();
}

class _SubscriptionErrorHandlerExampleScreenState
    extends ConsumerState<SubscriptionErrorHandlerExampleScreen> {
  final _formKey = GlobalKey<FormState>();
  final _storeNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _cityController = TextEditingController();
  final _addressController = TextEditingController();
  final _descriptionController = TextEditingController();

  bool _isLoading = false;
  SubscriptionError? _currentError;

  @override
  void dispose() {
    _storeNameController.dispose();
    _phoneController.dispose();
    _cityController.dispose();
    _addressController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// Example 1: Handle subscription submission with comprehensive error handling
  Future<void> _submitSubscription() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _currentError = null;
    });

    try {
      final request = SubscriptionRequest(
        storeName: _storeNameController.text.trim(),
        phone: _phoneController.text.trim(),
        city: _cityController.text.trim(),
        address: _addressController.text.trim(),
        description: _descriptionController.text.trim(),
        planType: 'premium',
        price: 99.99,
        userId: 'user_123', // This would come from auth state
      );

      final subscriptionService = ref.read(subscriptionServiceProvider);
      final result = await subscriptionService.submitSubscriptionRequest(
        request,
      );

      result.when(
        success: (response) {
          _handleSubscriptionSuccess(response);
        },
        failure: (error) {
          _handleSubscriptionError(error);
        },
      );
    } catch (e) {
      // Convert unexpected exceptions to SubscriptionError
      final error = SubscriptionErrorHelpers.fromException(
        e as Exception,
        context: 'subscription_submission',
      );
      _handleSubscriptionError(error);
    }
  }

  /// Example 2: Handle successful subscription
  void _handleSubscriptionSuccess(SubscriptionResponse response) {
    setState(() {
      _isLoading = false;
      _currentError = null;
    });

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('تم إرسال طلب الاشتراك بنجاح'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        behavior: SnackBarBehavior.floating,
      ),
    );

    // Navigate to success screen or update UI
    // Navigator.of(context).pushReplacementNamed('/subscription/success');
  }

  /// Example 3: Handle subscription errors with user-friendly dialogs
  void _handleSubscriptionError(SubscriptionError error) {
    setState(() {
      _isLoading = false;
      _currentError = error;
    });

    // Use SubscriptionErrorHandler to show appropriate dialog
    SubscriptionErrorHandler.handleSubscriptionError(
      context: context,
      error: error,
      onRetry: error.isRetryable ? _submitSubscription : null,
      onCancel: () {
        setState(() {
          _currentError = null;
        });
      },
    );
  }

  /// Example 4: Handle validation errors with field-specific messages
  void _handleValidationError() {
    final validationError = SubscriptionError.validationError(
      message: 'يرجى تصحيح الأخطاء التالية',
      fieldErrors: {
        'storeName': 'اسم المتجر مطلوب ويجب أن يكون أكثر من 3 أحرف',
        'phone': 'رقم الهاتف غير صحيح',
        'city': 'المدينة مطلوبة',
      },
      code: 'VALIDATION_ERROR',
    );

    SubscriptionErrorHandler.handleSubscriptionError(
      context: context,
      error: validationError,
    );
  }

  /// Example 5: Show error as snackbar for minor errors
  void _showMinorError() {
    final networkError = SubscriptionError.networkError(
      message: 'Connection timeout',
      code: 'TIMEOUT_ERROR',
    );

    SubscriptionErrorHandler.showErrorSnackBar(
      context: context,
      error: networkError,
      onRetry: _submitSubscription,
    );
  }

  /// Example 6: Use retry operation with exponential backoff
  Future<void> _submitWithRetry() async {
    setState(() {
      _isLoading = true;
      _currentError = null;
    });

    final result =
        await SubscriptionErrorHandler.handleRetryOperation<
          SubscriptionResponse
        >(
          context: context,
          operation: () async {
            final request = SubscriptionRequest(
              storeName: _storeNameController.text.trim(),
              phone: _phoneController.text.trim(),
              city: _cityController.text.trim(),
              address: _addressController.text.trim(),
              description: _descriptionController.text.trim(),
              planType: 'premium',
              price: 99.99,
              userId: 'user_123',
            );

            final subscriptionService = ref.read(subscriptionServiceProvider);
            final serviceResult = await subscriptionService
                .submitSubscriptionRequest(request);

            return serviceResult.when(
              success: (response) => response,
              failure: (error) => throw Exception(error.technicalMessage),
            );
          },
          errorMapper: (exception) => SubscriptionErrorHelpers.fromException(
            exception,
            context: 'subscription_retry',
          ),
          maxAttempts: 3,
          loadingMessage: 'جاري إعادة المحاولة...',
        );

    setState(() {
      _isLoading = false;
    });

    if (result != null) {
      _handleSubscriptionSuccess(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال على معالج أخطاء الاشتراك'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Show inline error card if there's a current error
              if (_currentError != null) ...[
                SubscriptionErrorHandler.buildErrorCard(
                  context: context,
                  error: _currentError!,
                  onRetry: _currentError!.isRetryable
                      ? _submitSubscription
                      : null,
                  onDismiss: () {
                    setState(() {
                      _currentError = null;
                    });
                  },
                ),
                const SizedBox(height: 16),
              ],

              // Form fields
              TextFormField(
                controller: _storeNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المتجر',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم المتجر مطلوب';
                  }
                  if (value.trim().length < 3) {
                    return 'اسم المتجر يجب أن يكون أكثر من 3 أحرف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'رقم الهاتف مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _cityController,
                decoration: const InputDecoration(
                  labelText: 'المدينة',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'المدينة مطلوبة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: 'العنوان',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'العنوان مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف المتجر (اختياري)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),

              // Action buttons
              FilledButton(
                onPressed: _isLoading ? null : _submitSubscription,
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('إرسال طلب الاشتراك'),
              ),
              const SizedBox(height: 12),

              OutlinedButton(
                onPressed: _isLoading ? null : _submitWithRetry,
                child: const Text('إرسال مع إعادة المحاولة التلقائية'),
              ),
              const SizedBox(height: 12),

              // Example buttons for testing different error types
              const Divider(),
              const Text(
                'أمثلة على أنواع الأخطاء المختلفة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),

              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ElevatedButton(
                    onPressed: _handleValidationError,
                    child: const Text('خطأ في التحقق'),
                  ),
                  ElevatedButton(
                    onPressed: _showMinorError,
                    child: const Text('خطأ بسيط (Snackbar)'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      final authError = SubscriptionError.authenticationError(
                        message: 'Session expired',
                        code: 'AUTH_EXPIRED',
                      );
                      SubscriptionErrorHandler.handleSubscriptionError(
                        context: context,
                        error: authError,
                      );
                    },
                    child: const Text('خطأ في المصادقة'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      final paymentError = SubscriptionError.paymentError(
                        message: 'Payment method declined',
                        code: 'PAYMENT_DECLINED',
                        paymentMethod: 'credit_card',
                      );
                      SubscriptionErrorHandler.handleSubscriptionError(
                        context: context,
                        error: paymentError,
                      );
                    },
                    child: const Text('خطأ في الدفع'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Example of how to integrate error handling in a service class
class ExampleSubscriptionService {
  /// Example method showing proper error handling and conversion
  Future<SubscriptionResponse> submitSubscription(
    SubscriptionRequest request,
  ) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Simulate different error scenarios
      final random = DateTime.now().millisecondsSinceEpoch % 5;

      switch (random) {
        case 0:
          throw SubscriptionError.networkError(
            message: 'Network connection failed',
            code: 'NETWORK_ERROR',
          );
        case 1:
          throw SubscriptionError.validationError(
            message: 'Invalid request data',
            fieldErrors: {
              'storeName': 'Store name is required',
              'phone': 'Invalid phone number format',
            },
            code: 'VALIDATION_ERROR',
          );
        case 2:
          throw SubscriptionError.serverError(
            message: 'Internal server error',
            code: 'SERVER_ERROR',
            statusCode: 500,
          );
        case 3:
          throw SubscriptionError.authenticationError(
            message: 'Authentication token expired',
            code: 'TOKEN_EXPIRED',
          );
        default:
          // Success case
          return SubscriptionResponse(
            id: 'sub_123',
            status: 'pending',
            createdAt: DateTime.now(),
            message: 'Subscription request submitted successfully',
          );
      }
    } catch (e) {
      if (e is SubscriptionError) {
        rethrow;
      }

      // Convert unexpected errors to SubscriptionError
      throw SubscriptionErrorHelpers.fromException(
        e as Exception,
        context: 'subscription_service',
      );
    }
  }
}

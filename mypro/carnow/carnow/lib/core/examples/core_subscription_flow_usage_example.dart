import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/subscription_flow_provider.dart';
import '../models/subscription_error.dart';

/// Example usage of the core subscription flow provider
/// This demonstrates how to integrate the core provider with existing subscription screens
class CoreSubscriptionFlowUsageExample extends ConsumerStatefulWidget {
  const CoreSubscriptionFlowUsageExample({super.key});

  @override
  ConsumerState<CoreSubscriptionFlowUsageExample> createState() =>
      _CoreSubscriptionFlowUsageExampleState();
}

class _CoreSubscriptionFlowUsageExampleState
    extends ConsumerState<CoreSubscriptionFlowUsageExample> {
  final _formKey = GlobalKey<FormState>();
  final _storeNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _cityController = TextEditingController();
  final _addressController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize the flow when the widget is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(coreSubscriptionFlowProviderProvider.notifier).initializeFlow();
    });
  }

  @override
  void dispose() {
    _storeNameController.dispose();
    _phoneController.dispose();
    _cityController.dispose();
    _addressController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final flowState = ref.watch(coreSubscriptionFlowProviderProvider);
    final isLoading = ref.watch(isCoreSubscriptionLoadingProvider);
    final validationErrors = ref.watch(
      coreSubscriptionValidationErrorsProvider,
    );
    final canSubmit = ref.watch(canSubmitCoreSubscriptionProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Core Subscription Flow Example'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Progress indicator
              if (flowState.progress > 0)
                LinearProgressIndicator(
                  value: flowState.progress,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              const SizedBox(height: 16),

              // Status display
              _buildStatusCard(flowState),
              const SizedBox(height: 16),

              // Form fields
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildTextField(
                        controller: _storeNameController,
                        label: 'اسم المتجر',
                        errorText: validationErrors['storeName'],
                        onChanged: (value) =>
                            _updateFormField(storeName: value),
                      ),
                      const SizedBox(height: 16),

                      _buildTextField(
                        controller: _phoneController,
                        label: 'رقم الهاتف',
                        errorText: validationErrors['phone'],
                        keyboardType: TextInputType.phone,
                        onChanged: (value) => _updateFormField(phone: value),
                      ),
                      const SizedBox(height: 16),

                      _buildTextField(
                        controller: _cityController,
                        label: 'المدينة',
                        errorText: validationErrors['city'],
                        onChanged: (value) => _updateFormField(city: value),
                      ),
                      const SizedBox(height: 16),

                      _buildTextField(
                        controller: _addressController,
                        label: 'العنوان',
                        errorText: validationErrors['address'],
                        maxLines: 2,
                        onChanged: (value) => _updateFormField(address: value),
                      ),
                      const SizedBox(height: 16),

                      _buildTextField(
                        controller: _descriptionController,
                        label: 'وصف المتجر',
                        errorText: validationErrors['description'],
                        maxLines: 3,
                        onChanged: (value) =>
                            _updateFormField(description: value),
                      ),
                      const SizedBox(height: 16),

                      // Plan selection (simplified for example)
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'نوع الخطة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 'basic',
                            child: Text('أساسية'),
                          ),
                          DropdownMenuItem(
                            value: 'premium',
                            child: Text('مميزة'),
                          ),
                          DropdownMenuItem(
                            value: 'enterprise',
                            child: Text('للشركات'),
                          ),
                        ],
                        onChanged: (value) => _updateFormField(
                          planType: value ?? '',
                          price: _getPriceForPlan(value ?? ''),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: isLoading ? null : _resetFlow,
                      child: const Text('إعادة تعيين'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: (canSubmit && !isLoading)
                          ? _submitSubscription
                          : null,
                      child: isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('إرسال طلب الاشتراك'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard(CoreSubscriptionFlowState flowState) {
    Color cardColor;
    IconData icon;
    String statusText;

    switch (flowState.status) {
      case CoreSubscriptionFlowStatus.initial:
        cardColor = Colors.grey;
        icon = Icons.info_outline;
        statusText = 'جاهز للبدء';
        break;
      case CoreSubscriptionFlowStatus.editing:
        cardColor = Colors.blue;
        icon = Icons.edit;
        statusText = 'جاري تعبئة البيانات';
        break;
      case CoreSubscriptionFlowStatus.validating:
        cardColor = Colors.orange;
        icon = Icons.check_circle_outline;
        statusText = 'جاري التحقق من البيانات';
        break;
      case CoreSubscriptionFlowStatus.submitting:
        cardColor = Colors.purple;
        icon = Icons.upload;
        statusText = 'جاري إرسال الطلب';
        break;
      case CoreSubscriptionFlowStatus.processing:
        cardColor = Colors.indigo;
        icon = Icons.hourglass_empty;
        statusText = 'جاري معالجة الطلب';
        break;
      case CoreSubscriptionFlowStatus.completed:
        cardColor = Colors.green;
        icon = Icons.check_circle;
        statusText = 'تم إنشاء الاشتراك بنجاح';
        break;
      case CoreSubscriptionFlowStatus.failed:
        cardColor = Colors.red;
        icon = Icons.error;
        statusText = 'فشل في إنشاء الاشتراك';
        break;
      case CoreSubscriptionFlowStatus.cancelled:
        cardColor = Colors.grey;
        icon = Icons.cancel;
        statusText = 'تم إلغاء العملية';
        break;
    }

    return Card(
      color: cardColor.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(icon, color: cardColor, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statusText,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: cardColor,
                    ),
                  ),
                  if (flowState.hasError) ...[
                    const SizedBox(height: 4),
                    Text(
                      flowState.errorMessageArabic ?? 'حدث خطأ غير متوقع',
                      style: TextStyle(color: Colors.red[700], fontSize: 12),
                    ),
                  ],
                  if (flowState.successMessageAr != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      flowState.successMessageAr!,
                      style: TextStyle(color: Colors.green[700], fontSize: 12),
                    ),
                  ],
                ],
              ),
            ),
            if (flowState.hasError && flowState.error?.isRetryable == true)
              IconButton(
                onPressed: _retrySubmission,
                icon: const Icon(Icons.refresh),
                tooltip: 'إعادة المحاولة',
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? errorText,
    TextInputType? keyboardType,
    int maxLines = 1,
    required ValueChanged<String> onChanged,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        errorText: errorText,
        border: const OutlineInputBorder(),
        helperText: errorText == null ? null : '',
      ),
      keyboardType: keyboardType,
      maxLines: maxLines,
      onChanged: onChanged,
      textDirection: TextDirection.rtl,
    );
  }

  void _updateFormField({
    String? storeName,
    String? phone,
    String? city,
    String? address,
    String? description,
    String? planType,
    double? price,
  }) {
    ref
        .read(coreSubscriptionFlowProviderProvider.notifier)
        .updateFormField(
          storeName: storeName,
          phone: phone,
          city: city,
          address: address,
          description: description,
          planType: planType,
          price: price,
        );
  }

  void _submitSubscription() {
    ref
        .read(coreSubscriptionFlowProviderProvider.notifier)
        .submitSubscription();
  }

  void _resetFlow() {
    ref.read(coreSubscriptionFlowProviderProvider.notifier).resetFlow();
    _clearControllers();
  }

  void _retrySubmission() {
    ref.read(coreSubscriptionFlowProviderProvider.notifier).retrySubmission();
  }

  void _clearControllers() {
    _storeNameController.clear();
    _phoneController.clear();
    _cityController.clear();
    _addressController.clear();
    _descriptionController.clear();
  }

  double _getPriceForPlan(String planType) {
    switch (planType) {
      case 'basic':
        return 200.0;
      case 'premium':
        return 400.0;
      case 'enterprise':
        return 1000.0;
      default:
        return 0.0;
    }
  }
}

/// Example of how to handle subscription errors using the core error types
class CoreSubscriptionErrorHandlingExample {
  static void handleSubscriptionError(
    BuildContext context,
    SubscriptionError error,
    VoidCallback onRetry,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          _getErrorTitle(error),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(error.userFriendlyMessageArabic),
            const SizedBox(height: 8),
            Text(
              error.suggestedActionArabic,
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          if (error.isRetryable)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              child: const Text('إعادة المحاولة'),
            ),
        ],
      ),
    );
  }

  static String _getErrorTitle(SubscriptionError error) {
    return error.when(
      networkError: (_, __, ___) => 'خطأ في الاتصال',
      databaseError: (_, __, ___) => 'خطأ في قاعدة البيانات',
      validationError: (_, __, ___) => 'خطأ في البيانات',
      navigationError: (_, __, ___) => 'خطأ في التنقل',
      authenticationError: (_, __, ___) => 'خطأ في المصادقة',
      serverError: (_, __, ___, ____) => 'خطأ في الخادم',
      paymentError: (_, __, ___, ____) => 'خطأ في الدفع',
      businessLogicError: (_, __, ___, ____) => 'خطأ في العملية',
      unknownError: (_, __, ___) => 'خطأ غير متوقع',
    );
  }
}

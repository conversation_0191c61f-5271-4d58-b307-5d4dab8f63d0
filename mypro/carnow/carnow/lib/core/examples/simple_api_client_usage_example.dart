// =============================================================================
// SimpleApiClient Usage Example - Forever Plan Architecture
// مثال استخدام SimpleApiClient - بنية الخطة الدائمة
// =============================================================================
// Flutter (UI Only) → Go API → Supabase (Data Only)

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:dio/dio.dart';
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';

part 'simple_api_client_usage_example.g.dart';

final _logger = Logger('SimpleApiClientExample');

// =============================================================================
// Example Models - نماذج المثال
// =============================================================================

class Product {
  final String id;
  final String name;
  final double price;
  final String description;
  final DateTime createdAt;

  const Product({
    required this.id,
    required this.name,
    required this.price,
    required this.description,
    required this.createdAt,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as String,
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      description: json['description'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class Category {
  final String id;
  final String name;
  final String? description;
  final DateTime createdAt;

  const Category({
    required this.id,
    required this.name,
    this.description,
    required this.createdAt,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }
}

// =============================================================================
// Example Providers - مزودات المثال
// =============================================================================

/// ✅ CORRECT: Simple provider using SimpleApiClient
/// Replaces direct Supabase calls
@riverpod
Future<List<Product>> products(Ref ref) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  try {
    _logger.info('Fetching products from Go backend...');
    
    final response = await apiClient.getApi<Map<String, dynamic>>('/products');
    
    if (response.isSuccess && response.data != null) {
      final data = response.data!;
      final productsJson = data['data'] as List<dynamic>;
      
      final products = productsJson
          .map((json) => Product.fromJson(json as Map<String, dynamic>))
          .toList();
      
      _logger.info('Successfully fetched ${products.length} products');
      return products;
    } else {
      throw Exception('Failed to fetch products: ${response.error}');
    }
  } on DioException catch (e) {
    _logger.warning('API error fetching products: ${e.message}');
    throw Exception('Network error: ${e.message}');
  } catch (e) {
    _logger.severe('Unexpected error fetching products: $e');
    throw Exception('Failed to fetch products: $e');
  }
}

/// ✅ CORRECT: Simple provider for categories
@riverpod
Future<List<Category>> categories(Ref ref) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  try {
    _logger.info('Fetching categories from Go backend...');
    
    final response = await apiClient.getApi<Map<String, dynamic>>('/categories');
    
    if (response.isSuccess && response.data != null) {
      final data = response.data!;
      final categoriesJson = data['data'] as List<dynamic>;
      
      final categories = categoriesJson
          .map((json) => Category.fromJson(json as Map<String, dynamic>))
          .toList();
      
      _logger.info('Successfully fetched ${categories.length} categories');
      return categories;
    } else {
      throw Exception('Failed to fetch categories: ${response.error}');
    }
  } on DioException catch (e) {
    _logger.warning('API error fetching categories: ${e.message}');
    throw Exception('Network error: ${e.message}');
  } catch (e) {
    _logger.severe('Unexpected error fetching categories: $e');
    throw Exception('Failed to fetch categories: $e');
  }
}

/// ✅ CORRECT: Provider for single product by ID
@riverpod
Future<Product> product(Ref ref, String productId) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  try {
    _logger.info('Fetching product $productId from Go backend...');
    
    final response = await apiClient.getApi<Map<String, dynamic>>('/products/$productId');
    
    if (response.isSuccess && response.data != null) {
      final data = response.data!;
      final productJson = data['data'] as Map<String, dynamic>;
      
      final product = Product.fromJson(productJson);
      
      _logger.info('Successfully fetched product: ${product.name}');
      return product;
    } else {
      throw Exception('Failed to fetch product: ${response.error}');
    }
  } on DioException catch (e) {
    _logger.warning('API error fetching product $productId: ${e.message}');
    throw Exception('Network error: ${e.message}');
  } catch (e) {
    _logger.severe('Unexpected error fetching product $productId: $e');
    throw Exception('Failed to fetch product: $e');
  }
}

/// ✅ CORRECT: Provider for creating a new product
@riverpod
class ProductCreator extends _$ProductCreator {
  @override
  AsyncValue<Product?> build() {
    return const AsyncValue.data(null);
  }

  Future<void> createProduct({
    required String name,
    required double price,
    required String description,
  }) async {
    state = const AsyncValue.loading();
    
    final apiClient = ref.read(simpleApiClientProvider);
    
    try {
      _logger.info('Creating new product: $name');
      
      final productData = {
        'name': name,
        'price': price,
        'description': description,
      };
      
      final response = await apiClient.post<Map<String, dynamic>>(
        '/products',
        data: productData,
      );
      
      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final productJson = data['data'] as Map<String, dynamic>;
        
        final product = Product.fromJson(productJson);
        
        _logger.info('Successfully created product: ${product.name}');
        state = AsyncValue.data(product);
        
        // Invalidate products list to refresh
        ref.invalidate(productsProvider);
      } else {
        throw Exception('Failed to create product: ${response.error}');
      }
    } on DioException catch (e) {
      _logger.warning('API error creating product: ${e.message}');
      state = AsyncValue.error(
        Exception('Network error: ${e.message}'),
        StackTrace.current,
      );
    } catch (e) {
      _logger.severe('Unexpected error creating product: $e');
      state = AsyncValue.error(
        Exception('Failed to create product: $e'),
        StackTrace.current,
      );
    }
  }
}

// =============================================================================
// Usage Examples in UI - أمثلة الاستخدام في واجهة المستخدم
// =============================================================================

/*
// ✅ CORRECT: Using in ConsumerWidget
class ProductListScreen extends ConsumerWidget {
  const ProductListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productsAsync = ref.watch(productsProvider);
    
    return Scaffold(
      appBar: AppBar(title: const Text('Products')),
      body: productsAsync.when(
        data: (products) => ListView.builder(
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            return ListTile(
              title: Text(product.name),
              subtitle: Text('\$${product.price}'),
              trailing: Text(product.description),
            );
          },
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: SelectableText.rich(
            TextSpan(
              children: [
                const TextSpan(
                  text: 'Error loading products: ',
                  style: TextStyle(color: Colors.red),
                ),
                TextSpan(
                  text: error.toString(),
                  style: const TextStyle(color: Colors.red),
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Refresh products
          ref.invalidate(productsProvider);
        },
        child: const Icon(Icons.refresh),
      ),
    );
  }
}

// ✅ CORRECT: Creating products
class CreateProductScreen extends ConsumerWidget {
  const CreateProductScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productCreator = ref.watch(productCreatorProvider);
    
    return Scaffold(
      appBar: AppBar(title: const Text('Create Product')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Form fields here...
            ElevatedButton(
              onPressed: productCreator.isLoading ? null : () {
                ref.read(productCreatorProvider.notifier).createProduct(
                  name: 'Sample Product',
                  price: 99.99,
                  description: 'A sample product',
                );
              },
              child: productCreator.isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Create Product'),
            ),
            if (productCreator.hasError)
              SelectableText.rich(
                TextSpan(
                  text: 'Error: ${productCreator.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
*/
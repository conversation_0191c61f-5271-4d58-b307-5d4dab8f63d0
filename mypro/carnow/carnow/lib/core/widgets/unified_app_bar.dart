import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../utils/unified_theme_extension.dart';
import '../navigation/unified_navigation_system.dart';
import '../../features/notifications/services/notification_service.dart';

/// AppBar موحد لجميع أنحاء التطبيق
/// يستخدم Material 3 Expressive design system
class UnifiedAppBar extends StatelessWidget implements PreferredSizeWidget {
  const UnifiedAppBar({
    required this.title,
    super.key,
    this.subtitle,
    this.actions,
    this.leading,
    this.showBackButton = true,
    this.onBackPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = true,
    this.bottom,
    this.flexibleSpace,
    this.toolbarHeight,
  });

  /// عنوان AppBar
  final String title;

  /// عنوان فرعي اختياري
  final String? subtitle;

  /// قائمة الأزرار
  final List<Widget>? actions;

  /// زر البداية المخصص
  final Widget? leading;

  /// إظهار زر الرجوع
  final bool showBackButton;

  /// وظيفة زر الرجوع المخصصة
  final VoidCallback? onBackPressed;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون المقدمة
  final Color? foregroundColor;

  /// مستوى الارتفاع
  final double? elevation;

  /// توسيط العنوان
  final bool centerTitle;

  /// الجزء السفلي
  final PreferredSizeWidget? bottom;

  /// المساحة المرنة
  final Widget? flexibleSpace;

  /// ارتفاع شريط الأدوات
  final double? toolbarHeight;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: subtitle != null ? _buildTitleWithSubtitle(context) : Text(title),
      actions: actions,
      leading: _buildLeading(context),
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
      centerTitle: centerTitle,
      bottom: bottom,
      flexibleSpace: flexibleSpace,
      toolbarHeight: toolbarHeight,
    );
  }

  Widget _buildTitleWithSubtitle(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: centerTitle
          ? CrossAxisAlignment.center
          : CrossAxisAlignment.start,
      children: [
        Text(title, style: context.titleLarge, overflow: TextOverflow.ellipsis),
        if (subtitle != null)
          Text(
            subtitle!,
            style: (context.bodySmall ?? Theme.of(context).textTheme.bodySmall)
                ?.copyWith(
                  color: context.colorScheme.onSurface.withSafeAlpha(0.7),
                ),
            overflow: TextOverflow.ellipsis,
          ),
      ],
    );
  }

  Widget? _buildLeading(BuildContext context) {
    if (leading != null) return leading;

    if (showBackButton && Navigator.of(context).canPop()) {
      return IconButton(
        onPressed: onBackPressed ?? () => context.pop(),
        icon: const Icon(Icons.arrow_back),
        tooltip: 'رجوع',
      );
    }

    return null;
  }

  @override
  Size get preferredSize => Size.fromHeight(
    (toolbarHeight ?? kToolbarHeight) + (bottom?.preferredSize.height ?? 0),
  );
}

/// AppBar بسيط للشاشات الأساسية
class SimpleUnifiedAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const SimpleUnifiedAppBar({
    required this.title,
    super.key,
    this.centerTitle = true,
  });

  final String title;
  final bool centerTitle;

  @override
  Widget build(BuildContext context) {
    return UnifiedAppBar(title: title, centerTitle: centerTitle);
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// AppBar مع بحث
class SearchUnifiedAppBar extends ConsumerWidget
    implements PreferredSizeWidget {
  const SearchUnifiedAppBar({
    required this.title,
    required this.onSearchChanged,
    super.key,
    this.searchHint = 'البحث...',
    this.isSearchActive = false,
    this.onSearchToggle,
    this.searchController,
    this.showNotifications = true,
  });

  final String title;
  final String searchHint;
  final bool isSearchActive;
  final ValueChanged<String> onSearchChanged;
  final VoidCallback? onSearchToggle;
  final TextEditingController? searchController;
  final bool showNotifications;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isSearchActive) {
      return AppBar(
        leading: IconButton(
          onPressed: onSearchToggle,
          icon: const Icon(Icons.arrow_back),
        ),
        title: TextField(
          controller: searchController,
          onChanged: onSearchChanged,
          autofocus: true,
          decoration: InputDecoration(
            hintText: searchHint,
            border: InputBorder.none,
            hintStyle:
                (context.bodyLarge ?? Theme.of(context).textTheme.bodyLarge)
                    ?.copyWith(
                      color: context.colorScheme.onSurface.withSafeAlpha(0.6),
                    ),
          ),
          style: context.bodyLarge,
        ),
        actions: [
          IconButton(
            onPressed: () {
              searchController?.clear();
              onSearchChanged('');
            },
            icon: const Icon(Icons.clear),
          ),
        ],
      );
    }

    return AppBar(
      title: Text(title),
      actions: [
        if (showNotifications)
          Consumer(
            builder: (context, ref, child) {
              final unreadCount = ref
                  .watch(unreadNotificationCountProvider)
                  .when(
                    data: (count) => count,
                    loading: () => 0,
                    error: (error, stackTrace) => 0,
                  );

              return IconButton(
                icon: Badge(
                  label: unreadCount == 0
                      ? null
                      : Text(
                          unreadCount > 9 ? '9+' : unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                  backgroundColor: Colors.red,
                  child: const Icon(Icons.notifications),
                ),
                onPressed: () =>
                    UnifiedNavigationSystem.goToNotifications(context),
                tooltip: 'الإشعارات',
              );
            },
          ),
        IconButton(
          onPressed: onSearchToggle,
          icon: const Icon(Icons.search),
          tooltip: 'بحث',
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// AppBar للإعدادات مع خيارات إضافية
class SettingsUnifiedAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const SettingsUnifiedAppBar({
    required this.title,
    super.key,
    this.showHelp = true,
    this.onHelpPressed,
    this.additionalActions,
  });

  final String title;
  final bool showHelp;
  final VoidCallback? onHelpPressed;
  final List<Widget>? additionalActions;

  @override
  Widget build(BuildContext context) {
    final actions = <Widget>[
      if (additionalActions != null) ...additionalActions!,
      if (showHelp)
        IconButton(
          onPressed:
              onHelpPressed ??
              () {
                // يمكن إضافة منطق المساعدة هنا
                context.showInfoSnackBar('المساعدة قادمة قريباً');
              },
          icon: const Icon(Icons.help_outline),
          tooltip: 'مساعدة',
        ),
    ];

    return UnifiedAppBar(
      title: title,
      actions: actions.isNotEmpty ? actions : null,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// AppBar مع تبديل الوضع المظلم
class ThemeToggleUnifiedAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const ThemeToggleUnifiedAppBar({
    required this.title,
    required this.isDarkMode,
    required this.onThemeToggle,
    super.key,
    this.additionalActions,
  });

  final String title;
  final bool isDarkMode;
  final VoidCallback onThemeToggle;
  final List<Widget>? additionalActions;

  @override
  Widget build(BuildContext context) {
    final actions = <Widget>[
      if (additionalActions != null) ...additionalActions!,
      IconButton(
        onPressed: onThemeToggle,
        icon: Icon(isDarkMode ? Icons.light_mode : Icons.dark_mode),
        tooltip: isDarkMode ? 'الوضع الفاتح' : 'الوضع المظلم',
      ),
    ];

    return UnifiedAppBar(title: title, actions: actions);
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

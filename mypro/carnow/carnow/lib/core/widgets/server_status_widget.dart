import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/smart_backend_service.dart';

/// Widget لعرض حالة السيرفر الحالي
class ServerStatusWidget extends ConsumerWidget {
  const ServerStatusWidget({
    super.key,
    this.showControls = false,
    this.showIcon = true,
    this.compact = false,
  });

  final bool showControls;
  final bool showIcon;
  final bool compact;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final serverStatusMessage = ref.watch(serverStatusMessageProvider);
    final currentServer = ref.watch(currentServerProvider);
    
    if (compact) {
      return _buildCompactStatus(context, serverStatusMessage, currentServer);
    }
    
    return _buildFullStatus(context, ref, serverStatusMessage, currentServer);
  }

  Widget _buildCompactStatus(
    BuildContext context, 
    String statusMessage, 
    ServerInfo? currentServer,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(currentServer?.status).withAlpha((255 * 0.1).round()),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(currentServer?.status).withAlpha((255 * 0.3).round()),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(
              _getStatusIcon(currentServer?.status),
              size: 14,
              color: _getStatusColor(currentServer?.status),
            ),
            const SizedBox(width: 4),
          ],
          Text(
            statusMessage,
            style: TextStyle(
              fontSize: 12,
              color: _getStatusColor(currentServer?.status),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullStatus(
    BuildContext context,
    WidgetRef ref,
    String statusMessage,
    ServerInfo? currentServer,
  ) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.dns_outlined,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'حالة السيرفر',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // حالة السيرفر الحالي
            _buildStatusRow(context, statusMessage, currentServer),
            
            if (showControls) ...[
              const SizedBox(height: 16),
              _buildControlButtons(context, ref),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(
    BuildContext context,
    String statusMessage,
    ServerInfo? currentServer,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getStatusColor(currentServer?.status).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getStatusColor(currentServer?.status).withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getStatusIcon(currentServer?.status),
            color: _getStatusColor(currentServer?.status),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  statusMessage,
                  style: TextStyle(
                    color: _getStatusColor(currentServer?.status),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                if (currentServer != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    currentServer.url,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  if (currentServer.responseTime != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      'آخر فحص: ${_formatLastChecked(currentServer.lastChecked)}',
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 11,
                      ),
                    ),
                  ],
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _recheckServers(ref),
            icon: const Icon(Icons.refresh, size: 18),
            label: const Text('إعادة فحص'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _showServerOptions(context, ref),
            icon: const Icon(Icons.settings, size: 18),
            label: const Text('خيارات'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(ServerStatus? status) {
    switch (status) {
      case ServerStatus.local:
        return Colors.green;
      case ServerStatus.hosted:
        return Colors.blue;
      case ServerStatus.offline:
        return Colors.red;
      case ServerStatus.checking:
        return Colors.orange;
      case null:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(ServerStatus? status) {
    switch (status) {
      case ServerStatus.local:
        return Icons.computer;
      case ServerStatus.hosted:
        return Icons.cloud;
      case ServerStatus.offline:
        return Icons.cloud_off;
      case ServerStatus.checking:
        return Icons.refresh;
      case null:
        return Icons.help_outline;
    }
  }

  String _formatLastChecked(DateTime lastChecked) {
    final now = DateTime.now();
    final difference = now.difference(lastChecked);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'منذ ${difference.inHours} ساعة';
    }
  }

  Future<void> _recheckServers(WidgetRef ref) async {
    final service = ref.read(smartBackendServiceProvider);
    await service.recheckNow();
  }

  void _showServerOptions(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      builder: (context) => ServerOptionsBottomSheet(),
    );
  }
}

/// Bottom sheet لخيارات السيرفر
class ServerOptionsBottomSheet extends ConsumerWidget {
  const ServerOptionsBottomSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final service = ref.watch(smartBackendServiceProvider);
    final allServers = service.allServers;
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان
          Row(
            children: [
              Icon(
                Icons.dns_outlined,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'خيارات السيرفر',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // قائمة السيرفرات
          ...allServers.entries.map((entry) {
            final serverKey = entry.key;
            final serverInfo = entry.value;
            final isSelected = service.currentServer?.url == serverInfo.url;
            
            return ListTile(
              leading: Icon(
                serverInfo.status == ServerStatus.local 
                  ? Icons.computer 
                  : Icons.cloud,
                color: isSelected 
                  ? Theme.of(context).primaryColor 
                  : Colors.grey,
              ),
              title: Text(serverInfo.displayName),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(serverInfo.url),
                  Text(
                    _getStatusText(serverInfo.status),
                    style: TextStyle(
                      color: _getStatusColor(serverInfo.status),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              trailing: isSelected 
                ? Icon(
                    Icons.check_circle,
                    color: Theme.of(context).primaryColor,
                  )
                : null,
              enabled: serverInfo.status != ServerStatus.offline,
              onTap: serverInfo.status != ServerStatus.offline
                ? () => _selectServer(context, ref, serverKey)
                : null,
            );
          }),
          
          const SizedBox(height: 16),
          
          // زر إعادة الفحص
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _recheckAndClose(context, ref),
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة فحص جميع السيرفرات'),
            ),
          ),
          
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Color _getStatusColor(ServerStatus status) {
    switch (status) {
      case ServerStatus.local:
        return Colors.green;
      case ServerStatus.hosted:
        return Colors.blue;
      case ServerStatus.offline:
        return Colors.red;
      case ServerStatus.checking:
        return Colors.orange;
    }
  }

  String _getStatusText(ServerStatus status) {
    switch (status) {
      case ServerStatus.local:
        return 'متاح - محلي';
      case ServerStatus.hosted:
        return 'متاح - مستضاف';
      case ServerStatus.offline:
        return 'غير متاح';
      case ServerStatus.checking:
        return 'جاري التحقق...';
    }
  }

  Future<void> _selectServer(
    BuildContext context, 
    WidgetRef ref, 
    String serverKey,
  ) async {
    final service = ref.read(smartBackendServiceProvider);
    await service.forceServer(serverKey);
    
    if (context.mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تغيير السيرفر بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _recheckAndClose(BuildContext context, WidgetRef ref) async {
    final service = ref.read(smartBackendServiceProvider);
    await service.recheckNow();
    
    if (context.mounted) {
      Navigator.of(context).pop();
    }
  }
}

/// Widget بسيط لشريط الحالة
class ServerStatusBar extends ConsumerWidget {
  const ServerStatusBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Theme.of(context).primaryColor.withAlpha((255 * 0.1).round()),
      child: Row(
        children: [
          Expanded(
            child: ServerStatusWidget(
              compact: true,
              showIcon: true,
            ),
          ),
          TextButton(
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('حالة السيرفر'),
                  content: const ServerStatusWidget(
                    showControls: true,
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              );
            },
            child: const Text('تفاصيل'),
          ),
        ],
      ),
    );
  }
}
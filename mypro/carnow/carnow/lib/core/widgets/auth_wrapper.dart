import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../features/auth/screens/new_login_screen.dart';
import '../auth/auth_models.dart';
import '../auth/unified_auth_provider.dart';
import '../auth/auth_provider_initializer.dart';

final _logger = Logger();

/// Widget that wraps the entire app and manages authentication state
/// This ensures consistent auth behavior across all screens
class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({
    required this.child,
    super.key,
    this.requireAuth = false,
    this.fallbackWidget,
  });
  final Widget child;
  final bool requireAuth;
  final Widget? fallbackWidget;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(safeAuthStateProvider);

    _logger.d('AuthWrapper: Current auth state: $authState');

    return authState.when(
      initial: () =>
          const Scaffold(body: Center(child: CircularProgressIndicator())),
      loading: (message, operation) =>
          const Scaffold(body: Center(child: CircularProgressIndicator())),
      authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) {
        return child;
      },
      unauthenticated: (reason) {
        if (requireAuth) {
          return fallbackWidget ?? const NewLoginScreen();
        }
        return child;
      },
      error: (message, errorCode, errorType, isRecoverable, originalException) {
        return Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'حدث خطأ في المصادقة',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  message,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                FilledButton(
                  onPressed: () {
                    // Refresh auth state
                    ref.invalidate(unifiedAuthProviderProvider);
                  },
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        );
      },
      emailVerificationPending: (email, sentAt) {
        if (requireAuth) {
          return fallbackWidget ?? const NewLoginScreen();
        }
        return child;
      },
      sessionExpired: (expiredAt, autoRefreshAttempted) {
        if (requireAuth) {
          return fallbackWidget ?? const NewLoginScreen();
        }
        return child;
      },
    );
  }
}

/// Widget that shows different content based on authentication status
class AuthStatusBuilder extends ConsumerWidget {
  const AuthStatusBuilder({
    required this.authenticatedBuilder,
    required this.unauthenticatedBuilder,
    super.key,
    this.loadingBuilder,
    this.errorBuilder,
  });
  final Widget Function(BuildContext context) authenticatedBuilder;
  final Widget Function(BuildContext context) unauthenticatedBuilder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context)? errorBuilder;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(safeAuthStateProvider);

    return authState.when(
      initial: () =>
          loadingBuilder?.call(context) ??
          const Center(child: CircularProgressIndicator()),
      loading: (message, operation) =>
          loadingBuilder?.call(context) ??
          const Center(child: CircularProgressIndicator()),
      authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) {
        return authenticatedBuilder(context);
      },
      unauthenticated: (reason) => unauthenticatedBuilder(context),
      error: (message, errorCode, errorType, isRecoverable, originalException) {
        return errorBuilder?.call(context) ??
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(height: 8),
                  Text(
                    'خطأ في المصادقة',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ],
              ),
            );
      },
      emailVerificationPending: (email, sentAt) =>
          unauthenticatedBuilder(context),
      sessionExpired: (expiredAt, autoRefreshAttempted) =>
          unauthenticatedBuilder(context),
    );
  }
}

/// Widget that only shows content when user is authenticated
class AuthenticatedOnly extends ConsumerWidget {
  const AuthenticatedOnly({required this.child, super.key, this.fallback});
  final Widget child;
  final Widget? fallback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    if (isAuthenticated) {
      return child;
    }

    return fallback ?? const SizedBox.shrink();
  }
}

/// Widget that only shows content when user is NOT authenticated
class UnauthenticatedOnly extends ConsumerWidget {
  const UnauthenticatedOnly({required this.child, super.key, this.fallback});
  final Widget child;
  final Widget? fallback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    if (!isAuthenticated) {
      return child;
    }

    return fallback ?? const SizedBox.shrink();
  }
}

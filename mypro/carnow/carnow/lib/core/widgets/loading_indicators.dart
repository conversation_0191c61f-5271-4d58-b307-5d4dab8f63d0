import 'package:flutter/material.dart';

/// A versatile loading spinner widget.
///
/// This widget provides a customizable circular progress indicator. It can be
/// used for simple loading spinners or more complex loading overlays.
class LoadingSpinner extends StatelessWidget {
  /// Creates a loading spinner.
  const LoadingSpinner({
    super.key,
    this.size = 24.0,
    this.color,
    this.strokeWidth = 3.0,
  });

  /// The size (width and height) of the spinner. Defaults to 24.0.
  final double size;

  /// The color of the spinner. Defaults to the theme's primary color.
  final Color? color;

  /// The thickness of the spinner's line. Defaults to 3.0.
  final double strokeWidth;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final indicatorColor = color ?? theme.colorScheme.primary;

    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
      ),
    );
  }
}

/// A collection of pre-configured loading indicator widgets for common use cases.
class LoadingIndicators {
  /// A centered, primary-colored circular progress indicator.
  static Widget primary([double size = 36.0]) =>
      Center(child: LoadingSpinner(size: size));

  /// A smaller loading indicator for inline use (e.g., in buttons).
  static Widget small() => const LoadingSpinner(size: 20, strokeWidth: 2);

  /// A loading indicator with a message displayed below it.
  static Widget withMessage(String message) => Center(
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const LoadingSpinner(),
        const SizedBox(height: 16),
        Text(
          message,
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 16),
        ),
      ],
    ),
  );

  /// A shimmer loading effect, typically used for skeleton screens.
  ///
  /// This can be replaced with a more advanced shimmer package if needed.
  static Widget shimmerListItem() => Container(
    padding: const EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          height: 16,
          color: Colors.grey.shade300,
        ),
        const SizedBox(height: 8),
        Container(width: 120, height: 16, color: Colors.grey.shade300),
      ],
    ),
  );
}

/// A widget that displays a loading overlay on top of its child.
///
/// This is useful for blocking UI interaction while an asynchronous operation
/// is in progress.
class LoadingOverlay extends StatelessWidget {
  /// Creates a loading overlay.
  const LoadingOverlay({
    required this.isLoading,
    required this.child,
    super.key,
    this.loadingMessage,
  });

  /// Whether the loading overlay is currently visible.
  final bool isLoading;

  /// The widget to display below the overlay.
  final Widget child;

  /// An optional message to display with the loading indicator.
  final String? loadingMessage;

  @override
  Widget build(BuildContext context) => Stack(
    children: [
      child,
      if (isLoading)
        Container(
          color: Colors.black.withAlpha(128), // 50% opacity
          child: loadingMessage != null
              ? LoadingIndicators.withMessage(loadingMessage!)
              : LoadingIndicators.primary(),
        ),
    ],
  );
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../core/theme/app_theme.dart';
import '../providers/support_ticket_provider.dart';

class SupportMenuItem extends ConsumerWidget {
  const SupportMenuItem({
    super.key,
    this.showText = true,
    this.iconColor,
    this.textColor,
  });
  final bool showText;
  final Color? iconColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final totalUnreadAsync = ref.watch(totalUnreadTicketsCountProvider);

    return totalUnreadAsync.when(
      data: (unreadCount) => _buildMenuItem(context, theme, unreadCount),
      loading: () => _buildMenuItem(context, theme, 0),
      error: (_, stack) => _buildMenuItem(context, theme, 0),
    );
  }

  Widget _buildMenuItem(
    BuildContext context,
    ThemeData theme,
    int unreadCount,
  ) => ListTile(
    leading: Stack(
      children: [
        Icon(
          Icons.support_agent,
          color: iconColor ?? theme.colorScheme.onSurfaceVariant,
        ),
        if (unreadCount > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: theme.colorScheme.error,
                borderRadius: BorderRadius.circular(8),
              ),
              constraints: const BoxConstraints(minWidth: 12, minHeight: 12),
              child: Text(
                unreadCount > 99 ? '99+' : unreadCount.toString(),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onError,
                  fontSize: 8,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    ),
    title: showText
        ? Text(
            'الدعم الفني',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: textColor ?? theme.colorScheme.onSurfaceVariant,
            ),
          )
        : null,
    subtitle: showText && unreadCount > 0
        ? Text(
            '$unreadCount رسالة جديدة',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          )
        : null,
    onTap: () {
      context.push('/support/tickets');
    },
  );
}

/// Floating action button for quick support access
class SupportFAB extends ConsumerWidget {
  const SupportFAB({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final totalUnreadAsync = ref.watch(totalUnreadTicketsCountProvider);

    return totalUnreadAsync.when(
      data: (unreadCount) => Stack(
        children: [
          FloatingActionButton(
            onPressed: () => context.push('/support/tickets'),
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
            child: const Icon(Icons.support_agent),
          ),
          if (unreadCount > 0)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: theme.colorScheme.error,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.surface,
                    width: 2,
                  ),
                ),
                child: Text(
                  unreadCount > 99 ? '99+' : unreadCount.toString(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onError,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
      loading: () => FloatingActionButton(
        onPressed: () => context.push('/support/tickets'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        child: const Icon(Icons.support_agent),
      ),
      error: (_, stack) => FloatingActionButton(
        onPressed: () => context.push('/support/tickets'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        child: const Icon(Icons.support_agent),
      ),
    );
  }
}

/// Support button for app bars
class SupportAppBarButton extends ConsumerWidget {
  const SupportAppBarButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final totalUnreadAsync = ref.watch(totalUnreadTicketsCountProvider);

    return totalUnreadAsync.when(
      data: (unreadCount) => Stack(
        children: [
          IconButton(
            onPressed: () => context.push('/support/tickets'),
            icon: const Icon(Icons.support_agent),
          ),
          if (unreadCount > 0)
            Positioned(
              right: 8,
              top: 8,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: theme.colorScheme.error,
                  borderRadius: BorderRadius.circular(8),
                ),
                constraints: const BoxConstraints(minWidth: 12, minHeight: 12),
                child: Text(
                  unreadCount > 99 ? '99+' : unreadCount.toString(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onError,
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
      loading: () => IconButton(
        onPressed: () => context.push('/support/tickets'),
        icon: const Icon(Icons.support_agent),
      ),
      error: (_, stack) => IconButton(
        onPressed: () => context.push('/support/tickets'),
        icon: const Icon(Icons.support_agent),
      ),
    );
  }
}

/// Quick support options bottom sheet
class SupportBottomSheet extends StatelessWidget {
  const SupportBottomSheet({super.key});

  static void show(BuildContext context) {
    showModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) => const SupportBottomSheet(),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppTheme.radiusL),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurfaceVariant.withAlpha(
                (0.40 * 255).toInt(),
              ),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: AppTheme.spacingL),

          // Title
          Text(
            'كيف يمكننا مساعدتك؟',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingL),

          // Options
          ListTile(
            leading: CircleAvatar(
              backgroundColor: theme.colorScheme.primary.withAlpha(
                (0.10 * 255).toInt(),
              ),
              child: Icon(
                Icons.chat_bubble_outline,
                color: theme.colorScheme.primary,
              ),
            ),
            title: const Text('تذاكر الدعم'),
            subtitle: const Text('عرض التذاكر الموجودة أو إنشاء تذكرة جديدة'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).pop();
              context.push('/support/tickets');
            },
          ),
          ListTile(
            leading: CircleAvatar(
              backgroundColor: theme.colorScheme.secondary.withAlpha(
                (0.10 * 255).toInt(),
              ),
              child: Icon(
                Icons.add_comment,
                color: theme.colorScheme.secondary,
              ),
            ),
            title: const Text('تذكرة جديدة'),
            subtitle: const Text('إنشاء تذكرة دعم جديدة للحصول على المساعدة'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).pop();
              context.push('/support/tickets/new');
            },
          ),

          const SizedBox(height: AppTheme.spacingL),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Modern toast widget for connection status changes
class ConnectionToast {
  static OverlayEntry? _currentOverlay;
  static bool _isShowing = false;

  /// Show connection restored toast
  static void showConnectionRestored(BuildContext context) {
    if (!context.mounted || _isShowing) return;

    _showToast(
      context,
      icon: Icons.wifi_rounded,
      message: 'تم استعادة الاتصال',
      backgroundColor: Colors.green.shade600,
    );
  }

  /// Show connection lost toast
  static void showConnectionLost(BuildContext context) {
    if (!context.mounted || _isShowing) return;

    _showToast(
      context,
      icon: Icons.wifi_off_rounded,
      message: 'انقطع الاتصال بالإنترنت',
      backgroundColor: Colors.red.shade600,
      duration: const Duration(seconds: 3),
    );
  }

  /// Show generic toast with custom parameters
  static void _showToast(
    BuildContext context, {
    required IconData icon,
    required String message,
    required Color backgroundColor,
    Duration duration = const Duration(seconds: 2),
  }) {
    // Remove previous toast if exists
    _hideCurrentToast();

    _isShowing = true;

    // Add haptic feedback
    HapticFeedback.lightImpact();

    final overlay = Overlay.of(context);

    _currentOverlay = OverlayEntry(
      builder: (context) => _ToastWidget(
        icon: icon,
        message: message,
        backgroundColor: backgroundColor,
        onAnimationComplete: () {
          _isShowing = false;
        },
      ),
    );

    overlay.insert(_currentOverlay!);

    // Auto-hide after duration
    Future.delayed(duration, _hideCurrentToast);
  }

  /// Hide current toast
  static void _hideCurrentToast() {
    if (_currentOverlay != null) {
      _currentOverlay!.remove();
      _currentOverlay = null;
    }
    _isShowing = false;
  }

  /// Force hide any visible toast
  static void hideAll() {
    _hideCurrentToast();
  }
}

/// Individual toast widget with animations
class _ToastWidget extends StatefulWidget {
  const _ToastWidget({
    required this.icon,
    required this.message,
    required this.backgroundColor,
    required this.onAnimationComplete,
  });

  final IconData icon;
  final String message;
  final Color backgroundColor;
  final VoidCallback onAnimationComplete;

  @override
  State<_ToastWidget> createState() => _ToastWidgetState();
}

class _ToastWidgetState extends State<_ToastWidget>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, -1), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutBack),
        );

    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    // Start animations
    _slideController.forward();
    _fadeController.forward();

    // Auto-hide animation
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        _slideController.reverse();
        _fadeController.reverse().then((_) {
          widget.onAnimationComplete();
        });
      }
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);

    return Positioned(
      top: mediaQuery.padding.top + 16,
      left: 16,
      right: 16,
      child: SlideTransition(
        position: _slideAnimation,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(widget.icon, color: Colors.white, size: 22),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        height: 1.2,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Simple connection toast following Forever Plan Architecture
/// Removed enhanced connection patterns as they violate the architecture rules
class SimpleConnectionToast extends ConsumerWidget {
  const SimpleConnectionToast({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Simplified approach - let API calls handle connection errors
    // No complex connection listeners needed
    return child;
  }
}

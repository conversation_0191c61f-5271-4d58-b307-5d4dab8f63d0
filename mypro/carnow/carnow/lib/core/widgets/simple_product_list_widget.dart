import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/simple_product_provider.dart';
import '../models/product_model.dart';

/// Simple product list widget demonstrating clean AsyncValue error handling
/// Follows Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
class SimpleProductListWidget extends ConsumerWidget {
  const SimpleProductListWidget({
    super.key,
    this.categoryId,
    this.showFeaturedOnly = false,
  });

  final String? categoryId;
  final bool showFeaturedOnly;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Select appropriate provider based on parameters
    final productsAsyncValue = _selectProductsProvider(ref);

    return RefreshIndicator(
      onRefresh: () async {
        // Simple refresh - invalidate the provider
        if (categoryId != null) {
          ref.invalidate(productsByCategoryProvider(categoryId!));
        } else if (showFeaturedOnly) {
          ref.invalidate(featuredProductsProvider);
        } else {
          ref.invalidate(allProductsProvider);
        }
      },
      child: productsAsyncValue.when(
        // ✅ Data loaded successfully
        data: (products) => _buildProductList(context, products),
        
        // ⏳ Loading state
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        
        // ❌ Error state with user-friendly message
        error: (error, stackTrace) => _buildErrorWidget(context, error, ref),
      ),
    );
  }

  /// Select the appropriate provider based on widget parameters
  AsyncValue<List<ProductModel>> _selectProductsProvider(WidgetRef ref) {
    if (categoryId != null) {
      return ref.watch(productsByCategoryProvider(categoryId!));
    } else if (showFeaturedOnly) {
      return ref.watch(featuredProductsProvider);
    } else {
      return ref.watch(allProductsProvider);
    }
  }

  /// Build the product list UI
  Widget _buildProductList(BuildContext context, List<ProductModel> products) {
    if (products.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return _ProductListItem(product: product);
      },
    );
  }

  /// Build empty state UI
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withAlpha((255 * 0.5).round()),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد منتجات حالياً',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withAlpha((255 * 0.7).round()),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سنضيف منتجات جديدة قريباً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  /// Build error widget with retry functionality
  Widget _buildErrorWidget(BuildContext context, Object error, WidgetRef ref) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل المنتجات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            SelectableText.rich(
              TextSpan(
                text: 'خطأ: ${_getErrorMessage(error)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _retryLoad(ref),
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// Get user-friendly error message
  String _getErrorMessage(Object error) {
    final errorString = error.toString();
    
    if (errorString.contains('NetworkException')) {
      return 'تحقق من اتصال الإنترنت';
    } else if (errorString.contains('AuthException')) {
      return 'يرجى تسجيل الدخول مرة أخرى';
    } else if (errorString.contains('ServerException')) {
      return 'خطأ في الخادم، حاول لاحقاً';
    } else {
      return 'خطأ غير متوقع';
    }
  }

  /// Retry loading data
  void _retryLoad(WidgetRef ref) {
    if (categoryId != null) {
      ref.invalidate(productsByCategoryProvider(categoryId!));
    } else if (showFeaturedOnly) {
      ref.invalidate(featuredProductsProvider);
    } else {
      ref.invalidate(allProductsProvider);
    }
  }
}

/// Simple product list item widget
class _ProductListItem extends ConsumerWidget {
  const _ProductListItem({required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: _buildProductImage(),
        title: Text(
          product.name,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (product.description != null) ...[
              Text(
                product.description!,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 4),
            ],
            Text(
              '${product.price.toStringAsFixed(2)} ر.س',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        trailing: IconButton(
          icon: Icon(
            // Use a simple boolean check or assume false if property doesn't exist
            false  // TODO: Replace with actual favorite status from ProductModel
                ? Icons.favorite
                : Icons.favorite_border,
            color: false  // TODO: Replace with actual favorite status from ProductModel
                ? Theme.of(context).colorScheme.error
                : null,
          ),
          onPressed: () => _toggleFavorite(ref),
        ),
        onTap: () => _navigateToProductDetails(context),
      ),
    );
  }

  /// Build product image widget
  Widget _buildProductImage() {
    // TODO: Replace with actual image URL property from ProductModel
    final imageUrl = null; // product.imageUrl or similar
    
    if (imageUrl != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(
          imageUrl,
          width: 56,
          height: 56,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _buildImagePlaceholder(),
        ),
      );
    } else {
      return _buildImagePlaceholder();
    }
  }

  /// Build image placeholder
  Widget _buildImagePlaceholder() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.image_outlined,
        color: Colors.grey,
      ),
    );
  }

  /// Toggle product favorite status
  void _toggleFavorite(WidgetRef ref) async {
    try {
      final actions = ref.read(productActionsProvider);
      await actions.toggleFavorite(product.id);
    } catch (e) {
      // Handle error silently or show snackbar
      debugPrint('Error toggling favorite: $e');
    }
  }

  /// Navigate to product details screen
  void _navigateToProductDetails(BuildContext context) {
    // Navigator.of(context).pushNamed('/product/${product.id}');
    debugPrint('Navigate to product: ${product.id}');
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import '../errors/app_error.dart';
import 'app_error_widget.dart';

final _logger = Logger();

/// Global Error Boundary لمعالجة الأخطاء على مستوى التطبيق
class GlobalErrorBoundary extends ConsumerStatefulWidget {
  const GlobalErrorBoundary({required this.child, super.key, this.onError});

  final Widget child;
  final void Function(Object error, StackTrace? stackTrace)? onError;

  @override
  ConsumerState<GlobalErrorBoundary> createState() =>
      _GlobalErrorBoundaryState();
}

class _GlobalErrorBoundaryState extends ConsumerState<GlobalErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  void initState() {
    super.initState();

    // تسجيل معالج الأخطاء العام
    FlutterError.onError = (FlutterErrorDetails details) {
      _logger.e(
        'Flutter Error caught by boundary',
        error: details.exception,
        stackTrace: details.stack,
      );

      setState(() {
        _error = details.exception;
        _stackTrace = details.stack;
      });

      widget.onError?.call(details.exception, details.stack);
    };
  }



  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      // عرض شاشة خطأ محسنة
      return _buildErrorWidget(context);
    }

    // استخدام ErrorWidget.builder للأخطاء أثناء البناء
    return ErrorWidgetBuilder(
      onError: (error, stackTrace) {
        _logger.e('Build error caught', error: error, stackTrace: stackTrace);

        setState(() {
          _error = error;
          _stackTrace = stackTrace;
        });

        widget.onError?.call(error, stackTrace);
      },
      child: widget.child,
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    final appError = _error is AppError
        ? _error as AppError
        : AppErrorFactory.fromError(_error!, stackTrace: _stackTrace);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.errorContainer,
      body: AppErrorWidget(
        message: appError.userMessage,
        details: appError.toString(),
        stackTrace: _stackTrace,
      ),
    );
  }
}

/// Widget builder لمعالجة أخطاء البناء
class ErrorWidgetBuilder extends StatefulWidget {
  const ErrorWidgetBuilder({
    required this.child,
    required this.onError,
    super.key,
  });

  final Widget child;
  final void Function(Object error, StackTrace? stackTrace) onError;

  @override
  State<ErrorWidgetBuilder> createState() => _ErrorWidgetBuilderState();
}

class _ErrorWidgetBuilderState extends State<ErrorWidgetBuilder> {
  @override
  void initState() {
    super.initState();

    // تخصيص ErrorWidget.builder
    final originalBuilder = ErrorWidget.builder;
    ErrorWidget.builder = (FlutterErrorDetails details) {
      widget.onError(details.exception, details.stack);

      // في development mode، نعرض الخطأ الأصلي
      if (const bool.fromEnvironment('dart.vm.product') == false) {
        return originalBuilder(details);
      }

      // في production، نعرض رسالة مخصصة
      return Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.all(16),
        child: const Text(
          'حدث خطأ في عرض هذا المحتوى',
          style: TextStyle(color: Colors.red),
          textDirection: TextDirection.rtl,
        ),
      );
    };
  }

  @override
  Widget build(BuildContext context) => widget.child;
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../errors/app_error.dart';
import '../services/simple_connection_service.dart';

/// User-friendly error display widget for Phase 3.1.1
///
/// Features:
/// - Different UI for different error types
/// - Retry functionality
/// - Offline mode indicators
/// - Graceful degradation
class ErrorDisplayWidget extends ConsumerWidget {
  const ErrorDisplayWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
    this.showRetryButton = true,
    this.showDetails = false,
  });

  final AppError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showRetryButton;
  final bool showDetails;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getBorderColor(), width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(_getIcon(), color: _getIconColor(), size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getTitle(),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: _getTextColor(),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      error.userMessage,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: _getTextColor().withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (showDetails && error.formatDetails().isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                error.formatDetails(),
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(fontFamily: 'monospace'),
              ),
            ),
          ],
          if (showRetryButton || onDismiss != null) ...[
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (onDismiss != null)
                  TextButton(onPressed: onDismiss, child: const Text('إغلاق')),
                if (showRetryButton && onRetry != null) ...[
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh, size: 18),
                    label: const Text('إعادة المحاولة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getButtonColor(),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (error.type) {
      case ErrorType.network:
        return Colors.blue.shade50;
      case ErrorType.authentication:
        return Colors.orange.shade50;
      case ErrorType.validation:
        return Colors.yellow.shade50;
      case ErrorType.server:
        return Colors.red.shade50;
      default:
        return Colors.grey.shade50;
    }
  }

  Color _getBorderColor() {
    switch (error.type) {
      case ErrorType.network:
        return Colors.blue.shade200;
      case ErrorType.authentication:
        return Colors.orange.shade200;
      case ErrorType.validation:
        return Colors.yellow.shade200;
      case ErrorType.server:
        return Colors.red.shade200;
      default:
        return Colors.grey.shade200;
    }
  }

  Color _getIconColor() {
    switch (error.type) {
      case ErrorType.network:
        return Colors.blue.shade600;
      case ErrorType.authentication:
        return Colors.orange.shade600;
      case ErrorType.validation:
        return Colors.yellow.shade700;
      case ErrorType.server:
        return Colors.red.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  Color _getTextColor() {
    switch (error.type) {
      case ErrorType.network:
        return Colors.blue.shade800;
      case ErrorType.authentication:
        return Colors.orange.shade800;
      case ErrorType.validation:
        return Colors.yellow.shade800;
      case ErrorType.server:
        return Colors.red.shade800;
      default:
        return Colors.grey.shade800;
    }
  }

  Color _getButtonColor() {
    switch (error.type) {
      case ErrorType.network:
        return Colors.blue.shade600;
      case ErrorType.authentication:
        return Colors.orange.shade600;
      case ErrorType.validation:
        return Colors.yellow.shade600;
      case ErrorType.server:
        return Colors.red.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  IconData _getIcon() {
    switch (error.type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.authentication:
        return Icons.lock_outline;
      case ErrorType.validation:
        return Icons.warning_outlined;
      case ErrorType.server:
        return Icons.error_outline;
      case ErrorType.notFound:
        return Icons.search_off;
      case ErrorType.authorization:
        return Icons.block;
      default:
        return Icons.info_outline;
    }
  }

  String _getTitle() {
    switch (error.type) {
      case ErrorType.network:
        return 'مشكلة في الاتصال';
      case ErrorType.authentication:
        return 'مشكلة في المصادقة';
      case ErrorType.validation:
        return 'خطأ في البيانات';
      case ErrorType.server:
        return 'خطأ في الخادم';
      case ErrorType.notFound:
        return 'غير موجود';
      case ErrorType.authorization:
        return 'غير مسموح';
      default:
        return 'خطأ';
    }
  }
}

/// Compact error display for smaller spaces
class CompactErrorWidget extends ConsumerWidget {
  const CompactErrorWidget({super.key, required this.error, this.onRetry});

  final AppError error;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red.shade600, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error.userMessage,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.red.shade800),
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              iconSize: 18,
              color: Colors.red.shade600,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            ),
          ],
        ],
      ),
    );
  }
}

/// Error boundary widget that catches and displays errors
class ErrorBoundary extends ConsumerWidget {
  const ErrorBoundary({
    super.key,
    required this.child,
    this.onError,
    this.fallback,
  });

  final Widget child;
  final void Function(Object error, StackTrace stackTrace)? onError;
  final Widget Function(Object error)? fallback;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return child; // In a real implementation, you'd use ErrorWidget.builder
  }
}

/// Offline indicator widget
class OfflineIndicator extends ConsumerWidget {
  const OfflineIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionStatus = ref.watch(connectionStatusProvider);

    return connectionStatus.when(
      data: (isOnline) {
        if (isOnline) return const SizedBox.shrink();

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 8),
          color: Colors.orange.shade100,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.wifi_off, color: Colors.orange.shade700, size: 16),
              const SizedBox(width: 8),
              Text(
                'وضع عدم الاتصال - سيتم مزامنة البيانات عند العودة للاتصال',
                style: TextStyle(color: Colors.orange.shade700, fontSize: 12),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }
}

/// Loading state with error handling
class LoadingWithError<T> extends ConsumerWidget {
  const LoadingWithError({
    super.key,
    required this.asyncValue,
    required this.builder,
    this.onRetry,
    this.loadingWidget,
    this.errorWidget,
  });

  final AsyncValue<T> asyncValue;
  final Widget Function(T data) builder;
  final VoidCallback? onRetry;
  final Widget? loadingWidget;
  final Widget Function(AppError error)? errorWidget;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return asyncValue.when(
      data: builder,
      loading: () =>
          loadingWidget ?? const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) {
        final appError = error is AppError
            ? error
            : AppError.unexpected(
                message: error.toString(),
                originalError: error,
              );

        if (errorWidget != null) {
          return errorWidget!(appError);
        }

        return ErrorDisplayWidget(error: appError, onRetry: onRetry);
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

/// مكون لإدخال علامات متعددة (Tags) كرقائق
class CustomChipInput extends HookWidget {
  const CustomChipInput({
    required this.title,
    required this.onChanged,
    super.key,
    this.initialValues = const [],
    this.hintText,
    this.icon,
    this.maxChips = 10,
    this.allowDuplicates = false,
  });
  final String title;
  final List<String> initialValues;
  final void Function(List<String>) onChanged;
  final String? hintText;
  final IconData? icon;
  final int maxChips;
  final bool allowDuplicates;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final controller = useTextEditingController();
    final values = useState<List<String>>(List.from(initialValues));
    final focusNode = useFocusNode();
    final error = useState<String?>(null);

    // تحديث القائمة الخارجية عند تغير القيم
    useEffect(() {
      onChanged(values.value);
      return null;
    });

    // إضافة علامة جديدة
    void addChip() {
      final text = controller.text.trim();
      if (text.isEmpty) {
        return;
      }

      if (values.value.length >= maxChips) {
        error.value = 'لا يمكن إضافة أكثر من $maxChips علامات';
        return;
      }

      if (!allowDuplicates && values.value.contains(text)) {
        error.value = 'هذه العلامة موجودة بالفعل';
        return;
      }

      error.value = null;
      values.value = [...values.value, text];
      controller.clear();
    }

    // حذف علامة
    void removeChip(String chip) {
      values.value = values.value.where((item) => item != chip).toList();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // العنوان
        Text(title, style: theme.textTheme.titleMedium),
        const SizedBox(height: 8),

        // حقل الإدخال
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                focusNode: focusNode,
                decoration: InputDecoration(
                  hintText: hintText ?? 'أضف علامة واضغط Enter',
                  prefixIcon: icon != null ? Icon(icon) : null,
                  border: const OutlineInputBorder(),
                  errorText: error.value,
                ),
                onSubmitted: (_) {
                  addChip();
                  focusNode.requestFocus();
                },
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: addChip,
              style: IconButton.styleFrom(
                foregroundColor: theme.colorScheme.primary,
                backgroundColor: theme.colorScheme.primaryContainer,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // عرض العلامات المضافة
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: values.value
              .map(
                (chip) => Chip(
                  label: Text(chip),
                  deleteIcon: const Icon(Icons.cancel, size: 18),
                  onDeleted: () => removeChip(chip),
                  backgroundColor: theme.colorScheme.primaryContainer,
                  labelStyle: TextStyle(
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              )
              .toList(),
        ),
      ],
    );
  }
}

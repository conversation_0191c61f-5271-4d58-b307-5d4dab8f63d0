/// ============================================================================
/// GOOGLE OAUTH BUTTON - Forever Plan Architecture
/// ============================================================================
///
/// Google OAuth button widget for CarNow authentication
/// Uses Go Backend for authentication (NO direct Supabase calls)
/// ============================================================================
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../auth/unified_auth_provider.dart';
import '../auth/auth_models.dart';

/// Google OAuth button widget
class GoogleOAuthButton extends ConsumerWidget {
  const GoogleOAuthButton({
    Key? key,
    this.onSuccess,
    this.onError,
    this.text = 'تسجيل الدخول بـ Google',
    this.style,
  }) : super(key: key);

  final VoidCallback? onSuccess;
  final Function(String)? onError;
  final String text;
  final ButtonStyle? style;

  static final _logger = Logger('GoogleOAuthButton');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(unifiedAuthProviderProvider);

    return ElevatedButton.icon(
      onPressed: authState is AuthStateLoading
          ? null
          : () => _handleGoogleSignIn(ref, context),
      icon: _buildGoogleIcon(),
      label: _buildButtonText(authState),
      style: style ?? _buildDefaultStyle(context),
    );
  }

  Widget _buildGoogleIcon() {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Icon(Icons.g_mobiledata, color: Colors.red, size: 20),
    );
  }

  Widget _buildButtonText(AuthState authState) {
    if (authState is AuthStateLoading) {
      return const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    return Text(
      text,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: Colors.white,
      ),
    );
  }

  ButtonStyle _buildDefaultStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: const Color(0xFF4285F4), // Google Blue
      foregroundColor: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 2,
    );
  }

  Future<void> _handleGoogleSignIn(WidgetRef ref, BuildContext context) async {
    try {
      _logger.info('🔍 GoogleOAuthButton: Starting Google OAuth sign-in...');

      final authProvider = ref.read(unifiedAuthProviderProvider.notifier);
      final result = await authProvider.signInWithGoogle();

      if (result is AuthResultSuccess) {
        _logger.info('✅ GoogleOAuthButton: Google OAuth successful');
        onSuccess?.call();
      } else if (result is AuthResultFailure) {
        final error = result.error;
        _logger.warning('❌ GoogleOAuthButton: Google OAuth failed: $error');
        onError?.call(error);

        // Show error message to user
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(error),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      _logger.severe('❌ GoogleOAuthButton: Unexpected error: $e');
      final errorMessage = 'خطأ غير متوقع: $e';
      onError?.call(errorMessage);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}

/// Google OAuth button with custom styling
class StyledGoogleOAuthButton extends StatelessWidget {
  const StyledGoogleOAuthButton({
    Key? key,
    this.onSuccess,
    this.onError,
    this.text,
    this.backgroundColor,
    this.textColor,
    this.borderRadius,
    this.padding,
    this.iconSize,
  }) : super(key: key);

  final VoidCallback? onSuccess;
  final Function(String)? onError;
  final String? text;
  final Color? backgroundColor;
  final Color? textColor;
  final double? borderRadius;
  final EdgeInsetsGeometry? padding;
  final double? iconSize;

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        return GoogleOAuthButton(
          onSuccess: onSuccess,
          onError: onError,
          text: text ?? 'تسجيل الدخول بـ Google',
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? const Color(0xFF4285F4),
            foregroundColor: textColor ?? Colors.white,
            padding:
                padding ??
                const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 8),
            ),
            elevation: 2,
          ),
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logger/logger.dart';
import '../theme/app_theme.dart';

final _logger = Logger(
  printer: PrettyPrinter(methodCount: 0, errorMethodCount: 5, lineLength: 50),
);

/// Clean Error Widget - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// Simple error display with user-friendly messages and retry mechanisms
class AppErrorWidget extends StatelessWidget {
  const AppErrorWidget({
    required this.message,
    this.details,
    this.onRetry,
    this.stackTrace,
    this.showDetails = false,
    super.key,
  });

  final String message;
  final String? details;
  final VoidCallback? onRetry;
  final StackTrace? stackTrace;
  final bool showDetails;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error Icon
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            
            const SizedBox(height: AppTheme.spacingM),
            
            // User-friendly error message
            Text(
              _getUserFriendlyMessage(message),
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: AppTheme.spacingM),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Retry button
                if (onRetry != null) ...[
                  ElevatedButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة المحاولة'),
                  ),
                  const SizedBox(width: AppTheme.spacingM),
                ],
                
                // Show details button (in debug mode)
                if (details != null) ...[
                  OutlinedButton.icon(
                    onPressed: () => _showErrorDetails(context),
                    icon: const Icon(Icons.info_outline),
                    label: const Text('التفاصيل'),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// Convert technical errors to user-friendly messages
  String _getUserFriendlyMessage(String errorMessage) {
    final lowerError = errorMessage.toLowerCase();
    
    // Network errors
    if (lowerError.contains('network') || 
        lowerError.contains('connection') ||
        lowerError.contains('timeout')) {
      return 'تحقق من اتصال الإنترنت وحاول مرة أخرى';
    }
    
    // Authentication errors
    if (lowerError.contains('auth') || 
        lowerError.contains('unauthorized') ||
        lowerError.contains('401')) {
      return 'يرجى تسجيل الدخول مرة أخرى';
    }
    
    // Server errors
    if (lowerError.contains('server') || 
        lowerError.contains('500') ||
        lowerError.contains('503')) {
      return 'مشكلة مؤقتة في الخدمة، يرجى المحاولة لاحقاً';
    }
    
    // Not found errors
    if (lowerError.contains('not found') || lowerError.contains('404')) {
      return 'المحتوى المطلوب غير موجود';
    }
    
    // Default user-friendly message
    return 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى';
  }
  
  /// Show detailed error information
  void _showErrorDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل الخطأ'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('رسالة الخطأ:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              SelectableText(
                message,
                style: TextStyle(
                  fontFamily: 'monospace',
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
              
              if (details != null) ...[
                const SizedBox(height: 16),
                const Text('التفاصيل:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                SelectableText(
                  details!,
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              ],
              
              if (stackTrace != null) ...[
                const SizedBox(height: 16),
                const Text('Stack Trace:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                SelectableText(
                  stackTrace.toString(),
                  style: TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          TextButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(
                text: 'Error: $message\nDetails: ${details ?? 'N/A'}\nStack: ${stackTrace ?? 'N/A'}',
              ));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم نسخ تفاصيل الخطأ')),
              );
            },
            child: const Text('نسخ'),
          ),
        ],
      ),
    );
  }
}

/// Simple error boundary widget for protecting UI sections
class ErrorBoundary extends StatefulWidget {
  const ErrorBoundary({
    required this.child,
    this.onError,
    this.errorBuilder,
    super.key,
  });

  final Widget child;
  final void Function(Object error, StackTrace stackTrace)? onError;
  final Widget Function(Object error, StackTrace stackTrace)? errorBuilder;

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      // عرض شاشة خطأ محسنة
      return AppErrorWidget(
        message: 'حدث خطأ غير متوقع',
        details: _error.toString(),
        stackTrace: _stackTrace,
        onRetry: () => setState(() {
          _error = null;
          _stackTrace = null;
        }),
      );
    }

    // استخدام ErrorWidget.builder للأخطاء أثناء البناء
    ErrorWidget.builder = (FlutterErrorDetails details) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _error = details.exception;
            _stackTrace = details.stack;
          });
        }
      });
      
      return AppErrorWidget(
        message: 'خطأ في البناء',
        details: details.exception.toString(),
        stackTrace: details.stack,
        onRetry: () {
          if (mounted) {
            setState(() {
              _error = null;
              _stackTrace = null;
            });
          }
        },
      );
    };

    return widget.child;
  }
}

/// Global error handling utilities
class GlobalErrorHandler {
  /// Handle AsyncValue errors with user-friendly messages
  static Widget handleAsyncError<T>(
    AsyncValue<T> asyncValue, {
    required Widget Function(T data) builder,
    Widget Function()? loadingBuilder,
    Widget Function(Object error, StackTrace stackTrace)? errorBuilder,
    VoidCallback? onRetry,
  }) {
    return asyncValue.when(
      data: builder,
      loading: loadingBuilder ?? () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => errorBuilder?.call(error, stack) ?? 
          AppErrorWidget(
            message: error.toString(),
            stackTrace: stack,
            onRetry: onRetry,
          ),
    );
  }
  
  /// Show error snackbar with user-friendly message
  static void showErrorSnackBar(
    BuildContext context,
    Object error, {
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_getUserFriendlyErrorMessage(error.toString())),
        backgroundColor: Theme.of(context).colorScheme.error,
        action: action,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  /// Convert error to user-friendly message
  static String _getUserFriendlyErrorMessage(String error) {
    final lowerError = error.toLowerCase();
    
    if (lowerError.contains('network') || lowerError.contains('connection')) {
      return 'مشكلة في الاتصال';
    }
    
    if (lowerError.contains('auth') || lowerError.contains('unauthorized')) {
      return 'يرجى تسجيل الدخول';
    }
    
    if (lowerError.contains('server') || lowerError.contains('500')) {
      return 'مشكلة في الخدمة';
    }
    
    return 'حدث خطأ غير متوقع';
  }
}

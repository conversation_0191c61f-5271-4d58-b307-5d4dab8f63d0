import 'package:flutter/material.dart';
import '../../shared/widgets/skeleton_container.dart';

class ProductListItemSkeleton extends StatelessWidget {
  const ProductListItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return const Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Row(
          children: [
            SkeletonContainer(
              width: 80,
              height: 80,
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            Sized<PERSON>ox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SkeletonContainer(width: double.infinity, height: 16),
                  <PERSON><PERSON><PERSON><PERSON>(height: 8),
                  Skeleton<PERSON>ontainer(width: 150, height: 14),
                  <PERSON><PERSON><PERSON><PERSON>(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Skeleton<PERSON>ontainer(width: 80, height: 20),
                      Skeleton<PERSON>ontainer(width: 60, height: 14),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

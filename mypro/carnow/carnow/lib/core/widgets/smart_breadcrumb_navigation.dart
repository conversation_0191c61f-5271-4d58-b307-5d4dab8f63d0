import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:go_router/go_router.dart';

import '../../l10n/app_localizations.dart';

part 'smart_breadcrumb_navigation.g.dart';

/// Safely calculates a color with alpha, with robust error handling.
Color _getSafeColor(ThemeData theme, Color? baseColor, double alpha) {
  try {
    // Use onSurface as a default if baseColor is null.
    final color = baseColor ?? theme.colorScheme.onSurface;

    // A simple check for NaN, Infinity, or other non-finite values.
    if (!alpha.isFinite) {
      // Return the solid color if alpha is not a valid number.
      return color;
    }

    // Safely calculate the alpha value.
    final int alphaInt = (alpha.clamp(0.0, 1.0) * 255).round();

    return color.withAlpha(alphaInt);
  } catch (e) {
    // If any error occurs (e.g., during round()), return a safe solid color.
    return baseColor ?? theme.colorScheme.onSurface;
  }
}

/// نظام التنقل التدريجي (Breadcrumb) الذكي
///
/// يعرض مسار التنقل الحالي مع إمكانية:
/// - النقر للعودة إلى مستوى معين
/// - التنقل السريع بين المستويات
/// - عرض السياق الحالي للمستخدم
/// - دعم الرسوم المتحركة
class SmartBreadcrumbNavigation extends ConsumerWidget {
  const SmartBreadcrumbNavigation({
    super.key,
    this.currentRoute,
    this.maxItems = 4,
    this.showHomeIcon = true,
    this.separator,
    this.style,
    this.onBreadcrumbTap,
  });

  final String? currentRoute;
  final int maxItems;
  final bool showHomeIcon;
  final Widget? separator;
  final TextStyle? style;
  final Function(String route)? onBreadcrumbTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final currentRoute =
        this.currentRoute ??
        GoRouter.of(
          context,
        ).routerDelegate.currentConfiguration.last.matchedLocation;

    final breadcrumbs = _generateBreadcrumbs(context, l10n, currentRoute);

    if (breadcrumbs.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: _buildBreadcrumbItems(context, theme, breadcrumbs),
        ),
      ),
    );
  }

  /// توليد قائمة التنقل التدريجي
  List<BreadcrumbItem> _generateBreadcrumbs(
    BuildContext context,
    AppLocalizations l10n,
    String currentRoute,
  ) {
    final List<BreadcrumbItem> breadcrumbs = [];

    // إضافة الصفحة الرئيسية دائماً
    if (showHomeIcon) {
      breadcrumbs.add(
        BreadcrumbItem(
          title: l10n.home,
          route: '/',
          icon: Icons.home,
          isHome: true,
        ),
      );
    }

    // تحليل المسار الحالي
    final segments = currentRoute
        .split('/')
        .where((s) => s.isNotEmpty)
        .toList();
    String buildRoute = '';

    for (int i = 0; i < segments.length; i++) {
      buildRoute += '/${segments[i]}';
      final segment = segments[i];

      // تخطي الصفحة الرئيسية إذا كانت معروضة بالفعل
      if (buildRoute == '/' && showHomeIcon) continue;

      final breadcrumbItem = _createBreadcrumbItem(
        context,
        l10n,
        segment,
        buildRoute,
        segments,
        i,
      );

      if (breadcrumbItem != null) {
        breadcrumbs.add(breadcrumbItem);
      }
    }

    // تقليل العناصر إذا تجاوزت الحد الأقصى
    if (breadcrumbs.length > maxItems) {
      final firstItem = breadcrumbs.first;
      final lastItems = breadcrumbs.sublist(
        breadcrumbs.length - (maxItems - 2),
      );

      return [
        firstItem,
        const BreadcrumbItem(title: '...', route: '', isEllipsis: true),
        ...lastItems,
      ];
    }

    return breadcrumbs;
  }

  /// إنشاء عنصر تنقل تدريجي
  BreadcrumbItem? _createBreadcrumbItem(
    BuildContext context,
    AppLocalizations l10n,
    String segment,
    String route,
    List<String> allSegments,
    int index,
  ) {
    // قاموس ترجمة الأقسام
    final Map<String, BreadcrumbItemData> segmentMap = {
      'categories': BreadcrumbItemData(
        title: l10n.categories,
        icon: Icons.category,
      ),
      'search': const BreadcrumbItemData(title: 'البحث', icon: Icons.search),
      'garage': const BreadcrumbItemData(title: 'الكراج', icon: Icons.build),
      'cart': BreadcrumbItemData(
        title: l10n.shoppingCart,
        icon: Icons.shopping_cart,
      ),
      'account': BreadcrumbItemData(title: l10n.account, icon: Icons.person),
      'orders': const BreadcrumbItemData(title: 'الطلبات', icon: Icons.receipt),
      'favorites': const BreadcrumbItemData(
        title: 'المفضلة',
        icon: Icons.favorite,
      ),
      'notifications': BreadcrumbItemData(
        title: l10n.notifications,
        icon: Icons.notifications,
      ),
      'chat': const BreadcrumbItemData(title: 'المحادثات', icon: Icons.chat),
      'support': const BreadcrumbItemData(title: 'الدعم', icon: Icons.help),
      'seller': const BreadcrumbItemData(title: 'البائع', icon: Icons.business),
      'products': const BreadcrumbItemData(
        title: 'المنتجات',
        icon: Icons.inventory,
      ),
      'add': const BreadcrumbItemData(title: 'إضافة', icon: Icons.add),
      'edit': const BreadcrumbItemData(title: 'تعديل', icon: Icons.edit),
      'profile': const BreadcrumbItemData(
        title: 'الملف الشخصي',
        icon: Icons.person,
      ),
      'settings': const BreadcrumbItemData(
        title: 'الإعدادات',
        icon: Icons.settings,
      ),
      'my-cars': const BreadcrumbItemData(
        title: 'سياراتي',
        icon: Icons.directions_car,
      ),
      'add-vehicle': const BreadcrumbItemData(
        title: 'إضافة مركبة',
        icon: Icons.add_circle,
      ),
      'stores': const BreadcrumbItemData(title: 'المتاجر', icon: Icons.store),
      'auctions': const BreadcrumbItemData(
        title: 'المزادات',
        icon: Icons.gavel,
      ),
    };

    // البحث عن البيانات المطابقة
    final itemData = segmentMap[segment];

    if (itemData != null) {
      return BreadcrumbItem(
        title: itemData.title,
        route: route,
        icon: itemData.icon,
      );
    }

    // معالجة المعرفات الرقمية (IDs)
    if (RegExp(r'^\d+$').hasMatch(segment)) {
      String? contextTitle;
      IconData? contextIcon;

      // تحديد السياق بناءً على القسم السابق
      if (index > 0) {
        final previousSegment = allSegments[index - 1];
        switch (previousSegment) {
          case 'products':
            contextTitle = 'منتج #$segment';
            contextIcon = Icons.inventory;
            break;
          case 'orders':
            contextTitle = 'طلب #$segment';
            contextIcon = Icons.receipt;
            break;
          case 'chat':
            contextTitle = 'محادثة #$segment';
            contextIcon = Icons.chat;
            break;
          case 'cars':
            contextTitle = 'مركبة #$segment';
            contextIcon = Icons.directions_car;
            break;
          default:
            contextTitle = '#$segment';
            contextIcon = Icons.tag;
        }
      } else {
        contextTitle = '#$segment';
        contextIcon = Icons.tag;
      }

      return BreadcrumbItem(
        title: contextTitle,
        route: route,
        icon: contextIcon,
      );
    }

    // إذا لم نجد مطابقة، استخدم النص كما هو
    return BreadcrumbItem(
      title: _formatSegmentTitle(segment),
      route: route,
      icon: Icons.folder,
    );
  }

  /// تنسيق عنوان القسم
  String _formatSegmentTitle(String segment) {
    return segment
        .replaceAll('-', ' ')
        .replaceAll('_', ' ')
        .split(' ')
        .map(
          (word) => word.isNotEmpty
              ? '${word[0].toUpperCase()}${word.substring(1)}'
              : '',
        )
        .join(' ');
  }

  /// بناء عناصر التنقل التدريجي
  List<Widget> _buildBreadcrumbItems(
    BuildContext context,
    ThemeData theme,
    List<BreadcrumbItem> breadcrumbs,
  ) {
    final List<Widget> items = [];

    for (int i = 0; i < breadcrumbs.length; i++) {
      final breadcrumb = breadcrumbs[i];
      final isLast = i == breadcrumbs.length - 1;

      // إضافة عنصر التنقل
      items.add(_buildBreadcrumbWidget(context, theme, breadcrumb, isLast));

      // إضافة الفاصل (إلا للعنصر الأخير)
      if (!isLast) {
        items.add(_buildSeparator(theme));
      }
    }

    return items;
  }

  /// بناء عنصر تنقل واحد
  Widget _buildBreadcrumbWidget(
    BuildContext context,
    ThemeData theme,
    BreadcrumbItem breadcrumb,
    bool isLast,
  ) {
    final textColor = isLast
        ? theme.colorScheme.primary
        : _getSafeColor(theme, theme.textTheme.bodyMedium?.color, 0.7);

    final Widget content = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (breadcrumb.icon != null) ...[
          Icon(breadcrumb.icon, size: 16, color: textColor),
          const SizedBox(width: 4),
        ],
        Text(
          breadcrumb.title,
          style: (style ?? theme.textTheme.bodyMedium)?.copyWith(
            color: textColor,
            fontWeight: isLast ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ],
    );

    // إذا كان هذا هو العنصر الأخير أو نقاط الحذف، لا نجعله قابل للنقر
    if (isLast || breadcrumb.isEllipsis) {
      return content;
    }

    return InkWell(
      onTap: () {
        if (onBreadcrumbTap != null) {
          onBreadcrumbTap!(breadcrumb.route);
        } else {
          context.go(breadcrumb.route);
        }
      },
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: content,
      ),
    );
  }

  /// بناء الفاصل
  Widget _buildSeparator(ThemeData theme) {
    if (separator != null) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: separator!,
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Icon(
        Icons.chevron_right,
        size: 16,
        color: _getSafeColor(theme, theme.textTheme.bodyMedium?.color, 0.5),
      ),
    );
  }
}

/// عنصر التنقل التدريجي
class BreadcrumbItem {
  const BreadcrumbItem({
    required this.title,
    required this.route,
    this.icon,
    this.isHome = false,
    this.isEllipsis = false,
  });

  final String title;
  final String route;
  final IconData? icon;
  final bool isHome;
  final bool isEllipsis;
}

/// بيانات عنصر التنقل التدريجي
class BreadcrumbItemData {
  const BreadcrumbItemData({required this.title, this.icon});

  final String title;
  final IconData? icon;
}

/// مزود التنقل التدريجي الحالي
@riverpod
class CurrentBreadcrumb extends _$CurrentBreadcrumb {
  @override
  List<BreadcrumbItem> build() {
    return [];
  }

  void setBreadcrumbs(List<BreadcrumbItem> breadcrumbs) {
    state = breadcrumbs;
  }

  void addBreadcrumb(BreadcrumbItem item) {
    state = [...state, item];
  }

  void removeBreadcrumb(int index) {
    if (index >= 0 && index < state.length) {
      state = [...state]..removeAt(index);
    }
  }

  void clear() {
    state = [];
  }
}

/// عرض مضغوط للتنقل التدريجي
class CompactBreadcrumb extends ConsumerWidget {
  const CompactBreadcrumb({super.key, this.currentRoute});

  final String? currentRoute;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentRoute =
        this.currentRoute ??
        GoRouter.of(
          context,
        ).routerDelegate.currentConfiguration.last.matchedLocation;

    // الحصول على الصفحة الحالية فقط
    final segments = currentRoute
        .split('/')
        .where((s) => s.isNotEmpty)
        .toList();
    if (segments.isEmpty) {
      return const SizedBox.shrink();
    }

    final lastSegment = segments.last;
    String title = 'الصفحة';
    IconData icon = Icons.folder;

    // تحديد العنوان والأيقونة
    switch (lastSegment) {
      case 'categories':
        title = 'الفئات';
        icon = Icons.category;
        break;
      case 'search':
        title = 'البحث';
        icon = Icons.search;
        break;
      case 'garage':
        title = 'الكراج';
        icon = Icons.build;
        break;
      case 'cart':
        title = 'سلة التسوق';
        icon = Icons.shopping_cart;
        break;
      case 'account':
        title = 'الحساب';
        icon = Icons.person;
        break;
      default:
        if (RegExp(r'^\d+$').hasMatch(lastSegment)) {
          title = '#$lastSegment';
          icon = Icons.tag;
        } else {
          title = lastSegment.replaceAll('-', ' ');
        }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getSafeColor(
          theme,
          theme.colorScheme.surfaceContainerHighest,
          0.5,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 6),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

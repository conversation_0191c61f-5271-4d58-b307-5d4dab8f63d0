import 'package:flutter/material.dart';

/// مكون مخصص للقوائم القابلة للتوسيع مع تحسينات مرئية
class CustomExpansionTile extends StatelessWidget {
  const CustomExpansionTile({
    required this.title,
    required this.content,
    required this.leadingIcon,
    required this.stepNumber,
    super.key,
    this.isExpanded = false,
    this.onExpansionChanged,
    this.isCompleted = false,
    this.progress = 0,
  });
  final String title;
  final Widget content;
  final bool isExpanded;
  final void Function(bool)? onExpansionChanged;
  final IconData leadingIcon;
  final bool isCompleted;
  final int stepNumber;
  final double progress;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      clipBehavior: Clip.antiAlias,
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          initiallyExpanded: isExpanded,
          onExpansionChanged: onExpansionChanged,
          childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          backgroundColor: isExpanded
              ? theme.colorScheme.surfaceContainerHighest.withAlpha(
                  (0.3 * 255).toInt(),
                )
              : null,
          leading: CircleAvatar(
            backgroundColor: isCompleted
                ? theme.colorScheme.primary
                : theme.colorScheme.surfaceContainerHighest,
            child: isCompleted
                ? Icon(Icons.check, color: theme.colorScheme.onPrimary)
                : Text(
                    stepNumber.toString(),
                    style: TextStyle(
                      color: isExpanded
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
          title: Row(
            children: [
              Icon(
                leadingIcon,
                color: isExpanded
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: isExpanded ? theme.colorScheme.primary : null,
                  fontWeight: isExpanded ? FontWeight.bold : null,
                ),
              ),
              const Spacer(),
              if (progress > 0 && progress < 1 && !isCompleted)
                SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    value: progress,
                    strokeWidth: 2,
                    backgroundColor: theme.colorScheme.surfaceContainerHighest,
                    color: theme.colorScheme.primary,
                  ),
                ),
            ],
          ),
          children: [content],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../l10n/app_localizations.dart';
import '../../../core/auth/unified_auth_provider.dart';

/// Floating Action Button ذكي مع إجراءات سريعة
///
/// يتكيف هذا المكون مع:
/// - حالة المصادقة (مسجل/غير مسجل)
/// - الشاشة الحالية
/// - سياق المستخدم (عادي/بائع)
/// - الإجراءات السريعة حسب الصفحة
class SmartFloatingActionButton extends ConsumerStatefulWidget {
  const SmartFloatingActionButton({
    super.key,
    this.currentRoute,
    this.customActions,
    this.showOnlyPrimary = false,
  });

  final String? currentRoute;
  final List<QuickAction>? customActions;
  final bool showOnlyPrimary;

  @override
  ConsumerState<SmartFloatingActionButton> createState() =>
      _SmartFloatingActionButtonState();
}

class _SmartFloatingActionButtonState
    extends ConsumerState<SmartFloatingActionButton>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _buttonAnimationIcon;
  late Animation<double> _translateButton;
  bool _isOpened = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _buttonAnimationIcon = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _translateButton = Tween<double>(begin: 72, end: -14).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final isAuthenticated = ref.watch(isAuthenticatedProvider);
    final currentRoute =
        widget.currentRoute ??
        GoRouter.of(
          context,
        ).routerDelegate.currentConfiguration.last.matchedLocation;

    // إذا كان المستخدم يريد عرض الزر الأساسي فقط
    if (widget.showOnlyPrimary) {
      return FloatingActionButton(
        onPressed: () =>
            _handlePrimaryAction(context, currentRoute, isAuthenticated),
        child: _getPrimaryIcon(currentRoute, isAuthenticated),
      );
    }

    // الحصول على الإجراءات السريعة
    final quickActions = _getQuickActions(
      context,
      l10n,
      currentRoute,
      isAuthenticated,
    );

    // إذا لم تكن هناك إجراءات إضافية، عرض الزر الأساسي فقط
    if (quickActions.isEmpty) {
      return FloatingActionButton(
        onPressed: () =>
            _handlePrimaryAction(context, currentRoute, isAuthenticated),
        child: _getPrimaryIcon(currentRoute, isAuthenticated),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // الإجراءات السريعة
        ...quickActions.asMap().entries.map((entry) {
          final index = entry.key;
          final action = entry.value;
          return Transform(
            transform: Matrix4.translationValues(
              0,
              _translateButton.value * (quickActions.length - index),
              0,
            ),
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Opacity(
                  opacity: _animationController.value,
                  child: child,
                );
              },
              child: Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Label للإجراء
                    if (_isOpened) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        margin: const EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          color: Theme.of(
                            context,
                          ).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(25),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          action.label,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ],
                    // الزر نفسه
                    FloatingActionButton.small(
                      heroTag: 'fab_${action.label}',
                      onPressed: action.onPressed,
                      backgroundColor: action.backgroundColor,
                      child: Icon(action.icon, color: action.iconColor),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),

        // الزر الرئيسي
        FloatingActionButton(
          heroTag: 'main_fab',
          onPressed: _toggle,
          child: AnimatedIcon(
            icon: AnimatedIcons.menu_close,
            progress: _buttonAnimationIcon,
          ),
        ),
      ],
    );
  }

  /// تبديل حالة الفتح/الإغلاق
  void _toggle() {
    if (_isOpened) {
      _animationController.reverse();
    } else {
      _animationController.forward();
    }
    setState(() {
      _isOpened = !_isOpened;
    });
  }

  /// الحصول على الإجراءات السريعة حسب الصفحة الحالية
  List<QuickAction> _getQuickActions(
    BuildContext context,
    AppLocalizations l10n,
    String currentRoute,
    bool isAuthenticated,
  ) {
    // إذا كانت هناك إجراءات مخصصة، استخدمها
    if (widget.customActions != null) {
      return widget.customActions!;
    }

    final List<QuickAction> actions = [];

    // إجراءات عامة متاحة دائماً
    actions.addAll([
      QuickAction(
        icon: Icons.search,
        label: 'البحث السريع',
        onPressed: () => _navigateWithAnimation(context, '/search'),
      ),
      QuickAction(
        icon: Icons.qr_code_scanner,
        label: 'مسح QR',
        onPressed: () => _navigateWithAnimation(context, '/qr-scanner'),
      ),
    ]);

    // إجراءات حسب الصفحة الحالية
    switch (currentRoute) {
      case '/':
        actions.addAll([
          QuickAction(
            icon: Icons.directions_car,
            label: 'إضافة مركبة',
            onPressed: () => _navigateWithAnimation(context, '/add-vehicle'),
          ),
          QuickAction(
            icon: Icons.category,
            label: 'تصفح الفئات',
            onPressed: () => _navigateWithAnimation(context, '/categories'),
          ),
        ]);
        break;

      case '/garage':
        actions.addAll([
          QuickAction(
            icon: Icons.add_circle,
            label: 'إضافة مركبة',
            onPressed: () => _navigateWithAnimation(context, '/add-vehicle'),
          ),
          QuickAction(
            icon: Icons.history,
            label: 'السجل',
            onPressed: () =>
                _navigateWithAnimation(context, '/maintenance-history'),
          ),
        ]);
        break;

      case '/cart':
        actions.addAll([
          QuickAction(
            icon: Icons.payment,
            label: 'الدفع',
            onPressed: () => _navigateWithAnimation(context, '/checkout'),
            backgroundColor: Colors.green,
          ),
          QuickAction(
            icon: Icons.favorite,
            label: 'المفضلة',
            onPressed: () => _navigateWithAnimation(context, '/favorites'),
          ),
        ]);
        break;
    }

    // إجراءات خاصة بالمستخدمين المسجلين
    if (isAuthenticated) {
      actions.addAll([
        QuickAction(
          icon: Icons.chat,
          label: 'محادثة جديدة',
          onPressed: () => _navigateWithAnimation(context, '/new-chat'),
        ),
        QuickAction(
          icon: Icons.notifications,
          label: 'الإشعارات',
          onPressed: () => _navigateWithAnimation(context, '/notifications'),
        ),
      ]);
    }

    return actions;
  }

  /// الحصول على أيقونة الزر الأساسي
  Widget _getPrimaryIcon(String currentRoute, bool isAuthenticated) {
    switch (currentRoute) {
      case '/':
        return const Icon(Icons.add);
      case '/search':
        return const Icon(Icons.filter_list);
      case '/garage':
        return const Icon(Icons.build);
      case '/cart':
        return const Icon(Icons.shopping_cart_checkout);
      case '/categories':
        return const Icon(Icons.tune);
      default:
        return const Icon(Icons.add);
    }
  }

  /// معالجة الإجراء الأساسي
  void _handlePrimaryAction(
    BuildContext context,
    String currentRoute,
    bool isAuthenticated,
  ) {
    switch (currentRoute) {
      case '/':
        _navigateWithAnimation(context, '/add-vehicle');
        break;
      case '/search':
        _showFilterDialog(context);
        break;
      case '/garage':
        _navigateWithAnimation(context, '/add-vehicle');
        break;
      case '/cart':
        _navigateWithAnimation(context, '/checkout');
        break;
      case '/categories':
        _showCategoryFilter(context);
        break;
      default:
        _showQuickActionSheet(context);
    }
  }

  /// التنقل مع الرسوم المتحركة
  void _navigateWithAnimation(BuildContext context, String route) {
    _toggle();
    Future.delayed(const Duration(milliseconds: 300), () {
      if (context.mounted) {
        context.push(route);
      }
    });
  }

  /// عرض حوار الفلاتر
  void _showFilterDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.3,
        expand: false,
        builder: (context, scrollController) {
          return Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'فلترة النتائج',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView(
                    controller: scrollController,
                    children: [
                      ListTile(
                        leading: const Icon(Icons.category),
                        title: const Text('الفئة'),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () {
                          Navigator.pop(context);
                          context.push('/category-filter');
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.attach_money),
                        title: const Text('السعر'),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () {
                          Navigator.pop(context);
                          context.push('/price-filter');
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.location_on),
                        title: const Text('الموقع'),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () {
                          Navigator.pop(context);
                          context.push('/location-filter');
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// عرض فلتر الفئات
  void _showCategoryFilter(BuildContext context) {
    // تنفيذ فلتر الفئات
    _showFilterDialog(context);
  }

  /// عرض قائمة الإجراءات السريعة
  void _showQuickActionSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'الإجراءات السريعة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              crossAxisCount: 3,
              children: [
                _buildQuickActionTile(
                  context,
                  Icons.search,
                  'البحث',
                  () => context.push('/search'),
                ),
                _buildQuickActionTile(
                  context,
                  Icons.qr_code_scanner,
                  'مسح QR',
                  () => context.push('/qr-scanner'),
                ),
                _buildQuickActionTile(
                  context,
                  Icons.directions_car,
                  'إضافة مركبة',
                  () => context.push('/add-vehicle'),
                ),
                _buildQuickActionTile(
                  context,
                  Icons.chat,
                  'محادثة',
                  () => context.push('/new-chat'),
                ),
                _buildQuickActionTile(
                  context,
                  Icons.notifications,
                  'الإشعارات',
                  () => context.push('/notifications'),
                ),
                _buildQuickActionTile(
                  context,
                  Icons.help,
                  'المساعدة',
                  () => context.push('/help'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر الإجراء السريع
  Widget _buildQuickActionTile(
    BuildContext context,
    IconData icon,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Theme.of(
            context,
          ).colorScheme.surfaceContainerHighest.withAlpha(128),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32),
            const SizedBox(height: 8),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// فئة تمثل إجراء سريع
class QuickAction {
  const QuickAction({
    required this.icon,
    required this.label,
    required this.onPressed,
    this.backgroundColor,
    this.iconColor,
  });

  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
}

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AdvancedFilterDialog extends HookConsumerWidget {
  const AdvancedFilterDialog({
    required this.onFiltersApplied,
    super.key,
    this.initialFilters = const {},
  });
  final void Function(Map<String, dynamic>) onFiltersApplied;
  final Map<String, dynamic> initialFilters;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final minPriceController = useTextEditingController(
      text: initialFilters['minPrice']?.toString() ?? '',
    );
    final maxPriceController = useTextEditingController(
      text: initialFilters['maxPrice']?.toString() ?? '',
    );

    final minRating = useState<double>(
      (initialFilters['minRating'] as num?)?.toDouble() ?? 1.0,
    );
    final showFavoritesOnly = useState<bool>(
      (initialFilters['showFavoritesOnly'] as bool?) ?? false,
    );
    final condition = useState<String?>(initialFilters['condition'] as String?);
    final sortBy = useState<String>(
      (initialFilters['sortBy'] as String?) ?? 'name',
    );
    final sortOrder = useState<String>(
      (initialFilters['sortOrder'] as String?) ?? 'asc',
    );

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'فلترة متقدمة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Price Range
            Text(
              'نطاق السعر',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: minPriceController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'الحد الأدنى',
                      prefixText: 'د.ل ',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: maxPriceController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'الحد الأقصى',
                      prefixText: 'د.ل ',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Rating Filter
            Text(
              'الحد الأدنى للتقييم',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Slider(
                    value: minRating.value,
                    min: 1,
                    max: 5,
                    divisions: 8,
                    label: minRating.value == minRating.value.toInt()
                        ? minRating.value.toInt().toString()
                        : minRating.value.toStringAsFixed(1),
                    onChanged: (value) => minRating.value = value,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withAlpha((0.1 * 255).toInt()),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.star,
                        color: Theme.of(context).colorScheme.primary,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        minRating.value == minRating.value.toInt()
                            ? minRating.value.toInt().toString()
                            : minRating.value.toStringAsFixed(1),
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Favorites Only
            CheckboxListTile(
              title: const Text('المفضلة فقط'),
              subtitle: const Text('عرض القطع المضافة للمفضلة فقط'),
              value: showFavoritesOnly.value,
              onChanged: (value) => showFavoritesOnly.value = value ?? false,
              contentPadding: EdgeInsets.zero,
            ),

            const SizedBox(height: 16),

            // Condition Filter
            Text(
              'حالة القطعة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String?>(
              value: condition.value,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'اختر حالة القطعة',
              ),
              items: [
                const DropdownMenuItem<String?>(child: Text('جميع الحالات')),
                ...['جديد', 'مستعمل', 'مجدد'].map(
                  (cond) =>
                      DropdownMenuItem<String>(value: cond, child: Text(cond)),
                ),
              ],
              onChanged: (value) => condition.value = value,
            ),

            const SizedBox(height: 24),

            // Sorting
            Text(
              'الترتيب',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: DropdownButtonFormField<String>(
                    value: sortBy.value,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: 'ترتيب حسب',
                    ),
                    items: const [
                      DropdownMenuItem(value: 'name', child: Text('الاسم')),
                      DropdownMenuItem(value: 'price', child: Text('السعر')),
                      DropdownMenuItem(value: 'rating', child: Text('التقييم')),
                      DropdownMenuItem(
                        value: 'created_at',
                        child: Text('تاريخ الإضافة'),
                      ),
                    ],
                    onChanged: (value) => sortBy.value = value ?? 'name',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: sortOrder.value,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: 'النوع',
                    ),
                    items: const [
                      DropdownMenuItem(value: 'asc', child: Text('تصاعدي')),
                      DropdownMenuItem(value: 'desc', child: Text('تنازلي')),
                    ],
                    onChanged: (value) => sortOrder.value = value ?? 'asc',
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      // Reset filters
                      minPriceController.clear();
                      maxPriceController.clear();
                      minRating.value = 1.0;
                      showFavoritesOnly.value = false;
                      condition.value = null;
                      sortBy.value = 'name';
                      sortOrder.value = 'asc';
                    },
                    child: const Text('إعادة تعيين'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FilledButton(
                    onPressed: () {
                      final filters = <String, dynamic>{
                        if (minPriceController.text.isNotEmpty)
                          'minPrice': double.tryParse(minPriceController.text),
                        if (maxPriceController.text.isNotEmpty)
                          'maxPrice': double.tryParse(maxPriceController.text),
                        'minRating': minRating.value,
                        'showFavoritesOnly': showFavoritesOnly.value,
                        if (condition.value != null)
                          'condition': condition.value,
                        'sortBy': sortBy.value,
                        'sortOrder': sortOrder.value,
                      };

                      Navigator.of(context).pop();
                      onFiltersApplied(filters);
                    },
                    child: const Text('تطبيق الفلاتر'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

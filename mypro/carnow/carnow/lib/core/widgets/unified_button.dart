import 'package:flutter/material.dart';
import '../utils/unified_theme_extension.dart';

/// Unified button system for Material 3 Expressive
/// نظام أزرار موحد يضمن تجربة متسقة عبر التطبيق
enum UnifiedButtonType { primary, secondary, tertiary, destructive, ghost }

enum UnifiedButtonSize { small, medium, large }

class UnifiedButton extends StatelessWidget {
  const UnifiedButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.type = UnifiedButtonType.primary,
    this.size = UnifiedButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isExpanded = false,
    this.isEnabled = true,
  });

  /// Primary button - main call to action
  const UnifiedButton.primary({
    super.key,
    required this.text,
    required this.onPressed,
    this.size = UnifiedButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isExpanded = false,
    this.isEnabled = true,
  }) : type = UnifiedButtonType.primary;

  /// Secondary button - secondary actions
  const UnifiedButton.secondary({
    super.key,
    required this.text,
    required this.onPressed,
    this.size = UnifiedButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isExpanded = false,
    this.isEnabled = true,
  }) : type = UnifiedButtonType.secondary;

  /// Tertiary button - minimal emphasis
  const UnifiedButton.tertiary({
    super.key,
    required this.text,
    required this.onPressed,
    this.size = UnifiedButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isExpanded = false,
    this.isEnabled = true,
  }) : type = UnifiedButtonType.tertiary;

  /// Destructive button - dangerous actions
  const UnifiedButton.destructive({
    super.key,
    required this.text,
    required this.onPressed,
    this.size = UnifiedButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isExpanded = false,
    this.isEnabled = true,
  }) : type = UnifiedButtonType.destructive;

  /// Ghost button - minimal visual weight
  const UnifiedButton.ghost({
    super.key,
    required this.text,
    required this.onPressed,
    this.size = UnifiedButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isExpanded = false,
    this.isEnabled = true,
  }) : type = UnifiedButtonType.ghost;

  final String text;
  final VoidCallback? onPressed;
  final UnifiedButtonType type;
  final UnifiedButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isExpanded;
  final bool isEnabled;

  @override
  Widget build(BuildContext context) {
    final bool enabled = isEnabled && onPressed != null && !isLoading;

    return SizedBox(
      width: isExpanded ? double.infinity : null,
      child: _buildButton(context, enabled),
    );
  }

  Widget _buildButton(BuildContext context, bool enabled) {
    switch (type) {
      case UnifiedButtonType.primary:
        return _buildFilledButton(context, enabled);
      case UnifiedButtonType.secondary:
        return _buildOutlinedButton(context, enabled);
      case UnifiedButtonType.tertiary:
      case UnifiedButtonType.ghost:
        return _buildTextButton(context, enabled);
      case UnifiedButtonType.destructive:
        return _buildDestructiveButton(context, enabled);
    }
  }

  Widget _buildFilledButton(BuildContext context, bool enabled) {
    return FilledButton(
      onPressed: enabled ? onPressed : null,
      style: FilledButton.styleFrom(
        backgroundColor: context.colorScheme.primary,
        foregroundColor: context.colorScheme.onPrimary,
        disabledBackgroundColor: context.colorScheme.onSurface.withValues(
          alpha: 0.12,
        ),
        disabledForegroundColor: context.colorScheme.onSurface.withValues(
          alpha: 0.38,
        ),
        elevation: enabled ? 2 : 0,
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        ),
        textStyle: _getTextStyle(context),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildOutlinedButton(BuildContext context, bool enabled) {
    return OutlinedButton(
      onPressed: enabled ? onPressed : null,
      style: OutlinedButton.styleFrom(
        foregroundColor: context.colorScheme.primary,
        disabledForegroundColor: context.colorScheme.onSurface.withValues(
          alpha: 0.38,
        ),
        side: BorderSide(
          color: enabled
              ? context.colorScheme.outline
              : context.colorScheme.onSurface.withValues(alpha: 0.12),
        ),
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        ),
        textStyle: _getTextStyle(context),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildTextButton(BuildContext context, bool enabled) {
    return TextButton(
      onPressed: enabled ? onPressed : null,
      style: TextButton.styleFrom(
        foregroundColor: type == UnifiedButtonType.ghost
            ? context.colorScheme.onSurface
            : context.colorScheme.primary,
        disabledForegroundColor: context.colorScheme.onSurface.withValues(
          alpha: 0.38,
        ),
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        ),
        textStyle: _getTextStyle(context),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildDestructiveButton(BuildContext context, bool enabled) {
    return FilledButton(
      onPressed: enabled ? onPressed : null,
      style: FilledButton.styleFrom(
        backgroundColor: context.colorScheme.error,
        foregroundColor: context.colorScheme.onError,
        disabledBackgroundColor: context.colorScheme.onSurface.withValues(
          alpha: 0.12,
        ),
        disabledForegroundColor: context.colorScheme.onSurface.withValues(
          alpha: 0.38,
        ),
        elevation: enabled ? 2 : 0,
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        ),
        textStyle: _getTextStyle(context),
      ),
      child: _buildButtonContent(context),
    );
  }

  Widget _buildButtonContent(BuildContext context) {
    if (isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: _getIconSize(),
            height: _getIconSize(),
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                type == UnifiedButtonType.primary ||
                        type == UnifiedButtonType.destructive
                    ? context.colorScheme.onPrimary
                    : context.colorScheme.primary,
              ),
            ),
          ),
          SizedBox(width: context.paddingSmall),
          Text(text),
        ],
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: _getIconSize()),
          SizedBox(width: context.paddingSmall),
          Text(text),
        ],
      );
    }

    return Text(text);
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case UnifiedButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case UnifiedButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
      case UnifiedButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 32, vertical: 16);
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case UnifiedButtonSize.small:
        return 16;
      case UnifiedButtonSize.medium:
        return 20;
      case UnifiedButtonSize.large:
        return 24;
    }
  }

  double _getIconSize() {
    switch (size) {
      case UnifiedButtonSize.small:
        return 16;
      case UnifiedButtonSize.medium:
        return 20;
      case UnifiedButtonSize.large:
        return 24;
    }
  }

  TextStyle _getTextStyle(BuildContext context) {
    switch (size) {
      case UnifiedButtonSize.small:
        return context.labelMedium ?? const TextStyle(fontSize: 12);
      case UnifiedButtonSize.medium:
        return context.labelLarge ?? const TextStyle(fontSize: 14);
      case UnifiedButtonSize.large:
        return context.titleMedium ?? const TextStyle(fontSize: 16);
    }
  }
}

/// Floating Action Button with unified styling
class UnifiedFAB extends StatelessWidget {
  const UnifiedFAB({
    super.key,
    required this.onPressed,
    required this.icon,
    this.label,
    this.isExtended = false,
    this.isSmall = false,
  });

  final VoidCallback? onPressed;
  final IconData icon;
  final String? label;
  final bool isExtended;
  final bool isSmall;

  @override
  Widget build(BuildContext context) {
    if (isExtended && label != null) {
      return FloatingActionButton.extended(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(label!),
        backgroundColor: context.colorScheme.primaryContainer,
        foregroundColor: context.colorScheme.onPrimaryContainer,
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      );
    }

    if (isSmall) {
      return FloatingActionButton.small(
        onPressed: onPressed,
        backgroundColor: context.colorScheme.primaryContainer,
        foregroundColor: context.colorScheme.onPrimaryContainer,
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Icon(icon),
      );
    }

    return FloatingActionButton(
      onPressed: onPressed,
      backgroundColor: context.colorScheme.primaryContainer,
      foregroundColor: context.colorScheme.onPrimaryContainer,
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Icon(icon),
    );
  }
}

/// Button group for related actions
class UnifiedButtonGroup extends StatelessWidget {
  const UnifiedButtonGroup({
    super.key,
    required this.buttons,
    this.axis = Axis.horizontal,
    this.spacing = 8.0,
    this.isExpanded = false,
  });

  final List<UnifiedButton> buttons;
  final Axis axis;
  final double spacing;
  final bool isExpanded;

  @override
  Widget build(BuildContext context) {
    if (axis == Axis.horizontal) {
      return Row(
        mainAxisSize: isExpanded ? MainAxisSize.max : MainAxisSize.min,
        children: buttons
            .asMap()
            .entries
            .map((entry) {
              final int index = entry.key;
              final UnifiedButton button = entry.value;

              return [
                if (isExpanded) Expanded(child: button) else button,
                if (index < buttons.length - 1) SizedBox(width: spacing),
              ];
            })
            .expand((element) => element)
            .toList(),
      );
    } else {
      return Column(
        mainAxisSize: isExpanded ? MainAxisSize.max : MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: buttons
            .asMap()
            .entries
            .map((entry) {
              final int index = entry.key;
              final UnifiedButton button = entry.value;

              return [
                button,
                if (index < buttons.length - 1) SizedBox(height: spacing),
              ];
            })
            .expand((element) => element)
            .toList(),
      );
    }
  }
}

/// Icon button with unified styling
class UnifiedIconButton extends StatelessWidget {
  const UnifiedIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.type = UnifiedButtonType.ghost,
    this.size = UnifiedButtonSize.medium,
    this.tooltip,
  });

  final IconData icon;
  final VoidCallback? onPressed;
  final UnifiedButtonType type;
  final UnifiedButtonSize size;
  final String? tooltip;

  @override
  Widget build(BuildContext context) {
    final Widget button = IconButton(
      onPressed: onPressed,
      icon: Icon(icon, size: _getIconSize()),
      style: IconButton.styleFrom(
        foregroundColor: _getForegroundColor(context),
        backgroundColor: _getBackgroundColor(context),
        padding: EdgeInsets.all(_getPadding()),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          side: _getBorder(context),
        ),
      ),
    );

    if (tooltip != null) {
      return Tooltip(message: tooltip!, child: button);
    }

    return button;
  }

  double _getIconSize() {
    switch (size) {
      case UnifiedButtonSize.small:
        return 16;
      case UnifiedButtonSize.medium:
        return 20;
      case UnifiedButtonSize.large:
        return 24;
    }
  }

  double _getPadding() {
    switch (size) {
      case UnifiedButtonSize.small:
        return 8;
      case UnifiedButtonSize.medium:
        return 12;
      case UnifiedButtonSize.large:
        return 16;
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case UnifiedButtonSize.small:
        return 8;
      case UnifiedButtonSize.medium:
        return 12;
      case UnifiedButtonSize.large:
        return 16;
    }
  }

  Color? _getForegroundColor(BuildContext context) {
    switch (type) {
      case UnifiedButtonType.primary:
        return context.colorScheme.onPrimary;
      case UnifiedButtonType.secondary:
        return context.colorScheme.primary;
      case UnifiedButtonType.destructive:
        return context.colorScheme.error;
      case UnifiedButtonType.tertiary:
      case UnifiedButtonType.ghost:
        return context.colorScheme.onSurface;
    }
  }

  Color? _getBackgroundColor(BuildContext context) {
    switch (type) {
      case UnifiedButtonType.primary:
        return context.colorScheme.primary;
      case UnifiedButtonType.secondary:
        return context.colorScheme.secondaryContainer;
      case UnifiedButtonType.destructive:
        return context.colorScheme.errorContainer;
      case UnifiedButtonType.tertiary:
      case UnifiedButtonType.ghost:
        return Colors.transparent;
    }
  }

  BorderSide _getBorder(BuildContext context) {
    if (type == UnifiedButtonType.secondary) {
      return BorderSide(color: context.colorScheme.outline);
    }
    return BorderSide.none;
  }
}

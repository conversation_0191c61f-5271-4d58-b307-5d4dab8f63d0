import 'package:flutter/material.dart';
import '../utils/unified_theme_extension.dart';

/// Unified card widget for consistent Material 3 styling across the app
class UnifiedCard extends StatelessWidget {
  const UnifiedCard({
    super.key,
    required this.child,
    this.backgroundColor,
    this.borderColor,
    this.elevation,
    this.margin,
    this.padding,
    this.onTap,
    this.isSelected = false,
  });

  /// Creates a card for displaying statistics/metrics
  factory UnifiedCard.stats({
    Key? key,
    required String title,
    required String value,
    required IconData icon,
    Color? iconColor,
    VoidCallback? onTap,
  }) {
    return UnifiedCard(
      key: key,
      onTap: onTap,
      child: _StatsContent(
        title: title,
        value: value,
        icon: icon,
        iconColor: iconColor,
      ),
    );
  }

  /// Creates a card for settings items
  factory UnifiedCard.settings({
    Key? key,
    required String title,
    required String subtitle,
    required IconData icon,
    VoidCallback? onTap,
    bool isDestructive = false,
  }) {
    return UnifiedCard(
      key: key,
      onTap: onTap,
      child: _SettingsContent(
        title: title,
        subtitle: subtitle,
        icon: icon,
        isDestructive: isDestructive,
      ),
    );
  }

  final Widget child;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? elevation;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    Widget card = Card(
      color: backgroundColor ?? context.colorScheme.surface,
      elevation: elevation ?? (isSelected ? 4 : 2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color:
              borderColor ??
              (isSelected
                  ? context.colorScheme.primary
                  : context.colorScheme.outline.withValues(alpha: 0.12)),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Padding(
        padding: padding ?? EdgeInsets.all(context.paddingMedium),
        child: child,
      ),
    );

    if (margin != null) {
      card = Container(margin: margin, child: card);
    }

    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: card,
      );
    }

    return card;
  }
}

/// Content widget for stats cards
class _StatsContent extends StatelessWidget {
  const _StatsContent({
    required this.title,
    required this.value,
    required this.icon,
    this.iconColor,
  });

  final String title;
  final String value;
  final IconData icon;
  final Color? iconColor;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: context.bodyMedium?.copyWith(
                color: context.colorScheme.onSurfaceVariant,
              ),
            ),
            Icon(
              icon,
              size: 20,
              color: iconColor ?? context.colorScheme.primary,
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: context.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: context.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}

/// Content widget for settings cards
class _SettingsContent extends StatelessWidget {
  const _SettingsContent({
    required this.title,
    required this.subtitle,
    required this.icon,
    this.isDestructive = false,
  });

  final String title;
  final String subtitle;
  final IconData icon;
  final bool isDestructive;

  @override
  Widget build(BuildContext context) {
    final color = isDestructive
        ? context.colorScheme.error
        : context.colorScheme.onSurface;

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isDestructive
                ? context.colorScheme.error.withValues(alpha: 0.1)
                : context.colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: isDestructive
                ? context.colorScheme.error
                : context.colorScheme.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: context.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                subtitle,
                style: context.bodySmall?.copyWith(
                  color: color.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
        Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: color.withValues(alpha: 0.4),
        ),
      ],
    );
  }
}

/// Product card widget for displaying product information with loading state
class ProductCard extends StatelessWidget {
  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.isLoading = false,
  });

  final dynamic product;
  final VoidCallback? onTap;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const ProductCardSkeleton();
    }

    return UnifiedCard(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: AspectRatio(
              aspectRatio: 16 / 12,
              child: product.images?.isNotEmpty == true
                  ? Image.network(
                      product.images!.first,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        color: context.colorScheme.surfaceContainerHighest,
                        child: Icon(
                          Icons.image_not_supported,
                          color: context.colorScheme.onSurfaceVariant,
                          size: 32,
                        ),
                      ),
                    )
                  : Container(
                      color: context.colorScheme.surfaceContainerHighest,
                      child: Icon(
                        Icons.image_not_supported,
                        color: context.colorScheme.onSurfaceVariant,
                        size: 32,
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 12),

          // Product Info
          Text(
            product.name ?? 'Unknown Product',
            style: context.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            '${product.price ?? 0} ر.س',
            style: context.bodyLarge?.copyWith(
              color: context.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (product.condition != null) ...[
            const SizedBox(height: 4),
            Text(
              product.condition.name ?? 'Unknown',
              style: context.bodySmall?.copyWith(
                color: context.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Skeleton loading widget for ProductCard
class ProductCardSkeleton extends StatelessWidget {
  const ProductCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return UnifiedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image skeleton
          Container(
            height: 120,
            decoration: BoxDecoration(
              color: context.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          const SizedBox(height: 12),

          // Title skeleton
          Container(
            height: 16,
            width: double.infinity,
            decoration: BoxDecoration(
              color: context.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 8),

          // Price skeleton
          Container(
            height: 14,
            width: 80,
            decoration: BoxDecoration(
              color: context.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 4),

          // Condition skeleton
          Container(
            height: 12,
            width: 60,
            decoration: BoxDecoration(
              color: context.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }
}

/// Settings card widget for settings screens
class SettingsCard extends StatelessWidget {
  const SettingsCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    this.onTap,
    this.isDestructive = false,
  });

  final String title;
  final String subtitle;
  final IconData icon;
  final VoidCallback? onTap;
  final bool isDestructive;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: _SettingsContent(
          title: title,
          subtitle: subtitle,
          icon: icon,
          isDestructive: isDestructive,
        ),
      ),
    );
  }
}

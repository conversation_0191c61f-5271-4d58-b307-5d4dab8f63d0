import 'package:flutter/material.dart';

class StarRating extends StatelessWidget {
  const StarRating({
    required this.rating,
    super.key,
    this.size = 20,
    this.showRating = true,
    this.color,
    this.onRatingChanged,
  });
  final double rating;
  final double size;
  final bool showRating;
  final Color? color;
  final void Function(double)? onRatingChanged;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final starColor = color ?? theme.colorScheme.primary;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...List.generate(5, (index) {
          final starIndex = index + 1;
          return GestureDetector(
            onTap: onRatingChanged != null
                ? () => onRatingChanged!(starIndex.toDouble())
                : null,
            child: Icon(
              _getStarIcon(starIndex),
              size: size,
              color: _getStarColor(starIndex, starColor),
            ),
          );
        }),
        if (showRating) ...[
          const SizedBox(width: 8),
          Text(
            rating.toStringAsFixed(1),
            style: TextStyle(
              fontSize: size * 0.8,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  IconData _getStarIcon(int starIndex) {
    if (rating >= starIndex) {
      return Icons.star;
    } else if (rating >= starIndex - 0.5) {
      return Icons.star_half;
    } else {
      return Icons.star_border;
    }
  }

  Color _getStarColor(int starIndex, Color baseColor) {
    if (rating >= starIndex) {
      return baseColor;
    } else if (rating >= starIndex - 0.5) {
      return baseColor;
    } else {
      return Colors.grey.shade300;
    }
  }
}

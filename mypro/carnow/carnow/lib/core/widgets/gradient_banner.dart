import 'package:flutter/material.dart';

class GradientBanner extends StatelessWidget {
  const GradientBanner({
    required this.title,
    required this.subtitle,
    required this.buttonText,
    required this.onButtonPressed,
    required this.gradientColors,
    super.key,
    this.icon,
    this.buttonBackgroundColor,
    this.buttonTextColor,
  });
  final String title;
  final String subtitle;
  final String buttonText;
  final VoidCallback onButtonPressed;
  final List<Color> gradientColors;
  final IconData? icon;
  final Color? buttonBackgroundColor;
  final Color? buttonTextColor;

  @override
  Widget build(BuildContext context) => Container(
    margin: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: gradientColors,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(20),
    ),
    child: Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null) ...[
            Icon(icon, color: Colors.white, size: 32),
            const SizedBox(height: 12),
          ],
          Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: const TextStyle(fontSize: 16, color: Colors.white),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: 150,
            child: FilledButton(
              onPressed: onButtonPressed,
              style: FilledButton.styleFrom(
                backgroundColor: buttonBackgroundColor ?? Colors.white,
                foregroundColor: buttonTextColor ?? gradientColors.first,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Text(
                buttonText,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

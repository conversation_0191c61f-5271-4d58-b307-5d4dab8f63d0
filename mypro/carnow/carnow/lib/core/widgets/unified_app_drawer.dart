import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../l10n/app_localizations.dart';
import '../auth/unified_auth_provider.dart';

// ignore_for_file: unused_element

/// Drawer التنقل الموحد لتطبيق CarNow
///
/// يوفر قائمة تنقل شاملة مع:
/// - الأقسام الرئيسية للتطبيق
/// - إدارة حالة المصادقة
/// - تخصيص المحتوى حسب نوع المستخدم
/// - تصميم عصري ومتجاوب
class UnifiedAppDrawer extends ConsumerWidget {
  const UnifiedAppDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    final currentUser = ref.watch(currentUserProvider);

    final currentPath = GoRouterState.of(context).uri.path;

    // Build navigation destinations
    final destinations = <_DrawerRouteDestination>[
      _DrawerRouteDestination(
        path: '/',
        icon: const Icon(Icons.home_outlined),
        selectedIcon: const Icon(Icons.home),
        label: l10n.home,
      ),
      _DrawerRouteDestination(
        path: '/categories',
        icon: const Icon(Icons.category_outlined),
        selectedIcon: const Icon(Icons.category),
        label: l10n.categories,
      ),
      const _DrawerRouteDestination(
        path: '/search',
        icon: Icon(Icons.search_outlined),
        selectedIcon: Icon(Icons.search),
        label: 'البحث',
      ),
      const _DrawerRouteDestination(
        path: '/garage',
        icon: Icon(Icons.garage_outlined),
        selectedIcon: Icon(Icons.garage),
        label: 'الكراج',
      ),
      const _DrawerRouteDestination(
        path: '/store',
        icon: Icon(Icons.store_outlined),
        selectedIcon: Icon(Icons.store),
        label: 'المتجر',
      ),
      _DrawerRouteDestination(
        path: '/account',
        icon: const Icon(Icons.person_outline),
        selectedIcon: const Icon(Icons.person),
        label: l10n.account,
      ),
    ];

    // Compute selected index among destinations only
    final selectedIndex = destinations.indexWhere(
      (d) => currentPath == d.path || (d.path == '/' && currentPath == '/'),
    );

    // Compose children: header + divider + destinations + footer
    final drawerChildren = <Widget>[
      _buildDrawerHeader(context, theme, l10n, isAuthenticated, currentUser),
      const Divider(),
      ...destinations.map(
        (d) => NavigationDrawerDestination(
          icon: d.icon,
          selectedIcon: d.selectedIcon,
          label: Text(d.label),
        ),
      ),
      const Divider(),
      _buildDrawerFooter(context, theme, l10n, ref),
    ];

    return NavigationDrawer(
      selectedIndex: selectedIndex >= 0 ? selectedIndex : 0,
      onDestinationSelected: (index) {
        final destination = destinations[index];
        Navigator.of(context).pop();
        context.go(destination.path);
      },
      children: drawerChildren,
    );
  }

  /// بناء Header الخاص بالمستخدم
  Widget _buildDrawerHeader(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
    bool isAuthenticated,
    dynamic currentUser,
  ) {
    return UserAccountsDrawerHeader(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.primaryColor,
            theme.primaryColor.withAlpha((0.8 * 255).toInt()),
          ],
        ),
      ),
      accountName: Text(
        isAuthenticated && currentUser?.email != null
            ? 'مرحباً بك'
            : 'مرحباً بك',
        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
      ),
      accountEmail: Text(
        isAuthenticated && currentUser?.email != null
            ? currentUser!.email
            : 'مستخدم زائر',
        style: const TextStyle(fontSize: 14),
      ),
      currentAccountPicture: CircleAvatar(
        backgroundColor: Colors.white,
        child: Icon(
          isAuthenticated ? Icons.person : Icons.person_outline,
          color: theme.primaryColor,
          size: 40,
        ),
      ),
      otherAccountsPictures: [
        if (!isAuthenticated)
          CircleAvatar(
            backgroundColor: Colors.white.withAlpha((0.9 * 255).toInt()),
            child: IconButton(
              icon: Icon(Icons.login, color: theme.primaryColor, size: 20),
              onPressed: () {
                Navigator.of(context).pop();
                context.push('/login');
              },
            ),
          ),
      ],
    );
  }

  /// الأقسام الرئيسية
  Widget _buildMainSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Column(
      children: [
        _buildSectionTitle('الأقسام الرئيسية'),
        _buildDrawerItem(
          context: context,
          icon: Icons.home_outlined,
          activeIcon: Icons.home,
          title: l10n.home,
          onTap: () => _navigateAndPop(context, '/'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.category_outlined,
          activeIcon: Icons.category,
          title: l10n.categories,
          onTap: () => _navigateAndPop(context, '/categories'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.search_outlined,
          activeIcon: Icons.search,
          title: 'البحث',
          onTap: () => _navigateAndPop(context, '/search'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.store_outlined,
          activeIcon: Icons.store,
          title: 'المتاجر',
          onTap: () => _navigateAndPop(context, '/stores'),
        ),
      ],
    );
  }

  /// أقسام السيارات والكراج
  Widget _buildVehicleSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Column(
      children: [
        _buildSectionTitle('الكراج والمركبات'),
        _buildDrawerItem(
          context: context,
          icon: Icons.directions_car_outlined,
          activeIcon: Icons.directions_car,
          title: 'سياراتي',
          onTap: () => _navigateAndPop(context, '/my-cars'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.build_outlined,
          activeIcon: Icons.build,
          title: 'الكراج الذكي',
          onTap: () => _navigateAndPop(context, '/garage'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.qr_code_scanner_outlined,
          activeIcon: Icons.qr_code_scanner,
          title: 'مسح VIN',
          onTap: () => _navigateAndPop(context, '/vin-scanner'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.search_outlined,
          activeIcon: Icons.search,
          title: 'البحث التدريجي للمركبات',
          onTap: () => _navigateAndPop(context, '/progressive-vehicle-search'),
        ),
      ],
    );
  }

  /// التسوق والتجارة
  Widget _buildShoppingSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    bool isAuthenticated,
  ) {
    return Column(
      children: [
        _buildSectionTitle('التسوق والتجارة'),
        _buildDrawerItem(
          context: context,
          icon: Icons.shopping_cart_outlined,
          activeIcon: Icons.shopping_cart,
          title: l10n.shoppingCart,
          onTap: () => _navigateAndPop(context, '/cart'),
        ),
        if (isAuthenticated) ...[
          _buildDrawerItem(
            context: context,
            icon: Icons.receipt_outlined,
            activeIcon: Icons.receipt,
            title: 'طلباتي',
            onTap: () => _navigateAndPop(context, '/orders'),
          ),
          _buildDrawerItem(
            context: context,
            icon: Icons.favorite_outline,
            activeIcon: Icons.favorite,
            title: 'المفضلة',
            onTap: () => _navigateAndPop(context, '/favorites'),
          ),
        ],
        _buildDrawerItem(
          context: context,
          icon: Icons.gavel_outlined,
          activeIcon: Icons.gavel,
          title: 'المزادات',
          onTap: () => _navigateAndPop(context, '/auctions'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.business_outlined,
          activeIcon: Icons.business,
          title: 'أن تصبح بائع',
          onTap: () => _navigateAndPop(context, '/seller/application'),
        ),
      ],
    );
  }

  /// الأقسام الخاصة بالمستخدمين المسجلين
  Widget _buildUserSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Column(
      children: [
        _buildSectionTitle('حسابي'),
        _buildDrawerItem(
          context: context,
          icon: Icons.person_outlined,
          activeIcon: Icons.person,
          title: l10n.account,
          onTap: () => _navigateAndPop(context, '/account'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.chat_outlined,
          activeIcon: Icons.chat,
          title: 'المحادثات',
          onTap: () => _navigateAndPop(context, '/chat'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.notifications_outlined,
          activeIcon: Icons.notifications,
          title: l10n.notifications,
          onTap: () => _navigateAndPop(context, '/notifications'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.analytics_outlined,
          activeIcon: Icons.analytics,
          title: 'التوصيات',
          onTap: () => _navigateAndPop(context, '/recommendations'),
        ),
      ],
    );
  }

  /// الدعم والمساعدة
  Widget _buildSupportSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Column(
      children: [
        _buildSectionTitle('الدعم والمساعدة'),
        _buildDrawerItem(
          context: context,
          icon: Icons.help_outline,
          activeIcon: Icons.help,
          title: 'مركز الدعم',
          onTap: () => _navigateAndPop(context, '/support'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.settings_outlined,
          activeIcon: Icons.settings,
          title: 'الإعدادات',
          onTap: () => _navigateAndPop(context, '/settings'),
        ),
        _buildDrawerItem(
          context: context,
          icon: Icons.info_outline,
          activeIcon: Icons.info,
          title: 'حول التطبيق',
          onTap: () => _navigateAndPop(context, '/about'),
        ),
      ],
    );
  }

  /// Footer مع معلومات التطبيق
  Widget _buildDrawerFooter(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
    WidgetRef ref,
  ) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: theme.dividerColor)),
      ),
      child: Column(
        children: [
          if (isAuthenticated)
            ListTile(
              leading: Icon(Icons.logout, color: theme.colorScheme.error),
              title: Text(
                'تسجيل الخروج',
                style: TextStyle(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () async {
                Navigator.of(context).pop();
                try {
                  await ref
                      .read(unifiedAuthProviderProvider.notifier)
                      .signOut();
                  // Navigation is now handled globally by the auth system
                  // No need for manual navigation here
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ في تسجيل الخروج: $e')),
                    );
                  }
                }
              },
            ),
          const SizedBox(height: 8),
          Text(
            'CarNow v1.0',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.textTheme.bodySmall?.color?.withAlpha(
                (0.6 * 255).toInt(),
              ),
            ),
          ),
          Text(
            '© 2024 CarNow. جميع الحقوق محفوظة',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.textTheme.bodySmall?.color?.withAlpha(
                (0.6 * 255).toInt(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر في الـ Drawer
  Widget _buildDrawerItem({
    required BuildContext context,
    required IconData icon,
    required IconData activeIcon,
    required String title,
    required VoidCallback onTap,
    String? subtitle,
    Widget? trailing,
  }) {
    final theme = Theme.of(context);
    final currentLocation = GoRouter.of(
      context,
    ).routerDelegate.currentConfiguration.last.matchedLocation;
    final isSelected = _isRouteSelected(currentLocation, title);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isSelected
            ? theme.primaryColor.withAlpha((0.1 * 255).toInt())
            : Colors.transparent,
      ),
      child: ListTile(
        leading: Icon(
          isSelected ? activeIcon : icon,
          color: isSelected ? theme.primaryColor : theme.iconTheme.color,
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected
                ? theme.primaryColor
                : theme.textTheme.bodyMedium?.color,
          ),
        ),
        subtitle: subtitle != null ? Text(subtitle) : null,
        trailing: trailing,
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Align(
        alignment: Alignment.centerRight,
        child: Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  /// التنقل وإغلاق الـ Drawer
  void _navigateAndPop(BuildContext context, String route) {
    Navigator.of(context).pop();
    context.go(route);
  }

  /// التحقق من إذا كان المسار محدد
  bool _isRouteSelected(String currentLocation, String title) {
    // تحقق بسيط للمسار
    if (title == 'الرئيسية' && currentLocation == '/') return true;
    if (title == 'الفئات' && currentLocation.startsWith('/categories')) {
      return true;
    }
    if (title == 'البحث' && currentLocation.startsWith('/search')) return true;
    if (title == 'المتاجر' && currentLocation.startsWith('/stores')) {
      return true;
    }
    if (title == 'سياراتي' && currentLocation.startsWith('/my-cars')) {
      return true;
    }
    if (title == 'الكراج الذكي' && currentLocation.startsWith('/garage')) {
      return true;
    }
    if (title == 'سلة التسوق' && currentLocation.startsWith('/cart')) {
      return true;
    }
    if (title == 'طلباتي' && currentLocation.startsWith('/orders')) return true;
    if (title == 'المفضلة' && currentLocation.startsWith('/favorites')) {
      return true;
    }
    if (title == 'الحساب' && currentLocation.startsWith('/account')) {
      return true;
    }
    if (title == 'المحادثات' && currentLocation.startsWith('/chat')) {
      return true;
    }
    if (title == 'الإشعارات' && currentLocation.startsWith('/notifications')) {
      return true;
    }
    if (title == 'التوصيات' && currentLocation.startsWith('/recommendations')) {
      return true;
    }
    if (title == 'مركز الدعم' && currentLocation.startsWith('/support')) {
      return true;
    }
    if (title == 'الإعدادات' && currentLocation.startsWith('/settings')) {
      return true;
    }
    return false;
  }

  /// حوار تسجيل الخروج
  Future<void> _showLogoutDialog(BuildContext context, WidgetRef ref) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(unifiedAuthProviderProvider.notifier).signOut();
                // Navigation is now handled globally by the auth system
                // No need for manual navigation here
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('خطأ في تسجيل الخروج: $e')),
                  );
                }
              }
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}

/// Simple model representing a navigation destination inside the drawer.
class _DrawerRouteDestination {
  const _DrawerRouteDestination({
    required this.path,
    required this.icon,
    required this.selectedIcon,
    required this.label,
  });

  final String path;
  final Icon icon;
  final Icon selectedIcon;
  final String label;
}

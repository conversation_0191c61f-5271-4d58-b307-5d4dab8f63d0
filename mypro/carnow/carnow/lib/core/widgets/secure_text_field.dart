import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../validators/input_validators.dart';
import '../validators/form_validation_service.dart';
import '../../l10n/app_localizations.dart';

/// حقل نص آمن مع التحقق من الصحة والتعقيم التلقائي
class SecureTextField extends StatefulWidget {
  const SecureTextField({
    super.key,
    this.controller,
    this.label,
    this.hint,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.keyboardType,
    this.inputFormatters,
    this.obscureText = false,
    this.enabled = true,
    this.maxLines = 1,
    this.maxLength,
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
    this.textCapitalization = TextCapitalization.none,
    this.readOnly = false,
    this.autoValidate = true,
    this.sanitizeInput = true,
    this.showSecurityIndicator = false,
    this.validationProvider,
    this.fieldName,
  });

  final TextEditingController? controller;
  final String? label;
  final String? hint;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final String? Function(String?, AppLocalizations?)? validator;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final bool obscureText;
  final bool enabled;
  final int? maxLines;
  final int? maxLength;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final FocusNode? focusNode;
  final TextCapitalization textCapitalization;
  final bool readOnly;
  final bool autoValidate;
  final bool sanitizeInput;
  final bool showSecurityIndicator;
  final FormValidationProvider? validationProvider;
  final String? fieldName;

  @override
  State<SecureTextField> createState() => _SecureTextFieldState();
}

class _SecureTextFieldState extends State<SecureTextField> {
  late bool _obscureText;
  bool _isFocused = false;
  String? _currentError;
  bool _hasSecurityIssue = false;

  @override
  void initState() {
    super.initState();
    _obscureText = widget.obscureText;
    widget.focusNode?.addListener(_onFocusChange);

    // Listen to validation provider if provided
    widget.validationProvider?.addListener(_onValidationChange);
  }

  @override
  void dispose() {
    widget.focusNode?.removeListener(_onFocusChange);
    widget.validationProvider?.removeListener(_onValidationChange);
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = widget.focusNode?.hasFocus ?? false;
    });
  }

  void _onValidationChange() {
    if (widget.fieldName != null && widget.validationProvider != null) {
      final error = widget.validationProvider!.getFieldError(widget.fieldName!);
      if (error != _currentError) {
        setState(() {
          _currentError = error;
        });
      }
    }
  }

  void _onTextChanged(String value) {
    // Check for security issues
    if (widget.sanitizeInput) {
      final hasIssue = InputValidators.containsDangerousContent(value);
      if (hasIssue != _hasSecurityIssue) {
        setState(() {
          _hasSecurityIssue = hasIssue;
        });
      }
    }

    // Auto-validate if enabled
    if (widget.autoValidate && widget.validator != null) {
      _validateField(value);
    }

    // Call original onChanged
    widget.onChanged?.call(value);
  }

  void _validateField(String value) {
    if (widget.validationProvider != null && widget.fieldName != null) {
      widget.validationProvider!.validateField(
        widget.fieldName!,
        value,
        widget.validator!,
        AppLocalizations.of(context),
        sanitize: widget.sanitizeInput,
      );
    } else if (widget.validator != null) {
      final l10n = AppLocalizations.of(context);
      String? processedValue = value;

      if (widget.sanitizeInput) {
        processedValue = InputValidators.sanitizeInput(value);
      }

      final error = widget.validator!(processedValue, l10n);
      setState(() {
        _currentError = error;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    // Get error from validation provider or local state
    final displayError =
        widget.validationProvider?.getFieldError(widget.fieldName!) ??
        _currentError;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Row(
            children: [
              Text(
                widget.label!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: _isFocused
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              if (widget.showSecurityIndicator) ...[
                const SizedBox(width: 8),
                _buildSecurityIndicator(theme),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],

        TextFormField(
          controller: widget.controller,
          keyboardType: widget.keyboardType,
          inputFormatters: [
            ...?widget.inputFormatters,
            if (widget.sanitizeInput) _SanitizingTextInputFormatter(),
          ],
          obscureText: _obscureText,
          enabled: widget.enabled,
          maxLines: widget.maxLines,
          maxLength: widget.maxLength,
          onChanged: _onTextChanged,
          onFieldSubmitted: widget.onSubmitted,
          focusNode: widget.focusNode,
          textCapitalization: widget.textCapitalization,
          readOnly: widget.readOnly,
          validator: widget.autoValidate
              ? null
              : (value) {
                  return widget.validator?.call(value, l10n);
                },
          style: theme.textTheme.bodyLarge?.copyWith(
            color: widget.enabled
                ? theme.colorScheme.onSurface
                : theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          decoration: InputDecoration(
            hintText: widget.hint,
            hintStyle: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),

            // Prefix icon
            prefixIcon: widget.prefixIcon != null
                ? Icon(
                    widget.prefixIcon,
                    color: _isFocused
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    size: 20,
                  )
                : null,

            // Suffix icon with security indicator
            suffixIcon: _buildSuffixIcon(theme),

            // Error styling
            errorText: displayError,
            errorStyle: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),

            // Border styling with security indication
            border: _buildBorder(theme.colorScheme.outline),
            enabledBorder: _buildBorder(
              _hasSecurityIssue
                  ? theme.colorScheme.error.withValues(alpha: 0.5)
                  : theme.colorScheme.outline,
            ),
            focusedBorder: _buildBorder(
              _hasSecurityIssue
                  ? theme.colorScheme.error
                  : theme.colorScheme.primary,
            ),
            errorBorder: _buildBorder(theme.colorScheme.error),
            focusedErrorBorder: _buildBorder(theme.colorScheme.error),
            disabledBorder: _buildBorder(
              theme.colorScheme.outline.withValues(alpha: 0.3),
            ),

            // Fill styling
            filled: true,
            fillColor: widget.enabled
                ? theme.colorScheme.surface
                : theme.colorScheme.surface.withValues(alpha: 0.5),

            // Content padding
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
        ),

        // Security warning
        if (_hasSecurityIssue && widget.showSecurityIndicator) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(Icons.security, size: 16, color: theme.colorScheme.error),
              const SizedBox(width: 4),
              Text(
                l10n?.securityWarning ?? 'Potentially unsafe content detected',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildSecurityIndicator(ThemeData theme) {
    if (_hasSecurityIssue) {
      return Icon(Icons.security, size: 16, color: theme.colorScheme.error);
    } else if (widget.sanitizeInput) {
      return Icon(
        Icons.verified_user,
        size: 16,
        color: theme.colorScheme.primary,
      );
    }
    return const SizedBox.shrink();
  }

  Widget? _buildSuffixIcon(ThemeData theme) {
    final List<Widget> icons = [];

    // Security indicator
    if (widget.showSecurityIndicator && widget.sanitizeInput) {
      icons.add(
        Icon(
          _hasSecurityIssue ? Icons.security : Icons.verified_user,
          color: _hasSecurityIssue
              ? theme.colorScheme.error
              : theme.colorScheme.primary,
          size: 20,
        ),
      );
    }

    // Password toggle
    if (widget.obscureText) {
      icons.add(
        IconButton(
          icon: Icon(
            _obscureText ? Icons.visibility_off : Icons.visibility,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            size: 20,
          ),
          onPressed: () {
            setState(() {
              _obscureText = !_obscureText;
            });
          },
        ),
      );
    } else if (widget.suffixIcon != null) {
      icons.add(
        Icon(
          widget.suffixIcon,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          size: 20,
        ),
      );
    }

    if (icons.isEmpty) return null;
    if (icons.length == 1) return icons.first;

    return Row(mainAxisSize: MainAxisSize.min, children: icons);
  }

  OutlineInputBorder _buildBorder(Color color) => OutlineInputBorder(
    borderRadius: BorderRadius.circular(12),
    borderSide: BorderSide(color: color, width: 1.5),
  );
}

/// مُنسق إدخال نصي للتعقيم التلقائي
class _SanitizingTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final sanitized = InputValidators.sanitizeInput(newValue.text);

    if (sanitized == newValue.text) {
      return newValue;
    }

    return TextEditingValue(
      text: sanitized,
      selection: TextSelection.collapsed(
        offset: sanitized.length.clamp(0, sanitized.length),
      ),
    );
  }
}

/// حقول نصية آمنة محددة مسبقاً

class SecureEmailField extends StatelessWidget {
  const SecureEmailField({
    super.key,
    this.controller,
    this.onChanged,
    this.enabled = true,
    this.validationProvider,
    this.fieldName = 'email',
  });

  final TextEditingController? controller;
  final void Function(String)? onChanged;
  final bool enabled;
  final FormValidationProvider? validationProvider;
  final String fieldName;

  @override
  Widget build(BuildContext context) => SecureTextField(
    controller: controller,
    label: 'البريد الإلكتروني',
    hint: '<EMAIL>',
    prefixIcon: Icons.email_outlined,
    keyboardType: TextInputType.emailAddress,
    validator: InputValidators.validateEmail,
    onChanged: onChanged,
    enabled: enabled,
    validationProvider: validationProvider,
    fieldName: fieldName,
    showSecurityIndicator: true,
  );
}

class SecurePasswordField extends StatelessWidget {
  const SecurePasswordField({
    super.key,
    this.controller,
    this.label,
    this.onChanged,
    this.enabled = true,
    this.validationProvider,
    this.fieldName = 'password',
  });

  final TextEditingController? controller;
  final String? label;
  final void Function(String)? onChanged;
  final bool enabled;
  final FormValidationProvider? validationProvider;
  final String fieldName;

  @override
  Widget build(BuildContext context) => SecureTextField(
    controller: controller,
    label: label ?? 'كلمة المرور',
    hint: '••••••••',
    prefixIcon: Icons.lock_outlined,
    obscureText: true,
    validator: InputValidators.validatePassword,
    onChanged: onChanged,
    enabled: enabled,
    sanitizeInput: false, // Don't sanitize passwords
    validationProvider: validationProvider,
    fieldName: fieldName,
    showSecurityIndicator: true,
  );
}

class SecureNameField extends StatelessWidget {
  const SecureNameField({
    super.key,
    this.controller,
    this.label,
    this.onChanged,
    this.enabled = true,
    this.validationProvider,
    this.fieldName = 'name',
  });

  final TextEditingController? controller;
  final String? label;
  final void Function(String)? onChanged;
  final bool enabled;
  final FormValidationProvider? validationProvider;
  final String fieldName;

  @override
  Widget build(BuildContext context) => SecureTextField(
    controller: controller,
    label: label ?? 'الاسم الكامل',
    hint: 'أدخل اسمك الكامل',
    prefixIcon: Icons.person_outline,
    textCapitalization: TextCapitalization.words,
    validator: InputValidators.validateName,
    onChanged: onChanged,
    enabled: enabled,
    validationProvider: validationProvider,
    fieldName: fieldName,
    showSecurityIndicator: true,
  );
}

class SecurePhoneField extends StatelessWidget {
  const SecurePhoneField({
    super.key,
    this.controller,
    this.onChanged,
    this.enabled = true,
    this.validationProvider,
    this.fieldName = 'phone',
  });

  final TextEditingController? controller;
  final void Function(String)? onChanged;
  final bool enabled;
  final FormValidationProvider? validationProvider;
  final String fieldName;

  @override
  Widget build(BuildContext context) => SecureTextField(
    controller: controller,
    label: 'رقم الهاتف',
    hint: '05xxxxxxxx',
    prefixIcon: Icons.phone_outlined,
    keyboardType: TextInputType.phone,
    inputFormatters: [
      FilteringTextInputFormatter.digitsOnly,
      LengthLimitingTextInputFormatter(15),
    ],
    validator: InputValidators.validatePhone,
    onChanged: onChanged,
    enabled: enabled,
    validationProvider: validationProvider,
    fieldName: fieldName,
    showSecurityIndicator: true,
  );
}

class SecureAmountField extends StatelessWidget {
  const SecureAmountField({
    super.key,
    this.controller,
    this.onChanged,
    this.enabled = true,
    this.minAmount,
    this.maxAmount,
    this.validationProvider,
    this.fieldName = 'amount',
  });

  final TextEditingController? controller;
  final void Function(String)? onChanged;
  final bool enabled;
  final double? minAmount;
  final double? maxAmount;
  final FormValidationProvider? validationProvider;
  final String fieldName;

  @override
  Widget build(BuildContext context) => SecureTextField(
    controller: controller,
    label: 'المبلغ',
    hint: '0.00',
    prefixIcon: Icons.attach_money,
    keyboardType: const TextInputType.numberWithOptions(decimal: true),
    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
    validator: (value, l10n) => InputValidators.validateAmount(
      value,
      l10n,
      minAmount,
      maxAmount,
    ),
    onChanged: onChanged,
    enabled: enabled,
    validationProvider: validationProvider,
    fieldName: fieldName,
    showSecurityIndicator: true,
  );
}

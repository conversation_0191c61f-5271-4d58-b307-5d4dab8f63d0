import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;

import '../config/environment_config.dart';

/// Code cleanup manager for removing deprecated authentication code
class CodeCleanupManager {
  static CodeCleanupManager? _instance;
  static CodeCleanupManager get instance => _instance ??= CodeCleanupManager._();

  CodeCleanupManager._();

  final List<String> _cleanupLog = [];
  final Map<String, List<String>> _deprecatedFiles = {};
  final Map<String, List<String>> _deprecatedDependencies = {};

  /// Initialize deprecated code mappings
  void _initializeDeprecatedMappings() {
    // Deprecated Flutter files
    _deprecatedFiles['flutter'] = [
      'lib/features/auth/legacy_auth_service.dart',
      'lib/features/auth/old_token_storage.dart',
      'lib/features/auth/simple_auth_system.dart', // DELETED
      'lib/features/auth/legacy_providers/',
      'lib/core/auth/old_auth_models.dart',
      'lib/core/auth/legacy_interfaces.dart',
      'lib/widgets/legacy_auth_widgets/',
      'lib/screens/old_login_screen.dart',
      'lib/screens/old_register_screen.dart',
      'test/legacy_auth_tests/',
    ];

    // Deprecated Go backend files
    _deprecatedFiles['go'] = [
      'internal/auth/legacy_handlers.go',
      'internal/auth/old_middleware.go',
      'internal/models/legacy_user.go',
      'internal/services/old_auth_service.go',
      'pkg/legacy/',
      'cmd/legacy_migration/',
    ];

    // Deprecated dependencies
    _deprecatedDependencies['flutter'] = [
      'firebase_auth', // If replaced with Supabase
      'google_sign_in_legacy',
      'old_secure_storage',
      'legacy_http_client',
    ];

    _deprecatedDependencies['go'] = [
      'github.com/old-auth/legacy',
      'github.com/deprecated/jwt-go',
      'github.com/old/middleware',
    ];
  }

  /// Start cleanup process
  Future<CleanupResult> startCleanup({
    bool dryRun = false,
    bool verbose = false,
    List<String>? specificPaths,
  }) async {
    _log('Starting code cleanup process');
    _log('Dry run: $dryRun, Verbose: $verbose');

    _initializeDeprecatedMappings();

    final result = CleanupResult();

    try {
      // Step 1: Analyze deprecated code
      await _analyzeDeprecatedCode(result, verbose);

      // Step 2: Remove deprecated files
      if (!dryRun) {
        await _removeDeprecatedFiles(result, verbose, specificPaths);
      } else {
        _log('[DRY RUN] Would remove ${result.filesToRemove.length} deprecated files');
      }

      // Step 3: Clean up dependencies
      if (!dryRun) {
        await _cleanupDependencies(result, verbose);
      } else {
        _log('[DRY RUN] Would clean up ${result.dependenciesToRemove.length} deprecated dependencies');
      }

      // Step 4: Update imports and references
      if (!dryRun) {
        await _updateImportsAndReferences(result, verbose);
      } else {
        _log('[DRY RUN] Would update imports in ${result.filesToUpdate.length} files');
      }

      // Step 5: Generate cleanup report
      await _generateCleanupReport(result, dryRun);

      result.success = true;
      _log('Code cleanup completed successfully');

    } catch (e) {
      result.success = false;
      result.errors.add('Cleanup failed: $e');
      _log('Code cleanup failed: $e');
    }

    return result;
  }

  /// Analyze deprecated code
  Future<void> _analyzeDeprecatedCode(CleanupResult result, bool verbose) async {
    _log('Analyzing deprecated code...');

    final projectRoot = Directory.current.path;

    // Analyze Flutter deprecated files
    for (final relativePath in _deprecatedFiles['flutter']!) {
      final fullPath = path.join(projectRoot, relativePath);
      final file = File(fullPath);
      final directory = Directory(fullPath);

      if (await file.exists()) {
        result.filesToRemove.add(fullPath);
        if (verbose) _log('Found deprecated file: $relativePath');
      } else if (await directory.exists()) {
        await _analyzeDirectory(directory, result, verbose);
      }
    }

    // Analyze Go deprecated files
    for (final relativePath in _deprecatedFiles['go']!) {
      final fullPath = path.join(projectRoot, 'backend-go', relativePath);
      final file = File(fullPath);
      final directory = Directory(fullPath);

      if (await file.exists()) {
        result.filesToRemove.add(fullPath);
        if (verbose) _log('Found deprecated Go file: $relativePath');
      } else if (await directory.exists()) {
        await _analyzeDirectory(directory, result, verbose);
      }
    }

    // Analyze dependencies
    await _analyzeDependencies(result, verbose);

    _log('Analysis completed: ${result.filesToRemove.length} files, ${result.dependenciesToRemove.length} dependencies');
  }

  /// Analyze directory recursively
  Future<void> _analyzeDirectory(Directory directory, CleanupResult result, bool verbose) async {
    try {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          result.filesToRemove.add(entity.path);
          if (verbose) _log('Found deprecated file: ${path.relative(entity.path)}');
        }
      }
    } catch (e) {
      _log('Error analyzing directory ${directory.path}: $e');
    }
  }

  /// Analyze deprecated dependencies
  Future<void> _analyzeDependencies(CleanupResult result, bool verbose) async {
    final projectRoot = Directory.current.path;

    // Analyze Flutter pubspec.yaml
    final pubspecFile = File(path.join(projectRoot, 'pubspec.yaml'));
    if (await pubspecFile.exists()) {
      final content = await pubspecFile.readAsString();
      for (final dep in _deprecatedDependencies['flutter']!) {
        if (content.contains(dep)) {
          result.dependenciesToRemove.add('flutter:$dep');
          if (verbose) _log('Found deprecated Flutter dependency: $dep');
        }
      }
    }

    // Analyze Go mod file
    final goModFile = File(path.join(projectRoot, 'backend-go', 'go.mod'));
    if (await goModFile.exists()) {
      final content = await goModFile.readAsString();
      for (final dep in _deprecatedDependencies['go']!) {
        if (content.contains(dep)) {
          result.dependenciesToRemove.add('go:$dep');
          if (verbose) _log('Found deprecated Go dependency: $dep');
        }
      }
    }
  }

  /// Remove deprecated files
  Future<void> _removeDeprecatedFiles(CleanupResult result, bool verbose, List<String>? specificPaths) async {
    _log('Removing deprecated files...');

    final filesToProcess = specificPaths ?? result.filesToRemove;

    for (final filePath in filesToProcess) {
      try {
        final file = File(filePath);
        final directory = Directory(filePath);

        if (await file.exists()) {
          await file.delete();
          result.removedFiles.add(filePath);
          if (verbose) _log('Removed file: ${path.relative(filePath)}');
        } else if (await directory.exists()) {
          await directory.delete(recursive: true);
          result.removedFiles.add(filePath);
          if (verbose) _log('Removed directory: ${path.relative(filePath)}');
        }
      } catch (e) {
        result.errors.add('Failed to remove $filePath: $e');
        _log('Error removing $filePath: $e');
      }
    }

    _log('Removed ${result.removedFiles.length} files/directories');
  }

  /// Clean up deprecated dependencies
  Future<void> _cleanupDependencies(CleanupResult result, bool verbose) async {
    _log('Cleaning up deprecated dependencies...');

    final projectRoot = Directory.current.path;

    // Clean up Flutter dependencies
    final flutterDeps = result.dependenciesToRemove
        .where((dep) => dep.startsWith('flutter:'))
        .map((dep) => dep.substring(8))
        .toList();

    if (flutterDeps.isNotEmpty) {
      await _cleanupFlutterDependencies(flutterDeps, result, verbose, projectRoot);
    }

    // Clean up Go dependencies
    final goDeps = result.dependenciesToRemove
        .where((dep) => dep.startsWith('go:'))
        .map((dep) => dep.substring(3))
        .toList();

    if (goDeps.isNotEmpty) {
      await _cleanupGoDependencies(goDeps, result, verbose, projectRoot);
    }

    _log('Cleaned up ${result.removedDependencies.length} dependencies');
  }

  /// Clean up Flutter dependencies
  Future<void> _cleanupFlutterDependencies(
    List<String> deps,
    CleanupResult result,
    bool verbose,
    String projectRoot,
  ) async {
    final pubspecFile = File(path.join(projectRoot, 'pubspec.yaml'));
    if (!await pubspecFile.exists()) return;

    try {
      String content = await pubspecFile.readAsString();
      bool modified = false;

      for (final dep in deps) {
        final regex = RegExp(r'^\s*' + RegExp.escape(dep) + r':.*$', multiLine: true);
        if (content.contains(regex)) {
          content = content.replaceAll(regex, '');
          result.removedDependencies.add('flutter:$dep');
          modified = true;
          if (verbose) _log('Removed Flutter dependency: $dep');
        }
      }

      if (modified) {
        await pubspecFile.writeAsString(content);
        result.filesToUpdate.add(pubspecFile.path);
      }
    } catch (e) {
      result.errors.add('Failed to clean Flutter dependencies: $e');
    }
  }

  /// Clean up Go dependencies
  Future<void> _cleanupGoDependencies(
    List<String> deps,
    CleanupResult result,
    bool verbose,
    String projectRoot,
  ) async {
    final goModFile = File(path.join(projectRoot, 'backend-go', 'go.mod'));
    if (!await goModFile.exists()) return;

    try {
      String content = await goModFile.readAsString();
      bool modified = false;

      for (final dep in deps) {
        final regex = RegExp(r'^\s*' + RegExp.escape(dep) + r'.*$', multiLine: true);
        if (content.contains(regex)) {
          content = content.replaceAll(regex, '');
          result.removedDependencies.add('go:$dep');
          modified = true;
          if (verbose) _log('Removed Go dependency: $dep');
        }
      }

      if (modified) {
        await goModFile.writeAsString(content);
        result.filesToUpdate.add(goModFile.path);
      }
    } catch (e) {
      result.errors.add('Failed to clean Go dependencies: $e');
    }
  }

  /// Update imports and references
  Future<void> _updateImportsAndReferences(CleanupResult result, bool verbose) async {
    _log('Updating imports and references...');

    final projectRoot = Directory.current.path;

    // Update Flutter imports
    await _updateFlutterImports(result, verbose, projectRoot);

    // Update Go imports
    await _updateGoImports(result, verbose, projectRoot);

    _log('Updated imports in ${result.filesToUpdate.length} files');
  }

  /// Update Flutter imports
  Future<void> _updateFlutterImports(CleanupResult result, bool verbose, String projectRoot) async {
    final libDir = Directory(path.join(projectRoot, 'lib'));
    if (!await libDir.exists()) return;

    await for (final entity in libDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        try {
          String content = await entity.readAsString();
          bool modified = false;

          // Remove imports to deprecated files
          for (final deprecatedFile in _deprecatedFiles['flutter']!) {
            final importPattern = RegExp(
              'import\\s+[\'"]package:carnow/${RegExp.escape(deprecatedFile.replaceFirst('lib/', ''))}[\'"];?\\s*\\n?',
              multiLine: true,
            );
            
            if (content.contains(importPattern)) {
              content = content.replaceAll(importPattern, '');
              modified = true;
              if (verbose) _log('Removed deprecated import in ${path.relative(entity.path)}');
            }
          }

          // Update import paths for moved files
          final importUpdates = {
            'package:carnow/features/auth/simple_auth_system.dart': 'package:carnow/core/auth/unified_auth_provider.dart',
            'package:carnow/core/auth/old_auth_models.dart': 'package:carnow/features/auth/models/auth_models.dart',
          };

          for (final entry in importUpdates.entries) {
            final oldImport = "import '${entry.key}';";
            final newImport = "import '${entry.value}';";
            
            if (content.contains(oldImport)) {
              content = content.replaceAll(oldImport, newImport);
              modified = true;
              if (verbose) _log('Updated import in ${path.relative(entity.path)}');
            }
          }

          if (modified) {
            await entity.writeAsString(content);
            if (!result.filesToUpdate.contains(entity.path)) {
              result.filesToUpdate.add(entity.path);
            }
          }
        } catch (e) {
          result.errors.add('Failed to update imports in ${entity.path}: $e');
        }
      }
    }
  }

  /// Update Go imports
  Future<void> _updateGoImports(CleanupResult result, bool verbose, String projectRoot) async {
    final goDir = Directory(path.join(projectRoot, 'backend-go'));
    if (!await goDir.exists()) return;

    await for (final entity in goDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.go')) {
        try {
          String content = await entity.readAsString();
          bool modified = false;

          // Remove imports to deprecated packages
          for (final deprecatedFile in _deprecatedFiles['go']!) {
            final importPattern = RegExp(
              'import\\s+[\'"]carnow-backend/${RegExp.escape(deprecatedFile)}[\'"]',
              multiLine: true,
            );
            
            if (content.contains(importPattern)) {
              content = content.replaceAll(importPattern, '');
              modified = true;
              if (verbose) _log('Removed deprecated Go import in ${path.relative(entity.path)}');
            }
          }

          if (modified) {
            await entity.writeAsString(content);
            if (!result.filesToUpdate.contains(entity.path)) {
              result.filesToUpdate.add(entity.path);
            }
          }
        } catch (e) {
          result.errors.add('Failed to update Go imports in ${entity.path}: $e');
        }
      }
    }
  }

  /// Generate cleanup report
  Future<void> _generateCleanupReport(CleanupResult result, bool dryRun) async {
    _log('Generating cleanup report...');

    final report = {
      'cleanup_summary': {
        'success': result.success,
        'dry_run': dryRun,
        'timestamp': DateTime.now().toIso8601String(),
        'environment': ConfigurationManager.current.environment.name,
      },
      'statistics': {
        'files_analyzed': result.filesToRemove.length,
        'files_removed': result.removedFiles.length,
        'dependencies_removed': result.removedDependencies.length,
        'files_updated': result.filesToUpdate.length,
        'errors_count': result.errors.length,
      },
      'removed_files': result.removedFiles,
      'removed_dependencies': result.removedDependencies,
      'updated_files': result.filesToUpdate,
      'errors': result.errors,
      'cleanup_log': _cleanupLog,
      'recommendations': _generateCleanupRecommendations(result),
    };

    final reportFile = File('cleanup_report_${DateTime.now().millisecondsSinceEpoch}.json');
    await reportFile.writeAsString(_formatJson(report));

    _log('Cleanup report saved to: ${reportFile.path}');
  }

  /// Generate cleanup recommendations
  List<String> _generateCleanupRecommendations(CleanupResult result) {
    final recommendations = <String>[];

    if (result.errors.isNotEmpty) {
      recommendations.add('Review and fix ${result.errors.length} cleanup errors');
    }

    if (result.filesToUpdate.isNotEmpty) {
      recommendations.add('Run flutter pub get and go mod tidy after dependency cleanup');
    }

    if (result.removedFiles.isNotEmpty) {
      recommendations.add('Test the application thoroughly after removing ${result.removedFiles.length} files');
    }

    recommendations.add('Update documentation to reflect removed legacy components');
    recommendations.add('Run full test suite to ensure no broken references');

    return recommendations;
  }

  /// Format JSON with proper indentation
  String _formatJson(Map<String, dynamic> data) {
    final encoder = JsonEncoder.withIndent('  ');
    return encoder.convert(data);
  }

  /// Add log entry
  void _log(String message) {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = '[$timestamp] $message';
    _cleanupLog.add(logEntry);
    
    if (kDebugMode) {
      print('Cleanup: $message');
    }
  }

  /// Get cleanup log
  List<String> getCleanupLog() => List.unmodifiable(_cleanupLog);

  /// Clear cleanup log
  void clearLog() {
    _cleanupLog.clear();
  }
}

/// Cleanup result data class
class CleanupResult {
  bool success = false;
  final List<String> filesToRemove = [];
  final List<String> removedFiles = [];
  final List<String> dependenciesToRemove = [];
  final List<String> removedDependencies = [];
  final List<String> filesToUpdate = [];
  final List<String> errors = [];

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'files_to_remove': filesToRemove,
      'removed_files': removedFiles,
      'dependencies_to_remove': dependenciesToRemove,
      'removed_dependencies': removedDependencies,
      'files_to_update': filesToUpdate,
      'errors': errors,
    };
  }
}

/// Cleanup utility functions
class CleanupUtils {
  /// Check if file is safe to remove
  static bool isSafeToRemove(String filePath) {
    final safePaths = [
      'lib/features/auth/legacy',
      'lib/core/auth/old',
      'test/legacy',
      'internal/auth/legacy',
      'pkg/legacy',
    ];

    return safePaths.any((safePath) => filePath.contains(safePath));
  }

  /// Backup file before removal
  static Future<void> backupFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final backupPath = '$filePath.backup.${DateTime.now().millisecondsSinceEpoch}';
        await file.copy(backupPath);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to backup $filePath: $e');
      }
    }
  }

  /// Restore file from backup
  static Future<void> restoreFile(String filePath, String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (await backupFile.exists()) {
        await backupFile.copy(filePath);
        await backupFile.delete();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to restore $filePath: $e');
      }
    }
  }
}

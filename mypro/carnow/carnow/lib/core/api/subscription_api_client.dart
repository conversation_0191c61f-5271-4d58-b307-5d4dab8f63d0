import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/seller/models/subscription_model.dart';
import '../../features/seller/models/discount_model.dart';

part 'subscription_api_client.g.dart';

/// API Client للتعامل مع الاشتراكات والخصومات
class SubscriptionApiClient {
  final Dio _dio;

  SubscriptionApiClient(this._dio);

  /// الحصول على خطط الاشتراك النشطة
  Future<List<SubscriptionPlan>> getSubscriptionPlans() async {
    try {
      final response = await _dio.get('/api/v1/subscription-plans');
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => SubscriptionPlan.fromJson(json)).toList();
      }
      
      throw Exception('Failed to fetch subscription plans');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// الحصول على خطة اشتراك محددة
  Future<SubscriptionPlan> getSubscriptionPlan(int planId) async {
    try {
      final response = await _dio.get('/api/v1/subscription-plans/$planId');
      
      if (response.statusCode == 200) {
        return SubscriptionPlan.fromJson(response.data);
      }
      
      throw Exception('Failed to fetch subscription plan');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// حساب سعر الاشتراك مع الخصم
  Future<DiscountCalculation> calculateSubscriptionPrice({
    required int planId,
    required String billingCycle,
    String? discountCode,
  }) async {
    try {
      final queryParameters = {
        'plan_id': planId.toString(),
        'billing_cycle': billingCycle,
        if (discountCode != null) 'discount_code': discountCode,
      };

      final response = await _dio.get(
        '/api/v1/subscriptions/calculate-price',
        queryParameters: queryParameters,
      );
      
      if (response.statusCode == 200) {
        return DiscountCalculation.fromJson(response.data);
      }
      
      throw Exception('Failed to calculate subscription price');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// إنشاء اشتراك جديد
  Future<SellerSubscription> createSubscription({
    required int planId,
    required String billingCycle,
    String? discountCode,
  }) async {
    try {
      final data = {
        'plan_id': planId,
        'billing_cycle': billingCycle,
        if (discountCode != null) 'discount_code': discountCode,
      };

      final response = await _dio.post(
        '/api/v1/subscriptions',
        data: data,
      );
      
      if (response.statusCode == 201) {
        return SellerSubscription.fromJson(response.data);
      }
      
      throw Exception('Failed to create subscription');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// تحديث اشتراك موجود
  Future<SellerSubscription> updateSubscription({
    required int subscriptionId,
    int? planId,
    String? billingCycle,
    String? discountCode,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (planId != null) data['plan_id'] = planId;
      if (billingCycle != null) data['billing_cycle'] = billingCycle;
      if (discountCode != null) data['discount_code'] = discountCode;

      final response = await _dio.put(
        '/api/v1/subscriptions/$subscriptionId',
        data: data,
      );
      
      if (response.statusCode == 200) {
        return SellerSubscription.fromJson(response.data);
      }
      
      throw Exception('Failed to update subscription');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// إلغاء اشتراك
  Future<void> cancelSubscription(int subscriptionId) async {
    try {
      final response = await _dio.delete('/api/v1/subscriptions/$subscriptionId');
      
      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Failed to cancel subscription');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// الحصول على اشتراكات البائع
  Future<List<SellerSubscription>> getSellerSubscriptions() async {
    try {
      final response = await _dio.get('/api/v1/subscriptions');
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => SellerSubscription.fromJson(json)).toList();
      }
      
      throw Exception('Failed to fetch seller subscriptions');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// الحصول على اشتراك محدد
  Future<SellerSubscription> getSubscription(int subscriptionId) async {
    try {
      final response = await _dio.get('/api/v1/subscriptions/$subscriptionId');
      
      if (response.statusCode == 200) {
        return SellerSubscription.fromJson(response.data);
      }
      
      throw Exception('Failed to fetch subscription');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// التحقق من صحة رمز الخصم
  Future<Discount?> validateDiscountCode(String code) async {
    try {
      final response = await _dio.get(
        '/api/v1/discounts/validate',
        queryParameters: {'code': code},
      );
      
      if (response.statusCode == 200) {
        return Discount.fromJson(response.data);
      } else if (response.statusCode == 400) {
        return null; // رمز الخصم غير صحيح
      }
      
      throw Exception('Failed to validate discount code');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// حساب الخصم
  Future<DiscountCalculation> calculateDiscount({
    required double amount,
    String? discountCode,
  }) async {
    try {
      final queryParameters = {
        'amount': amount.toString(),
        if (discountCode != null) 'code': discountCode,
      };

      final response = await _dio.get(
        '/api/v1/discounts/calculate',
        queryParameters: queryParameters,
      );
      
      if (response.statusCode == 200) {
        return DiscountCalculation.fromJson(response.data);
      }
      
      throw Exception('Failed to calculate discount');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// تطبيق الخصم
  Future<DiscountCalculation> applyDiscount({
    required double amount,
    String? discountCode,
    int? subscriptionId,
  }) async {
    try {
      final data = {
        'amount': amount,
        if (discountCode != null) 'discount_code': discountCode,
        if (subscriptionId != null) 'subscription_id': subscriptionId,
      };

      final response = await _dio.post(
        '/api/v1/discounts/apply',
        data: data,
      );
      
      if (response.statusCode == 200) {
        return DiscountCalculation.fromJson(response.data);
      }
      
      throw Exception('Failed to apply discount');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  /// الحصول على خصم الاشتراك السنوي
  Future<Discount> getYearlySubscriptionDiscount() async {
    try {
      final response = await _dio.get('/api/v1/discounts/yearly-subscription');
      
      if (response.statusCode == 200) {
        return Discount.fromJson(response.data);
      }
      
      throw Exception('Failed to fetch yearly subscription discount');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
}

/// Provider للـ SubscriptionApiClient
@riverpod
SubscriptionApiClient subscriptionApiClient(Ref ref) {
  final dio = ref.watch(dioProvider);
  return SubscriptionApiClient(dio);
}

/// Provider للـ Dio
@riverpod
Dio dio(Ref ref) {
  return Dio(BaseOptions(
    baseUrl: 'https://backend-go-8klm.onrender.com',
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  ));
} 
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_api_client.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$subscriptionApiClientHash() =>
    r'a836a7000ed30b9212fa78b644b72266d850f763';

/// Provider للـ SubscriptionApiClient
///
/// Copied from [subscriptionApiClient].
@ProviderFor(subscriptionApiClient)
final subscriptionApiClientProvider =
    AutoDisposeProvider<SubscriptionApiClient>.internal(
      subscriptionApiClient,
      name: r'subscriptionApiClientProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionApiClientHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionApiClientRef =
    AutoDisposeProviderRef<SubscriptionApiClient>;
String _$dioHash() => r'cafd46dafb3c4abb3898d7a5eb1b72305b77c763';

/// Provider للـ Dio
///
/// Copied from [dio].
@ProviderFor(dio)
final dioProvider = AutoDisposeProvider<Dio>.internal(
  dio,
  name: r'dioProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dioHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DioRef = AutoDisposeProviderRef<Dio>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

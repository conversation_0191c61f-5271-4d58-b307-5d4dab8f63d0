/// مساعد للتحقق من صحة مدخلات المستخدم مع تعزيزات أمنية
class InputValidators {
  // Security constants
  static const int _maxInputLength = 1000;
  static const int _maxNameLength = 100;
  static const int _maxEmailLength = 254;
  static const int _maxPhoneLength = 20;
  static const int _maxDescriptionLength = 2000;
  static const int _minPasswordLength = 8;
  static const int _maxPasswordLength = 128;

  // Dangerous patterns that could indicate injection attempts
  static final List<RegExp> _dangerousPatterns = [
    RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false),
    RegExp(r'javascript:', caseSensitive: false),
    RegExp(r'on\w+\s*=', caseSensitive: false),
    RegExp(r'<iframe[^>]*>', caseSensitive: false),
    RegExp(r'<object[^>]*>', caseSensitive: false),
    RegExp(r'<embed[^>]*>', caseSensitive: false),
    RegExp(r'<link[^>]*>', caseSensitive: false),
    RegExp(r'<meta[^>]*>', caseSensitive: false),
    RegExp(r'<style[^>]*>.*?</style>', caseSensitive: false),
    RegExp(r'expression\s*\(', caseSensitive: false),
    RegExp(r'url\s*\(', caseSensitive: false),
    RegExp(r'@import', caseSensitive: false),
    RegExp(r'vbscript:', caseSensitive: false),
    RegExp(r'data:', caseSensitive: false),
  ];

  // SQL injection patterns
  static final List<RegExp> _sqlInjectionPatterns = [
    RegExp(r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)', caseSensitive: false),
    RegExp(r'(\b(OR|AND)\s+\d+\s*=\s*\d+)', caseSensitive: false),
    RegExp(r'(\b(OR|AND)\s+\w+\s*=\s*\w+)', caseSensitive: false),
    RegExp(r'[;"\-]', caseSensitive: false),
    RegExp(r'--', caseSensitive: false),
    RegExp(r'/\*.*?\*/', caseSensitive: false),
  ];

  /// تنظيف وتعقيم النص من المحتوى الخطير
  static String sanitizeInput(String? input) {
    if (input == null) return '';
    
    String sanitized = input.trim();
    
    // Remove null bytes and control characters
    sanitized = sanitized.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');
    
    // Normalize whitespace
    sanitized = sanitized.replaceAll(RegExp(r'\s+'), ' ');
    
    // Remove dangerous HTML/JS patterns
    for (final pattern in _dangerousPatterns) {
      sanitized = sanitized.replaceAll(pattern, '');
    }
    
    return sanitized;
  }

  /// فحص النص للكشف عن محاولات الحقن
  static bool containsDangerousContent(String? input) {
    if (input == null || input.isEmpty) return false;
    
    final lowerInput = input.toLowerCase();
    
    // Check for XSS patterns
    for (final pattern in _dangerousPatterns) {
      if (pattern.hasMatch(lowerInput)) return true;
    }
    
    // Check for SQL injection patterns
    for (final pattern in _sqlInjectionPatterns) {
      if (pattern.hasMatch(lowerInput)) return true;
    }
    
    return false;
  }

  /// التحقق من طول النص
  static bool isValidLength(String? input, int maxLength) {
    if (input == null) return true;
    return input.length <= maxLength;
  }

  /// التحقق الأساسي من الأمان للمدخلات
  static String? validateSecureInput(String? value, int maxLength) {
    if (value != null && value.length > maxLength) {
      return 'Input is too long';
    }
    
    if (containsDangerousContent(value)) {
      return 'Invalid input detected';
    }
    
    return null;
  }

  /// التحقق من صحة الاسم مع تعزيزات أمنية
  static String? validateName(String? value, [dynamic l10n]) {
    if (value == null || value.trim().isEmpty) {
      return 'Name cannot be empty.';
    }

    final sanitized = sanitizeInput(value);
    if (sanitized.isEmpty) {
      return 'Invalid input detected';
    }

    if (sanitized.length < 2) {
      return 'Name must be at least 2 characters.';
    }

    if (sanitized.length > _maxNameLength) {
      return 'Name is too long';
    }

    // Check for valid name characters (letters, spaces, hyphens, apostrophes)
    final nameRegex = RegExp(r"^[a-zA-Z\u0600-\u06FF\s\-'\.]+$");
    if (!nameRegex.hasMatch(sanitized)) {
      return 'Name contains invalid characters';
    }

    return null;
  }

  /// التحقق من صحة رقم الهاتف مع تعزيزات أمنية
  static String? validatePhone(String? value, [dynamic l10n]) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number cannot be empty.';
    }

    final sanitized = sanitizeInput(value);
    if (sanitized.isEmpty) {
      return 'Invalid input detected';
    }

    if (sanitized.length > _maxPhoneLength) {
      return 'Phone number is too long';
    }

    // Remove all non-digit characters for validation
    final digitsOnly = sanitized.replaceAll(RegExp(r'[^\d+]'), '');
    
    if (digitsOnly.length < 8) {
      return 'Phone number is too short';
    }

    if (digitsOnly.length > 15) {
      return 'Phone number is too long';
    }

    // Enhanced phone validation pattern
    final phoneRegex = RegExp(r'^[+]?[(]?[\d\s\-\(\)\.]{8,20}$');
    if (!phoneRegex.hasMatch(sanitized)) {
      return 'Invalid phone number format.';
    }

    return null;
  }

  /// التحقق من صحة البريد الإلكتروني مع تعزيزات أمنية
  static String? validateEmail(String? value, [dynamic l10n]) {
    if (value == null || value.trim().isEmpty) {
      return 'This field is required';
    }

    final sanitized = sanitizeInput(value);
    if (sanitized.isEmpty) {
      return 'Invalid input detected';
    }

    if (sanitized.length > _maxEmailLength) {
      return 'Email is too long';
    }

    // Enhanced email validation
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?@[a-zA-Z0-9]([a-zA-Z0-9.-]*[a-zA-Z0-9])?\.[a-zA-Z]{2,}$',
    );
    
    if (!emailRegex.hasMatch(sanitized)) {
      return 'Please enter a valid email';
    }

    // Check for suspicious patterns in email
    if (sanitized.contains('..') || sanitized.startsWith('.') || sanitized.endsWith('.')) {
      return 'Please enter a valid email';
    }

    return null;
  }

  /// التحقق من صحة كلمة المرور مع تعزيزات أمنية
  static String? validatePassword(String? value, [dynamic l10n]) {
    if (value == null || value.isEmpty) {
      return 'This field is required';
    }

    if (value.length < _minPasswordLength) {
      return 'Password must be at least $_minPasswordLength characters';
    }

    if (value.length > _maxPasswordLength) {
      return 'Password is too long';
    }

    // Check for at least one uppercase letter
    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter';
    }

    // Check for at least one lowercase letter
    if (!RegExp(r'[a-z]').hasMatch(value)) {
      return 'Password must contain at least one lowercase letter';
    }

    // Check for at least one digit
    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number';
    }

    // Check for at least one special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return 'Password must contain at least one special character';
    }

    // Check for common weak passwords
    final commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123', 
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];
    
    if (commonPasswords.contains(value.toLowerCase())) {
      return 'Password is too common';
    }

    return null;
  }

  /// التحقق من تطابق كلمات المرور
  static String? validatePasswordConfirmation(String? password, String? confirmation) {
    if (confirmation == null || confirmation.isEmpty) {
      return 'This field is required';
    }

    if (password != confirmation) {
      return 'Passwords do not match';
    }

    return null;
  }

  /// التحقق من صحة الموقع مع تعزيزات أمنية
  static String? validateLocation(String? value, [dynamic l10n]) {
    if (value == null || value.trim().isEmpty) {
      return 'Location cannot be empty.';
    }

    final sanitized = sanitizeInput(value);
    if (sanitized.isEmpty) {
      return 'Invalid input detected';
    }

    if (sanitized.length < 3) {
      return 'Location must be at least 3 characters.';
    }

    if (sanitized.length > _maxInputLength) {
      return 'Location is too long';
    }

    return null;
  }

  /// التحقق من صحة الوصف مع تعزيزات أمنية
  static String? validateDescription(String? value, [dynamic l10n]) {
    if (value == null || value.trim().isEmpty) {
      return 'This field is required';
    }

    final sanitized = sanitizeInput(value);
    if (sanitized.isEmpty) {
      return 'Invalid input detected';
    }

    if (sanitized.length > _maxDescriptionLength) {
      return 'Description is too long';
    }

    return null;
  }

  /// التحقق من صحة الوصف الاختياري
  static String? validateOptionalDescription(String? value, [dynamic l10n]) {
    if (value == null || value.trim().isEmpty) {
      return null; // Optional field
    }

    return validateDescription(value, l10n);
  }

  /// التحقق من صحة المبلغ المالي
  static String? validateAmount(String? value, [dynamic l10n, double? minAmount, double? maxAmount]) {
    if (value == null || value.trim().isEmpty) {
      return 'This field is required';
    }

    final sanitized = sanitizeInput(value);
    if (sanitized.isEmpty) {
      return 'Invalid input detected';
    }

    final amount = double.tryParse(sanitized);
    if (amount == null) {
      return 'Please enter a valid amount';
    }

    if (amount <= 0) {
      return 'Amount must be positive';
    }

    if (minAmount != null && amount < minAmount) {
      return 'Amount is too small';
    }

    if (maxAmount != null && amount > maxAmount) {
      return 'Amount is too large';
    }

    return null;
  }

  /// التحقق من صحة رقم الهوية أو الآيبان
  static String? validateBankAccount(String? value, [dynamic l10n]) {
    if (value == null || value.trim().isEmpty) {
      return 'This field is required';
    }

    final sanitized = sanitizeInput(value);
    if (sanitized.isEmpty) {
      return 'Invalid input detected';
    }

    // Remove spaces and hyphens for validation
    final cleanValue = sanitized.replaceAll(RegExp(r'[\s\-]'), '');
    
    if (cleanValue.length < 8) {
      return 'Account number is too short';
    }

    if (cleanValue.length > 34) { // IBAN max length
      return 'Account number is too long';
    }

    // Basic alphanumeric check
    if (!RegExp(r'^[A-Z0-9]+$').hasMatch(cleanValue.toUpperCase())) {
      return 'Invalid account number format';
    }

    return null;
  }

  /// التحقق من صحة رقم VIN للسيارات
  static String? validateVIN(String? value, [dynamic l10n]) {
    if (value == null || value.trim().isEmpty) {
      return 'This field is required';
    }

    final sanitized = sanitizeInput(value).toUpperCase();
    if (sanitized.isEmpty) {
      return 'Invalid input detected';
    }

    if (sanitized.length != 17) {
      return 'VIN must be exactly 17 characters';
    }

    // VIN should not contain I, O, Q
    if (RegExp(r'[IOQ]').hasMatch(sanitized)) {
      return 'VIN cannot contain I, O, or Q';
    }

    // VIN should be alphanumeric
    if (!RegExp(r'^[A-HJ-NPR-Z0-9]{17}$').hasMatch(sanitized)) {
      return 'Invalid VIN format';
    }

    return null;
  }

  /// التحقق من صحة الرمز البريدي
  static String? validatePostalCode(String? value, [dynamic l10n]) {
    if (value == null || value.trim().isEmpty) {
      return null; // Usually optional
    }

    final sanitized = sanitizeInput(value);
    if (sanitized.isEmpty) {
      return 'Invalid input detected';
    }

    // Basic postal code validation (alphanumeric, spaces, hyphens)
    if (!RegExp(r'^[A-Z0-9\s\-]{3,10}$').hasMatch(sanitized.toUpperCase())) {
      return 'Invalid postal code format';
    }

    return null;
  }

  /// التحقق من صحة URL
  static String? validateURL(String? value, [dynamic l10n]) {
    if (value == null || value.trim().isEmpty) {
      return null; // Usually optional
    }

    final sanitized = sanitizeInput(value);
    if (sanitized.isEmpty) {
      return 'Invalid input detected';
    }

    try {
      final uri = Uri.parse(sanitized);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http') && !uri.scheme.startsWith('https'))) {
        return 'Please enter a valid URL';
      }
    } catch (e) {
      return 'Please enter a valid URL';
    }

    return null;
  }
}
import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import 'input_validators.dart';

/// نتيجة التحقق من صحة النموذج
class ValidationResult {
  final bool isValid;
  final Map<String, String> errors;
  final Map<String, String> sanitizedValues;

  ValidationResult({
    required this.isValid,
    required this.errors,
    required this.sanitizedValues,
  });

  bool hasError(String field) => errors.containsKey(field);
  String? getError(String field) => errors[field];
  String? getSanitizedValue(String field) => sanitizedValues[field];
}

/// قواعد التحقق من صحة الحقول
class FieldValidationRule {
  final String fieldName;
  final String? Function(String?, AppLocalizations?) validator;
  final bool required;
  final bool sanitize;

  FieldValidationRule({
    required this.fieldName,
    required this.validator,
    this.required = true,
    this.sanitize = true,
  });
}

/// خدمة التحقق من صحة النماذج مع معالجة الأخطاء والتعقيم
class FormValidationService {
  static const Duration _validationDebounceDelay = Duration(milliseconds: 300);

  /// التحقق من صحة نموذج كامل
  static ValidationResult validateForm(
    Map<String, String?> formData,
    List<FieldValidationRule> rules,
    AppLocalizations? l10n,
  ) {
    final errors = <String, String>{};
    final sanitizedValues = <String, String>{};

    for (final rule in rules) {
      final value = formData[rule.fieldName];
      
      // Sanitize input if required
      String? processedValue = value;
      if (rule.sanitize && value != null) {
        processedValue = InputValidators.sanitizeInput(value);
        sanitizedValues[rule.fieldName] = processedValue;
      }

      // Validate field
      final error = rule.validator(processedValue, l10n);
      if (error != null) {
        errors[rule.fieldName] = error;
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      sanitizedValues: sanitizedValues,
    );
  }

  /// التحقق من صحة حقل واحد مع التأخير
  static Future<String?> validateFieldWithDebounce(
    String? value,
    String? Function(String?, AppLocalizations?) validator,
    AppLocalizations? l10n, {
    Duration delay = _validationDebounceDelay,
  }) async {
    await Future.delayed(delay);
    return validator(value, l10n);
  }

  /// قواعد التحقق المحددة مسبقاً للنماذج الشائعة
  
  /// قواعد نموذج تسجيل الدخول
  static List<FieldValidationRule> get loginFormRules => [
    FieldValidationRule(
      fieldName: 'email',
      validator: (value, l10n) => InputValidators.validateEmail(value),
    ),
    FieldValidationRule(
      fieldName: 'password',
      validator: (value, l10n) => InputValidators.validatePassword(value),
      sanitize: false, // Don't sanitize passwords
    ),
  ];

  /// قواعد نموذج التسجيل
  static List<FieldValidationRule> get registrationFormRules => [
    FieldValidationRule(
      fieldName: 'name',
      validator: (value, l10n) => InputValidators.validateName(value),
    ),
    FieldValidationRule(
      fieldName: 'email',
      validator: (value, l10n) => InputValidators.validateEmail(value),
    ),
    FieldValidationRule(
      fieldName: 'password',
      validator: (value, l10n) => InputValidators.validatePassword(value),
      sanitize: false,
    ),
    FieldValidationRule(
      fieldName: 'phone',
      validator: (value, l10n) => InputValidators.validatePhone(value),
      required: false,
    ),
  ];

  /// قواعد نموذج الملف الشخصي
  static List<FieldValidationRule> get profileFormRules => [
    FieldValidationRule(
      fieldName: 'name',
      validator: (value, l10n) => InputValidators.validateName(value),
    ),
    FieldValidationRule(
      fieldName: 'phone',
      validator: (value, l10n) => InputValidators.validatePhone(value),
      required: false,
    ),
    FieldValidationRule(
      fieldName: 'location',
      validator: (value, l10n) => InputValidators.validateLocation(value),
      required: false,
    ),
    FieldValidationRule(
      fieldName: 'address',
      validator: (value, l10n) => InputValidators.validateOptionalDescription(value, l10n),
      required: false,
    ),
  ];

  /// قواعد نموذج المحفظة
  static List<FieldValidationRule> walletFormRules({
    double? minAmount,
    double? maxAmount,
  }) => [
    FieldValidationRule(
      fieldName: 'amount',
      validator: (value, l10n) => InputValidators.validateAmount(
        value, 
        l10n,
        minAmount, 
        maxAmount,
      ),
    ),
    FieldValidationRule(
      fieldName: 'description',
      validator: (value, l10n) => InputValidators.validateOptionalDescription(value),
      required: false,
    ),
  ];

  /// قواعد نموذج السحب البنكي
  static List<FieldValidationRule> get bankWithdrawalFormRules => [
    FieldValidationRule(
      fieldName: 'amount',
      validator: (value, l10n) => InputValidators.validateAmount(value),
    ),
    FieldValidationRule(
      fieldName: 'bankName',
      validator: (value, l10n) => InputValidators.validateName(value),
    ),
    FieldValidationRule(
      fieldName: 'accountNumber',
      validator: (value, l10n) => InputValidators.validateBankAccount(value),
    ),
    FieldValidationRule(
      fieldName: 'accountHolderName',
      validator: (value, l10n) => InputValidators.validateName(value),
    ),
    FieldValidationRule(
      fieldName: 'iban',
      validator: (value, l10n) => InputValidators.validateBankAccount(value),
      required: false,
    ),
  ];

  /// قواعد نموذج إضافة السيارة
  static List<FieldValidationRule> get vehicleFormRules => [
    FieldValidationRule(
      fieldName: 'vin',
      validator: (value, l10n) => InputValidators.validateVIN(value),
      required: false,
    ),
    FieldValidationRule(
      fieldName: 'description',
      validator: (value, l10n) => InputValidators.validateOptionalDescription(value),
      required: false,
    ),
  ];
}

/// مزود للتحقق من صحة النماذج مع إدارة الحالة
class FormValidationProvider extends ChangeNotifier {
  final Map<String, String> _errors = {};
  final Map<String, String> _sanitizedValues = {};
  bool _isValidating = false;

  Map<String, String> get errors => Map.unmodifiable(_errors);
  Map<String, String> get sanitizedValues => Map.unmodifiable(_sanitizedValues);
  bool get isValidating => _isValidating;
  bool get hasErrors => _errors.isNotEmpty;
  bool get isValid => _errors.isEmpty && !_isValidating;

  /// تعيين خطأ لحقل معين
  void setFieldError(String field, String? error) {
    if (error != null) {
      _errors[field] = error;
    } else {
      _errors.remove(field);
    }
    notifyListeners();
  }

  /// تعيين قيمة معقمة لحقل معين
  void setSanitizedValue(String field, String value) {
    _sanitizedValues[field] = value;
    notifyListeners();
  }

  /// الحصول على خطأ حقل معين
  String? getFieldError(String field) => _errors[field];

  /// الحصول على قيمة معقمة لحقل معين
  String? getSanitizedValue(String field) => _sanitizedValues[field];

  /// التحقق من صحة حقل واحد
  Future<void> validateField(
    String field,
    String? value,
    String? Function(String?, AppLocalizations?) validator,
    AppLocalizations? l10n, {
    bool sanitize = true,
  }) async {
    _isValidating = true;
    notifyListeners();

    try {
      String? processedValue = value;
      
      if (sanitize && value != null) {
        processedValue = InputValidators.sanitizeInput(value);
        setSanitizedValue(field, processedValue);
      }

      final error = await FormValidationService.validateFieldWithDebounce(
        processedValue,
        validator,
        l10n,
      );

      setFieldError(field, error);
    } finally {
      _isValidating = false;
      notifyListeners();
    }
  }

  /// التحقق من صحة نموذج كامل
  ValidationResult validateForm(
    Map<String, String?> formData,
    List<FieldValidationRule> rules,
    AppLocalizations? l10n,
  ) {
    final result = FormValidationService.validateForm(formData, rules, l10n);
    
    // Update internal state
    _errors.clear();
    _errors.addAll(result.errors);
    _sanitizedValues.clear();
    _sanitizedValues.addAll(result.sanitizedValues);
    
    notifyListeners();
    return result;
  }

  /// مسح جميع الأخطاء
  void clearErrors() {
    _errors.clear();
    _sanitizedValues.clear();
    notifyListeners();
  }

  /// مسح خطأ حقل معين
  void clearFieldError(String field) {
    _errors.remove(field);
    _sanitizedValues.remove(field);
    notifyListeners();
  }
}
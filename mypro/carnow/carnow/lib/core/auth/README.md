# نظام المصادقة الموحد - Unified Authentication System

## المقدمة - Introduction

تم إنشاء نظام مصادقة موحد واحترافي لتطبيق CarNow يدعم جميع طرق تسجيل الدخول ويحل جميع المشاكل السابقة.

A unified and professional authentication system for CarNow app that supports all sign-in methods and solves all previous issues.

## المكونات الأساسية - Core Components

### 1. النظام الأساسي - Core System

#### `UnifiedAuthSystem` (lib/core/auth/unified_auth_system.dart)
- المزود الرئيسي الوحيد للمصادقة
- Single main provider for authentication
- يدير جميع حالات المصادقة
- Manages all authentication states
- يدعم التسجيل التلقائي والفحص الدوري للجلسة
- Supports auto-login and periodic session checks

### 2. بيانات المصادقة - Auth Data

```dart
class AuthData {
  final AuthStatus status;      // حالة المصادقة
  final User? user;            // مستخدم Supabase
  final UserModel? profile;    // بيانات المستخدم من قاعدة البيانات
  final String? error;         // رسالة الخطأ
  final bool isAutoLoginAttempted; // تم محاولة التسجيل التلقائي
  
  // مساعدات للوصول السريع
  bool get isAuthenticated;    // مسجل دخول؟
  bool get isLoading;         // يتم التحميل؟
  bool get hasError;          // يوجد خطأ؟
}
```

### 3. حالات المصادقة - Auth Status

```dart
enum AuthStatus {
  initial,        // أولي
  checking,       // يتم الفحص
  authenticating, // يتم تسجيل الدخول
  authenticated,  // مسجل دخول
  unauthenticated,// غير مسجل دخول
  error,          // خطأ
}
```

## الاستخدام - Usage

### 1. المزودات الأساسية - Core Providers

```dart
// المزود الرئيسي للمصادقة
final authProvider = ref.watch(unifiedAuthSystemProvider);

// مراقبة حالة المصادقة
ref.listen(unifiedAuthSystemProvider, (previous, next) {
  next.whenData((state) {
    if (state is AuthStateAuthenticated) {
      // المستخدم مسجل دخول
      print('User authenticated: ${state.user.firstName}');
    } else if (state is AuthStateUnauthenticated) {
      // المستخدم غير مسجل دخول
      print('User not authenticated');
    }
  });
});
```

### 2. تسجيل الدخول - Sign In

```dart
// تسجيل الدخول بالبريد الإلكتروني
final result = await ref.read(unifiedAuthSystemProvider.notifier)
  .signInWithEmail(
    email: '<EMAIL>',
    password: 'password123',
  );

result.when(
  success: (user) {
    print('Welcome ${user.firstName}!');
  },
  failure: (error) {
    print('Login failed: $error');
  },
  cancelled: () {
    print('Login cancelled');
  },
);

// تسجيل الدخول بـ Google
final googleResult = await ref.read(unifiedAuthSystemProvider.notifier)
  .signInWithGoogle();

googleResult.when(
  success: (user) {
    print('Welcome ${user.firstName}!');
  },
  failure: (error) {
    print('Google login failed: $error');
  },
  cancelled: () {
    print('Google login cancelled');
  },
);
```

### 3. إنشاء حساب جديد - Sign Up

```dart
final result = await ref.read(unifiedAuthSystemProvider.notifier)
  .signUp(
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'password123',
  );

result.when(
  success: (user) {
    print('Account created for ${user.firstName}!');
  },
  failure: (error) {
    print('Registration failed: $error');
  },
  cancelled: () {
    print('Registration cancelled');
  },
);
```

### 4. تسجيل الخروج - Sign Out

```dart
await ref.read(unifiedAuthSystemProvider.notifier).signOut();
print('User signed out successfully');
```

## الميزات - Features

### ✅ الميزات المدعومة - Supported Features

1. **تسجيل الدخول بالبريد الإلكتروني** - Email Sign In
   - التحقق من صحة البريد الإلكتروني
   - التحقق من قوة كلمة المرور
   - رسائل خطأ واضحة

2. **تسجيل الدخول بـ Google** - Google Sign In
   - دعم OAuth 2.0
   - إدارة الحسابات المتعددة
   - تسجيل الخروج التلقائي

3. **إنشاء حساب جديد** - Account Creation
   - التحقق من البيانات
   - إنشاء ملف شخصي تلقائياً
   - إرسال بريد ترحيب

4. **إدارة الجلسة** - Session Management
   - حفظ التوكينات بشكل آمن
   - تجديد التوكينات تلقائياً
   - تسجيل الخروج من جميع الأجهزة

5. **التسجيل التلقائي** - Auto Login
   - حفظ حالة تسجيل الدخول
   - استعادة الجلسة عند إعادة تشغيل التطبيق
   - التحقق من صلاحية التوكينات

### 🔒 الأمان - Security

1. **تخزين آمن** - Secure Storage
   - استخدام Flutter Secure Storage
   - تشفير البيانات الحساسة
   - حماية من الوصول غير المصرح

2. **إدارة التوكينات** - Token Management
   - JWT tokens مع RSA-256
   - Refresh tokens للتجديد التلقائي
   - حذف التوكينات عند تسجيل الخروج

3. **حماية من الهجمات** - Attack Protection
   - Rate limiting
   - Input validation
   - SQL injection protection

## بنية الخطة الدائمة - Forever Plan Architecture

### ✅ متوافق مع الخطة الدائمة - Forever Plan Compliant

```
Flutter (UI Only) → Go API → Supabase (Data Only)
```

- **Flutter**: واجهة المستخدم فقط - لا توجد منطق أعمال
- **Go Backend**: جميع منطق الأعمال والتحقق من الصحة
- **Supabase**: تخزين البيانات فقط + خدمة المصادقة فقط

### 🚫 ما لا نفعله - What We Don't Do

- ❌ استدعاءات مباشرة لـ Supabase من Flutter
- ❌ منطق أعمال معقد في Flutter
- ❌ خدمات معقدة للعمل دون اتصال
- ❌ تكوينات قاعدة بيانات مزدوجة
- ❌ خدمات معززة غير ضرورية

## الاستخدام في الشاشات - Usage in Screens

### شاشة تسجيل الدخول - Login Screen

```dart
class LoginScreen extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authSystem = ref.watch(unifiedAuthSystemProvider);
    
    // مراقبة حالة المصادقة
    ref.listen(unifiedAuthSystemProvider, (previous, next) {
      next.whenData((state) {
        if (state is AuthStateAuthenticated) {
          context.go('/home');
        }
      });
    });
    
    // تسجيل الدخول
    Future<void> handleLogin() async {
      final result = await ref.read(unifiedAuthSystemProvider.notifier)
        .signInWithEmail(email: email, password: password);
      
      result.when(
        success: (user) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('مرحباً ${user.firstName}!')),
          );
        },
        failure: (error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تسجيل الدخول: $error'),
              backgroundColor: Colors.red,
            ),
          );
        },
        cancelled: () {},
      );
    }
    
    return Scaffold(
      // ... UI implementation
    );
  }
}
```

## الاختبار - Testing

### اختبارات الوحدة - Unit Tests

```dart
test('should authenticate user with valid credentials', () async {
  final container = ProviderContainer();
  
  final result = await container.read(unifiedAuthSystemProvider.notifier)
    .signInWithEmail(email: '<EMAIL>', password: 'password123');
  
  expect(result, isA<AuthResultSuccess>());
  expect(result.user.email, equals('<EMAIL>'));
});
```

### اختبارات التكامل - Integration Tests

```dart
testWidgets('should navigate to home after successful login', (tester) async {
  await tester.pumpWidget(
    ProviderScope(
      child: MaterialApp(home: LoginScreen()),
    ),
  );
  
  await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
  await tester.enterText(find.byType(TextFormField).last, 'password123');
  await tester.tap(find.text('تسجيل الدخول'));
  await tester.pumpAndSettle();
  
  expect(find.text('مرحباً بك في CarNow'), findsOneWidget);
});
```

## الدعم والمساعدة - Support

### 🔧 استكشاف الأخطاء - Troubleshooting

1. **مشاكل في تسجيل الدخول** - Login Issues
   - تحقق من صحة البريد الإلكتروني وكلمة المرور
   - تحقق من اتصال الإنترنت
   - تحقق من حالة الخادم

2. **مشاكل في Google Sign In** - Google Sign In Issues
   - تحقق من إعدادات Google OAuth
   - تحقق من SHA-1 fingerprint
   - تحقق من package name

3. **مشاكل في التخزين الآمن** - Secure Storage Issues
   - تحقق من أذونات التطبيق
   - تحقق من إعدادات Keychain (iOS)
   - تحقق من إعدادات Keystore (Android)

### 📞 الدعم الفني - Technical Support

للمساعدة والدعم الفني، يرجى التواصل مع فريق التطوير.

For technical support and assistance, please contact the development team.

---

**تم التطوير بواسطة فريق CarNow**  
**Developed by CarNow Team** 
// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_state_manager.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$StateTransitionContext implements DiagnosticableTreeMixin {

 AuthLifecycleState get fromState; AuthLifecycleState get toState; DateTime get timestamp; String? get reason; Map<String, dynamic>? get metadata; String? get triggeredBy;
/// Create a copy of StateTransitionContext
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StateTransitionContextCopyWith<StateTransitionContext> get copyWith => _$StateTransitionContextCopyWithImpl<StateTransitionContext>(this as StateTransitionContext, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'StateTransitionContext'))
    ..add(DiagnosticsProperty('fromState', fromState))..add(DiagnosticsProperty('toState', toState))..add(DiagnosticsProperty('timestamp', timestamp))..add(DiagnosticsProperty('reason', reason))..add(DiagnosticsProperty('metadata', metadata))..add(DiagnosticsProperty('triggeredBy', triggeredBy));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StateTransitionContext&&(identical(other.fromState, fromState) || other.fromState == fromState)&&(identical(other.toState, toState) || other.toState == toState)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.reason, reason) || other.reason == reason)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.triggeredBy, triggeredBy) || other.triggeredBy == triggeredBy));
}


@override
int get hashCode => Object.hash(runtimeType,fromState,toState,timestamp,reason,const DeepCollectionEquality().hash(metadata),triggeredBy);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'StateTransitionContext(fromState: $fromState, toState: $toState, timestamp: $timestamp, reason: $reason, metadata: $metadata, triggeredBy: $triggeredBy)';
}


}

/// @nodoc
abstract mixin class $StateTransitionContextCopyWith<$Res>  {
  factory $StateTransitionContextCopyWith(StateTransitionContext value, $Res Function(StateTransitionContext) _then) = _$StateTransitionContextCopyWithImpl;
@useResult
$Res call({
 AuthLifecycleState fromState, AuthLifecycleState toState, DateTime timestamp, String? reason, Map<String, dynamic>? metadata, String? triggeredBy
});




}
/// @nodoc
class _$StateTransitionContextCopyWithImpl<$Res>
    implements $StateTransitionContextCopyWith<$Res> {
  _$StateTransitionContextCopyWithImpl(this._self, this._then);

  final StateTransitionContext _self;
  final $Res Function(StateTransitionContext) _then;

/// Create a copy of StateTransitionContext
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? fromState = null,Object? toState = null,Object? timestamp = null,Object? reason = freezed,Object? metadata = freezed,Object? triggeredBy = freezed,}) {
  return _then(_self.copyWith(
fromState: null == fromState ? _self.fromState : fromState // ignore: cast_nullable_to_non_nullable
as AuthLifecycleState,toState: null == toState ? _self.toState : toState // ignore: cast_nullable_to_non_nullable
as AuthLifecycleState,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,triggeredBy: freezed == triggeredBy ? _self.triggeredBy : triggeredBy // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [StateTransitionContext].
extension StateTransitionContextPatterns on StateTransitionContext {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StateTransitionContext value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StateTransitionContext() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StateTransitionContext value)  $default,){
final _that = this;
switch (_that) {
case _StateTransitionContext():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StateTransitionContext value)?  $default,){
final _that = this;
switch (_that) {
case _StateTransitionContext() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( AuthLifecycleState fromState,  AuthLifecycleState toState,  DateTime timestamp,  String? reason,  Map<String, dynamic>? metadata,  String? triggeredBy)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StateTransitionContext() when $default != null:
return $default(_that.fromState,_that.toState,_that.timestamp,_that.reason,_that.metadata,_that.triggeredBy);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( AuthLifecycleState fromState,  AuthLifecycleState toState,  DateTime timestamp,  String? reason,  Map<String, dynamic>? metadata,  String? triggeredBy)  $default,) {final _that = this;
switch (_that) {
case _StateTransitionContext():
return $default(_that.fromState,_that.toState,_that.timestamp,_that.reason,_that.metadata,_that.triggeredBy);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( AuthLifecycleState fromState,  AuthLifecycleState toState,  DateTime timestamp,  String? reason,  Map<String, dynamic>? metadata,  String? triggeredBy)?  $default,) {final _that = this;
switch (_that) {
case _StateTransitionContext() when $default != null:
return $default(_that.fromState,_that.toState,_that.timestamp,_that.reason,_that.metadata,_that.triggeredBy);case _:
  return null;

}
}

}

/// @nodoc


class _StateTransitionContext with DiagnosticableTreeMixin implements StateTransitionContext {
  const _StateTransitionContext({required this.fromState, required this.toState, required this.timestamp, this.reason, final  Map<String, dynamic>? metadata, this.triggeredBy}): _metadata = metadata;
  

@override final  AuthLifecycleState fromState;
@override final  AuthLifecycleState toState;
@override final  DateTime timestamp;
@override final  String? reason;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String? triggeredBy;

/// Create a copy of StateTransitionContext
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StateTransitionContextCopyWith<_StateTransitionContext> get copyWith => __$StateTransitionContextCopyWithImpl<_StateTransitionContext>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'StateTransitionContext'))
    ..add(DiagnosticsProperty('fromState', fromState))..add(DiagnosticsProperty('toState', toState))..add(DiagnosticsProperty('timestamp', timestamp))..add(DiagnosticsProperty('reason', reason))..add(DiagnosticsProperty('metadata', metadata))..add(DiagnosticsProperty('triggeredBy', triggeredBy));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StateTransitionContext&&(identical(other.fromState, fromState) || other.fromState == fromState)&&(identical(other.toState, toState) || other.toState == toState)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.reason, reason) || other.reason == reason)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.triggeredBy, triggeredBy) || other.triggeredBy == triggeredBy));
}


@override
int get hashCode => Object.hash(runtimeType,fromState,toState,timestamp,reason,const DeepCollectionEquality().hash(_metadata),triggeredBy);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'StateTransitionContext(fromState: $fromState, toState: $toState, timestamp: $timestamp, reason: $reason, metadata: $metadata, triggeredBy: $triggeredBy)';
}


}

/// @nodoc
abstract mixin class _$StateTransitionContextCopyWith<$Res> implements $StateTransitionContextCopyWith<$Res> {
  factory _$StateTransitionContextCopyWith(_StateTransitionContext value, $Res Function(_StateTransitionContext) _then) = __$StateTransitionContextCopyWithImpl;
@override @useResult
$Res call({
 AuthLifecycleState fromState, AuthLifecycleState toState, DateTime timestamp, String? reason, Map<String, dynamic>? metadata, String? triggeredBy
});




}
/// @nodoc
class __$StateTransitionContextCopyWithImpl<$Res>
    implements _$StateTransitionContextCopyWith<$Res> {
  __$StateTransitionContextCopyWithImpl(this._self, this._then);

  final _StateTransitionContext _self;
  final $Res Function(_StateTransitionContext) _then;

/// Create a copy of StateTransitionContext
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? fromState = null,Object? toState = null,Object? timestamp = null,Object? reason = freezed,Object? metadata = freezed,Object? triggeredBy = freezed,}) {
  return _then(_StateTransitionContext(
fromState: null == fromState ? _self.fromState : fromState // ignore: cast_nullable_to_non_nullable
as AuthLifecycleState,toState: null == toState ? _self.toState : toState // ignore: cast_nullable_to_non_nullable
as AuthLifecycleState,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,triggeredBy: freezed == triggeredBy ? _self.triggeredBy : triggeredBy // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc
mixin _$AuthProviderResult implements DiagnosticableTreeMixin {

 AuthProvider get provider;
/// Create a copy of AuthProviderResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthProviderResultCopyWith<AuthProviderResult> get copyWith => _$AuthProviderResultCopyWithImpl<AuthProviderResult>(this as AuthProviderResult, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthProviderResult'))
    ..add(DiagnosticsProperty('provider', provider));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthProviderResult&&(identical(other.provider, provider) || other.provider == provider));
}


@override
int get hashCode => Object.hash(runtimeType,provider);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthProviderResult(provider: $provider)';
}


}

/// @nodoc
abstract mixin class $AuthProviderResultCopyWith<$Res>  {
  factory $AuthProviderResultCopyWith(AuthProviderResult value, $Res Function(AuthProviderResult) _then) = _$AuthProviderResultCopyWithImpl;
@useResult
$Res call({
 AuthProvider provider
});




}
/// @nodoc
class _$AuthProviderResultCopyWithImpl<$Res>
    implements $AuthProviderResultCopyWith<$Res> {
  _$AuthProviderResultCopyWithImpl(this._self, this._then);

  final AuthProviderResult _self;
  final $Res Function(AuthProviderResult) _then;

/// Create a copy of AuthProviderResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? provider = null,}) {
  return _then(_self.copyWith(
provider: null == provider ? _self.provider : provider // ignore: cast_nullable_to_non_nullable
as AuthProvider,
  ));
}

}


/// Adds pattern-matching-related methods to [AuthProviderResult].
extension AuthProviderResultPatterns on AuthProviderResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( AuthProviderSuccess value)?  success,TResult Function( AuthProviderFailure value)?  failure,TResult Function( AuthProviderCancelled value)?  cancelled,required TResult orElse(),}){
final _that = this;
switch (_that) {
case AuthProviderSuccess() when success != null:
return success(_that);case AuthProviderFailure() when failure != null:
return failure(_that);case AuthProviderCancelled() when cancelled != null:
return cancelled(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( AuthProviderSuccess value)  success,required TResult Function( AuthProviderFailure value)  failure,required TResult Function( AuthProviderCancelled value)  cancelled,}){
final _that = this;
switch (_that) {
case AuthProviderSuccess():
return success(_that);case AuthProviderFailure():
return failure(_that);case AuthProviderCancelled():
return cancelled(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( AuthProviderSuccess value)?  success,TResult? Function( AuthProviderFailure value)?  failure,TResult? Function( AuthProviderCancelled value)?  cancelled,}){
final _that = this;
switch (_that) {
case AuthProviderSuccess() when success != null:
return success(_that);case AuthProviderFailure() when failure != null:
return failure(_that);case AuthProviderCancelled() when cancelled != null:
return cancelled(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( AuthProvider provider,  AuthResult result,  Map<String, dynamic>? metadata)?  success,TResult Function( AuthProvider provider,  String error,  AuthErrorType errorType,  bool isRecoverable,  Map<String, dynamic>? details)?  failure,TResult Function( AuthProvider provider,  String? reason)?  cancelled,required TResult orElse(),}) {final _that = this;
switch (_that) {
case AuthProviderSuccess() when success != null:
return success(_that.provider,_that.result,_that.metadata);case AuthProviderFailure() when failure != null:
return failure(_that.provider,_that.error,_that.errorType,_that.isRecoverable,_that.details);case AuthProviderCancelled() when cancelled != null:
return cancelled(_that.provider,_that.reason);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( AuthProvider provider,  AuthResult result,  Map<String, dynamic>? metadata)  success,required TResult Function( AuthProvider provider,  String error,  AuthErrorType errorType,  bool isRecoverable,  Map<String, dynamic>? details)  failure,required TResult Function( AuthProvider provider,  String? reason)  cancelled,}) {final _that = this;
switch (_that) {
case AuthProviderSuccess():
return success(_that.provider,_that.result,_that.metadata);case AuthProviderFailure():
return failure(_that.provider,_that.error,_that.errorType,_that.isRecoverable,_that.details);case AuthProviderCancelled():
return cancelled(_that.provider,_that.reason);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( AuthProvider provider,  AuthResult result,  Map<String, dynamic>? metadata)?  success,TResult? Function( AuthProvider provider,  String error,  AuthErrorType errorType,  bool isRecoverable,  Map<String, dynamic>? details)?  failure,TResult? Function( AuthProvider provider,  String? reason)?  cancelled,}) {final _that = this;
switch (_that) {
case AuthProviderSuccess() when success != null:
return success(_that.provider,_that.result,_that.metadata);case AuthProviderFailure() when failure != null:
return failure(_that.provider,_that.error,_that.errorType,_that.isRecoverable,_that.details);case AuthProviderCancelled() when cancelled != null:
return cancelled(_that.provider,_that.reason);case _:
  return null;

}
}

}

/// @nodoc


class AuthProviderSuccess with DiagnosticableTreeMixin implements AuthProviderResult {
  const AuthProviderSuccess({required this.provider, required this.result, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  

@override final  AuthProvider provider;
 final  AuthResult result;
 final  Map<String, dynamic>? _metadata;
 Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of AuthProviderResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthProviderSuccessCopyWith<AuthProviderSuccess> get copyWith => _$AuthProviderSuccessCopyWithImpl<AuthProviderSuccess>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthProviderResult.success'))
    ..add(DiagnosticsProperty('provider', provider))..add(DiagnosticsProperty('result', result))..add(DiagnosticsProperty('metadata', metadata));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthProviderSuccess&&(identical(other.provider, provider) || other.provider == provider)&&(identical(other.result, result) || other.result == result)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}


@override
int get hashCode => Object.hash(runtimeType,provider,result,const DeepCollectionEquality().hash(_metadata));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthProviderResult.success(provider: $provider, result: $result, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $AuthProviderSuccessCopyWith<$Res> implements $AuthProviderResultCopyWith<$Res> {
  factory $AuthProviderSuccessCopyWith(AuthProviderSuccess value, $Res Function(AuthProviderSuccess) _then) = _$AuthProviderSuccessCopyWithImpl;
@override @useResult
$Res call({
 AuthProvider provider, AuthResult result, Map<String, dynamic>? metadata
});


$AuthResultCopyWith<$Res> get result;

}
/// @nodoc
class _$AuthProviderSuccessCopyWithImpl<$Res>
    implements $AuthProviderSuccessCopyWith<$Res> {
  _$AuthProviderSuccessCopyWithImpl(this._self, this._then);

  final AuthProviderSuccess _self;
  final $Res Function(AuthProviderSuccess) _then;

/// Create a copy of AuthProviderResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? provider = null,Object? result = null,Object? metadata = freezed,}) {
  return _then(AuthProviderSuccess(
provider: null == provider ? _self.provider : provider // ignore: cast_nullable_to_non_nullable
as AuthProvider,result: null == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as AuthResult,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

/// Create a copy of AuthProviderResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AuthResultCopyWith<$Res> get result {
  
  return $AuthResultCopyWith<$Res>(_self.result, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}

/// @nodoc


class AuthProviderFailure with DiagnosticableTreeMixin implements AuthProviderResult {
  const AuthProviderFailure({required this.provider, required this.error, required this.errorType, this.isRecoverable = true, final  Map<String, dynamic>? details}): _details = details;
  

@override final  AuthProvider provider;
 final  String error;
 final  AuthErrorType errorType;
@JsonKey() final  bool isRecoverable;
 final  Map<String, dynamic>? _details;
 Map<String, dynamic>? get details {
  final value = _details;
  if (value == null) return null;
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of AuthProviderResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthProviderFailureCopyWith<AuthProviderFailure> get copyWith => _$AuthProviderFailureCopyWithImpl<AuthProviderFailure>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthProviderResult.failure'))
    ..add(DiagnosticsProperty('provider', provider))..add(DiagnosticsProperty('error', error))..add(DiagnosticsProperty('errorType', errorType))..add(DiagnosticsProperty('isRecoverable', isRecoverable))..add(DiagnosticsProperty('details', details));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthProviderFailure&&(identical(other.provider, provider) || other.provider == provider)&&(identical(other.error, error) || other.error == error)&&(identical(other.errorType, errorType) || other.errorType == errorType)&&(identical(other.isRecoverable, isRecoverable) || other.isRecoverable == isRecoverable)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,provider,error,errorType,isRecoverable,const DeepCollectionEquality().hash(_details));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthProviderResult.failure(provider: $provider, error: $error, errorType: $errorType, isRecoverable: $isRecoverable, details: $details)';
}


}

/// @nodoc
abstract mixin class $AuthProviderFailureCopyWith<$Res> implements $AuthProviderResultCopyWith<$Res> {
  factory $AuthProviderFailureCopyWith(AuthProviderFailure value, $Res Function(AuthProviderFailure) _then) = _$AuthProviderFailureCopyWithImpl;
@override @useResult
$Res call({
 AuthProvider provider, String error, AuthErrorType errorType, bool isRecoverable, Map<String, dynamic>? details
});




}
/// @nodoc
class _$AuthProviderFailureCopyWithImpl<$Res>
    implements $AuthProviderFailureCopyWith<$Res> {
  _$AuthProviderFailureCopyWithImpl(this._self, this._then);

  final AuthProviderFailure _self;
  final $Res Function(AuthProviderFailure) _then;

/// Create a copy of AuthProviderResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? provider = null,Object? error = null,Object? errorType = null,Object? isRecoverable = null,Object? details = freezed,}) {
  return _then(AuthProviderFailure(
provider: null == provider ? _self.provider : provider // ignore: cast_nullable_to_non_nullable
as AuthProvider,error: null == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String,errorType: null == errorType ? _self.errorType : errorType // ignore: cast_nullable_to_non_nullable
as AuthErrorType,isRecoverable: null == isRecoverable ? _self.isRecoverable : isRecoverable // ignore: cast_nullable_to_non_nullable
as bool,details: freezed == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class AuthProviderCancelled with DiagnosticableTreeMixin implements AuthProviderResult {
  const AuthProviderCancelled({required this.provider, this.reason});
  

@override final  AuthProvider provider;
 final  String? reason;

/// Create a copy of AuthProviderResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthProviderCancelledCopyWith<AuthProviderCancelled> get copyWith => _$AuthProviderCancelledCopyWithImpl<AuthProviderCancelled>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthProviderResult.cancelled'))
    ..add(DiagnosticsProperty('provider', provider))..add(DiagnosticsProperty('reason', reason));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthProviderCancelled&&(identical(other.provider, provider) || other.provider == provider)&&(identical(other.reason, reason) || other.reason == reason));
}


@override
int get hashCode => Object.hash(runtimeType,provider,reason);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthProviderResult.cancelled(provider: $provider, reason: $reason)';
}


}

/// @nodoc
abstract mixin class $AuthProviderCancelledCopyWith<$Res> implements $AuthProviderResultCopyWith<$Res> {
  factory $AuthProviderCancelledCopyWith(AuthProviderCancelled value, $Res Function(AuthProviderCancelled) _then) = _$AuthProviderCancelledCopyWithImpl;
@override @useResult
$Res call({
 AuthProvider provider, String? reason
});




}
/// @nodoc
class _$AuthProviderCancelledCopyWithImpl<$Res>
    implements $AuthProviderCancelledCopyWith<$Res> {
  _$AuthProviderCancelledCopyWithImpl(this._self, this._then);

  final AuthProviderCancelled _self;
  final $Res Function(AuthProviderCancelled) _then;

/// Create a copy of AuthProviderResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? provider = null,Object? reason = freezed,}) {
  return _then(AuthProviderCancelled(
provider: null == provider ? _self.provider : provider // ignore: cast_nullable_to_non_nullable
as AuthProvider,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc
mixin _$AuthStateManagerConfig implements DiagnosticableTreeMixin {

/// Maximum time to wait for initialization
 Duration get initializationTimeout;/// Token validation interval
 Duration get tokenValidationInterval;/// Session refresh threshold (refresh when token expires in this time)
 Duration get sessionRefreshThreshold;/// Maximum retry attempts for failed operations
 int get maxRetryAttempts;/// Enable automatic token refresh
 bool get enableAutoRefresh;/// Enable state transition logging
 bool get enableStateLogging;
/// Create a copy of AuthStateManagerConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthStateManagerConfigCopyWith<AuthStateManagerConfig> get copyWith => _$AuthStateManagerConfigCopyWithImpl<AuthStateManagerConfig>(this as AuthStateManagerConfig, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthStateManagerConfig'))
    ..add(DiagnosticsProperty('initializationTimeout', initializationTimeout))..add(DiagnosticsProperty('tokenValidationInterval', tokenValidationInterval))..add(DiagnosticsProperty('sessionRefreshThreshold', sessionRefreshThreshold))..add(DiagnosticsProperty('maxRetryAttempts', maxRetryAttempts))..add(DiagnosticsProperty('enableAutoRefresh', enableAutoRefresh))..add(DiagnosticsProperty('enableStateLogging', enableStateLogging));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthStateManagerConfig&&(identical(other.initializationTimeout, initializationTimeout) || other.initializationTimeout == initializationTimeout)&&(identical(other.tokenValidationInterval, tokenValidationInterval) || other.tokenValidationInterval == tokenValidationInterval)&&(identical(other.sessionRefreshThreshold, sessionRefreshThreshold) || other.sessionRefreshThreshold == sessionRefreshThreshold)&&(identical(other.maxRetryAttempts, maxRetryAttempts) || other.maxRetryAttempts == maxRetryAttempts)&&(identical(other.enableAutoRefresh, enableAutoRefresh) || other.enableAutoRefresh == enableAutoRefresh)&&(identical(other.enableStateLogging, enableStateLogging) || other.enableStateLogging == enableStateLogging));
}


@override
int get hashCode => Object.hash(runtimeType,initializationTimeout,tokenValidationInterval,sessionRefreshThreshold,maxRetryAttempts,enableAutoRefresh,enableStateLogging);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthStateManagerConfig(initializationTimeout: $initializationTimeout, tokenValidationInterval: $tokenValidationInterval, sessionRefreshThreshold: $sessionRefreshThreshold, maxRetryAttempts: $maxRetryAttempts, enableAutoRefresh: $enableAutoRefresh, enableStateLogging: $enableStateLogging)';
}


}

/// @nodoc
abstract mixin class $AuthStateManagerConfigCopyWith<$Res>  {
  factory $AuthStateManagerConfigCopyWith(AuthStateManagerConfig value, $Res Function(AuthStateManagerConfig) _then) = _$AuthStateManagerConfigCopyWithImpl;
@useResult
$Res call({
 Duration initializationTimeout, Duration tokenValidationInterval, Duration sessionRefreshThreshold, int maxRetryAttempts, bool enableAutoRefresh, bool enableStateLogging
});




}
/// @nodoc
class _$AuthStateManagerConfigCopyWithImpl<$Res>
    implements $AuthStateManagerConfigCopyWith<$Res> {
  _$AuthStateManagerConfigCopyWithImpl(this._self, this._then);

  final AuthStateManagerConfig _self;
  final $Res Function(AuthStateManagerConfig) _then;

/// Create a copy of AuthStateManagerConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? initializationTimeout = null,Object? tokenValidationInterval = null,Object? sessionRefreshThreshold = null,Object? maxRetryAttempts = null,Object? enableAutoRefresh = null,Object? enableStateLogging = null,}) {
  return _then(_self.copyWith(
initializationTimeout: null == initializationTimeout ? _self.initializationTimeout : initializationTimeout // ignore: cast_nullable_to_non_nullable
as Duration,tokenValidationInterval: null == tokenValidationInterval ? _self.tokenValidationInterval : tokenValidationInterval // ignore: cast_nullable_to_non_nullable
as Duration,sessionRefreshThreshold: null == sessionRefreshThreshold ? _self.sessionRefreshThreshold : sessionRefreshThreshold // ignore: cast_nullable_to_non_nullable
as Duration,maxRetryAttempts: null == maxRetryAttempts ? _self.maxRetryAttempts : maxRetryAttempts // ignore: cast_nullable_to_non_nullable
as int,enableAutoRefresh: null == enableAutoRefresh ? _self.enableAutoRefresh : enableAutoRefresh // ignore: cast_nullable_to_non_nullable
as bool,enableStateLogging: null == enableStateLogging ? _self.enableStateLogging : enableStateLogging // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [AuthStateManagerConfig].
extension AuthStateManagerConfigPatterns on AuthStateManagerConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AuthStateManagerConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AuthStateManagerConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AuthStateManagerConfig value)  $default,){
final _that = this;
switch (_that) {
case _AuthStateManagerConfig():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AuthStateManagerConfig value)?  $default,){
final _that = this;
switch (_that) {
case _AuthStateManagerConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( Duration initializationTimeout,  Duration tokenValidationInterval,  Duration sessionRefreshThreshold,  int maxRetryAttempts,  bool enableAutoRefresh,  bool enableStateLogging)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AuthStateManagerConfig() when $default != null:
return $default(_that.initializationTimeout,_that.tokenValidationInterval,_that.sessionRefreshThreshold,_that.maxRetryAttempts,_that.enableAutoRefresh,_that.enableStateLogging);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( Duration initializationTimeout,  Duration tokenValidationInterval,  Duration sessionRefreshThreshold,  int maxRetryAttempts,  bool enableAutoRefresh,  bool enableStateLogging)  $default,) {final _that = this;
switch (_that) {
case _AuthStateManagerConfig():
return $default(_that.initializationTimeout,_that.tokenValidationInterval,_that.sessionRefreshThreshold,_that.maxRetryAttempts,_that.enableAutoRefresh,_that.enableStateLogging);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( Duration initializationTimeout,  Duration tokenValidationInterval,  Duration sessionRefreshThreshold,  int maxRetryAttempts,  bool enableAutoRefresh,  bool enableStateLogging)?  $default,) {final _that = this;
switch (_that) {
case _AuthStateManagerConfig() when $default != null:
return $default(_that.initializationTimeout,_that.tokenValidationInterval,_that.sessionRefreshThreshold,_that.maxRetryAttempts,_that.enableAutoRefresh,_that.enableStateLogging);case _:
  return null;

}
}

}

/// @nodoc


class _AuthStateManagerConfig with DiagnosticableTreeMixin implements AuthStateManagerConfig {
  const _AuthStateManagerConfig({this.initializationTimeout = const Duration(seconds: 10), this.tokenValidationInterval = const Duration(minutes: 5), this.sessionRefreshThreshold = const Duration(minutes: 10), this.maxRetryAttempts = 3, this.enableAutoRefresh = true, this.enableStateLogging = true});
  

/// Maximum time to wait for initialization
@override@JsonKey() final  Duration initializationTimeout;
/// Token validation interval
@override@JsonKey() final  Duration tokenValidationInterval;
/// Session refresh threshold (refresh when token expires in this time)
@override@JsonKey() final  Duration sessionRefreshThreshold;
/// Maximum retry attempts for failed operations
@override@JsonKey() final  int maxRetryAttempts;
/// Enable automatic token refresh
@override@JsonKey() final  bool enableAutoRefresh;
/// Enable state transition logging
@override@JsonKey() final  bool enableStateLogging;

/// Create a copy of AuthStateManagerConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AuthStateManagerConfigCopyWith<_AuthStateManagerConfig> get copyWith => __$AuthStateManagerConfigCopyWithImpl<_AuthStateManagerConfig>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthStateManagerConfig'))
    ..add(DiagnosticsProperty('initializationTimeout', initializationTimeout))..add(DiagnosticsProperty('tokenValidationInterval', tokenValidationInterval))..add(DiagnosticsProperty('sessionRefreshThreshold', sessionRefreshThreshold))..add(DiagnosticsProperty('maxRetryAttempts', maxRetryAttempts))..add(DiagnosticsProperty('enableAutoRefresh', enableAutoRefresh))..add(DiagnosticsProperty('enableStateLogging', enableStateLogging));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AuthStateManagerConfig&&(identical(other.initializationTimeout, initializationTimeout) || other.initializationTimeout == initializationTimeout)&&(identical(other.tokenValidationInterval, tokenValidationInterval) || other.tokenValidationInterval == tokenValidationInterval)&&(identical(other.sessionRefreshThreshold, sessionRefreshThreshold) || other.sessionRefreshThreshold == sessionRefreshThreshold)&&(identical(other.maxRetryAttempts, maxRetryAttempts) || other.maxRetryAttempts == maxRetryAttempts)&&(identical(other.enableAutoRefresh, enableAutoRefresh) || other.enableAutoRefresh == enableAutoRefresh)&&(identical(other.enableStateLogging, enableStateLogging) || other.enableStateLogging == enableStateLogging));
}


@override
int get hashCode => Object.hash(runtimeType,initializationTimeout,tokenValidationInterval,sessionRefreshThreshold,maxRetryAttempts,enableAutoRefresh,enableStateLogging);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthStateManagerConfig(initializationTimeout: $initializationTimeout, tokenValidationInterval: $tokenValidationInterval, sessionRefreshThreshold: $sessionRefreshThreshold, maxRetryAttempts: $maxRetryAttempts, enableAutoRefresh: $enableAutoRefresh, enableStateLogging: $enableStateLogging)';
}


}

/// @nodoc
abstract mixin class _$AuthStateManagerConfigCopyWith<$Res> implements $AuthStateManagerConfigCopyWith<$Res> {
  factory _$AuthStateManagerConfigCopyWith(_AuthStateManagerConfig value, $Res Function(_AuthStateManagerConfig) _then) = __$AuthStateManagerConfigCopyWithImpl;
@override @useResult
$Res call({
 Duration initializationTimeout, Duration tokenValidationInterval, Duration sessionRefreshThreshold, int maxRetryAttempts, bool enableAutoRefresh, bool enableStateLogging
});




}
/// @nodoc
class __$AuthStateManagerConfigCopyWithImpl<$Res>
    implements _$AuthStateManagerConfigCopyWith<$Res> {
  __$AuthStateManagerConfigCopyWithImpl(this._self, this._then);

  final _AuthStateManagerConfig _self;
  final $Res Function(_AuthStateManagerConfig) _then;

/// Create a copy of AuthStateManagerConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? initializationTimeout = null,Object? tokenValidationInterval = null,Object? sessionRefreshThreshold = null,Object? maxRetryAttempts = null,Object? enableAutoRefresh = null,Object? enableStateLogging = null,}) {
  return _then(_AuthStateManagerConfig(
initializationTimeout: null == initializationTimeout ? _self.initializationTimeout : initializationTimeout // ignore: cast_nullable_to_non_nullable
as Duration,tokenValidationInterval: null == tokenValidationInterval ? _self.tokenValidationInterval : tokenValidationInterval // ignore: cast_nullable_to_non_nullable
as Duration,sessionRefreshThreshold: null == sessionRefreshThreshold ? _self.sessionRefreshThreshold : sessionRefreshThreshold // ignore: cast_nullable_to_non_nullable
as Duration,maxRetryAttempts: null == maxRetryAttempts ? _self.maxRetryAttempts : maxRetryAttempts // ignore: cast_nullable_to_non_nullable
as int,enableAutoRefresh: null == enableAutoRefresh ? _self.enableAutoRefresh : enableAutoRefresh // ignore: cast_nullable_to_non_nullable
as bool,enableStateLogging: null == enableStateLogging ? _self.enableStateLogging : enableStateLogging // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on

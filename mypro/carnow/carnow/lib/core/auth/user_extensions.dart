/// ============================================================================
/// USER EXTENSIONS - امتدادات المستخدم
/// ============================================================================
/// 
/// امتدادات للـ User وأنواع أخرى لإضافة الطرق المفقودة التي يتوقعها الكود
/// Extensions for User and other types to add missing methods that the code expects
/// ============================================================================
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'auth_models.dart';
import '../../features/account/models/user_model.dart';

/// امتداد User لإضافة طرق AsyncValue-style
/// User extension to add AsyncValue-style methods
extension UserExtensions on User? {
  
  /// محاكاة .valueOrNull للتوافق مع كود AsyncValue
  /// Simulate .valueOrNull for AsyncValue compatibility
  User? get valueOrNull => this;

  /// محاكاة .value للتوافق
  /// Simulate .value for compatibility
  User? get value => this;

  /// محاكاة .asData للتوافق
  /// Simulate .asData for compatibility
  AsyncData<User>? get asData => this != null ? AsyncData(this!) : null;

  /// طريقة when للتوافق مع AsyncValue
  /// when method for AsyncValue compatibility
  T when<T>({
    required T Function() loading,
    required T Function(Object error, StackTrace stackTrace) error,
    required T Function(User data) data,
  }) {
    if (this == null) {
      return loading(); // treat null as loading state
    }
    return data(this!);
  }
}

/// امتداد AutoDisposeProvider لإضافة .future
/// AutoDisposeProvider extension to add .future
extension AutoDisposeProviderExtensions<T> on AutoDisposeProvider<T> {
  
  /// محاكاة .future للتوافق مع AsyncProvider
  /// Simulate .future for AsyncProvider compatibility
  Provider<Future<T>> get future {
    return Provider<Future<T>>((ref) {
      return Future.value(ref.read(this));
    });
  }
}

/// امتداد للUserModel لإضافة خصائص إضافية
/// UserModel extension to add additional properties
extension UserModelAdditionalExtensions on UserModel {
  
  /// التحقق من كون المستخدم بائع (يوجد بالفعل في النموذج)
  /// Check if user is seller (already exists in model)
  /// Note: isSeller is already defined in UserModelExtensions in user_model.dart
  
  /// تحويل cityId إلى String للتوافق
  /// Convert cityId to String for compatibility
  String? get cityIdAsString => cityId?.toString();
} 
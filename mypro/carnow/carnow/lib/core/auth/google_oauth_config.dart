import 'package:flutter/foundation.dart';

/// Google OAuth Configuration
/// تكوين Google OAuth
class GoogleOAuthConfig {
  // Private constructor to prevent instantiation
  GoogleOAuthConfig._();

  /// Google OAuth Client ID for Android
  /// معرف العميل لـ Google OAuth لنظام Android
  /// 
  /// Configured from environment: CARNOW_GOOGLE_ANDROID_CLIENT_ID
  static const String androidClientId = '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com';

  /// Google OAuth Client ID for iOS
  /// معرف العميل لـ Google OAuth لنظام iOS
  /// 
  /// Configured from environment: CARNOW_GOOGLE_CLIENT_ID
  static const String iosClientId = '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com';

  /// Google OAuth Client ID for Web
  /// معرف العميل لـ Google OAuth للويب
  /// 
  /// Configured from environment: CARNOW_GOOGLE_CLIENT_ID
  static const String webClientId = '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com';

  /// Default scopes for Google OAuth
  /// النطاقات الافتراضية لـ Google OAuth
  static const List<String> defaultScopes = [
    'email',
    'profile',
  ];

  /// Extended scopes for additional permissions
  /// نطاقات موسعة للأذونات الإضافية
  static const List<String> extendedScopes = [
    'email',
    'profile',
    'openid',
  ];

  /// Get the appropriate client ID based on platform
  /// الحصول على معرف العميل المناسب حسب النظام
  static String getClientId() {
    if (kIsWeb) {
      return webClientId;
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      return androidClientId;
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      return iosClientId;
    } else {
      // Fallback to web client ID
      return webClientId;
    }
  }

  /// Get scopes based on requirements
  /// الحصول على النطاقات حسب المتطلبات
  static List<String> getScopes({bool extended = false}) {
    return extended ? extendedScopes : defaultScopes;
  }

  /// Validate configuration
  /// التحقق من صحة التكوين
  static bool isValid() {
    final clientId = getClientId();
    return clientId.isNotEmpty && 
           clientId != 'YOUR_ANDROID_CLIENT_ID.apps.googleusercontent.com' &&
           clientId != 'YOUR_IOS_CLIENT_ID.apps.googleusercontent.com' &&
           clientId != 'YOUR_WEB_CLIENT_ID.apps.googleusercontent.com';
  }

  /// Get configuration status
  /// الحصول على حالة التكوين
  static String getStatus() {
    if (!isValid()) {
      return '❌ Google OAuth not configured. Please set up client IDs.';
    }
    return '✅ Google OAuth configured for ${defaultTargetPlatform.name}';
  }
} 
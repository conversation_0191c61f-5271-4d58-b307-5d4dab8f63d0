// Enhanced Error Handler for Authentication
// معالج الأخطاء المحسن للمصادقة
//
// This file provides comprehensive error handling with Arabic localization
// for the CarNow unified authentication system.

import 'dart:developer' as developer;
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../networking/production_api_client.dart';
import '../performance/startup_optimizer.dart'; // Import for TimeoutException
import 'auth_models.dart';

/// Enhanced error handler with Arabic localization and user-friendly messages
/// معالج الأخطاء المحسن مع الترجمة العربية والرسائل الودودة للمستخدم
class EnhancedErrorHandler {
  static const String _loggerName = 'EnhancedErrorHandler';

  /// Convert any error to a user-friendly AuthErrorType with Arabic message
  /// تحويل أي خطأ إلى نوع خطأ مصادقة مع رسالة عربية ودودة
  static AuthErrorInfo handleError(
    dynamic error, {
    String? context,
    Map<String, dynamic>? additionalData,
  }) {
    developer.log(
      'Handling error in context: $context - $error',
      name: _loggerName,
      level: 1000,
    );

    // Handle ApiException from ProductionApiClient
    if (error is ApiException) {
      return _handleApiException(error, context);
    }

    // Handle common Flutter/Dart exceptions
    if (error is FormatException) {
      return _handleFormatException(error, context);
    }

    if (error is TimeoutException) {
      return _handleTimeoutException(error, context);
    }

    // Handle string error messages
    if (error is String) {
      return _handleStringError(error, context);
    }

    // Handle generic exceptions
    if (error is Exception) {
      return _handleGenericException(error, context);
    }

    // Fallback for unknown error types
    return _handleUnknownError(error, context);
  }

  /// Handle ApiException with specific error mapping
  /// معالجة ApiException مع ربط محدد للأخطاء
  static AuthErrorInfo _handleApiException(ApiException error, String? context) {
    switch (error.type) {
      case ApiErrorType.networkError:
        return AuthErrorInfo(
          type: AuthErrorType.networkError,
          message: 'خطأ في الاتصال بالشبكة',
          arabicMessage: 'تأكد من اتصالك بالإنترنت وحاول مرة أخرى',
          technicalDetails: error.message,
          isRetryable: true,
          suggestedAction: 'تحقق من اتصال الإنترنت',
        );

      case ApiErrorType.timeout:
        return AuthErrorInfo(
          type: AuthErrorType.networkError,
          message: 'انتهت مهلة الاتصال',
          arabicMessage: 'استغرق الطلب وقتاً أطول من المتوقع',
          technicalDetails: error.message,
          isRetryable: true,
          suggestedAction: 'حاول مرة أخرى',
        );

      case ApiErrorType.unauthorized:
        return AuthErrorInfo(
          type: AuthErrorType.sessionExpired,
          message: 'انتهت صلاحية الجلسة',
          arabicMessage: 'يجب تسجيل الدخول مرة أخرى',
          technicalDetails: error.message,
          isRetryable: false,
          suggestedAction: 'سجل الدخول مرة أخرى',
        );

      case ApiErrorType.forbidden:
        return AuthErrorInfo(
          type: AuthErrorType.oauthError,
          message: 'غير مصرح لك بالوصول',
          arabicMessage: 'ليس لديك صلاحية للوصول إلى هذه الخدمة',
          technicalDetails: error.message,
          isRetryable: false,
          suggestedAction: 'تواصل مع الدعم الفني',
        );

      case ApiErrorType.notFound:
        return AuthErrorInfo(
          type: AuthErrorType.serverError,
          message: 'الخدمة غير متاحة',
          arabicMessage: 'الخدمة المطلوبة غير متاحة حالياً',
          technicalDetails: error.message,
          isRetryable: true,
          suggestedAction: 'حاول مرة أخرى لاحقاً',
        );

      case ApiErrorType.badRequest:
        return AuthErrorInfo(
          type: AuthErrorType.invalidEmail,
          message: 'بيانات غير صحيحة',
          arabicMessage: 'تحقق من البيانات المدخلة وحاول مرة أخرى',
          technicalDetails: error.message,
          isRetryable: false,
          suggestedAction: 'تحقق من البيانات المدخلة',
        );

      case ApiErrorType.conflict:
        return AuthErrorInfo(
          type: AuthErrorType.emailAlreadyExists,
          message: 'الحساب موجود مسبقاً',
          arabicMessage: 'يوجد حساب مسجل بهذا البريد الإلكتروني',
          technicalDetails: error.message,
          isRetryable: false,
          suggestedAction: 'استخدم بريد إلكتروني آخر أو سجل الدخول',
        );

      case ApiErrorType.validation:
        return AuthErrorInfo(
          type: AuthErrorType.invalidEmail,
          message: 'خطأ في التحقق من البيانات',
          arabicMessage: 'البيانات المدخلة لا تتوافق مع المتطلبات',
          technicalDetails: error.message,
          isRetryable: false,
          suggestedAction: 'تحقق من صحة البيانات المدخلة',
        );

      case ApiErrorType.rateLimited:
        return AuthErrorInfo(
          type: AuthErrorType.rateLimitExceeded,
          message: 'تم تجاوز حد الطلبات',
          arabicMessage: 'لقد تجاوزت الحد المسموح من المحاولات',
          technicalDetails: error.message,
          isRetryable: true,
          suggestedAction: 'انتظر قليلاً ثم حاول مرة أخرى',
        );

      case ApiErrorType.serverError:
        return AuthErrorInfo(
          type: AuthErrorType.serverError,
          message: 'خطأ في الخادم',
          arabicMessage: 'حدث خطأ في الخادم، يرجى المحاولة لاحقاً',
          technicalDetails: error.message,
          isRetryable: true,
          suggestedAction: 'حاول مرة أخرى لاحقاً',
        );

      case ApiErrorType.serviceUnavailable:
        return AuthErrorInfo(
          type: AuthErrorType.serverError,
          message: 'الخدمة غير متاحة',
          arabicMessage: 'الخدمة غير متاحة حالياً للصيانة',
          technicalDetails: error.message,
          isRetryable: true,
          suggestedAction: 'حاول مرة أخرى لاحقاً',
        );

      case ApiErrorType.cancelled:
        return AuthErrorInfo(
          type: AuthErrorType.oauthError,
          message: 'تم إلغاء العملية',
          arabicMessage: 'تم إلغاء العملية بواسطة المستخدم',
          technicalDetails: error.message,
          isRetryable: true,
          suggestedAction: 'حاول مرة أخرى',
        );

      default:
        return AuthErrorInfo(
          type: AuthErrorType.unknown,
          message: 'حدث خطأ غير متوقع',
          arabicMessage: 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى',
          technicalDetails: error.message,
          isRetryable: true,
          suggestedAction: 'حاول مرة أخرى أو تواصل مع الدعم',
        );
    }
  }

  /// Handle FormatException
  /// معالجة FormatException
  static AuthErrorInfo _handleFormatException(FormatException error, String? context) {
    return AuthErrorInfo(
      type: AuthErrorType.invalidEmail,
      message: 'تنسيق البيانات غير صحيح',
      arabicMessage: 'البيانات المدخلة غير صحيحة التنسيق',
      technicalDetails: error.message,
      isRetryable: false,
      suggestedAction: 'تحقق من تنسيق البيانات المدخلة',
    );
  }

  /// Handle TimeoutException
  /// معالجة TimeoutException
  static AuthErrorInfo _handleTimeoutException(dynamic error, String? context) {
    return AuthErrorInfo(
      type: AuthErrorType.networkError,
      message: 'انتهت مهلة الاتصال',
      arabicMessage: 'استغرق الاتصال وقتاً أطول من المتوقع',
      technicalDetails: error.toString(),
      isRetryable: true,
      suggestedAction: 'تحقق من اتصال الإنترنت وحاول مرة أخرى',
    );
  }

  /// Handle string error messages
  /// معالجة رسائل الخطأ النصية
  static AuthErrorInfo _handleStringError(String error, String? context) {
    // Check for common error patterns
    final lowerError = error.toLowerCase();

    if (lowerError.contains('network') || lowerError.contains('connection')) {
      return AuthErrorInfo(
        type: AuthErrorType.networkError,
        message: 'خطأ في الاتصال',
        arabicMessage: 'تحقق من اتصالك بالإنترنت',
        technicalDetails: error,
        isRetryable: true,
        suggestedAction: 'تحقق من اتصال الإنترنت',
      );
    }

    if (lowerError.contains('timeout')) {
      return AuthErrorInfo(
        type: AuthErrorType.networkError,
        message: 'انتهت مهلة الاتصال',
        arabicMessage: 'استغرق الطلب وقتاً أطول من المتوقع',
        technicalDetails: error,
        isRetryable: true,
        suggestedAction: 'حاول مرة أخرى',
      );
    }

    if (lowerError.contains('unauthorized') || lowerError.contains('401')) {
      return AuthErrorInfo(
        type: AuthErrorType.sessionExpired,
        message: 'انتهت صلاحية الجلسة',
        arabicMessage: 'يجب تسجيل الدخول مرة أخرى',
        technicalDetails: error,
        isRetryable: false,
        suggestedAction: 'سجل الدخول مرة أخرى',
      );
    }

    if (lowerError.contains('email') && lowerError.contains('exist')) {
      return AuthErrorInfo(
        type: AuthErrorType.emailAlreadyExists,
        message: 'البريد الإلكتروني مستخدم',
        arabicMessage: 'يوجد حساب مسجل بهذا البريد الإلكتروني',
        technicalDetails: error,
        isRetryable: false,
        suggestedAction: 'استخدم بريد إلكتروني آخر أو سجل الدخول',
      );
    }

    if (lowerError.contains('password') && lowerError.contains('invalid')) {
      return AuthErrorInfo(
        type: AuthErrorType.invalidCredentials,
        message: 'كلمة المرور غير صحيحة',
        arabicMessage: 'كلمة المرور التي أدخلتها غير صحيحة',
        technicalDetails: error,
        isRetryable: false,
        suggestedAction: 'تحقق من كلمة المرور أو اطلب إعادة تعيين',
      );
    }

    // Default string error handling
    return AuthErrorInfo(
      type: AuthErrorType.unknown,
      message: 'حدث خطأ',
      arabicMessage: error.isNotEmpty ? error : 'حدث خطأ غير محدد',
      technicalDetails: error,
      isRetryable: true,
      suggestedAction: 'حاول مرة أخرى',
    );
  }

  /// Handle generic exceptions
  /// معالجة الاستثناءات العامة
  static AuthErrorInfo _handleGenericException(Exception error, String? context) {
    final errorString = error.toString();
    
    return AuthErrorInfo(
      type: AuthErrorType.unknown,
      message: 'حدث خطأ في التطبيق',
      arabicMessage: 'حدث خطأ غير متوقع في التطبيق',
      technicalDetails: errorString,
      isRetryable: true,
      suggestedAction: 'أعد تشغيل التطبيق أو حاول مرة أخرى',
    );
  }

  /// Handle unknown error types
  /// معالجة أنواع الأخطاء غير المعروفة
  static AuthErrorInfo _handleUnknownError(dynamic error, String? context) {
    return AuthErrorInfo(
      type: AuthErrorType.unknown,
      message: 'خطأ غير معروف',
      arabicMessage: 'حدث خطأ غير معروف، يرجى إعادة المحاولة',
      technicalDetails: error?.toString() ?? 'Unknown error',
      isRetryable: true,
      suggestedAction: 'أعد تشغيل التطبيق أو تواصل مع الدعم',
    );
  }

  /// Get context-specific error message for authentication operations
  /// الحصول على رسالة خطأ خاصة بالسياق لعمليات المصادقة
  static String getContextualMessage(AuthErrorType errorType, String operation) {
    switch (operation.toLowerCase()) {
      case 'login':
      case 'signin':
        return _getLoginErrorMessage(errorType);
      case 'register':
      case 'signup':
        return _getRegisterErrorMessage(errorType);
      case 'google_auth':
        return _getGoogleAuthErrorMessage(errorType);
      case 'token_refresh':
        return _getTokenRefreshErrorMessage(errorType);
      case 'password_reset':
        return _getPasswordResetErrorMessage(errorType);
      default:
        return _getGenericErrorMessage(errorType);
    }
  }

  static String _getLoginErrorMessage(AuthErrorType errorType) {
    switch (errorType) {
      case AuthErrorType.invalidCredentials:
        return 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
      case AuthErrorType.networkError:
        return 'خطأ في الاتصال أثناء تسجيل الدخول';
      case AuthErrorType.userNotFound:
        return 'لا يوجد حساب مسجل بهذا البريد الإلكتروني';
      case AuthErrorType.accountDisabled:
        return 'تم تعطيل هذا الحساب، تواصل مع الدعم';
      default:
        return 'فشل في تسجيل الدخول، حاول مرة أخرى';
    }
  }

  static String _getRegisterErrorMessage(AuthErrorType errorType) {
    switch (errorType) {
      case AuthErrorType.emailAlreadyExists:
        return 'يوجد حساب مسجل بهذا البريد الإلكتروني';
      case AuthErrorType.invalidEmail:
        return 'تحقق من البيانات المدخلة للتسجيل';
      case AuthErrorType.networkError:
        return 'خطأ في الاتصال أثناء إنشاء الحساب';
      case AuthErrorType.weakPassword:
        return 'كلمة المرور ضعيفة، استخدم كلمة مرور أقوى';
      default:
        return 'فشل في إنشاء الحساب، حاول مرة أخرى';
    }
  }

  static String _getGoogleAuthErrorMessage(AuthErrorType errorType) {
    switch (errorType) {
      case AuthErrorType.oauthError:
        return 'تم إلغاء تسجيل الدخول بواسطة Google';
      case AuthErrorType.networkError:
        return 'خطأ في الاتصال أثناء تسجيل الدخول بـ Google';
      case AuthErrorType.serverError:
        return 'خدمة Google غير متاحة حالياً';
      default:
        return 'فشل في تسجيل الدخول بـ Google';
    }
  }

  static String _getTokenRefreshErrorMessage(AuthErrorType errorType) {
    switch (errorType) {
      case AuthErrorType.sessionExpired:
        return 'انتهت صلاحية الجلسة، سجل الدخول مرة أخرى';
      case AuthErrorType.networkError:
        return 'خطأ في الاتصال أثناء تحديث الجلسة';
      default:
        return 'فشل في تحديث الجلسة';
    }
  }

  static String _getPasswordResetErrorMessage(AuthErrorType errorType) {
    switch (errorType) {
      case AuthErrorType.userNotFound:
        return 'لا يوجد حساب مسجل بهذا البريد الإلكتروني';
      case AuthErrorType.networkError:
        return 'خطأ في الاتصال أثناء إرسال رابط إعادة التعيين';
      default:
        return 'فشل في إرسال رابط إعادة تعيين كلمة المرور';
    }
  }

  static String _getGenericErrorMessage(AuthErrorType errorType) {
    switch (errorType) {
      case AuthErrorType.networkError:
        return 'خطأ في الاتصال بالشبكة';
      case AuthErrorType.serverError:
        return 'خطأ في الخادم';
      case AuthErrorType.rateLimitExceeded:
        return 'تم تجاوز حد المحاولات المسموح';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }
}

/// Comprehensive error information with Arabic localization
/// معلومات شاملة عن الخطأ مع الترجمة العربية
class AuthErrorInfo {
  final AuthErrorType type;
  final String message;
  final String arabicMessage;
  final String? technicalDetails;
  final bool isRetryable;
  final String? suggestedAction;
  final DateTime timestamp;

  AuthErrorInfo({
    required this.type,
    required this.message,
    required this.arabicMessage,
    this.technicalDetails,
    required this.isRetryable,
    this.suggestedAction,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Get user-friendly display message
  /// الحصول على رسالة عرض ودودة للمستخدم
  String get displayMessage => arabicMessage.isNotEmpty ? arabicMessage : message;

  /// Get technical details for debugging
  /// الحصول على التفاصيل التقنية للتصحيح
  String get debugInfo => technicalDetails ?? message;

  /// Check if error should trigger automatic retry
  /// التحقق من إمكانية إعادة المحاولة التلقائية
  bool get shouldAutoRetry => isRetryable && (
    type == AuthErrorType.network ||
    type == AuthErrorType.networkError ||
    type == AuthErrorType.serverError
  );

  @override
  String toString() {
    return 'AuthErrorInfo(type: $type, message: $message, isRetryable: $isRetryable)';
  }
}

/// Extended AuthErrorType enum with additional error types
/// تمديد enum AuthErrorType مع أنواع أخطاء إضافية
extension AuthErrorTypeExtension on AuthErrorType {
  /// Get Arabic description for error type
  /// الحصول على وصف عربي لنوع الخطأ
  String get arabicDescription {
    switch (this) {
      case AuthErrorType.network:
        return 'خطأ في الشبكة';
      case AuthErrorType.networkError:
        return 'خطأ في الشبكة';
      case AuthErrorType.invalidCredentials:
        return 'بيانات اعتماد غير صحيحة';
      case AuthErrorType.emailAlreadyExists:
        return 'الحساب موجود مسبقاً';
      case AuthErrorType.userNotFound:
        return 'الحساب غير موجود';
      case AuthErrorType.accountDisabled:
        return 'الحساب معطل';
      case AuthErrorType.emailNotVerified:
        return 'البريد الإلكتروني غير مفعل';
      case AuthErrorType.weakPassword:
        return 'كلمة مرور ضعيفة';
      case AuthErrorType.sessionExpired:
        return 'انتهت صلاحية الجلسة';
      case AuthErrorType.rateLimitExceeded:
        return 'تجاوز حد المحاولات';
      case AuthErrorType.tooManyAttempts:
        return 'محاولات كثيرة جداً';
      case AuthErrorType.serverError:
        return 'خطأ في الخادم';
      case AuthErrorType.serviceUnavailable:
        return 'الخدمة غير متاحة';
      case AuthErrorType.oauthError:
        return 'خطأ في المصادقة الخارجية';
      case AuthErrorType.invalidEmail:
        return 'بريد إلكتروني غير صحيح';
      case AuthErrorType.verificationRequired:
        return 'مطلوب التحقق من البريد الإلكتروني';
      case AuthErrorType.securityViolation:
        return 'انتهاك أمني';
      case AuthErrorType.unknown:
        return 'خطأ غير معروف';
    }
  }

  /// Check if error type is recoverable
  /// التحقق من إمكانية التعافي من نوع الخطأ
  bool get isRecoverable {
    switch (this) {
      case AuthErrorType.networkError:
      case AuthErrorType.serverError:
      case AuthErrorType.rateLimitExceeded:
        return true;
      default:
        return false;
    }
  }
}

/// Riverpod provider for EnhancedErrorHandler
/// مزود Riverpod لـ EnhancedErrorHandler
final enhancedErrorHandlerProvider = Provider<EnhancedErrorHandler>((ref) {
  return EnhancedErrorHandler();
});

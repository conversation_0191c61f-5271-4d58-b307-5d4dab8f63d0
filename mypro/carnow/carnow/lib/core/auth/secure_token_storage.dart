import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:riverpod/riverpod.dart';

part 'secure_token_storage.freezed.dart';
part 'secure_token_storage.g.dart';

/// Token data model for secure storage
@freezed
abstract class TokenData with _$TokenData {
  const factory TokenData({
    required String accessToken,
    String? refreshToken,
    required DateTime expiresAt,
    required DateTime createdAt,
    String? userId,
  }) = _TokenData;

  factory TokenData.fromJson(Map<String, dynamic> json) =>
      _$TokenDataFromJson(json);
}

/// Storage result for token operations
@freezed
class StorageResult<T> with _$StorageResult<T> {
  const factory StorageResult.success(T data) = StorageResultSuccess<T>;
  const factory StorageResult.failure(String error) = StorageResultFailure<T>;
}

/// Secure token storage service using Flutter Secure Storage
/// Implements encryption, expiry validation, and automatic cleanup
class SecureTokenStorage {
  static const String _accessTokenKey = 'carnow_access_token';
  static const String _refreshTokenKey = 'carnow_refresh_token';
  static const String _tokenDataKey = 'carnow_token_data';
  static const String _encryptionKeyKey = 'carnow_encryption_key';

  final FlutterSecureStorage _secureStorage;
  String? _cachedEncryptionKey;

  SecureTokenStorage({FlutterSecureStorage? secureStorage})
    : _secureStorage =
          secureStorage ??
          const FlutterSecureStorage(
            aOptions: AndroidOptions(
              encryptedSharedPreferences: true,
              sharedPreferencesName: 'carnow_secure_prefs',
              preferencesKeyPrefix: 'carnow_',
            ),
            iOptions: IOSOptions(
              groupId: 'group.com.carnow.app',
              accountName: 'carnow_tokens',
              accessibility: KeychainAccessibility.first_unlock_this_device,
            ),
          );

  /// Initialize the storage service and generate encryption key if needed
  Future<void> initialize() async {
    await _ensureEncryptionKey();
  }

  /// Store token data securely with encryption
  Future<StorageResult<void>> storeTokenData(TokenData tokenData) async {
    try {
      // Encrypt the token data
      final encryptedData = await _encryptData(jsonEncode(tokenData.toJson()));

      // Store encrypted data
      await _secureStorage.write(key: _tokenDataKey, value: encryptedData);

      // Store individual tokens for backward compatibility
      await Future.wait([
        _secureStorage.write(
          key: _accessTokenKey,
          value: await _encryptData(tokenData.accessToken),
        ),
        if (tokenData.refreshToken != null)
          _secureStorage.write(
            key: _refreshTokenKey,
            value: await _encryptData(tokenData.refreshToken!),
          ),
      ]);

      return const StorageResult.success(null);
    } catch (e) {
      return StorageResult.failure('Failed to store token data: $e');
    }
  }

  /// Retrieve token data with automatic expiry validation
  Future<StorageResult<TokenData?>> getTokenData() async {
    try {
      final encryptedData = await _secureStorage.read(key: _tokenDataKey);

      if (encryptedData == null) {
        return const StorageResult.success(null);
      }

      // Decrypt and parse token data
      final decryptedData = await _decryptData(encryptedData);
      final tokenData = TokenData.fromJson(jsonDecode(decryptedData));

      // Validate token expiry
      if (_isTokenExpired(tokenData)) {
        // Token is expired, clean it up
        await clearTokenData();
        return const StorageResult.success(null);
      }

      return StorageResult.success(tokenData);
    } catch (e) {
      return StorageResult.failure('Failed to retrieve token data: $e');
    }
  }

  /// Get access token with expiry validation
  Future<StorageResult<String?>> getAccessToken() async {
    try {
      final tokenDataResult = await getTokenData();

      return tokenDataResult.when(
        success: (tokenData) => StorageResult.success(tokenData?.accessToken),
        failure: (error) => StorageResult.failure(error),
      );
    } catch (e) {
      return StorageResult.failure('Failed to get access token: $e');
    }
  }

  /// Get refresh token
  Future<StorageResult<String?>> getRefreshToken() async {
    try {
      final tokenDataResult = await getTokenData();

      return tokenDataResult.when(
        success: (tokenData) => StorageResult.success(tokenData?.refreshToken),
        failure: (error) => StorageResult.failure(error),
      );
    } catch (e) {
      return StorageResult.failure('Failed to get refresh token: $e');
    }
  }

  /// Check if token exists and is valid
  Future<bool> hasValidToken() async {
    final result = await getTokenData();
    return result.when(
      success: (tokenData) => tokenData != null,
      failure: (_) => false,
    );
  }

  /// Check if token is expired
  Future<bool> isTokenExpired() async {
    final result = await getTokenData();
    return result.when(
      success: (tokenData) => tokenData == null || _isTokenExpired(tokenData),
      failure: (_) => true,
    );
  }

  /// Check if token exists and is valid (not expired)
  Future<bool> isTokenValid(String token) async {
    final result = await getTokenData();
    return result.when(
      success: (tokenData) {
        if (tokenData == null) return false;
        if (_isTokenExpired(tokenData)) return false;
        // Optionally check if the provided token matches the stored token
        return tokenData.accessToken == token;
      },
      failure: (_) => false,
    );
  }

  /// Clear all stored token data
  Future<StorageResult<void>> clearTokenData() async {
    try {
      await Future.wait([
        _secureStorage.delete(key: _tokenDataKey),
        _secureStorage.delete(key: _accessTokenKey),
        _secureStorage.delete(key: _refreshTokenKey),
      ]);

      return const StorageResult.success(null);
    } catch (e) {
      return StorageResult.failure('Failed to clear token data: $e');
    }
  }

  /// Update access token while keeping other data
  Future<StorageResult<void>> updateAccessToken(
    String newAccessToken,
    DateTime expiresAt,
  ) async {
    try {
      final currentResult = await getTokenData();

      return await currentResult.when(
        success: (currentData) async {
          if (currentData == null) {
            return const StorageResult.failure('No existing token data found');
          }

          final updatedData = currentData.copyWith(
            accessToken: newAccessToken,
            expiresAt: expiresAt,
          );

          return await storeTokenData(updatedData);
        },
        failure: (error) => StorageResult.failure(error),
      );
    } catch (e) {
      return StorageResult.failure('Failed to update access token: $e');
    }
  }

  /// Perform automatic cleanup of expired tokens
  Future<StorageResult<void>> performCleanup() async {
    try {
      final result = await getTokenData();

      return await result.when(
        success: (tokenData) async {
          if (tokenData != null && _isTokenExpired(tokenData)) {
            return await clearTokenData();
          }
          return const StorageResult.success(null);
        },
        failure: (error) => StorageResult.failure(error),
      );
    } catch (e) {
      return StorageResult.failure('Failed to perform cleanup: $e');
    }
  }

  /// Clear all storage data (for logout or reset)
  Future<StorageResult<void>> clearAllData() async {
    try {
      await _secureStorage.deleteAll();
      _cachedEncryptionKey = null;
      return const StorageResult.success(null);
    } catch (e) {
      return StorageResult.failure('Failed to clear all data: $e');
    }
  }

  // Private helper methods

  /// Check if token data is expired
  bool _isTokenExpired(TokenData tokenData) {
    final now = DateTime.now();
    return now.isAfter(tokenData.expiresAt);
  }

  /// Ensure encryption key exists or generate a new one
  Future<void> _ensureEncryptionKey() async {
    if (_cachedEncryptionKey != null) return;

    String? existingKey = await _secureStorage.read(key: _encryptionKeyKey);

    if (existingKey == null) {
      // Generate new encryption key
      final bytes = List<int>.generate(
        32,
        (i) => DateTime.now().millisecondsSinceEpoch.hashCode + i,
      );
      final key = base64Encode(bytes);

      await _secureStorage.write(key: _encryptionKeyKey, value: key);
      _cachedEncryptionKey = key;
    } else {
      _cachedEncryptionKey = existingKey;
    }
  }

  /// Encrypt data using AES encryption
  Future<String> _encryptData(String data) async {
    await _ensureEncryptionKey();

    // Simple encryption using base64 and key mixing
    // For production, consider using more robust encryption
    final keyBytes = base64Decode(_cachedEncryptionKey!);
    final dataBytes = utf8.encode(data);

    // XOR encryption with key
    final encryptedBytes = <int>[];
    for (int i = 0; i < dataBytes.length; i++) {
      encryptedBytes.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return base64Encode(encryptedBytes);
  }

  /// Decrypt data using AES decryption
  Future<String> _decryptData(String encryptedData) async {
    await _ensureEncryptionKey();

    final keyBytes = base64Decode(_cachedEncryptionKey!);
    final encryptedBytes = base64Decode(encryptedData);

    // XOR decryption with key
    final decryptedBytes = <int>[];
    for (int i = 0; i < encryptedBytes.length; i++) {
      decryptedBytes.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return utf8.decode(decryptedBytes);
  }
}

/// Provider for SecureTokenStorage
@riverpod
SecureTokenStorage secureTokenStorage(Ref ref) {
  return SecureTokenStorage();
}

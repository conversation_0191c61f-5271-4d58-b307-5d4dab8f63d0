// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'google_oauth_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$googleOAuthServiceHash() =>
    r'6bdc4a418799d6cd714a9038ec85c325379b69f8';

/// Enhanced Google OAuth service with comprehensive error handling
/// and integration with the CarNow unified authentication system
///
/// Copied from [GoogleOAuthService].
@ProviderFor(GoogleOAuthService)
final googleOAuthServiceProvider =
    AutoDisposeAsyncNotifierProvider<GoogleOAuthService, void>.internal(
      GoogleOAuthService.new,
      name: r'googleOAuthServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$googleOAuthServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$GoogleOAuthService = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

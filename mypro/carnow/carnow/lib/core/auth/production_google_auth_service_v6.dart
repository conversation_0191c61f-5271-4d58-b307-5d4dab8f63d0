import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io' show Platform;

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;

import 'auth_interfaces.dart';

// =============================================================================
// PRODUCTION GOOGLE AUTHENTICATION SERVICE
// =============================================================================

/// Production Google OAuth authentication service for Google Sign-In v6.3.0
///
/// This service implements real Google OAuth authentication with proper
/// backend integration following the Forever Plan Architecture:
/// Flutter UI Only → Go API → Supabase Data
class ProductionGoogleAuthServiceV6 implements IGoogleAuthService {
  // Configuration
  bool _isConfigured = false;
  List<String> _configuredScopes = ['email', 'profile'];
  String? _serverClientId;
  GoogleSignIn? _googleSignIn;

  // Backend configuration
  static const String _backendBaseUrl = 'https://backend-go-8klm.onrender.com';
  static const Duration _requestTimeout = Duration(seconds: 30);

  // ---------------------------------------------------------------------------
  // Initialization
  // ---------------------------------------------------------------------------

  @override
  bool get isConfigured => _isConfigured;

  @override
  List<String> get configuredScopes => List.from(_configuredScopes);

  @override
  Future<void> initialize({
    required String clientId,
    List<String> scopes = const ['email', 'profile'],
  }) async {
    try {
      developer.log(
        'Initializing Google Sign-In v6.3.0 with serverClientId',
        name: 'ProductionGoogleAuthServiceV6',
      );

      _configuredScopes = List.from(scopes);
      _serverClientId = clientId;

      // Create GoogleSignIn instance with serverClientId for v6.3.0
      _googleSignIn = GoogleSignIn(
        scopes: scopes,
        serverClientId:
            _serverClientId, // Web Client ID for server authentication
      );

      developer.log(
        'GoogleSignIn instance created with serverClientId: ${_serverClientId?.substring(0, 20)}...',
        name: 'ProductionGoogleAuthServiceV6',
      );

      developer.log(
        'Google Sign-In configured with scopes: $scopes',
        name: 'ProductionGoogleAuthServiceV6',
      );

      _isConfigured = true;
    } catch (error) {
      developer.log(
        'Failed to initialize Google Sign-In: $error',
        name: 'ProductionGoogleAuthServiceV6',
        error: error,
      );
      _isConfigured = false;
      rethrow;
    }
  }

  // ---------------------------------------------------------------------------
  // Google OAuth Implementation
  // ---------------------------------------------------------------------------

  @override
  Future<GoogleAuthResult> signIn() async {
    try {
      if (!_isConfigured || _googleSignIn == null) {
        return GoogleAuthResult.failure(
          error: 'Google Sign-In not configured. Call initialize() first.',
          errorCode: 'not_configured',
        );
      }

      developer.log(
        'Starting Google Sign-In process with v6.3.0',
        name: 'ProductionGoogleAuthServiceV6',
      );

      // Force account selection by signing out first
      // This ensures user always sees the account picker
      try {
        await _googleSignIn!.signOut();
        developer.log(
          'Signed out previous session to force account selection',
          name: 'ProductionGoogleAuthServiceV6',
        );
      } catch (e) {
        // Ignore sign out errors - user might not be signed in
        developer.log(
          'Sign out before sign in (expected if no previous session): $e',
          name: 'ProductionGoogleAuthServiceV6',
        );
      }

      // Attempt Google Sign-In using signIn() method for v6.3.0
      GoogleSignInAccount? googleUser;
      try {
        googleUser = await _googleSignIn!.signIn();
      } on PlatformException catch (e) {
        developer.log(
          'Google Sign-In platform exception: ${e.code} - ${e.message}',
          name: 'ProductionGoogleAuthServiceV6',
          error: e,
        );

        if (e.code == 'sign_in_cancelled') {
          return GoogleAuthResult.cancelled();
        }
        rethrow;
      }

      // Check if user cancelled sign-in
      if (googleUser == null) {
        developer.log(
          'Google Sign-In cancelled by user',
          name: 'ProductionGoogleAuthServiceV6',
        );
        return GoogleAuthResult.cancelled();
      }

      developer.log(
        'Google Sign-In successful for user: ${googleUser.email}',
        name: 'ProductionGoogleAuthServiceV6',
      );

      // Get authentication details
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.idToken == null) {
        return GoogleAuthResult.failure(
          error: 'Failed to get Google ID token',
          errorCode: 'no_id_token',
        );
      }

      developer.log(
        'Google ID token obtained successfully',
        name: 'ProductionGoogleAuthServiceV6',
      );

      // Exchange Google ID token with backend
      final backendResponse = await _exchangeGoogleTokenWithBackend(
        googleAuth.idToken!,
      );

      if (backendResponse == null) {
        return GoogleAuthResult.failure(
          error: 'Failed to authenticate with backend server',
          errorCode: 'backend_auth_failed',
        );
      }

      // Create GoogleUserInfo with proper field mapping
      final googleUserInfo = GoogleUserInfo(
        id: googleUser.id,
        email: googleUser.email,
        displayName: googleUser.displayName,
        firstName: googleUser.displayName?.split(' ').first,
        lastName: googleUser.displayName?.split(' ').skip(1).join(' '),
        photoUrl: googleUser.photoUrl,
        name: googleUser.displayName,
        givenName: googleUser.displayName?.split(' ').first,
        familyName: googleUser.displayName?.split(' ').skip(1).join(' '),
      );

      return GoogleAuthResult.success(
        idToken: googleAuth.idToken!,
        accessToken: googleAuth.accessToken,
        userInfo: googleUserInfo,
        expiryDate: backendResponse['expires_at'] != null
            ? DateTime.fromMillisecondsSinceEpoch(
                backendResponse['expires_at'] * 1000,
              )
            : null,
      );
    } catch (error) {
      developer.log(
        'Google Sign-In failed with error: $error',
        name: 'ProductionGoogleAuthServiceV6',
        error: error,
      );

      return GoogleAuthResult.failure(
        error: 'Google Sign-In failed: ${error.toString()}',
        errorCode: 'sign_in_failed',
      );
    }
  }

  @override
  Future<void> signOut() async {
    if (!_isConfigured || _googleSignIn == null) {
      developer.log(
        'Google Sign-In not configured for sign out',
        name: 'ProductionGoogleAuthServiceV6',
      );
      return;
    }

    try {
      await _googleSignIn!.signOut();
      developer.log(
        'Google Sign-Out successful',
        name: 'ProductionGoogleAuthServiceV6',
      );
    } catch (error) {
      developer.log(
        'Google Sign-Out failed: $error',
        name: 'ProductionGoogleAuthServiceV6',
        error: error,
      );
    }
  }

  @override
  Future<GoogleUserInfo?> getCurrentUser() async {
    if (!_isConfigured || _googleSignIn == null) {
      return null;
    }

    try {
      final currentUser = _googleSignIn!.currentUser;
      if (currentUser == null) return null;

      return GoogleUserInfo(
        id: currentUser.id,
        email: currentUser.email,
        displayName: currentUser.displayName,
        firstName: currentUser.displayName?.split(' ').first,
        lastName: currentUser.displayName?.split(' ').skip(1).join(' '),
        photoUrl: currentUser.photoUrl,
        name: currentUser.displayName,
        givenName: currentUser.displayName?.split(' ').first,
        familyName: currentUser.displayName?.split(' ').skip(1).join(' '),
      );
    } catch (error) {
      developer.log(
        'Failed to get current Google user: $error',
        name: 'ProductionGoogleAuthServiceV6',
        error: error,
      );
      return null;
    }
  }

  @override
  Future<String?> getAccessToken() async {
    if (!_isConfigured || _googleSignIn == null) {
      return null;
    }

    try {
      final currentUser = _googleSignIn!.currentUser;
      if (currentUser == null) return null;

      final auth = await currentUser.authentication;
      return auth.accessToken;
    } catch (error) {
      developer.log(
        'Failed to get access token: $error',
        name: 'ProductionGoogleAuthServiceV6',
        error: error,
      );
      return null;
    }
  }

  @override
  Future<String?> getIdToken() async {
    if (!_isConfigured || _googleSignIn == null) {
      return null;
    }

    try {
      final currentUser = _googleSignIn!.currentUser;
      if (currentUser == null) return null;

      final auth = await currentUser.authentication;
      return auth.idToken;
    } catch (error) {
      developer.log(
        'Failed to get ID token: $error',
        name: 'ProductionGoogleAuthServiceV6',
        error: error,
      );
      return null;
    }
  }

  @override
  Future<void> disconnect() async {
    if (!_isConfigured || _googleSignIn == null) {
      return;
    }

    try {
      await _googleSignIn!.disconnect();
      developer.log(
        'Google account disconnected successfully',
        name: 'ProductionGoogleAuthServiceV6',
      );
    } catch (error) {
      developer.log(
        'Failed to disconnect Google account: $error',
        name: 'ProductionGoogleAuthServiceV6',
        error: error,
      );
    }
  }

  @override
  Future<bool> isSignedIn() async {
    if (!_isConfigured || _googleSignIn == null) {
      return false;
    }

    try {
      return _googleSignIn!.currentUser != null;
    } catch (error) {
      developer.log(
        'Failed to check sign-in status: $error',
        name: 'ProductionGoogleAuthServiceV6',
        error: error,
      );
      return false;
    }
  }

  @override
  Future<GoogleAuthResult> signInSilently() async {
    if (!_isConfigured || _googleSignIn == null) {
      return GoogleAuthResult.failure(
        error: 'Google Sign-In service not configured',
        errorCode: 'service_not_configured',
      );
    }

    try {
      developer.log(
        'Attempting silent Google Sign-In...',
        name: 'ProductionGoogleAuthServiceV6',
      );

      final googleUser = await _googleSignIn!.signInSilently();
      if (googleUser == null) {
        return GoogleAuthResult.failure(
          error: 'Silent sign-in failed - no cached user',
          errorCode: 'no_cached_user',
        );
      }

      final googleAuth = await googleUser.authentication;
      if (googleAuth.idToken == null) {
        return GoogleAuthResult.failure(
          error: 'Failed to get Google ID token during silent sign-in',
          errorCode: 'no_id_token',
        );
      }

      // Exchange Google ID token with backend
      final backendResponse = await _exchangeGoogleTokenWithBackend(
        googleAuth.idToken!,
      );
      if (backendResponse == null) {
        return GoogleAuthResult.failure(
          error: 'Backend authentication failed during silent sign-in',
          errorCode: 'backend_auth_failed',
        );
      }

      // Create GoogleUserInfo with proper field mapping
      final googleUserInfo = GoogleUserInfo(
        id: googleUser.id,
        email: googleUser.email,
        displayName: googleUser.displayName,
        firstName: googleUser.displayName?.split(' ').first,
        lastName: googleUser.displayName?.split(' ').skip(1).join(' '),
        photoUrl: googleUser.photoUrl,
        name: googleUser.displayName,
        givenName: googleUser.displayName?.split(' ').first,
        familyName: googleUser.displayName?.split(' ').skip(1).join(' '),
      );

      return GoogleAuthResult.success(
        idToken: googleAuth.idToken!,
        accessToken: googleAuth.accessToken,
        userInfo: googleUserInfo,
        expiryDate: backendResponse['expires_at'] != null
            ? DateTime.fromMillisecondsSinceEpoch(
                backendResponse['expires_at'] * 1000,
              )
            : null,
      );
    } catch (error, stackTrace) {
      developer.log(
        'Silent Google Sign-In failed: $error',
        name: 'ProductionGoogleAuthServiceV6',
        error: error,
        stackTrace: stackTrace,
      );

      return GoogleAuthResult.failure(
        error: 'Silent sign-in failed: ${error.toString()}',
        errorCode: 'silent_signin_error',
      );
    }
  }

  // ---------------------------------------------------------------------------
  // Backend Integration
  // ---------------------------------------------------------------------------

  Future<Map<String, dynamic>?> _exchangeGoogleTokenWithBackend(
    String idToken,
  ) async {
    try {
      developer.log(
        'Exchanging Google ID token with backend',
        name: 'ProductionGoogleAuthServiceV6',
      );

      final response = await http
          .post(
            Uri.parse('$_backendBaseUrl/api/v1/auth/google'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode({'id_token': idToken}),
          )
          .timeout(_requestTimeout);

      developer.log(
        'Backend response status: ${response.statusCode}',
        name: 'ProductionGoogleAuthServiceV6',
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;

        if (responseData['success'] == true) {
          developer.log(
            'Backend authentication successful',
            name: 'ProductionGoogleAuthServiceV6',
          );
          return responseData;
        } else {
          developer.log(
            'Backend authentication failed: ${responseData['message']}',
            name: 'ProductionGoogleAuthServiceV6',
          );
          return null;
        }
      } else {
        developer.log(
          'Backend request failed with status ${response.statusCode}: ${response.body}',
          name: 'ProductionGoogleAuthServiceV6',
        );
        return null;
      }
    } catch (error) {
      developer.log(
        'Backend token exchange failed: $error',
        name: 'ProductionGoogleAuthServiceV6',
        error: error,
      );
      return null;
    }
  }
}

// =============================================================================
// RIVERPOD PROVIDERS FOR V6
// =============================================================================

/// Provider for ProductionGoogleAuthServiceV6
final productionGoogleAuthServiceProviderV6 = Provider<IGoogleAuthService>((ref) {
  final service = ProductionGoogleAuthServiceV6();
  
  // Auto-initialize the service with the proper client ID
  service.initialize(
    clientId: _getGoogleClientId(),
    scopes: ['email', 'profile'],
  );
  
  return service;
});

/// Get Google OAuth Client ID based on platform
String _getGoogleClientId() {
  // These are the actual client IDs from the environment configuration
  // They should match what's in EnvConfig and LocalEnvConfig
  if (kIsWeb) {
    return '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com'; // Web Client ID
  } else if (Platform.isAndroid) {
    return '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com'; // Android Client ID
  } else if (Platform.isIOS) {
    return '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com'; // iOS Client ID
  } else {
    return '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com'; // Default Client ID
  }
}

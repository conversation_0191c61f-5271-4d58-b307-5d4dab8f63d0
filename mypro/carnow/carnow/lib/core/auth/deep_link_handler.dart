/// ============================================================================
/// DEEP LINK HANDLER - Forever Plan Architecture
/// ============================================================================
///
/// معالج الروابط العميقة - بنية الخطة الدائمة
/// Deep link handler for OAuth authentication callbacks
///
/// يتعامل مع روابط OAuth callbacks ويسجل المعلومات للتشخيص
/// ============================================================================
library;

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../utils/unified_logger.dart';
import 'simple_oauth_config.dart';

/// نتيجة معالجة OAuth callback
/// OAuth callback processing result
class OAuthCallbackResult {
  final bool isSuccess;
  final String? authCode;
  final String? accessToken;
  final String? state;
  final String? error;

  const OAuthCallbackResult({
    required this.isSuccess,
    this.authCode,
    this.accessToken,
    this.state,
    this.error,
  });

  factory OAuthCallbackResult.success({
    String? authCode,
    String? accessToken,
    String? state,
  }) {
    return OAuthCallbackResult(
      isSuccess: true,
      authCode: authCode,
      accessToken: accessToken,
      state: state,
    );
  }

  factory OAuthCallbackResult.error(String error) {
    return OAuthCallbackResult(isSuccess: false, error: error);
  }
}

/// نوع الرابط العميق
/// Deep link type
enum DeepLinkType {
  oauth,
  passwordReset,
  emailVerification,
  unknown,
}

/// مجموعة معرفات الحالة
/// State identifier collection
class StateIdentifiers {
  static const String oauthCallback = 'oauth_callback';
  static const String passwordReset = 'password_reset';
  static const String emailVerification = 'email_verification';
  static const String authRedirect = 'auth_redirect';
}

/// إعدادات deep link configuration
/// Deep link configuration settings
class DeepLinkConfig {
  static const String appScheme = 'io.supabase.carnow';
  static const String authHost = 'login-callback';
  static const String passwordResetHost = 'password-reset';
  static const String emailVerificationHost = 'email-verification';
  
  /// قائمة المضيفين المسموحين
  static const List<String> allowedHosts = [
    authHost,
    passwordResetHost,
    emailVerificationHost,
  ];

  /// قائمة المعاملات المطلوبة للمصادقة
  static const List<String> requiredAuthParams = ['code'];
  
  /// قائمة المعاملات المقبولة للأخطاء
  static const List<String> errorParams = ['error', 'error_description'];
}

/// معالج الروابط العميقة للمصادقة
/// Deep link handler for authentication
class DeepLinkHandler {
  // Platform-specific method channel for future native implementations
  @pragma('vm:prefer-inline')
  static const MethodChannel _channel = MethodChannel('carnow/deep_links');
  
  /// Future method to initialize platform-specific deep link handling
  static Future<void> initializePlatformChannel() async {
    // This method will be implemented when platform-specific functionality is needed
    // Currently kept for future extensibility
    if (kDebugMode) {
      UnifiedLogger.debug('Platform channel ${_channel.name} available for future use', tag: 'DeepLink');
    }
  }

  /// تسجيل معلومات deep link للتشخيص
  /// Log deep link information for debugging
  static void logDeepLinkInfo(String url) {
    if (!kDebugMode) return;

    UnifiedLogger.debug('=== Deep Link Handler Debug Info ===', tag: 'DeepLink');
    UnifiedLogger.debug('Received URL: $url', tag: 'DeepLink');
    UnifiedLogger.debug('Expected scheme: ${SimpleOAuthConfig.getAppScheme()}', tag: 'DeepLink');
    UnifiedLogger.debug('Expected host: ${SimpleOAuthConfig.getAuthHost()}', tag: 'DeepLink');

    // تحليل الرابط
    final uri = Uri.tryParse(url);
    if (uri != null) {
      UnifiedLogger.debug('Parsed URI:', tag: 'DeepLink');
      UnifiedLogger.debug('  Scheme: ${uri.scheme}', tag: 'DeepLink');
      UnifiedLogger.debug('  Host: ${uri.host}', tag: 'DeepLink');
      UnifiedLogger.debug('  Path: ${uri.path}', tag: 'DeepLink');
      UnifiedLogger.debug('  Query: ${uri.query}', tag: 'DeepLink');
      UnifiedLogger.debug('  Fragment: ${uri.fragment}', tag: 'DeepLink');

      // التحقق من صحة الرابط
      final isValid = SimpleOAuthConfig.isValidOAuthCallback(url);
      UnifiedLogger.debug('Is valid OAuth callback: $isValid', tag: 'DeepLink');

      if (isValid) {
        final code = SimpleOAuthConfig.extractAuthCodeFromUrl(url);
        final error = SimpleOAuthConfig.extractErrorFromUrl(url);

        if (code != null) {
          final codePreview = code.length > 10
              ? '${code.substring(0, 10)}...'
              : code;
          UnifiedLogger.debug('Authorization code found: $codePreview', tag: 'DeepLink');
        }

        if (error != null) {
          UnifiedLogger.warning('OAuth error found: $error', tag: 'DeepLink');
        }
      }
    } else {
      UnifiedLogger.error('Failed to parse URL', tag: 'DeepLink');
    }

    UnifiedLogger.debug('=====================================', tag: 'DeepLink');
  }

  /// التحقق من أن deep link يطابق التوقعات (يدعم الآن fallback URLs)
  /// Verify that deep link matches expectations (now supports fallback URLs)
  static bool verifyDeepLink(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) {
      if (kDebugMode) {
        UnifiedLogger.error('❌ DeepLinkHandler: Invalid URL format: $url', tag: 'DeepLink');
      }
      return false;
    }

    final expectedScheme = SimpleOAuthConfig.getAppScheme();
    final expectedHost = SimpleOAuthConfig.getAuthHost();
    final expectedHostFallback = SimpleOAuthConfig.getAuthHostFallback();

    // التحقق من scheme
    if (uri.scheme != expectedScheme) {
      if (kDebugMode) {
        UnifiedLogger.error(
          '❌ DeepLinkHandler: Scheme mismatch. Expected: $expectedScheme, Got: ${uri.scheme}',
          tag: 'DeepLink',
        );
      }
      return false;
    }

    // التحقق من host (يدعم الآن primary و fallback)
    if (uri.host != expectedHost && uri.host != expectedHostFallback) {
      if (kDebugMode) {
        UnifiedLogger.error(
          '❌ DeepLinkHandler: Host mismatch. Expected: $expectedHost or $expectedHostFallback, Got: ${uri.host}',
          tag: 'DeepLink',
        );
      }
      return false;
    }

    if (kDebugMode) {
      final hostType = uri.host == expectedHost ? 'primary' : 'fallback';
      UnifiedLogger.info(
        '✅ DeepLinkHandler: Deep link verification passed ($hostType host)',
        tag: 'DeepLink',
      );
    }
    return true;
  }

  /// التحقق من صحة OAuth deep link
  /// Validate OAuth deep link
  bool isValidOAuthDeepLink(String url) {
    return verifyDeepLink(url) && SimpleOAuthConfig.isValidOAuthCallback(url);
  }

  /// معالجة OAuth callback مع إرجاع نتيجة مفصلة
  /// Handle OAuth callback with detailed result
  Future<OAuthCallbackResult> handleOAuthCallback(String url) async {
    logDeepLinkInfo(url);

    if (!verifyDeepLink(url)) {
      return OAuthCallbackResult.error('Invalid deep link scheme');
    }

    final uri = Uri.tryParse(url);
    if (uri == null) {
      return OAuthCallbackResult.error('Failed to parse URL');
    }

    // التحقق من وجود المعاملات المطلوبة أولاً
    final hasCode = uri.queryParameters.containsKey('code');
    final hasAccessToken = uri.queryParameters.containsKey('access_token');
    final hasError = uri.queryParameters.containsKey('error');

    if (!hasCode && !hasAccessToken && !hasError) {
      return OAuthCallbackResult.error('Missing required parameters');
    }

    if (!SimpleOAuthConfig.isValidOAuthCallback(url)) {
      final error = SimpleOAuthConfig.extractErrorFromUrl(url);
      return OAuthCallbackResult.error(error ?? 'Invalid OAuth callback');
    }

    if (hasError) {
      final error = SimpleOAuthConfig.extractErrorFromUrl(url);
      return OAuthCallbackResult.error(error ?? 'OAuth error occurred');
    }

    final authCode = SimpleOAuthConfig.extractAuthCodeFromUrl(url);
    final state = uri.queryParameters['state'];
    final accessToken = uri.queryParameters['access_token'];

    return OAuthCallbackResult.success(
      authCode: authCode,
      accessToken: accessToken,
      state: state,
    );
  }

  /// استخراج authorization code من URL
  /// Extract authorization code from URL
  String? extractAuthCode(String url) {
    return SimpleOAuthConfig.extractAuthCodeFromUrl(url);
  }

  /// استخراج access token من URL
  /// Extract access token from URL
  String? extractAccessToken(String url) {
    final uri = Uri.tryParse(url);
    return uri?.queryParameters['access_token'];
  }

  /// استخراج state parameter من URL
  /// Extract state parameter from URL
  String? extractState(String url) {
    final uri = Uri.tryParse(url);
    return uri?.queryParameters['state'];
  }

  /// استخراج error information من URL
  /// Extract error information from URL
  String? extractError(String url) {
    return SimpleOAuthConfig.extractErrorFromUrl(url);
  }

  /// معالجة OAuth callback (static method for backward compatibility)
  /// Handle OAuth callback (static method for backward compatibility)
  static Map<String, String?> handleOAuthCallbackStatic(String url) {
    logDeepLinkInfo(url);

    if (!verifyDeepLink(url)) {
      return {'success': 'false', 'error': 'Invalid deep link format'};
    }

    if (!SimpleOAuthConfig.isValidOAuthCallback(url)) {
      final error = SimpleOAuthConfig.extractErrorFromUrl(url);
      return {'success': 'false', 'error': error ?? 'Invalid OAuth callback'};
    }

    final code = SimpleOAuthConfig.extractAuthCodeFromUrl(url);
    if (code != null) {
      return {'success': 'true', 'code': code};
    }

    return {'success': 'false', 'error': 'No authorization code found'};
  }

  /// اختبار deep link configuration
  /// Test deep link configuration
  static void testDeepLinkConfiguration() {
    if (!kDebugMode) return;

    UnifiedLogger.info('=== Deep Link Configuration Test ===', tag: 'DeepLink');

    // اختبار الروابط المختلفة
    final testUrls = [
      'io.supabase.carnow://login-callback/?code=test123',
      'io.supabase.carnow://login-callback/?error=access_denied',
      'http://localhost:3000/?code=test123', // الرابط المشكل
      'https://lpxtghyvxuenyyisrrro.supabase.co/auth/v1/callback?code=test123',
    ];

    for (final url in testUrls) {
      UnifiedLogger.info('\nTesting URL: $url', tag: 'DeepLink');
      final result = handleOAuthCallbackStatic(url);
      UnifiedLogger.info('Result: $result', tag: 'DeepLink');
    }

    UnifiedLogger.info('====================================', tag: 'DeepLink');
  }

  /// إنشاء deep link للاختبار
  /// Generate deep link for testing
  static String generateTestDeepLink({
    String? code,
    String? error,
    String? state,
  }) {
    final baseUrl = SimpleOAuthConfig.buildAuthDeepLink();
    final params = <String, String>{};

    if (code != null) params['code'] = code;
    if (error != null) params['error'] = error;
    if (state != null) params['state'] = state;

    if (params.isEmpty) return baseUrl;

    final query = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return '$baseUrl?$query';
  }
}

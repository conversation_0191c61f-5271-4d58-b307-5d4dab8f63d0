# Google OAuth Setup Guide
# دليل إعداد Google OAuth

## Overview
## نظرة عامة

This guide explains how to set up Google OAuth for the CarNow Flutter application.
هذا الدليل يوضح كيفية إعداد Google OAuth لتطبيق CarNow Flutter.

## Prerequisites
## المتطلبات المسبقة

1. Google Cloud Console account
2. Flutter project with `google_sign_in: ^6.2.1` dependency
3. Android/iOS development environment

## Step 1: Google Cloud Console Setup
## الخطوة 1: إعداد Google Cloud Console

### 1.1 Create a New Project
### 1.1 إنشاء مشروع جديد

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API and Google Sign-In API

### 1.2 Configure OAuth Consent Screen
### 1.2 تكوين شاشة موافقة OAuth

1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type
3. Fill in the required information:
   - App name: "<PERSON><PERSON>ow"
   - User support email: Your email
   - Developer contact information: Your email
4. Add scopes: `email`, `profile`, `openid`
5. Add test users if needed

### 1.3 Create OAuth 2.0 Client IDs
### 1.3 إنشاء معرفات العميل OAuth 2.0

#### For Android:
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Android" as application type
4. Fill in the details:
   - Package name: `com.example.carnow` (or your actual package name)
   - SHA-1 certificate fingerprint: Get this from your keystore
5. Copy the generated Client ID

#### For iOS:
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "iOS" as application type
4. Fill in the details:
   - Bundle ID: `com.example.carnow` (or your actual bundle ID)
5. Copy the generated Client ID

#### For Web (if needed):
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application" as application type
4. Add authorized JavaScript origins and redirect URIs
5. Copy the generated Client ID

## Step 2: Update Configuration
## الخطوة 2: تحديث التكوين

### 2.1 Update GoogleOAuthConfig
### 2.1 تحديث GoogleOAuthConfig

Edit `lib/core/auth/google_oauth_config.dart`:

```dart
class GoogleOAuthConfig {
  // Replace with your actual client IDs
  static const String androidClientId = 'YOUR_ACTUAL_ANDROID_CLIENT_ID.apps.googleusercontent.com';
  static const String iosClientId = 'YOUR_ACTUAL_IOS_CLIENT_ID.apps.googleusercontent.com';
  static const String webClientId = 'YOUR_ACTUAL_WEB_CLIENT_ID.apps.googleusercontent.com';
}
```

### 2.2 Android Configuration
### 2.2 تكوين Android

#### Update `android/app/build.gradle`:
```gradle
android {
    defaultConfig {
        // ... other config
        manifestPlaceholders += [
            'appAuthRedirectScheme': 'com.example.carnow'
        ]
    }
}
```

#### Update `android/app/src/main/AndroidManifest.xml`:
```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <uses-permission android:name="android.permission.INTERNET"/>
    
    <application
        android:label="CarNow"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        
        <!-- Add this inside the application tag -->
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
            
    </application>
</manifest>
```

### 2.3 iOS Configuration
### 2.3 تكوين iOS

#### Update `ios/Runner/Info.plist`:
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>com.example.carnow</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>com.example.carnow</string>
        </array>
    </dict>
</array>
```

## Step 3: Testing
## الخطوة 3: الاختبار

### 3.1 Run the Test Suite
### 3.1 تشغيل مجموعة الاختبارات

```bash
# Run the Google OAuth test suite
flutter run --target=lib/core/auth/google_oauth_test_suite.dart
```

### 3.2 Test Individual Components
### 3.2 اختبار المكونات الفردية

```bash
# Test basic Google Sign-In
flutter run --target=test_google_signin.dart
```

### 3.3 Integration Test
### 3.3 اختبار التكامل

```bash
# Test with the main app
flutter run
```

## Step 4: Troubleshooting
## الخطوة 4: استكشاف الأخطاء

### Common Issues:
### المشاكل الشائعة:

1. **"Sign in failed" error**
   - Check that client IDs are correct
   - Verify SHA-1 fingerprint for Android
   - Ensure OAuth consent screen is configured

2. **"Network error"**
   - Check internet connection
   - Verify Google Play Services is installed (Android)
   - Check firewall settings

3. **"Invalid client" error**
   - Verify client ID matches the platform
   - Check that the project is enabled in Google Cloud Console

### Debug Steps:
### خطوات التصحيح:

1. Enable debug logging:
```dart
final googleSignIn = GoogleSignIn(
  scopes: ['email', 'profile'],
  clientId: GoogleOAuthConfig.getClientId(),
);
```

2. Check configuration status:
```dart
print(GoogleOAuthConfig.getStatus());
```

3. Test with the test suite:
```dart
// Use GoogleOAuthTestSuite widget
```

## Step 5: Production Deployment
## الخطوة 5: نشر الإنتاج

### 5.1 Update OAuth Consent Screen
### 5.1 تحديث شاشة موافقة OAuth

1. Go to Google Cloud Console
2. Update OAuth consent screen to "Production"
3. Add privacy policy and terms of service URLs
4. Submit for verification if needed

### 5.2 Security Considerations
### 5.2 اعتبارات الأمان

1. Store client IDs securely
2. Use different client IDs for development and production
3. Implement proper token validation on the backend
4. Add rate limiting for OAuth endpoints

## File Structure
## هيكل الملفات

```
lib/core/auth/
├── google_oauth_config.dart      # OAuth configuration
├── simple_auth_system.dart       # Main auth system
├── google_auth_test.dart         # Basic test widget
├── google_oauth_test_suite.dart  # Comprehensive test suite
└── GOOGLE_OAUTH_SETUP.md         # This guide
```

## API Reference
## مرجع API

### GoogleOAuthConfig
```dart
// Get client ID for current platform
String clientId = GoogleOAuthConfig.getClientId();

// Get scopes
List<String> scopes = GoogleOAuthConfig.getScopes();

// Check if configuration is valid
bool isValid = GoogleOAuthConfig.isValid();

// Get configuration status
String status = GoogleOAuthConfig.getStatus();
```

### SimpleAuthSystem
```dart
// Sign in with Google
final authSystem = ref.read(simpleAuthSystemProvider.notifier);
bool success = await authSystem.signInWithGoogle();

// Check auth state
final authState = ref.watch(simpleAuthSystemProvider);
bool isAuthenticated = authState.isAuthenticated;
```

## Support
## الدعم

If you encounter issues:

1. Check the test suite results
2. Verify configuration in Google Cloud Console
3. Review the troubleshooting section
4. Check Flutter and google_sign_in package versions
5. Ensure all dependencies are up to date

## Version Compatibility
## توافق الإصدارات

- Flutter: >=3.8.1
- google_sign_in: ^6.2.1
- flutter_riverpod: ^2.6.1
- dio: ^5.8.0+1 
// =============================================================================
// ENHANCED GOOGLE AUTHENTICATION SERVICE - CarNow Production
// =============================================================================
//
// خدمة مصادقة Google محسنة للإنتاج - تطبيق CarNow
//
// This service provides production-ready Google OAuth authentication
// with comprehensive error handling, session management, and backend integration.
//
// Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
//
// Key Features:
// - Real Google Sign-In integration (no mock mode)
// - Proper session persistence and restoration
// - Comprehensive error handling with Arabic messages
// - Backend JWT token exchange
// - Automatic token refresh and validation
// - Production-ready logging and monitoring
//
// Author: CarNow Development Team
// =============================================================================

import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'auth_interfaces.dart';

// =============================================================================
// ENHANCED GOOGLE AUTHENTICATION SERVICE
// =============================================================================

/// Enhanced Google OAuth authentication service for production use
///
/// This service implements real Google OAuth authentication with proper
/// backend integration following the Forever Plan Architecture.
class EnhancedGoogleAuthService implements IGoogleAuthService {
  // ---------------------------------------------------------------------------
  // Configuration and State
  // ---------------------------------------------------------------------------

  bool _isConfigured = false;
  List<String> _configuredScopes = ['email', 'profile', 'openid'];
  GoogleSignIn? _googleSignIn;

  // Backend configuration
  static const String _backendBaseUrl = 'https://backend-go-8klm.onrender.com';
  static const Duration _requestTimeout = Duration(seconds: 30);
  static const Duration _signInTimeout = Duration(seconds: 60);

  // Google OAuth Client ID (Web Client ID used for server authentication)
  static const String _webClientId =
      '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com';

  // Constructor - lazy initialization for better performance
  EnhancedGoogleAuthService() {
    // Initialize synchronously for better performance
    _initializeSync();
  }

  /// Initialize service synchronously for better performance
  void _initializeSync() {
    try {
      _configuredScopes = ['email', 'profile', 'openid'];

      // Create GoogleSignIn instance immediately
      _googleSignIn = GoogleSignIn(
        scopes: _configuredScopes,
        serverClientId:
            _webClientId, // Always use web client ID for server auth
        forceCodeForRefreshToken: true, // Required for backend token exchange
      );

      _isConfigured = true;

      developer.log(
        '⚡ Google Auth Service initialized synchronously',
        name: 'EnhancedGoogleAuthService',
      );
    } catch (error) {
      developer.log(
        '❌ Sync initialization failed: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
      );
      _isConfigured = false;
    }
  }

  // ---------------------------------------------------------------------------
  // Interface Implementation
  // ---------------------------------------------------------------------------

  @override
  bool get isConfigured => _isConfigured;

  @override
  List<String> get configuredScopes => List.from(_configuredScopes);

  @override
  Future<void> initialize({
    required String clientId,
    List<String> scopes = const ['email', 'profile', 'openid'],
  }) async {
    try {
      developer.log(
        '🚀 Initializing Enhanced Google Auth Service...',
        name: 'EnhancedGoogleAuthService',
      );

      _configuredScopes = List.from(scopes);

      // Create GoogleSignIn instance with proper configuration
      _googleSignIn = GoogleSignIn(
        scopes: scopes,
        serverClientId:
            _webClientId, // Always use web client ID for server auth
        forceCodeForRefreshToken: true, // Required for backend token exchange
      );

      developer.log(
        '✅ Google Sign-In configured successfully',
        name: 'EnhancedGoogleAuthService',
      );
      developer.log('📋 Scopes: $scopes', name: 'EnhancedGoogleAuthService');
      developer.log(
        '🔑 Server Client ID: ${_webClientId.substring(0, 20)}...',
        name: 'EnhancedGoogleAuthService',
      );

      _isConfigured = true;
    } catch (error, stackTrace) {
      developer.log(
        '❌ Failed to initialize Google Auth Service: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );
      _isConfigured = false;
      rethrow;
    }
  }

  @override
  Future<GoogleAuthResult> signIn() async {
    try {
      if (!_isConfigured || _googleSignIn == null) {
        return GoogleAuthResult.failure(
          error: 'خدمة Google غير مكونة - يرجى إعادة تشغيل التطبيق',
          errorCode: 'service_not_configured',
        );
      }

      developer.log(
        '🔐 Starting Google Sign-In process...',
        name: 'EnhancedGoogleAuthService',
      );

      // Only clear session if user is currently signed in (performance optimization)
      if (_googleSignIn!.currentUser != null) {
        try {
          await _googleSignIn!.signOut();
          developer.log(
            '🧹 Cleared previous Google session',
            name: 'EnhancedGoogleAuthService',
          );
        } catch (e) {
          developer.log(
            '⚠️ Failed to clear session: $e',
            name: 'EnhancedGoogleAuthService',
          );
        }
      }

      // Perform Google Sign-In with timeout
      GoogleSignInAccount? googleUser;
      try {
        googleUser = await _googleSignIn!.signIn().timeout(_signInTimeout);
      } on TimeoutException {
        return GoogleAuthResult.failure(
          error: 'انتهت مهلة تسجيل الدخول - يرجى المحاولة مرة أخرى',
          errorCode: 'sign_in_timeout',
        );
      } on PlatformException catch (e) {
        developer.log(
          '⚠️ Google Sign-In platform exception: ${e.code} - ${e.message}',
          name: 'EnhancedGoogleAuthService',
          error: e,
        );

        if (e.code == 'sign_in_cancelled') {
          return GoogleAuthResult.cancelled(
            reason: 'تم إلغاء تسجيل الدخول بواسطة المستخدم',
          );
        } else if (e.code == 'network_error') {
          return GoogleAuthResult.failure(
            error: 'خطأ في الاتصال بالشبكة - يرجى التحقق من اتصالك بالإنترنت',
            errorCode: 'network_error',
          );
        } else if (e.code == 'sign_in_failed') {
          return GoogleAuthResult.failure(
            error: 'فشل تسجيل الدخول بـ Google - يرجى المحاولة مرة أخرى',
            errorCode: 'sign_in_failed',
          );
        }

        return GoogleAuthResult.failure(
          error: _getArabicErrorMessage(e.code),
          errorCode: e.code,
        );
      }

      // Check if user cancelled sign-in
      if (googleUser == null) {
        developer.log(
          '❌ Google Sign-In cancelled by user',
          name: 'EnhancedGoogleAuthService',
        );
        return GoogleAuthResult.cancelled(reason: 'تم إلغاء تسجيل الدخول');
      }

      developer.log(
        '✅ Google Sign-In successful for: ${googleUser.email}',
        name: 'EnhancedGoogleAuthService',
      );

      // Get authentication tokens
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.idToken == null) {
        developer.log(
          '❌ Failed to get Google ID token',
          name: 'EnhancedGoogleAuthService',
        );
        return GoogleAuthResult.failure(
          error: 'فشل في الحصول على رمز التعريف من Google',
          errorCode: 'missing_id_token',
        );
      }

      developer.log(
        '🎫 Google ID token obtained successfully',
        name: 'EnhancedGoogleAuthService',
      );

      // Exchange Google ID token with backend
      final backendResponse = await _exchangeTokenWithBackend(
        googleAuth.idToken!,
      );

      if (backendResponse == null) {
        return GoogleAuthResult.failure(
          error: 'فشل في التحقق من بيانات Google مع الخادم',
          errorCode: 'backend_exchange_failed',
        );
      }

      // Create comprehensive user info
      final googleUserInfo = GoogleUserInfo(
        id: googleUser.id,
        email: googleUser.email,
        displayName: googleUser.displayName ?? googleUser.email,
        photoUrl: googleUser.photoUrl,
        firstName: _extractFirstName(googleUser.displayName),
        lastName: _extractLastName(googleUser.displayName),
        name: googleUser.displayName,
        givenName: _extractFirstName(googleUser.displayName),
        familyName: _extractLastName(googleUser.displayName),
        locale: 'ar', // Default to Arabic
      );

      developer.log(
        '🎉 Google authentication completed successfully',
        name: 'EnhancedGoogleAuthService',
      );

      return GoogleAuthResult.success(
        idToken: googleAuth.idToken!,
        accessToken: googleAuth.accessToken ?? '',
        userInfo: googleUserInfo,
        expiryDate: _calculateTokenExpiry(backendResponse),
      );
    } catch (error, stackTrace) {
      developer.log(
        '💥 Google Sign-In failed with unexpected error: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );

      return GoogleAuthResult.failure(
        error: 'حدث خطأ غير متوقع أثناء تسجيل الدخول بـ Google',
        errorCode: 'unexpected_error',
      );
    }
  }

  @override
  Future<GoogleAuthResult?> signInSilently() async {
    try {
      if (!_isConfigured || _googleSignIn == null) {
        developer.log(
          '⚠️ Google Auth Service not configured for silent sign-in',
          name: 'EnhancedGoogleAuthService',
        );
        return null;
      }

      developer.log(
        '🤫 Attempting silent Google Sign-In...',
        name: 'EnhancedGoogleAuthService',
      );

      final googleUser = await _googleSignIn!.signInSilently();
      if (googleUser == null) {
        developer.log(
          '❌ Silent sign-in failed - no cached user',
          name: 'EnhancedGoogleAuthService',
        );
        return null;
      }

      final googleAuth = await googleUser.authentication;
      if (googleAuth.idToken == null) {
        developer.log(
          '❌ Silent sign-in failed - no ID token',
          name: 'EnhancedGoogleAuthService',
        );
        return null;
      }

      // Exchange token with backend
      final backendResponse = await _exchangeTokenWithBackend(
        googleAuth.idToken!,
      );
      if (backendResponse == null) {
        developer.log(
          '❌ Silent sign-in failed - backend exchange failed',
          name: 'EnhancedGoogleAuthService',
        );
        return null;
      }

      final googleUserInfo = GoogleUserInfo(
        id: googleUser.id,
        email: googleUser.email,
        displayName: googleUser.displayName ?? googleUser.email,
        photoUrl: googleUser.photoUrl,
        firstName: _extractFirstName(googleUser.displayName),
        lastName: _extractLastName(googleUser.displayName),
        name: googleUser.displayName,
        givenName: _extractFirstName(googleUser.displayName),
        familyName: _extractLastName(googleUser.displayName),
        locale: 'ar',
      );

      developer.log(
        '✅ Silent Google Sign-In successful',
        name: 'EnhancedGoogleAuthService',
      );

      return GoogleAuthResult.success(
        idToken: googleAuth.idToken!,
        accessToken: googleAuth.accessToken ?? '',
        userInfo: googleUserInfo,
        expiryDate: _calculateTokenExpiry(backendResponse),
      );
    } catch (error, stackTrace) {
      developer.log(
        '❌ Silent Google Sign-In failed: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  @override
  Future<void> signOut() async {
    try {
      if (!_isConfigured || _googleSignIn == null) {
        developer.log(
          '⚠️ Google Auth Service not configured for sign out',
          name: 'EnhancedGoogleAuthService',
        );
        return;
      }

      developer.log(
        '🚪 Signing out from Google...',
        name: 'EnhancedGoogleAuthService',
      );

      await _googleSignIn!.signOut();

      developer.log(
        '✅ Google Sign-Out successful',
        name: 'EnhancedGoogleAuthService',
      );
    } catch (error, stackTrace) {
      developer.log(
        '❌ Google Sign-Out failed: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - sign out should always succeed from UI perspective
    }
  }

  @override
  Future<void> disconnect() async {
    try {
      if (!_isConfigured || _googleSignIn == null) {
        return;
      }

      developer.log(
        '🔌 Disconnecting Google account...',
        name: 'EnhancedGoogleAuthService',
      );

      await _googleSignIn!.disconnect();

      developer.log(
        '✅ Google account disconnected successfully',
        name: 'EnhancedGoogleAuthService',
      );
    } catch (error, stackTrace) {
      developer.log(
        '❌ Failed to disconnect Google account: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - disconnect should always succeed from UI perspective
    }
  }

  @override
  Future<bool> isSignedIn() async {
    try {
      if (!_isConfigured || _googleSignIn == null) {
        return false;
      }

      return _googleSignIn!.currentUser != null;
    } catch (error) {
      developer.log(
        '❌ Error checking Google sign-in status: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
      );
      return false;
    }
  }

  @override
  Future<GoogleUserInfo?> getCurrentUser() async {
    try {
      if (!_isConfigured || _googleSignIn == null) {
        return null;
      }

      final currentUser = _googleSignIn!.currentUser;
      if (currentUser == null) return null;

      return GoogleUserInfo(
        id: currentUser.id,
        email: currentUser.email,
        displayName: currentUser.displayName ?? currentUser.email,
        photoUrl: currentUser.photoUrl,
        firstName: _extractFirstName(currentUser.displayName),
        lastName: _extractLastName(currentUser.displayName),
        name: currentUser.displayName,
        givenName: _extractFirstName(currentUser.displayName),
        familyName: _extractLastName(currentUser.displayName),
        locale: 'ar',
      );
    } catch (error) {
      developer.log(
        '❌ Error getting current Google user: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
      );
      return null;
    }
  }

  @override
  Future<String?> getAccessToken() async {
    try {
      if (!_isConfigured || _googleSignIn == null) {
        return null;
      }

      final currentUser = _googleSignIn!.currentUser;
      if (currentUser == null) return null;

      final auth = await currentUser.authentication;
      return auth.accessToken;
    } catch (error) {
      developer.log(
        '❌ Error getting Google access token: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
      );
      return null;
    }
  }

  @override
  Future<String?> getIdToken() async {
    try {
      if (!_isConfigured || _googleSignIn == null) {
        return null;
      }

      final currentUser = _googleSignIn!.currentUser;
      if (currentUser == null) return null;

      final auth = await currentUser.authentication;
      return auth.idToken;
    } catch (error) {
      developer.log(
        '❌ Error getting Google ID token: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
      );
      return null;
    }
  }

  // ---------------------------------------------------------------------------
  // Backend Integration
  // ---------------------------------------------------------------------------

  /// Exchange Google ID token with Go backend for JWT tokens
  Future<Map<String, dynamic>?> _exchangeTokenWithBackend(
    String idToken,
  ) async {
    try {
      developer.log(
        '🔄 Exchanging Google ID token with backend...',
        name: 'EnhancedGoogleAuthService',
      );

      // Prepare device info synchronously for better performance
      final deviceInfo = _getDeviceInfoSync();

      final response = await http
          .post(
            Uri.parse('$_backendBaseUrl/api/v1/auth/google'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'User-Agent': 'CarNow-Flutter/1.0.0',
            },
            body: jsonEncode({'id_token': idToken, 'device_info': deviceInfo}),
          )
          .timeout(_requestTimeout);

      developer.log(
        '📡 Backend response status: ${response.statusCode}',
        name: 'EnhancedGoogleAuthService',
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;

        if (responseData['success'] == true) {
          developer.log(
            '✅ Backend token exchange successful',
            name: 'EnhancedGoogleAuthService',
          );
          return responseData;
        } else {
          final errorMessage =
              responseData['message'] ?? 'Unknown backend error';
          developer.log(
            '❌ Backend returned error: $errorMessage',
            name: 'EnhancedGoogleAuthService',
          );
          return null;
        }
      } else {
        developer.log(
          '❌ Backend request failed with status ${response.statusCode}',
          name: 'EnhancedGoogleAuthService',
        );
        developer.log(
          '📄 Response body: ${response.body}',
          name: 'EnhancedGoogleAuthService',
        );
        return null;
      }
    } on TimeoutException {
      developer.log(
        '⏰ Backend request timed out',
        name: 'EnhancedGoogleAuthService',
      );
      return null;
    } catch (error, stackTrace) {
      developer.log(
        '💥 Backend token exchange failed: $error',
        name: 'EnhancedGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  // ---------------------------------------------------------------------------
  // Utility Methods
  // ---------------------------------------------------------------------------

  /// Extract first name from display name
  String? _extractFirstName(String? displayName) {
    if (displayName == null || displayName.isEmpty) return null;
    final parts = displayName.trim().split(' ');
    return parts.isNotEmpty ? parts.first : null;
  }

  /// Extract last name from display name
  String? _extractLastName(String? displayName) {
    if (displayName == null || displayName.isEmpty) return null;
    final parts = displayName.trim().split(' ');
    return parts.length > 1 ? parts.skip(1).join(' ') : null;
  }

  /// Calculate token expiry from backend response
  DateTime? _calculateTokenExpiry(Map<String, dynamic> backendResponse) {
    try {
      final expiresIn = backendResponse['expires_in'] as int?;
      if (expiresIn != null) {
        return DateTime.now().add(Duration(seconds: expiresIn));
      }

      final expiresAt = backendResponse['expires_at'] as int?;
      if (expiresAt != null) {
        return DateTime.fromMillisecondsSinceEpoch(expiresAt * 1000);
      }

      // Default to 1 hour if no expiry info
      return DateTime.now().add(const Duration(hours: 1));
    } catch (e) {
      developer.log(
        '⚠️ Failed to calculate token expiry: $e',
        name: 'EnhancedGoogleAuthService',
      );
      return DateTime.now().add(const Duration(hours: 1));
    }
  }

  /// Get Arabic error message for error codes
  String _getArabicErrorMessage(String errorCode) {
    switch (errorCode.toLowerCase()) {
      case 'sign_in_cancelled':
        return 'تم إلغاء تسجيل الدخول';
      case 'sign_in_failed':
        return 'فشل تسجيل الدخول بـ Google';
      case 'network_error':
        return 'خطأ في الاتصال بالشبكة';
      case 'sign_in_required':
        return 'يجب تسجيل الدخول أولاً';
      case 'account_exists_with_different_credential':
        return 'يوجد حساب بنفس البريد الإلكتروني بطريقة مختلفة';
      case 'invalid_credential':
        return 'بيانات الاعتماد غير صحيحة';
      case 'operation_not_allowed':
        return 'تسجيل الدخول بـ Google غير مفعل';
      case 'user_disabled':
        return 'تم تعطيل هذا الحساب';
      case 'user_not_found':
        return 'لم يتم العثور على المستخدم';
      case 'service_not_configured':
        return 'خدمة Google غير مكونة';
      case 'missing_id_token':
        return 'فشل في الحصول على رمز التعريف';
      case 'backend_exchange_failed':
        return 'فشل في التحقق مع الخادم';
      case 'sign_in_timeout':
        return 'انتهت مهلة تسجيل الدخول';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }

  /// Get device info synchronously for better performance
  String _getDeviceInfoSync() {
    try {
      if (kIsWeb) {
        return 'Web Browser';
      } else if (Platform.isAndroid) {
        return 'Android Device';
      } else if (Platform.isIOS) {
        return 'iOS Device';
      } else {
        return 'Unknown Device';
      }
    } catch (e) {
      return 'Unknown Device';
    }
  }
}

// =============================================================================
// RIVERPOD PROVIDERS
// =============================================================================

/// Provider for Enhanced Google Auth Service
final enhancedGoogleAuthServiceProvider = Provider<IGoogleAuthService>((ref) {
  return EnhancedGoogleAuthService();
});

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'token_validation_service.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$CircuitBreakerConfig implements DiagnosticableTreeMixin {

/// Maximum number of failures before opening circuit
 int get failureThreshold;/// Timeout duration when circuit is open
 Duration get timeout;/// Maximum retry attempts in half-open state
 int get maxRetryAttempts;/// Reset timeout after successful operation
 Duration get resetTimeout;
/// Create a copy of CircuitBreakerConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CircuitBreakerConfigCopyWith<CircuitBreakerConfig> get copyWith => _$CircuitBreakerConfigCopyWithImpl<CircuitBreakerConfig>(this as CircuitBreakerConfig, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'CircuitBreakerConfig'))
    ..add(DiagnosticsProperty('failureThreshold', failureThreshold))..add(DiagnosticsProperty('timeout', timeout))..add(DiagnosticsProperty('maxRetryAttempts', maxRetryAttempts))..add(DiagnosticsProperty('resetTimeout', resetTimeout));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CircuitBreakerConfig&&(identical(other.failureThreshold, failureThreshold) || other.failureThreshold == failureThreshold)&&(identical(other.timeout, timeout) || other.timeout == timeout)&&(identical(other.maxRetryAttempts, maxRetryAttempts) || other.maxRetryAttempts == maxRetryAttempts)&&(identical(other.resetTimeout, resetTimeout) || other.resetTimeout == resetTimeout));
}


@override
int get hashCode => Object.hash(runtimeType,failureThreshold,timeout,maxRetryAttempts,resetTimeout);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'CircuitBreakerConfig(failureThreshold: $failureThreshold, timeout: $timeout, maxRetryAttempts: $maxRetryAttempts, resetTimeout: $resetTimeout)';
}


}

/// @nodoc
abstract mixin class $CircuitBreakerConfigCopyWith<$Res>  {
  factory $CircuitBreakerConfigCopyWith(CircuitBreakerConfig value, $Res Function(CircuitBreakerConfig) _then) = _$CircuitBreakerConfigCopyWithImpl;
@useResult
$Res call({
 int failureThreshold, Duration timeout, int maxRetryAttempts, Duration resetTimeout
});




}
/// @nodoc
class _$CircuitBreakerConfigCopyWithImpl<$Res>
    implements $CircuitBreakerConfigCopyWith<$Res> {
  _$CircuitBreakerConfigCopyWithImpl(this._self, this._then);

  final CircuitBreakerConfig _self;
  final $Res Function(CircuitBreakerConfig) _then;

/// Create a copy of CircuitBreakerConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? failureThreshold = null,Object? timeout = null,Object? maxRetryAttempts = null,Object? resetTimeout = null,}) {
  return _then(_self.copyWith(
failureThreshold: null == failureThreshold ? _self.failureThreshold : failureThreshold // ignore: cast_nullable_to_non_nullable
as int,timeout: null == timeout ? _self.timeout : timeout // ignore: cast_nullable_to_non_nullable
as Duration,maxRetryAttempts: null == maxRetryAttempts ? _self.maxRetryAttempts : maxRetryAttempts // ignore: cast_nullable_to_non_nullable
as int,resetTimeout: null == resetTimeout ? _self.resetTimeout : resetTimeout // ignore: cast_nullable_to_non_nullable
as Duration,
  ));
}

}


/// Adds pattern-matching-related methods to [CircuitBreakerConfig].
extension CircuitBreakerConfigPatterns on CircuitBreakerConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CircuitBreakerConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CircuitBreakerConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CircuitBreakerConfig value)  $default,){
final _that = this;
switch (_that) {
case _CircuitBreakerConfig():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CircuitBreakerConfig value)?  $default,){
final _that = this;
switch (_that) {
case _CircuitBreakerConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int failureThreshold,  Duration timeout,  int maxRetryAttempts,  Duration resetTimeout)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CircuitBreakerConfig() when $default != null:
return $default(_that.failureThreshold,_that.timeout,_that.maxRetryAttempts,_that.resetTimeout);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int failureThreshold,  Duration timeout,  int maxRetryAttempts,  Duration resetTimeout)  $default,) {final _that = this;
switch (_that) {
case _CircuitBreakerConfig():
return $default(_that.failureThreshold,_that.timeout,_that.maxRetryAttempts,_that.resetTimeout);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int failureThreshold,  Duration timeout,  int maxRetryAttempts,  Duration resetTimeout)?  $default,) {final _that = this;
switch (_that) {
case _CircuitBreakerConfig() when $default != null:
return $default(_that.failureThreshold,_that.timeout,_that.maxRetryAttempts,_that.resetTimeout);case _:
  return null;

}
}

}

/// @nodoc


class _CircuitBreakerConfig with DiagnosticableTreeMixin implements CircuitBreakerConfig {
  const _CircuitBreakerConfig({this.failureThreshold = 3, this.timeout = const Duration(seconds: 30), this.maxRetryAttempts = 1, this.resetTimeout = const Duration(seconds: 60)});
  

/// Maximum number of failures before opening circuit
@override@JsonKey() final  int failureThreshold;
/// Timeout duration when circuit is open
@override@JsonKey() final  Duration timeout;
/// Maximum retry attempts in half-open state
@override@JsonKey() final  int maxRetryAttempts;
/// Reset timeout after successful operation
@override@JsonKey() final  Duration resetTimeout;

/// Create a copy of CircuitBreakerConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CircuitBreakerConfigCopyWith<_CircuitBreakerConfig> get copyWith => __$CircuitBreakerConfigCopyWithImpl<_CircuitBreakerConfig>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'CircuitBreakerConfig'))
    ..add(DiagnosticsProperty('failureThreshold', failureThreshold))..add(DiagnosticsProperty('timeout', timeout))..add(DiagnosticsProperty('maxRetryAttempts', maxRetryAttempts))..add(DiagnosticsProperty('resetTimeout', resetTimeout));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CircuitBreakerConfig&&(identical(other.failureThreshold, failureThreshold) || other.failureThreshold == failureThreshold)&&(identical(other.timeout, timeout) || other.timeout == timeout)&&(identical(other.maxRetryAttempts, maxRetryAttempts) || other.maxRetryAttempts == maxRetryAttempts)&&(identical(other.resetTimeout, resetTimeout) || other.resetTimeout == resetTimeout));
}


@override
int get hashCode => Object.hash(runtimeType,failureThreshold,timeout,maxRetryAttempts,resetTimeout);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'CircuitBreakerConfig(failureThreshold: $failureThreshold, timeout: $timeout, maxRetryAttempts: $maxRetryAttempts, resetTimeout: $resetTimeout)';
}


}

/// @nodoc
abstract mixin class _$CircuitBreakerConfigCopyWith<$Res> implements $CircuitBreakerConfigCopyWith<$Res> {
  factory _$CircuitBreakerConfigCopyWith(_CircuitBreakerConfig value, $Res Function(_CircuitBreakerConfig) _then) = __$CircuitBreakerConfigCopyWithImpl;
@override @useResult
$Res call({
 int failureThreshold, Duration timeout, int maxRetryAttempts, Duration resetTimeout
});




}
/// @nodoc
class __$CircuitBreakerConfigCopyWithImpl<$Res>
    implements _$CircuitBreakerConfigCopyWith<$Res> {
  __$CircuitBreakerConfigCopyWithImpl(this._self, this._then);

  final _CircuitBreakerConfig _self;
  final $Res Function(_CircuitBreakerConfig) _then;

/// Create a copy of CircuitBreakerConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? failureThreshold = null,Object? timeout = null,Object? maxRetryAttempts = null,Object? resetTimeout = null,}) {
  return _then(_CircuitBreakerConfig(
failureThreshold: null == failureThreshold ? _self.failureThreshold : failureThreshold // ignore: cast_nullable_to_non_nullable
as int,timeout: null == timeout ? _self.timeout : timeout // ignore: cast_nullable_to_non_nullable
as Duration,maxRetryAttempts: null == maxRetryAttempts ? _self.maxRetryAttempts : maxRetryAttempts // ignore: cast_nullable_to_non_nullable
as int,resetTimeout: null == resetTimeout ? _self.resetTimeout : resetTimeout // ignore: cast_nullable_to_non_nullable
as Duration,
  ));
}


}

/// @nodoc
mixin _$TokenValidationResult implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'TokenValidationResult'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TokenValidationResult);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'TokenValidationResult()';
}


}

/// @nodoc
class $TokenValidationResultCopyWith<$Res>  {
$TokenValidationResultCopyWith(TokenValidationResult _, $Res Function(TokenValidationResult) __);
}


/// Adds pattern-matching-related methods to [TokenValidationResult].
extension TokenValidationResultPatterns on TokenValidationResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( TokenValidationSuccess value)?  success,TResult Function( TokenValidationFailure value)?  failure,TResult Function( TokenValidationCircuitOpen value)?  circuitOpen,required TResult orElse(),}){
final _that = this;
switch (_that) {
case TokenValidationSuccess() when success != null:
return success(_that);case TokenValidationFailure() when failure != null:
return failure(_that);case TokenValidationCircuitOpen() when circuitOpen != null:
return circuitOpen(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( TokenValidationSuccess value)  success,required TResult Function( TokenValidationFailure value)  failure,required TResult Function( TokenValidationCircuitOpen value)  circuitOpen,}){
final _that = this;
switch (_that) {
case TokenValidationSuccess():
return success(_that);case TokenValidationFailure():
return failure(_that);case TokenValidationCircuitOpen():
return circuitOpen(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( TokenValidationSuccess value)?  success,TResult? Function( TokenValidationFailure value)?  failure,TResult? Function( TokenValidationCircuitOpen value)?  circuitOpen,}){
final _that = this;
switch (_that) {
case TokenValidationSuccess() when success != null:
return success(_that);case TokenValidationFailure() when failure != null:
return failure(_that);case TokenValidationCircuitOpen() when circuitOpen != null:
return circuitOpen(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( bool isValid,  String token,  DateTime? expiryDate,  Map<String, dynamic>? metadata)?  success,TResult Function( String error,  AuthErrorType errorType,  bool isRecoverable,  int? retryAfterSeconds,  Map<String, dynamic>? details)?  failure,TResult Function( String reason,  Duration retryAfter,  int failureCount)?  circuitOpen,required TResult orElse(),}) {final _that = this;
switch (_that) {
case TokenValidationSuccess() when success != null:
return success(_that.isValid,_that.token,_that.expiryDate,_that.metadata);case TokenValidationFailure() when failure != null:
return failure(_that.error,_that.errorType,_that.isRecoverable,_that.retryAfterSeconds,_that.details);case TokenValidationCircuitOpen() when circuitOpen != null:
return circuitOpen(_that.reason,_that.retryAfter,_that.failureCount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( bool isValid,  String token,  DateTime? expiryDate,  Map<String, dynamic>? metadata)  success,required TResult Function( String error,  AuthErrorType errorType,  bool isRecoverable,  int? retryAfterSeconds,  Map<String, dynamic>? details)  failure,required TResult Function( String reason,  Duration retryAfter,  int failureCount)  circuitOpen,}) {final _that = this;
switch (_that) {
case TokenValidationSuccess():
return success(_that.isValid,_that.token,_that.expiryDate,_that.metadata);case TokenValidationFailure():
return failure(_that.error,_that.errorType,_that.isRecoverable,_that.retryAfterSeconds,_that.details);case TokenValidationCircuitOpen():
return circuitOpen(_that.reason,_that.retryAfter,_that.failureCount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( bool isValid,  String token,  DateTime? expiryDate,  Map<String, dynamic>? metadata)?  success,TResult? Function( String error,  AuthErrorType errorType,  bool isRecoverable,  int? retryAfterSeconds,  Map<String, dynamic>? details)?  failure,TResult? Function( String reason,  Duration retryAfter,  int failureCount)?  circuitOpen,}) {final _that = this;
switch (_that) {
case TokenValidationSuccess() when success != null:
return success(_that.isValid,_that.token,_that.expiryDate,_that.metadata);case TokenValidationFailure() when failure != null:
return failure(_that.error,_that.errorType,_that.isRecoverable,_that.retryAfterSeconds,_that.details);case TokenValidationCircuitOpen() when circuitOpen != null:
return circuitOpen(_that.reason,_that.retryAfter,_that.failureCount);case _:
  return null;

}
}

}

/// @nodoc


class TokenValidationSuccess with DiagnosticableTreeMixin implements TokenValidationResult {
  const TokenValidationSuccess({required this.isValid, required this.token, this.expiryDate, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  

 final  bool isValid;
 final  String token;
 final  DateTime? expiryDate;
 final  Map<String, dynamic>? _metadata;
 Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of TokenValidationResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TokenValidationSuccessCopyWith<TokenValidationSuccess> get copyWith => _$TokenValidationSuccessCopyWithImpl<TokenValidationSuccess>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'TokenValidationResult.success'))
    ..add(DiagnosticsProperty('isValid', isValid))..add(DiagnosticsProperty('token', token))..add(DiagnosticsProperty('expiryDate', expiryDate))..add(DiagnosticsProperty('metadata', metadata));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TokenValidationSuccess&&(identical(other.isValid, isValid) || other.isValid == isValid)&&(identical(other.token, token) || other.token == token)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}


@override
int get hashCode => Object.hash(runtimeType,isValid,token,expiryDate,const DeepCollectionEquality().hash(_metadata));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'TokenValidationResult.success(isValid: $isValid, token: $token, expiryDate: $expiryDate, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $TokenValidationSuccessCopyWith<$Res> implements $TokenValidationResultCopyWith<$Res> {
  factory $TokenValidationSuccessCopyWith(TokenValidationSuccess value, $Res Function(TokenValidationSuccess) _then) = _$TokenValidationSuccessCopyWithImpl;
@useResult
$Res call({
 bool isValid, String token, DateTime? expiryDate, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$TokenValidationSuccessCopyWithImpl<$Res>
    implements $TokenValidationSuccessCopyWith<$Res> {
  _$TokenValidationSuccessCopyWithImpl(this._self, this._then);

  final TokenValidationSuccess _self;
  final $Res Function(TokenValidationSuccess) _then;

/// Create a copy of TokenValidationResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? isValid = null,Object? token = null,Object? expiryDate = freezed,Object? metadata = freezed,}) {
  return _then(TokenValidationSuccess(
isValid: null == isValid ? _self.isValid : isValid // ignore: cast_nullable_to_non_nullable
as bool,token: null == token ? _self.token : token // ignore: cast_nullable_to_non_nullable
as String,expiryDate: freezed == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class TokenValidationFailure with DiagnosticableTreeMixin implements TokenValidationResult {
  const TokenValidationFailure({required this.error, required this.errorType, this.isRecoverable = false, this.retryAfterSeconds, final  Map<String, dynamic>? details}): _details = details;
  

 final  String error;
 final  AuthErrorType errorType;
@JsonKey() final  bool isRecoverable;
 final  int? retryAfterSeconds;
 final  Map<String, dynamic>? _details;
 Map<String, dynamic>? get details {
  final value = _details;
  if (value == null) return null;
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of TokenValidationResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TokenValidationFailureCopyWith<TokenValidationFailure> get copyWith => _$TokenValidationFailureCopyWithImpl<TokenValidationFailure>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'TokenValidationResult.failure'))
    ..add(DiagnosticsProperty('error', error))..add(DiagnosticsProperty('errorType', errorType))..add(DiagnosticsProperty('isRecoverable', isRecoverable))..add(DiagnosticsProperty('retryAfterSeconds', retryAfterSeconds))..add(DiagnosticsProperty('details', details));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TokenValidationFailure&&(identical(other.error, error) || other.error == error)&&(identical(other.errorType, errorType) || other.errorType == errorType)&&(identical(other.isRecoverable, isRecoverable) || other.isRecoverable == isRecoverable)&&(identical(other.retryAfterSeconds, retryAfterSeconds) || other.retryAfterSeconds == retryAfterSeconds)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,error,errorType,isRecoverable,retryAfterSeconds,const DeepCollectionEquality().hash(_details));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'TokenValidationResult.failure(error: $error, errorType: $errorType, isRecoverable: $isRecoverable, retryAfterSeconds: $retryAfterSeconds, details: $details)';
}


}

/// @nodoc
abstract mixin class $TokenValidationFailureCopyWith<$Res> implements $TokenValidationResultCopyWith<$Res> {
  factory $TokenValidationFailureCopyWith(TokenValidationFailure value, $Res Function(TokenValidationFailure) _then) = _$TokenValidationFailureCopyWithImpl;
@useResult
$Res call({
 String error, AuthErrorType errorType, bool isRecoverable, int? retryAfterSeconds, Map<String, dynamic>? details
});




}
/// @nodoc
class _$TokenValidationFailureCopyWithImpl<$Res>
    implements $TokenValidationFailureCopyWith<$Res> {
  _$TokenValidationFailureCopyWithImpl(this._self, this._then);

  final TokenValidationFailure _self;
  final $Res Function(TokenValidationFailure) _then;

/// Create a copy of TokenValidationResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? error = null,Object? errorType = null,Object? isRecoverable = null,Object? retryAfterSeconds = freezed,Object? details = freezed,}) {
  return _then(TokenValidationFailure(
error: null == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String,errorType: null == errorType ? _self.errorType : errorType // ignore: cast_nullable_to_non_nullable
as AuthErrorType,isRecoverable: null == isRecoverable ? _self.isRecoverable : isRecoverable // ignore: cast_nullable_to_non_nullable
as bool,retryAfterSeconds: freezed == retryAfterSeconds ? _self.retryAfterSeconds : retryAfterSeconds // ignore: cast_nullable_to_non_nullable
as int?,details: freezed == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class TokenValidationCircuitOpen with DiagnosticableTreeMixin implements TokenValidationResult {
  const TokenValidationCircuitOpen({required this.reason, required this.retryAfter, required this.failureCount});
  

 final  String reason;
 final  Duration retryAfter;
 final  int failureCount;

/// Create a copy of TokenValidationResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TokenValidationCircuitOpenCopyWith<TokenValidationCircuitOpen> get copyWith => _$TokenValidationCircuitOpenCopyWithImpl<TokenValidationCircuitOpen>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'TokenValidationResult.circuitOpen'))
    ..add(DiagnosticsProperty('reason', reason))..add(DiagnosticsProperty('retryAfter', retryAfter))..add(DiagnosticsProperty('failureCount', failureCount));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TokenValidationCircuitOpen&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.retryAfter, retryAfter) || other.retryAfter == retryAfter)&&(identical(other.failureCount, failureCount) || other.failureCount == failureCount));
}


@override
int get hashCode => Object.hash(runtimeType,reason,retryAfter,failureCount);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'TokenValidationResult.circuitOpen(reason: $reason, retryAfter: $retryAfter, failureCount: $failureCount)';
}


}

/// @nodoc
abstract mixin class $TokenValidationCircuitOpenCopyWith<$Res> implements $TokenValidationResultCopyWith<$Res> {
  factory $TokenValidationCircuitOpenCopyWith(TokenValidationCircuitOpen value, $Res Function(TokenValidationCircuitOpen) _then) = _$TokenValidationCircuitOpenCopyWithImpl;
@useResult
$Res call({
 String reason, Duration retryAfter, int failureCount
});




}
/// @nodoc
class _$TokenValidationCircuitOpenCopyWithImpl<$Res>
    implements $TokenValidationCircuitOpenCopyWith<$Res> {
  _$TokenValidationCircuitOpenCopyWithImpl(this._self, this._then);

  final TokenValidationCircuitOpen _self;
  final $Res Function(TokenValidationCircuitOpen) _then;

/// Create a copy of TokenValidationResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? reason = null,Object? retryAfter = null,Object? failureCount = null,}) {
  return _then(TokenValidationCircuitOpen(
reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,retryAfter: null == retryAfter ? _self.retryAfter : retryAfter // ignore: cast_nullable_to_non_nullable
as Duration,failureCount: null == failureCount ? _self.failureCount : failureCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc
mixin _$BackoffConfig implements DiagnosticableTreeMixin {

/// Initial delay for first retry
 Duration get initialDelay;/// Maximum delay between retries
 Duration get maxDelay;/// Multiplier for exponential backoff
 double get multiplier;/// Maximum number of retry attempts
 int get maxAttempts;/// Jitter factor to avoid thundering herd
 double get jitterFactor;
/// Create a copy of BackoffConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BackoffConfigCopyWith<BackoffConfig> get copyWith => _$BackoffConfigCopyWithImpl<BackoffConfig>(this as BackoffConfig, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'BackoffConfig'))
    ..add(DiagnosticsProperty('initialDelay', initialDelay))..add(DiagnosticsProperty('maxDelay', maxDelay))..add(DiagnosticsProperty('multiplier', multiplier))..add(DiagnosticsProperty('maxAttempts', maxAttempts))..add(DiagnosticsProperty('jitterFactor', jitterFactor));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BackoffConfig&&(identical(other.initialDelay, initialDelay) || other.initialDelay == initialDelay)&&(identical(other.maxDelay, maxDelay) || other.maxDelay == maxDelay)&&(identical(other.multiplier, multiplier) || other.multiplier == multiplier)&&(identical(other.maxAttempts, maxAttempts) || other.maxAttempts == maxAttempts)&&(identical(other.jitterFactor, jitterFactor) || other.jitterFactor == jitterFactor));
}


@override
int get hashCode => Object.hash(runtimeType,initialDelay,maxDelay,multiplier,maxAttempts,jitterFactor);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'BackoffConfig(initialDelay: $initialDelay, maxDelay: $maxDelay, multiplier: $multiplier, maxAttempts: $maxAttempts, jitterFactor: $jitterFactor)';
}


}

/// @nodoc
abstract mixin class $BackoffConfigCopyWith<$Res>  {
  factory $BackoffConfigCopyWith(BackoffConfig value, $Res Function(BackoffConfig) _then) = _$BackoffConfigCopyWithImpl;
@useResult
$Res call({
 Duration initialDelay, Duration maxDelay, double multiplier, int maxAttempts, double jitterFactor
});




}
/// @nodoc
class _$BackoffConfigCopyWithImpl<$Res>
    implements $BackoffConfigCopyWith<$Res> {
  _$BackoffConfigCopyWithImpl(this._self, this._then);

  final BackoffConfig _self;
  final $Res Function(BackoffConfig) _then;

/// Create a copy of BackoffConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? initialDelay = null,Object? maxDelay = null,Object? multiplier = null,Object? maxAttempts = null,Object? jitterFactor = null,}) {
  return _then(_self.copyWith(
initialDelay: null == initialDelay ? _self.initialDelay : initialDelay // ignore: cast_nullable_to_non_nullable
as Duration,maxDelay: null == maxDelay ? _self.maxDelay : maxDelay // ignore: cast_nullable_to_non_nullable
as Duration,multiplier: null == multiplier ? _self.multiplier : multiplier // ignore: cast_nullable_to_non_nullable
as double,maxAttempts: null == maxAttempts ? _self.maxAttempts : maxAttempts // ignore: cast_nullable_to_non_nullable
as int,jitterFactor: null == jitterFactor ? _self.jitterFactor : jitterFactor // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [BackoffConfig].
extension BackoffConfigPatterns on BackoffConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BackoffConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BackoffConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BackoffConfig value)  $default,){
final _that = this;
switch (_that) {
case _BackoffConfig():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BackoffConfig value)?  $default,){
final _that = this;
switch (_that) {
case _BackoffConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( Duration initialDelay,  Duration maxDelay,  double multiplier,  int maxAttempts,  double jitterFactor)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BackoffConfig() when $default != null:
return $default(_that.initialDelay,_that.maxDelay,_that.multiplier,_that.maxAttempts,_that.jitterFactor);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( Duration initialDelay,  Duration maxDelay,  double multiplier,  int maxAttempts,  double jitterFactor)  $default,) {final _that = this;
switch (_that) {
case _BackoffConfig():
return $default(_that.initialDelay,_that.maxDelay,_that.multiplier,_that.maxAttempts,_that.jitterFactor);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( Duration initialDelay,  Duration maxDelay,  double multiplier,  int maxAttempts,  double jitterFactor)?  $default,) {final _that = this;
switch (_that) {
case _BackoffConfig() when $default != null:
return $default(_that.initialDelay,_that.maxDelay,_that.multiplier,_that.maxAttempts,_that.jitterFactor);case _:
  return null;

}
}

}

/// @nodoc


class _BackoffConfig with DiagnosticableTreeMixin implements BackoffConfig {
  const _BackoffConfig({this.initialDelay = const Duration(milliseconds: 500), this.maxDelay = const Duration(seconds: 30), this.multiplier = 2.0, this.maxAttempts = 5, this.jitterFactor = 0.1});
  

/// Initial delay for first retry
@override@JsonKey() final  Duration initialDelay;
/// Maximum delay between retries
@override@JsonKey() final  Duration maxDelay;
/// Multiplier for exponential backoff
@override@JsonKey() final  double multiplier;
/// Maximum number of retry attempts
@override@JsonKey() final  int maxAttempts;
/// Jitter factor to avoid thundering herd
@override@JsonKey() final  double jitterFactor;

/// Create a copy of BackoffConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BackoffConfigCopyWith<_BackoffConfig> get copyWith => __$BackoffConfigCopyWithImpl<_BackoffConfig>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'BackoffConfig'))
    ..add(DiagnosticsProperty('initialDelay', initialDelay))..add(DiagnosticsProperty('maxDelay', maxDelay))..add(DiagnosticsProperty('multiplier', multiplier))..add(DiagnosticsProperty('maxAttempts', maxAttempts))..add(DiagnosticsProperty('jitterFactor', jitterFactor));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BackoffConfig&&(identical(other.initialDelay, initialDelay) || other.initialDelay == initialDelay)&&(identical(other.maxDelay, maxDelay) || other.maxDelay == maxDelay)&&(identical(other.multiplier, multiplier) || other.multiplier == multiplier)&&(identical(other.maxAttempts, maxAttempts) || other.maxAttempts == maxAttempts)&&(identical(other.jitterFactor, jitterFactor) || other.jitterFactor == jitterFactor));
}


@override
int get hashCode => Object.hash(runtimeType,initialDelay,maxDelay,multiplier,maxAttempts,jitterFactor);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'BackoffConfig(initialDelay: $initialDelay, maxDelay: $maxDelay, multiplier: $multiplier, maxAttempts: $maxAttempts, jitterFactor: $jitterFactor)';
}


}

/// @nodoc
abstract mixin class _$BackoffConfigCopyWith<$Res> implements $BackoffConfigCopyWith<$Res> {
  factory _$BackoffConfigCopyWith(_BackoffConfig value, $Res Function(_BackoffConfig) _then) = __$BackoffConfigCopyWithImpl;
@override @useResult
$Res call({
 Duration initialDelay, Duration maxDelay, double multiplier, int maxAttempts, double jitterFactor
});




}
/// @nodoc
class __$BackoffConfigCopyWithImpl<$Res>
    implements _$BackoffConfigCopyWith<$Res> {
  __$BackoffConfigCopyWithImpl(this._self, this._then);

  final _BackoffConfig _self;
  final $Res Function(_BackoffConfig) _then;

/// Create a copy of BackoffConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? initialDelay = null,Object? maxDelay = null,Object? multiplier = null,Object? maxAttempts = null,Object? jitterFactor = null,}) {
  return _then(_BackoffConfig(
initialDelay: null == initialDelay ? _self.initialDelay : initialDelay // ignore: cast_nullable_to_non_nullable
as Duration,maxDelay: null == maxDelay ? _self.maxDelay : maxDelay // ignore: cast_nullable_to_non_nullable
as Duration,multiplier: null == multiplier ? _self.multiplier : multiplier // ignore: cast_nullable_to_non_nullable
as double,maxAttempts: null == maxAttempts ? _self.maxAttempts : maxAttempts // ignore: cast_nullable_to_non_nullable
as int,jitterFactor: null == jitterFactor ? _self.jitterFactor : jitterFactor // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on

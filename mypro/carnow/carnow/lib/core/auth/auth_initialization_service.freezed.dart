// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_initialization_service.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AuthConfiguration {

/// Whether Google OAuth is enabled
 bool get googleAuthEnabled;/// Whether email authentication is enabled
 bool get emailAuthEnabled;/// API base URL for backend communication
 String get apiBaseUrl;/// Token refresh interval
 Duration get tokenRefreshInterval;/// Maximum time to wait for initialization
 Duration get initializationTimeout;/// Maximum number of retry attempts
 int get maxRetryAttempts;/// Whether offline mode is enabled
 bool get enableOfflineMode;/// Whether to validate stored tokens on startup
 bool get validateStoredTokens;/// Whether to use secure storage for tokens
 bool get useSecureStorage;
/// Create a copy of AuthConfiguration
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthConfigurationCopyWith<AuthConfiguration> get copyWith => _$AuthConfigurationCopyWithImpl<AuthConfiguration>(this as AuthConfiguration, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthConfiguration&&(identical(other.googleAuthEnabled, googleAuthEnabled) || other.googleAuthEnabled == googleAuthEnabled)&&(identical(other.emailAuthEnabled, emailAuthEnabled) || other.emailAuthEnabled == emailAuthEnabled)&&(identical(other.apiBaseUrl, apiBaseUrl) || other.apiBaseUrl == apiBaseUrl)&&(identical(other.tokenRefreshInterval, tokenRefreshInterval) || other.tokenRefreshInterval == tokenRefreshInterval)&&(identical(other.initializationTimeout, initializationTimeout) || other.initializationTimeout == initializationTimeout)&&(identical(other.maxRetryAttempts, maxRetryAttempts) || other.maxRetryAttempts == maxRetryAttempts)&&(identical(other.enableOfflineMode, enableOfflineMode) || other.enableOfflineMode == enableOfflineMode)&&(identical(other.validateStoredTokens, validateStoredTokens) || other.validateStoredTokens == validateStoredTokens)&&(identical(other.useSecureStorage, useSecureStorage) || other.useSecureStorage == useSecureStorage));
}


@override
int get hashCode => Object.hash(runtimeType,googleAuthEnabled,emailAuthEnabled,apiBaseUrl,tokenRefreshInterval,initializationTimeout,maxRetryAttempts,enableOfflineMode,validateStoredTokens,useSecureStorage);

@override
String toString() {
  return 'AuthConfiguration(googleAuthEnabled: $googleAuthEnabled, emailAuthEnabled: $emailAuthEnabled, apiBaseUrl: $apiBaseUrl, tokenRefreshInterval: $tokenRefreshInterval, initializationTimeout: $initializationTimeout, maxRetryAttempts: $maxRetryAttempts, enableOfflineMode: $enableOfflineMode, validateStoredTokens: $validateStoredTokens, useSecureStorage: $useSecureStorage)';
}


}

/// @nodoc
abstract mixin class $AuthConfigurationCopyWith<$Res>  {
  factory $AuthConfigurationCopyWith(AuthConfiguration value, $Res Function(AuthConfiguration) _then) = _$AuthConfigurationCopyWithImpl;
@useResult
$Res call({
 bool googleAuthEnabled, bool emailAuthEnabled, String apiBaseUrl, Duration tokenRefreshInterval, Duration initializationTimeout, int maxRetryAttempts, bool enableOfflineMode, bool validateStoredTokens, bool useSecureStorage
});




}
/// @nodoc
class _$AuthConfigurationCopyWithImpl<$Res>
    implements $AuthConfigurationCopyWith<$Res> {
  _$AuthConfigurationCopyWithImpl(this._self, this._then);

  final AuthConfiguration _self;
  final $Res Function(AuthConfiguration) _then;

/// Create a copy of AuthConfiguration
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? googleAuthEnabled = null,Object? emailAuthEnabled = null,Object? apiBaseUrl = null,Object? tokenRefreshInterval = null,Object? initializationTimeout = null,Object? maxRetryAttempts = null,Object? enableOfflineMode = null,Object? validateStoredTokens = null,Object? useSecureStorage = null,}) {
  return _then(_self.copyWith(
googleAuthEnabled: null == googleAuthEnabled ? _self.googleAuthEnabled : googleAuthEnabled // ignore: cast_nullable_to_non_nullable
as bool,emailAuthEnabled: null == emailAuthEnabled ? _self.emailAuthEnabled : emailAuthEnabled // ignore: cast_nullable_to_non_nullable
as bool,apiBaseUrl: null == apiBaseUrl ? _self.apiBaseUrl : apiBaseUrl // ignore: cast_nullable_to_non_nullable
as String,tokenRefreshInterval: null == tokenRefreshInterval ? _self.tokenRefreshInterval : tokenRefreshInterval // ignore: cast_nullable_to_non_nullable
as Duration,initializationTimeout: null == initializationTimeout ? _self.initializationTimeout : initializationTimeout // ignore: cast_nullable_to_non_nullable
as Duration,maxRetryAttempts: null == maxRetryAttempts ? _self.maxRetryAttempts : maxRetryAttempts // ignore: cast_nullable_to_non_nullable
as int,enableOfflineMode: null == enableOfflineMode ? _self.enableOfflineMode : enableOfflineMode // ignore: cast_nullable_to_non_nullable
as bool,validateStoredTokens: null == validateStoredTokens ? _self.validateStoredTokens : validateStoredTokens // ignore: cast_nullable_to_non_nullable
as bool,useSecureStorage: null == useSecureStorage ? _self.useSecureStorage : useSecureStorage // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [AuthConfiguration].
extension AuthConfigurationPatterns on AuthConfiguration {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AuthConfiguration value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AuthConfiguration() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AuthConfiguration value)  $default,){
final _that = this;
switch (_that) {
case _AuthConfiguration():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AuthConfiguration value)?  $default,){
final _that = this;
switch (_that) {
case _AuthConfiguration() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool googleAuthEnabled,  bool emailAuthEnabled,  String apiBaseUrl,  Duration tokenRefreshInterval,  Duration initializationTimeout,  int maxRetryAttempts,  bool enableOfflineMode,  bool validateStoredTokens,  bool useSecureStorage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AuthConfiguration() when $default != null:
return $default(_that.googleAuthEnabled,_that.emailAuthEnabled,_that.apiBaseUrl,_that.tokenRefreshInterval,_that.initializationTimeout,_that.maxRetryAttempts,_that.enableOfflineMode,_that.validateStoredTokens,_that.useSecureStorage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool googleAuthEnabled,  bool emailAuthEnabled,  String apiBaseUrl,  Duration tokenRefreshInterval,  Duration initializationTimeout,  int maxRetryAttempts,  bool enableOfflineMode,  bool validateStoredTokens,  bool useSecureStorage)  $default,) {final _that = this;
switch (_that) {
case _AuthConfiguration():
return $default(_that.googleAuthEnabled,_that.emailAuthEnabled,_that.apiBaseUrl,_that.tokenRefreshInterval,_that.initializationTimeout,_that.maxRetryAttempts,_that.enableOfflineMode,_that.validateStoredTokens,_that.useSecureStorage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool googleAuthEnabled,  bool emailAuthEnabled,  String apiBaseUrl,  Duration tokenRefreshInterval,  Duration initializationTimeout,  int maxRetryAttempts,  bool enableOfflineMode,  bool validateStoredTokens,  bool useSecureStorage)?  $default,) {final _that = this;
switch (_that) {
case _AuthConfiguration() when $default != null:
return $default(_that.googleAuthEnabled,_that.emailAuthEnabled,_that.apiBaseUrl,_that.tokenRefreshInterval,_that.initializationTimeout,_that.maxRetryAttempts,_that.enableOfflineMode,_that.validateStoredTokens,_that.useSecureStorage);case _:
  return null;

}
}

}

/// @nodoc


class _AuthConfiguration extends AuthConfiguration {
  const _AuthConfiguration({this.googleAuthEnabled = true, this.emailAuthEnabled = true, required this.apiBaseUrl, this.tokenRefreshInterval = const Duration(minutes: 45), this.initializationTimeout = const Duration(seconds: 15), this.maxRetryAttempts = 3, this.enableOfflineMode = true, this.validateStoredTokens = true, this.useSecureStorage = true}): super._();
  

/// Whether Google OAuth is enabled
@override@JsonKey() final  bool googleAuthEnabled;
/// Whether email authentication is enabled
@override@JsonKey() final  bool emailAuthEnabled;
/// API base URL for backend communication
@override final  String apiBaseUrl;
/// Token refresh interval
@override@JsonKey() final  Duration tokenRefreshInterval;
/// Maximum time to wait for initialization
@override@JsonKey() final  Duration initializationTimeout;
/// Maximum number of retry attempts
@override@JsonKey() final  int maxRetryAttempts;
/// Whether offline mode is enabled
@override@JsonKey() final  bool enableOfflineMode;
/// Whether to validate stored tokens on startup
@override@JsonKey() final  bool validateStoredTokens;
/// Whether to use secure storage for tokens
@override@JsonKey() final  bool useSecureStorage;

/// Create a copy of AuthConfiguration
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AuthConfigurationCopyWith<_AuthConfiguration> get copyWith => __$AuthConfigurationCopyWithImpl<_AuthConfiguration>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AuthConfiguration&&(identical(other.googleAuthEnabled, googleAuthEnabled) || other.googleAuthEnabled == googleAuthEnabled)&&(identical(other.emailAuthEnabled, emailAuthEnabled) || other.emailAuthEnabled == emailAuthEnabled)&&(identical(other.apiBaseUrl, apiBaseUrl) || other.apiBaseUrl == apiBaseUrl)&&(identical(other.tokenRefreshInterval, tokenRefreshInterval) || other.tokenRefreshInterval == tokenRefreshInterval)&&(identical(other.initializationTimeout, initializationTimeout) || other.initializationTimeout == initializationTimeout)&&(identical(other.maxRetryAttempts, maxRetryAttempts) || other.maxRetryAttempts == maxRetryAttempts)&&(identical(other.enableOfflineMode, enableOfflineMode) || other.enableOfflineMode == enableOfflineMode)&&(identical(other.validateStoredTokens, validateStoredTokens) || other.validateStoredTokens == validateStoredTokens)&&(identical(other.useSecureStorage, useSecureStorage) || other.useSecureStorage == useSecureStorage));
}


@override
int get hashCode => Object.hash(runtimeType,googleAuthEnabled,emailAuthEnabled,apiBaseUrl,tokenRefreshInterval,initializationTimeout,maxRetryAttempts,enableOfflineMode,validateStoredTokens,useSecureStorage);

@override
String toString() {
  return 'AuthConfiguration(googleAuthEnabled: $googleAuthEnabled, emailAuthEnabled: $emailAuthEnabled, apiBaseUrl: $apiBaseUrl, tokenRefreshInterval: $tokenRefreshInterval, initializationTimeout: $initializationTimeout, maxRetryAttempts: $maxRetryAttempts, enableOfflineMode: $enableOfflineMode, validateStoredTokens: $validateStoredTokens, useSecureStorage: $useSecureStorage)';
}


}

/// @nodoc
abstract mixin class _$AuthConfigurationCopyWith<$Res> implements $AuthConfigurationCopyWith<$Res> {
  factory _$AuthConfigurationCopyWith(_AuthConfiguration value, $Res Function(_AuthConfiguration) _then) = __$AuthConfigurationCopyWithImpl;
@override @useResult
$Res call({
 bool googleAuthEnabled, bool emailAuthEnabled, String apiBaseUrl, Duration tokenRefreshInterval, Duration initializationTimeout, int maxRetryAttempts, bool enableOfflineMode, bool validateStoredTokens, bool useSecureStorage
});




}
/// @nodoc
class __$AuthConfigurationCopyWithImpl<$Res>
    implements _$AuthConfigurationCopyWith<$Res> {
  __$AuthConfigurationCopyWithImpl(this._self, this._then);

  final _AuthConfiguration _self;
  final $Res Function(_AuthConfiguration) _then;

/// Create a copy of AuthConfiguration
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? googleAuthEnabled = null,Object? emailAuthEnabled = null,Object? apiBaseUrl = null,Object? tokenRefreshInterval = null,Object? initializationTimeout = null,Object? maxRetryAttempts = null,Object? enableOfflineMode = null,Object? validateStoredTokens = null,Object? useSecureStorage = null,}) {
  return _then(_AuthConfiguration(
googleAuthEnabled: null == googleAuthEnabled ? _self.googleAuthEnabled : googleAuthEnabled // ignore: cast_nullable_to_non_nullable
as bool,emailAuthEnabled: null == emailAuthEnabled ? _self.emailAuthEnabled : emailAuthEnabled // ignore: cast_nullable_to_non_nullable
as bool,apiBaseUrl: null == apiBaseUrl ? _self.apiBaseUrl : apiBaseUrl // ignore: cast_nullable_to_non_nullable
as String,tokenRefreshInterval: null == tokenRefreshInterval ? _self.tokenRefreshInterval : tokenRefreshInterval // ignore: cast_nullable_to_non_nullable
as Duration,initializationTimeout: null == initializationTimeout ? _self.initializationTimeout : initializationTimeout // ignore: cast_nullable_to_non_nullable
as Duration,maxRetryAttempts: null == maxRetryAttempts ? _self.maxRetryAttempts : maxRetryAttempts // ignore: cast_nullable_to_non_nullable
as int,enableOfflineMode: null == enableOfflineMode ? _self.enableOfflineMode : enableOfflineMode // ignore: cast_nullable_to_non_nullable
as bool,validateStoredTokens: null == validateStoredTokens ? _self.validateStoredTokens : validateStoredTokens // ignore: cast_nullable_to_non_nullable
as bool,useSecureStorage: null == useSecureStorage ? _self.useSecureStorage : useSecureStorage // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc
mixin _$AuthInitializationResult {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthInitializationResult);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AuthInitializationResult()';
}


}

/// @nodoc
class $AuthInitializationResultCopyWith<$Res>  {
$AuthInitializationResultCopyWith(AuthInitializationResult _, $Res Function(AuthInitializationResult) __);
}


/// Adds pattern-matching-related methods to [AuthInitializationResult].
extension AuthInitializationResultPatterns on AuthInitializationResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( AuthInitializationSuccess value)?  success,TResult Function( AuthInitializationFailure value)?  failure,TResult Function( AuthInitializationTimeout value)?  timeout,required TResult orElse(),}){
final _that = this;
switch (_that) {
case AuthInitializationSuccess() when success != null:
return success(_that);case AuthInitializationFailure() when failure != null:
return failure(_that);case AuthInitializationTimeout() when timeout != null:
return timeout(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( AuthInitializationSuccess value)  success,required TResult Function( AuthInitializationFailure value)  failure,required TResult Function( AuthInitializationTimeout value)  timeout,}){
final _that = this;
switch (_that) {
case AuthInitializationSuccess():
return success(_that);case AuthInitializationFailure():
return failure(_that);case AuthInitializationTimeout():
return timeout(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( AuthInitializationSuccess value)?  success,TResult? Function( AuthInitializationFailure value)?  failure,TResult? Function( AuthInitializationTimeout value)?  timeout,}){
final _that = this;
switch (_that) {
case AuthInitializationSuccess() when success != null:
return success(_that);case AuthInitializationFailure() when failure != null:
return failure(_that);case AuthInitializationTimeout() when timeout != null:
return timeout(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( AuthState initialState,  Duration initializationTime,  AuthConfiguration configuration,  Map<String, dynamic> metadata)?  success,TResult Function( String error,  AuthErrorType errorType,  bool canRetry,  AuthState? fallbackState,  Map<String, dynamic> details)?  failure,TResult Function( Duration timeoutDuration,  AuthState fallbackState,  bool shouldRetry)?  timeout,required TResult orElse(),}) {final _that = this;
switch (_that) {
case AuthInitializationSuccess() when success != null:
return success(_that.initialState,_that.initializationTime,_that.configuration,_that.metadata);case AuthInitializationFailure() when failure != null:
return failure(_that.error,_that.errorType,_that.canRetry,_that.fallbackState,_that.details);case AuthInitializationTimeout() when timeout != null:
return timeout(_that.timeoutDuration,_that.fallbackState,_that.shouldRetry);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( AuthState initialState,  Duration initializationTime,  AuthConfiguration configuration,  Map<String, dynamic> metadata)  success,required TResult Function( String error,  AuthErrorType errorType,  bool canRetry,  AuthState? fallbackState,  Map<String, dynamic> details)  failure,required TResult Function( Duration timeoutDuration,  AuthState fallbackState,  bool shouldRetry)  timeout,}) {final _that = this;
switch (_that) {
case AuthInitializationSuccess():
return success(_that.initialState,_that.initializationTime,_that.configuration,_that.metadata);case AuthInitializationFailure():
return failure(_that.error,_that.errorType,_that.canRetry,_that.fallbackState,_that.details);case AuthInitializationTimeout():
return timeout(_that.timeoutDuration,_that.fallbackState,_that.shouldRetry);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( AuthState initialState,  Duration initializationTime,  AuthConfiguration configuration,  Map<String, dynamic> metadata)?  success,TResult? Function( String error,  AuthErrorType errorType,  bool canRetry,  AuthState? fallbackState,  Map<String, dynamic> details)?  failure,TResult? Function( Duration timeoutDuration,  AuthState fallbackState,  bool shouldRetry)?  timeout,}) {final _that = this;
switch (_that) {
case AuthInitializationSuccess() when success != null:
return success(_that.initialState,_that.initializationTime,_that.configuration,_that.metadata);case AuthInitializationFailure() when failure != null:
return failure(_that.error,_that.errorType,_that.canRetry,_that.fallbackState,_that.details);case AuthInitializationTimeout() when timeout != null:
return timeout(_that.timeoutDuration,_that.fallbackState,_that.shouldRetry);case _:
  return null;

}
}

}

/// @nodoc


class AuthInitializationSuccess implements AuthInitializationResult {
  const AuthInitializationSuccess({required this.initialState, required this.initializationTime, required this.configuration, final  Map<String, dynamic> metadata = const {}}): _metadata = metadata;
  

/// Initial authentication state
 final  AuthState initialState;
/// Time taken for initialization
 final  Duration initializationTime;
/// Configuration used for initialization
 final  AuthConfiguration configuration;
/// Additional metadata
 final  Map<String, dynamic> _metadata;
/// Additional metadata
@JsonKey() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}


/// Create a copy of AuthInitializationResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthInitializationSuccessCopyWith<AuthInitializationSuccess> get copyWith => _$AuthInitializationSuccessCopyWithImpl<AuthInitializationSuccess>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthInitializationSuccess&&(identical(other.initialState, initialState) || other.initialState == initialState)&&(identical(other.initializationTime, initializationTime) || other.initializationTime == initializationTime)&&(identical(other.configuration, configuration) || other.configuration == configuration)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}


@override
int get hashCode => Object.hash(runtimeType,initialState,initializationTime,configuration,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'AuthInitializationResult.success(initialState: $initialState, initializationTime: $initializationTime, configuration: $configuration, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $AuthInitializationSuccessCopyWith<$Res> implements $AuthInitializationResultCopyWith<$Res> {
  factory $AuthInitializationSuccessCopyWith(AuthInitializationSuccess value, $Res Function(AuthInitializationSuccess) _then) = _$AuthInitializationSuccessCopyWithImpl;
@useResult
$Res call({
 AuthState initialState, Duration initializationTime, AuthConfiguration configuration, Map<String, dynamic> metadata
});


$AuthStateCopyWith<$Res> get initialState;$AuthConfigurationCopyWith<$Res> get configuration;

}
/// @nodoc
class _$AuthInitializationSuccessCopyWithImpl<$Res>
    implements $AuthInitializationSuccessCopyWith<$Res> {
  _$AuthInitializationSuccessCopyWithImpl(this._self, this._then);

  final AuthInitializationSuccess _self;
  final $Res Function(AuthInitializationSuccess) _then;

/// Create a copy of AuthInitializationResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? initialState = null,Object? initializationTime = null,Object? configuration = null,Object? metadata = null,}) {
  return _then(AuthInitializationSuccess(
initialState: null == initialState ? _self.initialState : initialState // ignore: cast_nullable_to_non_nullable
as AuthState,initializationTime: null == initializationTime ? _self.initializationTime : initializationTime // ignore: cast_nullable_to_non_nullable
as Duration,configuration: null == configuration ? _self.configuration : configuration // ignore: cast_nullable_to_non_nullable
as AuthConfiguration,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

/// Create a copy of AuthInitializationResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AuthStateCopyWith<$Res> get initialState {
  
  return $AuthStateCopyWith<$Res>(_self.initialState, (value) {
    return _then(_self.copyWith(initialState: value));
  });
}/// Create a copy of AuthInitializationResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AuthConfigurationCopyWith<$Res> get configuration {
  
  return $AuthConfigurationCopyWith<$Res>(_self.configuration, (value) {
    return _then(_self.copyWith(configuration: value));
  });
}
}

/// @nodoc


class AuthInitializationFailure implements AuthInitializationResult {
  const AuthInitializationFailure({required this.error, required this.errorType, required this.canRetry, this.fallbackState, final  Map<String, dynamic> details = const {}}): _details = details;
  

/// Error message
 final  String error;
/// Error type for categorization
 final  AuthErrorType errorType;
/// Whether initialization can be retried
 final  bool canRetry;
/// Fallback state to use
 final  AuthState? fallbackState;
/// Additional error details
 final  Map<String, dynamic> _details;
/// Additional error details
@JsonKey() Map<String, dynamic> get details {
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_details);
}


/// Create a copy of AuthInitializationResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthInitializationFailureCopyWith<AuthInitializationFailure> get copyWith => _$AuthInitializationFailureCopyWithImpl<AuthInitializationFailure>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthInitializationFailure&&(identical(other.error, error) || other.error == error)&&(identical(other.errorType, errorType) || other.errorType == errorType)&&(identical(other.canRetry, canRetry) || other.canRetry == canRetry)&&(identical(other.fallbackState, fallbackState) || other.fallbackState == fallbackState)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,error,errorType,canRetry,fallbackState,const DeepCollectionEquality().hash(_details));

@override
String toString() {
  return 'AuthInitializationResult.failure(error: $error, errorType: $errorType, canRetry: $canRetry, fallbackState: $fallbackState, details: $details)';
}


}

/// @nodoc
abstract mixin class $AuthInitializationFailureCopyWith<$Res> implements $AuthInitializationResultCopyWith<$Res> {
  factory $AuthInitializationFailureCopyWith(AuthInitializationFailure value, $Res Function(AuthInitializationFailure) _then) = _$AuthInitializationFailureCopyWithImpl;
@useResult
$Res call({
 String error, AuthErrorType errorType, bool canRetry, AuthState? fallbackState, Map<String, dynamic> details
});


$AuthStateCopyWith<$Res>? get fallbackState;

}
/// @nodoc
class _$AuthInitializationFailureCopyWithImpl<$Res>
    implements $AuthInitializationFailureCopyWith<$Res> {
  _$AuthInitializationFailureCopyWithImpl(this._self, this._then);

  final AuthInitializationFailure _self;
  final $Res Function(AuthInitializationFailure) _then;

/// Create a copy of AuthInitializationResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? error = null,Object? errorType = null,Object? canRetry = null,Object? fallbackState = freezed,Object? details = null,}) {
  return _then(AuthInitializationFailure(
error: null == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String,errorType: null == errorType ? _self.errorType : errorType // ignore: cast_nullable_to_non_nullable
as AuthErrorType,canRetry: null == canRetry ? _self.canRetry : canRetry // ignore: cast_nullable_to_non_nullable
as bool,fallbackState: freezed == fallbackState ? _self.fallbackState : fallbackState // ignore: cast_nullable_to_non_nullable
as AuthState?,details: null == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

/// Create a copy of AuthInitializationResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AuthStateCopyWith<$Res>? get fallbackState {
    if (_self.fallbackState == null) {
    return null;
  }

  return $AuthStateCopyWith<$Res>(_self.fallbackState!, (value) {
    return _then(_self.copyWith(fallbackState: value));
  });
}
}

/// @nodoc


class AuthInitializationTimeout implements AuthInitializationResult {
  const AuthInitializationTimeout({required this.timeoutDuration, required this.fallbackState, this.shouldRetry = true});
  

/// Duration that was waited
 final  Duration timeoutDuration;
/// Fallback state to use
 final  AuthState fallbackState;
/// Whether retry is recommended
@JsonKey() final  bool shouldRetry;

/// Create a copy of AuthInitializationResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthInitializationTimeoutCopyWith<AuthInitializationTimeout> get copyWith => _$AuthInitializationTimeoutCopyWithImpl<AuthInitializationTimeout>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthInitializationTimeout&&(identical(other.timeoutDuration, timeoutDuration) || other.timeoutDuration == timeoutDuration)&&(identical(other.fallbackState, fallbackState) || other.fallbackState == fallbackState)&&(identical(other.shouldRetry, shouldRetry) || other.shouldRetry == shouldRetry));
}


@override
int get hashCode => Object.hash(runtimeType,timeoutDuration,fallbackState,shouldRetry);

@override
String toString() {
  return 'AuthInitializationResult.timeout(timeoutDuration: $timeoutDuration, fallbackState: $fallbackState, shouldRetry: $shouldRetry)';
}


}

/// @nodoc
abstract mixin class $AuthInitializationTimeoutCopyWith<$Res> implements $AuthInitializationResultCopyWith<$Res> {
  factory $AuthInitializationTimeoutCopyWith(AuthInitializationTimeout value, $Res Function(AuthInitializationTimeout) _then) = _$AuthInitializationTimeoutCopyWithImpl;
@useResult
$Res call({
 Duration timeoutDuration, AuthState fallbackState, bool shouldRetry
});


$AuthStateCopyWith<$Res> get fallbackState;

}
/// @nodoc
class _$AuthInitializationTimeoutCopyWithImpl<$Res>
    implements $AuthInitializationTimeoutCopyWith<$Res> {
  _$AuthInitializationTimeoutCopyWithImpl(this._self, this._then);

  final AuthInitializationTimeout _self;
  final $Res Function(AuthInitializationTimeout) _then;

/// Create a copy of AuthInitializationResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? timeoutDuration = null,Object? fallbackState = null,Object? shouldRetry = null,}) {
  return _then(AuthInitializationTimeout(
timeoutDuration: null == timeoutDuration ? _self.timeoutDuration : timeoutDuration // ignore: cast_nullable_to_non_nullable
as Duration,fallbackState: null == fallbackState ? _self.fallbackState : fallbackState // ignore: cast_nullable_to_non_nullable
as AuthState,shouldRetry: null == shouldRetry ? _self.shouldRetry : shouldRetry // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of AuthInitializationResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AuthStateCopyWith<$Res> get fallbackState {
  
  return $AuthStateCopyWith<$Res>(_self.fallbackState, (value) {
    return _then(_self.copyWith(fallbackState: value));
  });
}
}

// dart format on

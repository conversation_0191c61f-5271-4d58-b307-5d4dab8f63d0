// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$User implements DiagnosticableTreeMixin {

/// Unique user identifier from backend
 String get id;/// User's email address (validated)
 String get email;/// User's first name
@JsonKey(name: 'first_name') String get firstName;/// User's last name
@JsonKey(name: 'last_name') String get lastName;/// User's display name (computed from first + last name)
@JsonKey(name: 'display_name') String? get displayName;/// User's phone number (optional)
@JsonKey(name: 'phone_number') String? get phoneNumber;/// User's avatar/profile picture URL
@JsonKey(name: 'avatar_url') String? get avatarUrl;/// Whether the user account is active
@JsonKey(name: 'is_active') bool get isActive;/// Whether the user's email is verified
@JsonKey(name: 'email_verified') bool get emailVerified;/// Authentication provider (email, google)
@JsonKey(name: 'auth_provider') AuthProvider get authProvider;/// User's preferred language/locale
@JsonKey(name: 'locale') String get locale;/// User's timezone
@JsonKey(name: 'timezone') String? get timezone;/// Last login timestamp
@JsonKey(name: 'last_login_at') DateTime? get lastLoginAt;/// Account creation timestamp
@JsonKey(name: 'created_at') DateTime get createdAt;/// Last update timestamp
@JsonKey(name: 'updated_at') DateTime get updatedAt;
/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserCopyWith<User> get copyWith => _$UserCopyWithImpl<User>(this as User, _$identity);

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'User'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('firstName', firstName))..add(DiagnosticsProperty('lastName', lastName))..add(DiagnosticsProperty('displayName', displayName))..add(DiagnosticsProperty('phoneNumber', phoneNumber))..add(DiagnosticsProperty('avatarUrl', avatarUrl))..add(DiagnosticsProperty('isActive', isActive))..add(DiagnosticsProperty('emailVerified', emailVerified))..add(DiagnosticsProperty('authProvider', authProvider))..add(DiagnosticsProperty('locale', locale))..add(DiagnosticsProperty('timezone', timezone))..add(DiagnosticsProperty('lastLoginAt', lastLoginAt))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('updatedAt', updatedAt));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is User&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.emailVerified, emailVerified) || other.emailVerified == emailVerified)&&(identical(other.authProvider, authProvider) || other.authProvider == authProvider)&&(identical(other.locale, locale) || other.locale == locale)&&(identical(other.timezone, timezone) || other.timezone == timezone)&&(identical(other.lastLoginAt, lastLoginAt) || other.lastLoginAt == lastLoginAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,email,firstName,lastName,displayName,phoneNumber,avatarUrl,isActive,emailVerified,authProvider,locale,timezone,lastLoginAt,createdAt,updatedAt);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'User(id: $id, email: $email, firstName: $firstName, lastName: $lastName, displayName: $displayName, phoneNumber: $phoneNumber, avatarUrl: $avatarUrl, isActive: $isActive, emailVerified: $emailVerified, authProvider: $authProvider, locale: $locale, timezone: $timezone, lastLoginAt: $lastLoginAt, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $UserCopyWith<$Res>  {
  factory $UserCopyWith(User value, $Res Function(User) _then) = _$UserCopyWithImpl;
@useResult
$Res call({
 String id, String email,@JsonKey(name: 'first_name') String firstName,@JsonKey(name: 'last_name') String lastName,@JsonKey(name: 'display_name') String? displayName,@JsonKey(name: 'phone_number') String? phoneNumber,@JsonKey(name: 'avatar_url') String? avatarUrl,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'email_verified') bool emailVerified,@JsonKey(name: 'auth_provider') AuthProvider authProvider,@JsonKey(name: 'locale') String locale,@JsonKey(name: 'timezone') String? timezone,@JsonKey(name: 'last_login_at') DateTime? lastLoginAt,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt
});




}
/// @nodoc
class _$UserCopyWithImpl<$Res>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._self, this._then);

  final User _self;
  final $Res Function(User) _then;

/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? email = null,Object? firstName = null,Object? lastName = null,Object? displayName = freezed,Object? phoneNumber = freezed,Object? avatarUrl = freezed,Object? isActive = null,Object? emailVerified = null,Object? authProvider = null,Object? locale = null,Object? timezone = freezed,Object? lastLoginAt = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,displayName: freezed == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String?,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,emailVerified: null == emailVerified ? _self.emailVerified : emailVerified // ignore: cast_nullable_to_non_nullable
as bool,authProvider: null == authProvider ? _self.authProvider : authProvider // ignore: cast_nullable_to_non_nullable
as AuthProvider,locale: null == locale ? _self.locale : locale // ignore: cast_nullable_to_non_nullable
as String,timezone: freezed == timezone ? _self.timezone : timezone // ignore: cast_nullable_to_non_nullable
as String?,lastLoginAt: freezed == lastLoginAt ? _self.lastLoginAt : lastLoginAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [User].
extension UserPatterns on User {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _User value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _User() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _User value)  $default,){
final _that = this;
switch (_that) {
case _User():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _User value)?  $default,){
final _that = this;
switch (_that) {
case _User() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String email, @JsonKey(name: 'first_name')  String firstName, @JsonKey(name: 'last_name')  String lastName, @JsonKey(name: 'display_name')  String? displayName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'email_verified')  bool emailVerified, @JsonKey(name: 'auth_provider')  AuthProvider authProvider, @JsonKey(name: 'locale')  String locale, @JsonKey(name: 'timezone')  String? timezone, @JsonKey(name: 'last_login_at')  DateTime? lastLoginAt, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _User() when $default != null:
return $default(_that.id,_that.email,_that.firstName,_that.lastName,_that.displayName,_that.phoneNumber,_that.avatarUrl,_that.isActive,_that.emailVerified,_that.authProvider,_that.locale,_that.timezone,_that.lastLoginAt,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String email, @JsonKey(name: 'first_name')  String firstName, @JsonKey(name: 'last_name')  String lastName, @JsonKey(name: 'display_name')  String? displayName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'email_verified')  bool emailVerified, @JsonKey(name: 'auth_provider')  AuthProvider authProvider, @JsonKey(name: 'locale')  String locale, @JsonKey(name: 'timezone')  String? timezone, @JsonKey(name: 'last_login_at')  DateTime? lastLoginAt, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _User():
return $default(_that.id,_that.email,_that.firstName,_that.lastName,_that.displayName,_that.phoneNumber,_that.avatarUrl,_that.isActive,_that.emailVerified,_that.authProvider,_that.locale,_that.timezone,_that.lastLoginAt,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String email, @JsonKey(name: 'first_name')  String firstName, @JsonKey(name: 'last_name')  String lastName, @JsonKey(name: 'display_name')  String? displayName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'email_verified')  bool emailVerified, @JsonKey(name: 'auth_provider')  AuthProvider authProvider, @JsonKey(name: 'locale')  String locale, @JsonKey(name: 'timezone')  String? timezone, @JsonKey(name: 'last_login_at')  DateTime? lastLoginAt, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _User() when $default != null:
return $default(_that.id,_that.email,_that.firstName,_that.lastName,_that.displayName,_that.phoneNumber,_that.avatarUrl,_that.isActive,_that.emailVerified,_that.authProvider,_that.locale,_that.timezone,_that.lastLoginAt,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _User extends User with DiagnosticableTreeMixin {
  const _User({required this.id, required this.email, @JsonKey(name: 'first_name') required this.firstName, @JsonKey(name: 'last_name') required this.lastName, @JsonKey(name: 'display_name') this.displayName, @JsonKey(name: 'phone_number') this.phoneNumber, @JsonKey(name: 'avatar_url') this.avatarUrl, @JsonKey(name: 'is_active') this.isActive = true, @JsonKey(name: 'email_verified') this.emailVerified = false, @JsonKey(name: 'auth_provider') this.authProvider = AuthProvider.email, @JsonKey(name: 'locale') this.locale = 'en', @JsonKey(name: 'timezone') this.timezone, @JsonKey(name: 'last_login_at') this.lastLoginAt, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt}): super._();
  factory _User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

/// Unique user identifier from backend
@override final  String id;
/// User's email address (validated)
@override final  String email;
/// User's first name
@override@JsonKey(name: 'first_name') final  String firstName;
/// User's last name
@override@JsonKey(name: 'last_name') final  String lastName;
/// User's display name (computed from first + last name)
@override@JsonKey(name: 'display_name') final  String? displayName;
/// User's phone number (optional)
@override@JsonKey(name: 'phone_number') final  String? phoneNumber;
/// User's avatar/profile picture URL
@override@JsonKey(name: 'avatar_url') final  String? avatarUrl;
/// Whether the user account is active
@override@JsonKey(name: 'is_active') final  bool isActive;
/// Whether the user's email is verified
@override@JsonKey(name: 'email_verified') final  bool emailVerified;
/// Authentication provider (email, google)
@override@JsonKey(name: 'auth_provider') final  AuthProvider authProvider;
/// User's preferred language/locale
@override@JsonKey(name: 'locale') final  String locale;
/// User's timezone
@override@JsonKey(name: 'timezone') final  String? timezone;
/// Last login timestamp
@override@JsonKey(name: 'last_login_at') final  DateTime? lastLoginAt;
/// Account creation timestamp
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
/// Last update timestamp
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;

/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserCopyWith<_User> get copyWith => __$UserCopyWithImpl<_User>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'User'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('firstName', firstName))..add(DiagnosticsProperty('lastName', lastName))..add(DiagnosticsProperty('displayName', displayName))..add(DiagnosticsProperty('phoneNumber', phoneNumber))..add(DiagnosticsProperty('avatarUrl', avatarUrl))..add(DiagnosticsProperty('isActive', isActive))..add(DiagnosticsProperty('emailVerified', emailVerified))..add(DiagnosticsProperty('authProvider', authProvider))..add(DiagnosticsProperty('locale', locale))..add(DiagnosticsProperty('timezone', timezone))..add(DiagnosticsProperty('lastLoginAt', lastLoginAt))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('updatedAt', updatedAt));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _User&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.emailVerified, emailVerified) || other.emailVerified == emailVerified)&&(identical(other.authProvider, authProvider) || other.authProvider == authProvider)&&(identical(other.locale, locale) || other.locale == locale)&&(identical(other.timezone, timezone) || other.timezone == timezone)&&(identical(other.lastLoginAt, lastLoginAt) || other.lastLoginAt == lastLoginAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,email,firstName,lastName,displayName,phoneNumber,avatarUrl,isActive,emailVerified,authProvider,locale,timezone,lastLoginAt,createdAt,updatedAt);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'User(id: $id, email: $email, firstName: $firstName, lastName: $lastName, displayName: $displayName, phoneNumber: $phoneNumber, avatarUrl: $avatarUrl, isActive: $isActive, emailVerified: $emailVerified, authProvider: $authProvider, locale: $locale, timezone: $timezone, lastLoginAt: $lastLoginAt, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$UserCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$UserCopyWith(_User value, $Res Function(_User) _then) = __$UserCopyWithImpl;
@override @useResult
$Res call({
 String id, String email,@JsonKey(name: 'first_name') String firstName,@JsonKey(name: 'last_name') String lastName,@JsonKey(name: 'display_name') String? displayName,@JsonKey(name: 'phone_number') String? phoneNumber,@JsonKey(name: 'avatar_url') String? avatarUrl,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'email_verified') bool emailVerified,@JsonKey(name: 'auth_provider') AuthProvider authProvider,@JsonKey(name: 'locale') String locale,@JsonKey(name: 'timezone') String? timezone,@JsonKey(name: 'last_login_at') DateTime? lastLoginAt,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt
});




}
/// @nodoc
class __$UserCopyWithImpl<$Res>
    implements _$UserCopyWith<$Res> {
  __$UserCopyWithImpl(this._self, this._then);

  final _User _self;
  final $Res Function(_User) _then;

/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? email = null,Object? firstName = null,Object? lastName = null,Object? displayName = freezed,Object? phoneNumber = freezed,Object? avatarUrl = freezed,Object? isActive = null,Object? emailVerified = null,Object? authProvider = null,Object? locale = null,Object? timezone = freezed,Object? lastLoginAt = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_User(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,displayName: freezed == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String?,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,emailVerified: null == emailVerified ? _self.emailVerified : emailVerified // ignore: cast_nullable_to_non_nullable
as bool,authProvider: null == authProvider ? _self.authProvider : authProvider // ignore: cast_nullable_to_non_nullable
as AuthProvider,locale: null == locale ? _self.locale : locale // ignore: cast_nullable_to_non_nullable
as String,timezone: freezed == timezone ? _self.timezone : timezone // ignore: cast_nullable_to_non_nullable
as String?,lastLoginAt: freezed == lastLoginAt ? _self.lastLoginAt : lastLoginAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

/// @nodoc
mixin _$AuthState implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthState'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthState()';
}


}

/// @nodoc
class $AuthStateCopyWith<$Res>  {
$AuthStateCopyWith(AuthState _, $Res Function(AuthState) __);
}


/// Adds pattern-matching-related methods to [AuthState].
extension AuthStatePatterns on AuthState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( AuthStateInitial value)?  initial,TResult Function( AuthStateLoading value)?  loading,TResult Function( AuthStateAuthenticated value)?  authenticated,TResult Function( AuthStateUnauthenticated value)?  unauthenticated,TResult Function( AuthStateError value)?  error,TResult Function( AuthStateEmailVerificationPending value)?  emailVerificationPending,TResult Function( AuthStateSessionExpired value)?  sessionExpired,required TResult orElse(),}){
final _that = this;
switch (_that) {
case AuthStateInitial() when initial != null:
return initial(_that);case AuthStateLoading() when loading != null:
return loading(_that);case AuthStateAuthenticated() when authenticated != null:
return authenticated(_that);case AuthStateUnauthenticated() when unauthenticated != null:
return unauthenticated(_that);case AuthStateError() when error != null:
return error(_that);case AuthStateEmailVerificationPending() when emailVerificationPending != null:
return emailVerificationPending(_that);case AuthStateSessionExpired() when sessionExpired != null:
return sessionExpired(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( AuthStateInitial value)  initial,required TResult Function( AuthStateLoading value)  loading,required TResult Function( AuthStateAuthenticated value)  authenticated,required TResult Function( AuthStateUnauthenticated value)  unauthenticated,required TResult Function( AuthStateError value)  error,required TResult Function( AuthStateEmailVerificationPending value)  emailVerificationPending,required TResult Function( AuthStateSessionExpired value)  sessionExpired,}){
final _that = this;
switch (_that) {
case AuthStateInitial():
return initial(_that);case AuthStateLoading():
return loading(_that);case AuthStateAuthenticated():
return authenticated(_that);case AuthStateUnauthenticated():
return unauthenticated(_that);case AuthStateError():
return error(_that);case AuthStateEmailVerificationPending():
return emailVerificationPending(_that);case AuthStateSessionExpired():
return sessionExpired(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( AuthStateInitial value)?  initial,TResult? Function( AuthStateLoading value)?  loading,TResult? Function( AuthStateAuthenticated value)?  authenticated,TResult? Function( AuthStateUnauthenticated value)?  unauthenticated,TResult? Function( AuthStateError value)?  error,TResult? Function( AuthStateEmailVerificationPending value)?  emailVerificationPending,TResult? Function( AuthStateSessionExpired value)?  sessionExpired,}){
final _that = this;
switch (_that) {
case AuthStateInitial() when initial != null:
return initial(_that);case AuthStateLoading() when loading != null:
return loading(_that);case AuthStateAuthenticated() when authenticated != null:
return authenticated(_that);case AuthStateUnauthenticated() when unauthenticated != null:
return unauthenticated(_that);case AuthStateError() when error != null:
return error(_that);case AuthStateEmailVerificationPending() when emailVerificationPending != null:
return emailVerificationPending(_that);case AuthStateSessionExpired() when sessionExpired != null:
return sessionExpired(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  initial,TResult Function( String? message,  AuthOperation operation)?  loading,TResult Function( User user,  String token,  String? refreshToken, @JsonKey(name: 'token_expiry')  DateTime? tokenExpiry, @JsonKey(name: 'session_start')  DateTime? sessionStart)?  authenticated,TResult Function( String? reason)?  unauthenticated,TResult Function( String message,  String? errorCode,  AuthErrorType errorType,  bool isRecoverable,  Object? originalException)?  error,TResult Function( String email,  DateTime? sentAt)?  emailVerificationPending,TResult Function( DateTime? expiredAt,  bool autoRefreshAttempted)?  sessionExpired,required TResult orElse(),}) {final _that = this;
switch (_that) {
case AuthStateInitial() when initial != null:
return initial();case AuthStateLoading() when loading != null:
return loading(_that.message,_that.operation);case AuthStateAuthenticated() when authenticated != null:
return authenticated(_that.user,_that.token,_that.refreshToken,_that.tokenExpiry,_that.sessionStart);case AuthStateUnauthenticated() when unauthenticated != null:
return unauthenticated(_that.reason);case AuthStateError() when error != null:
return error(_that.message,_that.errorCode,_that.errorType,_that.isRecoverable,_that.originalException);case AuthStateEmailVerificationPending() when emailVerificationPending != null:
return emailVerificationPending(_that.email,_that.sentAt);case AuthStateSessionExpired() when sessionExpired != null:
return sessionExpired(_that.expiredAt,_that.autoRefreshAttempted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  initial,required TResult Function( String? message,  AuthOperation operation)  loading,required TResult Function( User user,  String token,  String? refreshToken, @JsonKey(name: 'token_expiry')  DateTime? tokenExpiry, @JsonKey(name: 'session_start')  DateTime? sessionStart)  authenticated,required TResult Function( String? reason)  unauthenticated,required TResult Function( String message,  String? errorCode,  AuthErrorType errorType,  bool isRecoverable,  Object? originalException)  error,required TResult Function( String email,  DateTime? sentAt)  emailVerificationPending,required TResult Function( DateTime? expiredAt,  bool autoRefreshAttempted)  sessionExpired,}) {final _that = this;
switch (_that) {
case AuthStateInitial():
return initial();case AuthStateLoading():
return loading(_that.message,_that.operation);case AuthStateAuthenticated():
return authenticated(_that.user,_that.token,_that.refreshToken,_that.tokenExpiry,_that.sessionStart);case AuthStateUnauthenticated():
return unauthenticated(_that.reason);case AuthStateError():
return error(_that.message,_that.errorCode,_that.errorType,_that.isRecoverable,_that.originalException);case AuthStateEmailVerificationPending():
return emailVerificationPending(_that.email,_that.sentAt);case AuthStateSessionExpired():
return sessionExpired(_that.expiredAt,_that.autoRefreshAttempted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  initial,TResult? Function( String? message,  AuthOperation operation)?  loading,TResult? Function( User user,  String token,  String? refreshToken, @JsonKey(name: 'token_expiry')  DateTime? tokenExpiry, @JsonKey(name: 'session_start')  DateTime? sessionStart)?  authenticated,TResult? Function( String? reason)?  unauthenticated,TResult? Function( String message,  String? errorCode,  AuthErrorType errorType,  bool isRecoverable,  Object? originalException)?  error,TResult? Function( String email,  DateTime? sentAt)?  emailVerificationPending,TResult? Function( DateTime? expiredAt,  bool autoRefreshAttempted)?  sessionExpired,}) {final _that = this;
switch (_that) {
case AuthStateInitial() when initial != null:
return initial();case AuthStateLoading() when loading != null:
return loading(_that.message,_that.operation);case AuthStateAuthenticated() when authenticated != null:
return authenticated(_that.user,_that.token,_that.refreshToken,_that.tokenExpiry,_that.sessionStart);case AuthStateUnauthenticated() when unauthenticated != null:
return unauthenticated(_that.reason);case AuthStateError() when error != null:
return error(_that.message,_that.errorCode,_that.errorType,_that.isRecoverable,_that.originalException);case AuthStateEmailVerificationPending() when emailVerificationPending != null:
return emailVerificationPending(_that.email,_that.sentAt);case AuthStateSessionExpired() when sessionExpired != null:
return sessionExpired(_that.expiredAt,_that.autoRefreshAttempted);case _:
  return null;

}
}

}

/// @nodoc


class AuthStateInitial with DiagnosticableTreeMixin implements AuthState {
  const AuthStateInitial();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthState.initial'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthStateInitial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthState.initial()';
}


}




/// @nodoc


class AuthStateLoading with DiagnosticableTreeMixin implements AuthState {
  const AuthStateLoading({this.message, this.operation = AuthOperation.signIn});
  

/// Optional message to show during loading
 final  String? message;
/// Type of operation being performed
@JsonKey() final  AuthOperation operation;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthStateLoadingCopyWith<AuthStateLoading> get copyWith => _$AuthStateLoadingCopyWithImpl<AuthStateLoading>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthState.loading'))
    ..add(DiagnosticsProperty('message', message))..add(DiagnosticsProperty('operation', operation));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthStateLoading&&(identical(other.message, message) || other.message == message)&&(identical(other.operation, operation) || other.operation == operation));
}


@override
int get hashCode => Object.hash(runtimeType,message,operation);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthState.loading(message: $message, operation: $operation)';
}


}

/// @nodoc
abstract mixin class $AuthStateLoadingCopyWith<$Res> implements $AuthStateCopyWith<$Res> {
  factory $AuthStateLoadingCopyWith(AuthStateLoading value, $Res Function(AuthStateLoading) _then) = _$AuthStateLoadingCopyWithImpl;
@useResult
$Res call({
 String? message, AuthOperation operation
});




}
/// @nodoc
class _$AuthStateLoadingCopyWithImpl<$Res>
    implements $AuthStateLoadingCopyWith<$Res> {
  _$AuthStateLoadingCopyWithImpl(this._self, this._then);

  final AuthStateLoading _self;
  final $Res Function(AuthStateLoading) _then;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,Object? operation = null,}) {
  return _then(AuthStateLoading(
message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,operation: null == operation ? _self.operation : operation // ignore: cast_nullable_to_non_nullable
as AuthOperation,
  ));
}


}

/// @nodoc


class AuthStateAuthenticated with DiagnosticableTreeMixin implements AuthState {
  const AuthStateAuthenticated({required this.user, required this.token, this.refreshToken, @JsonKey(name: 'token_expiry') this.tokenExpiry, @JsonKey(name: 'session_start') this.sessionStart});
  

/// Authenticated user information
 final  User user;
/// JWT access token
 final  String token;
/// Optional refresh token
 final  String? refreshToken;
/// Token expiry timestamp
@JsonKey(name: 'token_expiry') final  DateTime? tokenExpiry;
/// Session start time
@JsonKey(name: 'session_start') final  DateTime? sessionStart;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthStateAuthenticatedCopyWith<AuthStateAuthenticated> get copyWith => _$AuthStateAuthenticatedCopyWithImpl<AuthStateAuthenticated>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthState.authenticated'))
    ..add(DiagnosticsProperty('user', user))..add(DiagnosticsProperty('token', token))..add(DiagnosticsProperty('refreshToken', refreshToken))..add(DiagnosticsProperty('tokenExpiry', tokenExpiry))..add(DiagnosticsProperty('sessionStart', sessionStart));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthStateAuthenticated&&(identical(other.user, user) || other.user == user)&&(identical(other.token, token) || other.token == token)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.tokenExpiry, tokenExpiry) || other.tokenExpiry == tokenExpiry)&&(identical(other.sessionStart, sessionStart) || other.sessionStart == sessionStart));
}


@override
int get hashCode => Object.hash(runtimeType,user,token,refreshToken,tokenExpiry,sessionStart);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthState.authenticated(user: $user, token: $token, refreshToken: $refreshToken, tokenExpiry: $tokenExpiry, sessionStart: $sessionStart)';
}


}

/// @nodoc
abstract mixin class $AuthStateAuthenticatedCopyWith<$Res> implements $AuthStateCopyWith<$Res> {
  factory $AuthStateAuthenticatedCopyWith(AuthStateAuthenticated value, $Res Function(AuthStateAuthenticated) _then) = _$AuthStateAuthenticatedCopyWithImpl;
@useResult
$Res call({
 User user, String token, String? refreshToken,@JsonKey(name: 'token_expiry') DateTime? tokenExpiry,@JsonKey(name: 'session_start') DateTime? sessionStart
});


$UserCopyWith<$Res> get user;

}
/// @nodoc
class _$AuthStateAuthenticatedCopyWithImpl<$Res>
    implements $AuthStateAuthenticatedCopyWith<$Res> {
  _$AuthStateAuthenticatedCopyWithImpl(this._self, this._then);

  final AuthStateAuthenticated _self;
  final $Res Function(AuthStateAuthenticated) _then;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? user = null,Object? token = null,Object? refreshToken = freezed,Object? tokenExpiry = freezed,Object? sessionStart = freezed,}) {
  return _then(AuthStateAuthenticated(
user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,token: null == token ? _self.token : token // ignore: cast_nullable_to_non_nullable
as String,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,tokenExpiry: freezed == tokenExpiry ? _self.tokenExpiry : tokenExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,sessionStart: freezed == sessionStart ? _self.sessionStart : sessionStart // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get user {
  
  return $UserCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}

/// @nodoc


class AuthStateUnauthenticated with DiagnosticableTreeMixin implements AuthState {
  const AuthStateUnauthenticated({this.reason});
  

/// Optional reason for being unauthenticated
 final  String? reason;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthStateUnauthenticatedCopyWith<AuthStateUnauthenticated> get copyWith => _$AuthStateUnauthenticatedCopyWithImpl<AuthStateUnauthenticated>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthState.unauthenticated'))
    ..add(DiagnosticsProperty('reason', reason));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthStateUnauthenticated&&(identical(other.reason, reason) || other.reason == reason));
}


@override
int get hashCode => Object.hash(runtimeType,reason);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthState.unauthenticated(reason: $reason)';
}


}

/// @nodoc
abstract mixin class $AuthStateUnauthenticatedCopyWith<$Res> implements $AuthStateCopyWith<$Res> {
  factory $AuthStateUnauthenticatedCopyWith(AuthStateUnauthenticated value, $Res Function(AuthStateUnauthenticated) _then) = _$AuthStateUnauthenticatedCopyWithImpl;
@useResult
$Res call({
 String? reason
});




}
/// @nodoc
class _$AuthStateUnauthenticatedCopyWithImpl<$Res>
    implements $AuthStateUnauthenticatedCopyWith<$Res> {
  _$AuthStateUnauthenticatedCopyWithImpl(this._self, this._then);

  final AuthStateUnauthenticated _self;
  final $Res Function(AuthStateUnauthenticated) _then;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? reason = freezed,}) {
  return _then(AuthStateUnauthenticated(
reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class AuthStateError with DiagnosticableTreeMixin implements AuthState {
  const AuthStateError({required this.message, this.errorCode, this.errorType = AuthErrorType.unknown, this.isRecoverable = true, this.originalException});
  

/// Error message
 final  String message;
/// Error code for programmatic handling
 final  String? errorCode;
/// Type of error
@JsonKey() final  AuthErrorType errorType;
/// Whether the error is recoverable
@JsonKey() final  bool isRecoverable;
/// Original exception if available
 final  Object? originalException;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthStateErrorCopyWith<AuthStateError> get copyWith => _$AuthStateErrorCopyWithImpl<AuthStateError>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthState.error'))
    ..add(DiagnosticsProperty('message', message))..add(DiagnosticsProperty('errorCode', errorCode))..add(DiagnosticsProperty('errorType', errorType))..add(DiagnosticsProperty('isRecoverable', isRecoverable))..add(DiagnosticsProperty('originalException', originalException));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthStateError&&(identical(other.message, message) || other.message == message)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode)&&(identical(other.errorType, errorType) || other.errorType == errorType)&&(identical(other.isRecoverable, isRecoverable) || other.isRecoverable == isRecoverable)&&const DeepCollectionEquality().equals(other.originalException, originalException));
}


@override
int get hashCode => Object.hash(runtimeType,message,errorCode,errorType,isRecoverable,const DeepCollectionEquality().hash(originalException));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthState.error(message: $message, errorCode: $errorCode, errorType: $errorType, isRecoverable: $isRecoverable, originalException: $originalException)';
}


}

/// @nodoc
abstract mixin class $AuthStateErrorCopyWith<$Res> implements $AuthStateCopyWith<$Res> {
  factory $AuthStateErrorCopyWith(AuthStateError value, $Res Function(AuthStateError) _then) = _$AuthStateErrorCopyWithImpl;
@useResult
$Res call({
 String message, String? errorCode, AuthErrorType errorType, bool isRecoverable, Object? originalException
});




}
/// @nodoc
class _$AuthStateErrorCopyWithImpl<$Res>
    implements $AuthStateErrorCopyWith<$Res> {
  _$AuthStateErrorCopyWithImpl(this._self, this._then);

  final AuthStateError _self;
  final $Res Function(AuthStateError) _then;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,Object? errorCode = freezed,Object? errorType = null,Object? isRecoverable = null,Object? originalException = freezed,}) {
  return _then(AuthStateError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,errorType: null == errorType ? _self.errorType : errorType // ignore: cast_nullable_to_non_nullable
as AuthErrorType,isRecoverable: null == isRecoverable ? _self.isRecoverable : isRecoverable // ignore: cast_nullable_to_non_nullable
as bool,originalException: freezed == originalException ? _self.originalException : originalException ,
  ));
}


}

/// @nodoc


class AuthStateEmailVerificationPending with DiagnosticableTreeMixin implements AuthState {
  const AuthStateEmailVerificationPending({required this.email, this.sentAt});
  

/// User's email that needs verification
 final  String email;
/// When verification email was sent
 final  DateTime? sentAt;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthStateEmailVerificationPendingCopyWith<AuthStateEmailVerificationPending> get copyWith => _$AuthStateEmailVerificationPendingCopyWithImpl<AuthStateEmailVerificationPending>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthState.emailVerificationPending'))
    ..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('sentAt', sentAt));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthStateEmailVerificationPending&&(identical(other.email, email) || other.email == email)&&(identical(other.sentAt, sentAt) || other.sentAt == sentAt));
}


@override
int get hashCode => Object.hash(runtimeType,email,sentAt);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthState.emailVerificationPending(email: $email, sentAt: $sentAt)';
}


}

/// @nodoc
abstract mixin class $AuthStateEmailVerificationPendingCopyWith<$Res> implements $AuthStateCopyWith<$Res> {
  factory $AuthStateEmailVerificationPendingCopyWith(AuthStateEmailVerificationPending value, $Res Function(AuthStateEmailVerificationPending) _then) = _$AuthStateEmailVerificationPendingCopyWithImpl;
@useResult
$Res call({
 String email, DateTime? sentAt
});




}
/// @nodoc
class _$AuthStateEmailVerificationPendingCopyWithImpl<$Res>
    implements $AuthStateEmailVerificationPendingCopyWith<$Res> {
  _$AuthStateEmailVerificationPendingCopyWithImpl(this._self, this._then);

  final AuthStateEmailVerificationPending _self;
  final $Res Function(AuthStateEmailVerificationPending) _then;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? email = null,Object? sentAt = freezed,}) {
  return _then(AuthStateEmailVerificationPending(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,sentAt: freezed == sentAt ? _self.sentAt : sentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

/// @nodoc


class AuthStateSessionExpired with DiagnosticableTreeMixin implements AuthState {
  const AuthStateSessionExpired({this.expiredAt, this.autoRefreshAttempted = false});
  

/// When the session expired
 final  DateTime? expiredAt;
/// Whether auto-refresh was attempted
@JsonKey() final  bool autoRefreshAttempted;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthStateSessionExpiredCopyWith<AuthStateSessionExpired> get copyWith => _$AuthStateSessionExpiredCopyWithImpl<AuthStateSessionExpired>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthState.sessionExpired'))
    ..add(DiagnosticsProperty('expiredAt', expiredAt))..add(DiagnosticsProperty('autoRefreshAttempted', autoRefreshAttempted));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthStateSessionExpired&&(identical(other.expiredAt, expiredAt) || other.expiredAt == expiredAt)&&(identical(other.autoRefreshAttempted, autoRefreshAttempted) || other.autoRefreshAttempted == autoRefreshAttempted));
}


@override
int get hashCode => Object.hash(runtimeType,expiredAt,autoRefreshAttempted);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthState.sessionExpired(expiredAt: $expiredAt, autoRefreshAttempted: $autoRefreshAttempted)';
}


}

/// @nodoc
abstract mixin class $AuthStateSessionExpiredCopyWith<$Res> implements $AuthStateCopyWith<$Res> {
  factory $AuthStateSessionExpiredCopyWith(AuthStateSessionExpired value, $Res Function(AuthStateSessionExpired) _then) = _$AuthStateSessionExpiredCopyWithImpl;
@useResult
$Res call({
 DateTime? expiredAt, bool autoRefreshAttempted
});




}
/// @nodoc
class _$AuthStateSessionExpiredCopyWithImpl<$Res>
    implements $AuthStateSessionExpiredCopyWith<$Res> {
  _$AuthStateSessionExpiredCopyWithImpl(this._self, this._then);

  final AuthStateSessionExpired _self;
  final $Res Function(AuthStateSessionExpired) _then;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? expiredAt = freezed,Object? autoRefreshAttempted = null,}) {
  return _then(AuthStateSessionExpired(
expiredAt: freezed == expiredAt ? _self.expiredAt : expiredAt // ignore: cast_nullable_to_non_nullable
as DateTime?,autoRefreshAttempted: null == autoRefreshAttempted ? _self.autoRefreshAttempted : autoRefreshAttempted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc
mixin _$AuthResult implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthResult'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthResult);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthResult()';
}


}

/// @nodoc
class $AuthResultCopyWith<$Res>  {
$AuthResultCopyWith(AuthResult _, $Res Function(AuthResult) __);
}


/// Adds pattern-matching-related methods to [AuthResult].
extension AuthResultPatterns on AuthResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( AuthResultSuccess value)?  success,TResult Function( AuthResultFailure value)?  failure,TResult Function( AuthResultCancelled value)?  cancelled,TResult Function( AuthResultPending value)?  pending,required TResult orElse(),}){
final _that = this;
switch (_that) {
case AuthResultSuccess() when success != null:
return success(_that);case AuthResultFailure() when failure != null:
return failure(_that);case AuthResultCancelled() when cancelled != null:
return cancelled(_that);case AuthResultPending() when pending != null:
return pending(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( AuthResultSuccess value)  success,required TResult Function( AuthResultFailure value)  failure,required TResult Function( AuthResultCancelled value)  cancelled,required TResult Function( AuthResultPending value)  pending,}){
final _that = this;
switch (_that) {
case AuthResultSuccess():
return success(_that);case AuthResultFailure():
return failure(_that);case AuthResultCancelled():
return cancelled(_that);case AuthResultPending():
return pending(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( AuthResultSuccess value)?  success,TResult? Function( AuthResultFailure value)?  failure,TResult? Function( AuthResultCancelled value)?  cancelled,TResult? Function( AuthResultPending value)?  pending,}){
final _that = this;
switch (_that) {
case AuthResultSuccess() when success != null:
return success(_that);case AuthResultFailure() when failure != null:
return failure(_that);case AuthResultCancelled() when cancelled != null:
return cancelled(_that);case AuthResultPending() when pending != null:
return pending(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( User user,  String token,  String? refreshToken,  DateTime? tokenExpiry,  Map<String, dynamic>? metadata)?  success,TResult Function( String error,  String? errorCode,  AuthErrorType errorType,  bool isRecoverable,  Map<String, dynamic>? details)?  failure,TResult Function( String? reason)?  cancelled,TResult Function( String message,  AuthOperation pendingAction,  Map<String, dynamic>? actionData)?  pending,required TResult orElse(),}) {final _that = this;
switch (_that) {
case AuthResultSuccess() when success != null:
return success(_that.user,_that.token,_that.refreshToken,_that.tokenExpiry,_that.metadata);case AuthResultFailure() when failure != null:
return failure(_that.error,_that.errorCode,_that.errorType,_that.isRecoverable,_that.details);case AuthResultCancelled() when cancelled != null:
return cancelled(_that.reason);case AuthResultPending() when pending != null:
return pending(_that.message,_that.pendingAction,_that.actionData);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( User user,  String token,  String? refreshToken,  DateTime? tokenExpiry,  Map<String, dynamic>? metadata)  success,required TResult Function( String error,  String? errorCode,  AuthErrorType errorType,  bool isRecoverable,  Map<String, dynamic>? details)  failure,required TResult Function( String? reason)  cancelled,required TResult Function( String message,  AuthOperation pendingAction,  Map<String, dynamic>? actionData)  pending,}) {final _that = this;
switch (_that) {
case AuthResultSuccess():
return success(_that.user,_that.token,_that.refreshToken,_that.tokenExpiry,_that.metadata);case AuthResultFailure():
return failure(_that.error,_that.errorCode,_that.errorType,_that.isRecoverable,_that.details);case AuthResultCancelled():
return cancelled(_that.reason);case AuthResultPending():
return pending(_that.message,_that.pendingAction,_that.actionData);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( User user,  String token,  String? refreshToken,  DateTime? tokenExpiry,  Map<String, dynamic>? metadata)?  success,TResult? Function( String error,  String? errorCode,  AuthErrorType errorType,  bool isRecoverable,  Map<String, dynamic>? details)?  failure,TResult? Function( String? reason)?  cancelled,TResult? Function( String message,  AuthOperation pendingAction,  Map<String, dynamic>? actionData)?  pending,}) {final _that = this;
switch (_that) {
case AuthResultSuccess() when success != null:
return success(_that.user,_that.token,_that.refreshToken,_that.tokenExpiry,_that.metadata);case AuthResultFailure() when failure != null:
return failure(_that.error,_that.errorCode,_that.errorType,_that.isRecoverable,_that.details);case AuthResultCancelled() when cancelled != null:
return cancelled(_that.reason);case AuthResultPending() when pending != null:
return pending(_that.message,_that.pendingAction,_that.actionData);case _:
  return null;

}
}

}

/// @nodoc


class AuthResultSuccess with DiagnosticableTreeMixin implements AuthResult {
  const AuthResultSuccess({required this.user, required this.token, this.refreshToken, this.tokenExpiry, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  

/// Authenticated user information
 final  User user;
/// JWT access token
 final  String token;
/// Optional refresh token
 final  String? refreshToken;
/// Token expiry timestamp
 final  DateTime? tokenExpiry;
/// Additional metadata
 final  Map<String, dynamic>? _metadata;
/// Additional metadata
 Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthResultSuccessCopyWith<AuthResultSuccess> get copyWith => _$AuthResultSuccessCopyWithImpl<AuthResultSuccess>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthResult.success'))
    ..add(DiagnosticsProperty('user', user))..add(DiagnosticsProperty('token', token))..add(DiagnosticsProperty('refreshToken', refreshToken))..add(DiagnosticsProperty('tokenExpiry', tokenExpiry))..add(DiagnosticsProperty('metadata', metadata));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthResultSuccess&&(identical(other.user, user) || other.user == user)&&(identical(other.token, token) || other.token == token)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.tokenExpiry, tokenExpiry) || other.tokenExpiry == tokenExpiry)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}


@override
int get hashCode => Object.hash(runtimeType,user,token,refreshToken,tokenExpiry,const DeepCollectionEquality().hash(_metadata));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthResult.success(user: $user, token: $token, refreshToken: $refreshToken, tokenExpiry: $tokenExpiry, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $AuthResultSuccessCopyWith<$Res> implements $AuthResultCopyWith<$Res> {
  factory $AuthResultSuccessCopyWith(AuthResultSuccess value, $Res Function(AuthResultSuccess) _then) = _$AuthResultSuccessCopyWithImpl;
@useResult
$Res call({
 User user, String token, String? refreshToken, DateTime? tokenExpiry, Map<String, dynamic>? metadata
});


$UserCopyWith<$Res> get user;

}
/// @nodoc
class _$AuthResultSuccessCopyWithImpl<$Res>
    implements $AuthResultSuccessCopyWith<$Res> {
  _$AuthResultSuccessCopyWithImpl(this._self, this._then);

  final AuthResultSuccess _self;
  final $Res Function(AuthResultSuccess) _then;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? user = null,Object? token = null,Object? refreshToken = freezed,Object? tokenExpiry = freezed,Object? metadata = freezed,}) {
  return _then(AuthResultSuccess(
user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,token: null == token ? _self.token : token // ignore: cast_nullable_to_non_nullable
as String,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,tokenExpiry: freezed == tokenExpiry ? _self.tokenExpiry : tokenExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get user {
  
  return $UserCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}

/// @nodoc


class AuthResultFailure with DiagnosticableTreeMixin implements AuthResult {
  const AuthResultFailure({required this.error, this.errorCode, this.errorType = AuthErrorType.unknown, this.isRecoverable = true, final  Map<String, dynamic>? details}): _details = details;
  

/// Error message
 final  String error;
/// Error code for programmatic handling
 final  String? errorCode;
/// Type of error
@JsonKey() final  AuthErrorType errorType;
/// Whether the error is recoverable
@JsonKey() final  bool isRecoverable;
/// Additional error details
 final  Map<String, dynamic>? _details;
/// Additional error details
 Map<String, dynamic>? get details {
  final value = _details;
  if (value == null) return null;
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthResultFailureCopyWith<AuthResultFailure> get copyWith => _$AuthResultFailureCopyWithImpl<AuthResultFailure>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthResult.failure'))
    ..add(DiagnosticsProperty('error', error))..add(DiagnosticsProperty('errorCode', errorCode))..add(DiagnosticsProperty('errorType', errorType))..add(DiagnosticsProperty('isRecoverable', isRecoverable))..add(DiagnosticsProperty('details', details));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthResultFailure&&(identical(other.error, error) || other.error == error)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode)&&(identical(other.errorType, errorType) || other.errorType == errorType)&&(identical(other.isRecoverable, isRecoverable) || other.isRecoverable == isRecoverable)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,error,errorCode,errorType,isRecoverable,const DeepCollectionEquality().hash(_details));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthResult.failure(error: $error, errorCode: $errorCode, errorType: $errorType, isRecoverable: $isRecoverable, details: $details)';
}


}

/// @nodoc
abstract mixin class $AuthResultFailureCopyWith<$Res> implements $AuthResultCopyWith<$Res> {
  factory $AuthResultFailureCopyWith(AuthResultFailure value, $Res Function(AuthResultFailure) _then) = _$AuthResultFailureCopyWithImpl;
@useResult
$Res call({
 String error, String? errorCode, AuthErrorType errorType, bool isRecoverable, Map<String, dynamic>? details
});




}
/// @nodoc
class _$AuthResultFailureCopyWithImpl<$Res>
    implements $AuthResultFailureCopyWith<$Res> {
  _$AuthResultFailureCopyWithImpl(this._self, this._then);

  final AuthResultFailure _self;
  final $Res Function(AuthResultFailure) _then;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? error = null,Object? errorCode = freezed,Object? errorType = null,Object? isRecoverable = null,Object? details = freezed,}) {
  return _then(AuthResultFailure(
error: null == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,errorType: null == errorType ? _self.errorType : errorType // ignore: cast_nullable_to_non_nullable
as AuthErrorType,isRecoverable: null == isRecoverable ? _self.isRecoverable : isRecoverable // ignore: cast_nullable_to_non_nullable
as bool,details: freezed == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class AuthResultCancelled with DiagnosticableTreeMixin implements AuthResult {
  const AuthResultCancelled({this.reason});
  

/// Optional reason for cancellation
 final  String? reason;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthResultCancelledCopyWith<AuthResultCancelled> get copyWith => _$AuthResultCancelledCopyWithImpl<AuthResultCancelled>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthResult.cancelled'))
    ..add(DiagnosticsProperty('reason', reason));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthResultCancelled&&(identical(other.reason, reason) || other.reason == reason));
}


@override
int get hashCode => Object.hash(runtimeType,reason);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthResult.cancelled(reason: $reason)';
}


}

/// @nodoc
abstract mixin class $AuthResultCancelledCopyWith<$Res> implements $AuthResultCopyWith<$Res> {
  factory $AuthResultCancelledCopyWith(AuthResultCancelled value, $Res Function(AuthResultCancelled) _then) = _$AuthResultCancelledCopyWithImpl;
@useResult
$Res call({
 String? reason
});




}
/// @nodoc
class _$AuthResultCancelledCopyWithImpl<$Res>
    implements $AuthResultCancelledCopyWith<$Res> {
  _$AuthResultCancelledCopyWithImpl(this._self, this._then);

  final AuthResultCancelled _self;
  final $Res Function(AuthResultCancelled) _then;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? reason = freezed,}) {
  return _then(AuthResultCancelled(
reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class AuthResultPending with DiagnosticableTreeMixin implements AuthResult {
  const AuthResultPending({required this.message, this.pendingAction = AuthOperation.verifyEmail, final  Map<String, dynamic>? actionData}): _actionData = actionData;
  

/// Message describing what action is needed
 final  String message;
/// Type of pending action
@JsonKey() final  AuthOperation pendingAction;
/// Additional data for the pending action
 final  Map<String, dynamic>? _actionData;
/// Additional data for the pending action
 Map<String, dynamic>? get actionData {
  final value = _actionData;
  if (value == null) return null;
  if (_actionData is EqualUnmodifiableMapView) return _actionData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthResultPendingCopyWith<AuthResultPending> get copyWith => _$AuthResultPendingCopyWithImpl<AuthResultPending>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthResult.pending'))
    ..add(DiagnosticsProperty('message', message))..add(DiagnosticsProperty('pendingAction', pendingAction))..add(DiagnosticsProperty('actionData', actionData));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthResultPending&&(identical(other.message, message) || other.message == message)&&(identical(other.pendingAction, pendingAction) || other.pendingAction == pendingAction)&&const DeepCollectionEquality().equals(other._actionData, _actionData));
}


@override
int get hashCode => Object.hash(runtimeType,message,pendingAction,const DeepCollectionEquality().hash(_actionData));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthResult.pending(message: $message, pendingAction: $pendingAction, actionData: $actionData)';
}


}

/// @nodoc
abstract mixin class $AuthResultPendingCopyWith<$Res> implements $AuthResultCopyWith<$Res> {
  factory $AuthResultPendingCopyWith(AuthResultPending value, $Res Function(AuthResultPending) _then) = _$AuthResultPendingCopyWithImpl;
@useResult
$Res call({
 String message, AuthOperation pendingAction, Map<String, dynamic>? actionData
});




}
/// @nodoc
class _$AuthResultPendingCopyWithImpl<$Res>
    implements $AuthResultPendingCopyWith<$Res> {
  _$AuthResultPendingCopyWithImpl(this._self, this._then);

  final AuthResultPending _self;
  final $Res Function(AuthResultPending) _then;

/// Create a copy of AuthResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,Object? pendingAction = null,Object? actionData = freezed,}) {
  return _then(AuthResultPending(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,pendingAction: null == pendingAction ? _self.pendingAction : pendingAction // ignore: cast_nullable_to_non_nullable
as AuthOperation,actionData: freezed == actionData ? _self._actionData : actionData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$AuthData implements DiagnosticableTreeMixin {

 User get user;@JsonKey(name: 'access_token') String get accessToken;@JsonKey(name: 'refresh_token') String? get refreshToken;@JsonKey(name: 'expires_in') int get expiresIn;
/// Create a copy of AuthData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthDataCopyWith<AuthData> get copyWith => _$AuthDataCopyWithImpl<AuthData>(this as AuthData, _$identity);

  /// Serializes this AuthData to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthData'))
    ..add(DiagnosticsProperty('user', user))..add(DiagnosticsProperty('accessToken', accessToken))..add(DiagnosticsProperty('refreshToken', refreshToken))..add(DiagnosticsProperty('expiresIn', expiresIn));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthData&&(identical(other.user, user) || other.user == user)&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.expiresIn, expiresIn) || other.expiresIn == expiresIn));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,user,accessToken,refreshToken,expiresIn);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthData(user: $user, accessToken: $accessToken, refreshToken: $refreshToken, expiresIn: $expiresIn)';
}


}

/// @nodoc
abstract mixin class $AuthDataCopyWith<$Res>  {
  factory $AuthDataCopyWith(AuthData value, $Res Function(AuthData) _then) = _$AuthDataCopyWithImpl;
@useResult
$Res call({
 User user,@JsonKey(name: 'access_token') String accessToken,@JsonKey(name: 'refresh_token') String? refreshToken,@JsonKey(name: 'expires_in') int expiresIn
});


$UserCopyWith<$Res> get user;

}
/// @nodoc
class _$AuthDataCopyWithImpl<$Res>
    implements $AuthDataCopyWith<$Res> {
  _$AuthDataCopyWithImpl(this._self, this._then);

  final AuthData _self;
  final $Res Function(AuthData) _then;

/// Create a copy of AuthData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? user = null,Object? accessToken = null,Object? refreshToken = freezed,Object? expiresIn = null,}) {
  return _then(_self.copyWith(
user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,accessToken: null == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,expiresIn: null == expiresIn ? _self.expiresIn : expiresIn // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of AuthData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get user {
  
  return $UserCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// Adds pattern-matching-related methods to [AuthData].
extension AuthDataPatterns on AuthData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AuthData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AuthData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AuthData value)  $default,){
final _that = this;
switch (_that) {
case _AuthData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AuthData value)?  $default,){
final _that = this;
switch (_that) {
case _AuthData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( User user, @JsonKey(name: 'access_token')  String accessToken, @JsonKey(name: 'refresh_token')  String? refreshToken, @JsonKey(name: 'expires_in')  int expiresIn)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AuthData() when $default != null:
return $default(_that.user,_that.accessToken,_that.refreshToken,_that.expiresIn);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( User user, @JsonKey(name: 'access_token')  String accessToken, @JsonKey(name: 'refresh_token')  String? refreshToken, @JsonKey(name: 'expires_in')  int expiresIn)  $default,) {final _that = this;
switch (_that) {
case _AuthData():
return $default(_that.user,_that.accessToken,_that.refreshToken,_that.expiresIn);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( User user, @JsonKey(name: 'access_token')  String accessToken, @JsonKey(name: 'refresh_token')  String? refreshToken, @JsonKey(name: 'expires_in')  int expiresIn)?  $default,) {final _that = this;
switch (_that) {
case _AuthData() when $default != null:
return $default(_that.user,_that.accessToken,_that.refreshToken,_that.expiresIn);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AuthData with DiagnosticableTreeMixin implements AuthData {
  const _AuthData({required this.user, @JsonKey(name: 'access_token') required this.accessToken, @JsonKey(name: 'refresh_token') this.refreshToken, @JsonKey(name: 'expires_in') required this.expiresIn});
  factory _AuthData.fromJson(Map<String, dynamic> json) => _$AuthDataFromJson(json);

@override final  User user;
@override@JsonKey(name: 'access_token') final  String accessToken;
@override@JsonKey(name: 'refresh_token') final  String? refreshToken;
@override@JsonKey(name: 'expires_in') final  int expiresIn;

/// Create a copy of AuthData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AuthDataCopyWith<_AuthData> get copyWith => __$AuthDataCopyWithImpl<_AuthData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AuthDataToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthData'))
    ..add(DiagnosticsProperty('user', user))..add(DiagnosticsProperty('accessToken', accessToken))..add(DiagnosticsProperty('refreshToken', refreshToken))..add(DiagnosticsProperty('expiresIn', expiresIn));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AuthData&&(identical(other.user, user) || other.user == user)&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.expiresIn, expiresIn) || other.expiresIn == expiresIn));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,user,accessToken,refreshToken,expiresIn);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthData(user: $user, accessToken: $accessToken, refreshToken: $refreshToken, expiresIn: $expiresIn)';
}


}

/// @nodoc
abstract mixin class _$AuthDataCopyWith<$Res> implements $AuthDataCopyWith<$Res> {
  factory _$AuthDataCopyWith(_AuthData value, $Res Function(_AuthData) _then) = __$AuthDataCopyWithImpl;
@override @useResult
$Res call({
 User user,@JsonKey(name: 'access_token') String accessToken,@JsonKey(name: 'refresh_token') String? refreshToken,@JsonKey(name: 'expires_in') int expiresIn
});


@override $UserCopyWith<$Res> get user;

}
/// @nodoc
class __$AuthDataCopyWithImpl<$Res>
    implements _$AuthDataCopyWith<$Res> {
  __$AuthDataCopyWithImpl(this._self, this._then);

  final _AuthData _self;
  final $Res Function(_AuthData) _then;

/// Create a copy of AuthData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? user = null,Object? accessToken = null,Object? refreshToken = freezed,Object? expiresIn = null,}) {
  return _then(_AuthData(
user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,accessToken: null == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,expiresIn: null == expiresIn ? _self.expiresIn : expiresIn // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of AuthData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get user {
  
  return $UserCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}


/// @nodoc
mixin _$LoginRequest implements DiagnosticableTreeMixin {

 String get email; String get password;
/// Create a copy of LoginRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LoginRequestCopyWith<LoginRequest> get copyWith => _$LoginRequestCopyWithImpl<LoginRequest>(this as LoginRequest, _$identity);

  /// Serializes this LoginRequest to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'LoginRequest'))
    ..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('password', password));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoginRequest&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,password);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'LoginRequest(email: $email, password: $password)';
}


}

/// @nodoc
abstract mixin class $LoginRequestCopyWith<$Res>  {
  factory $LoginRequestCopyWith(LoginRequest value, $Res Function(LoginRequest) _then) = _$LoginRequestCopyWithImpl;
@useResult
$Res call({
 String email, String password
});




}
/// @nodoc
class _$LoginRequestCopyWithImpl<$Res>
    implements $LoginRequestCopyWith<$Res> {
  _$LoginRequestCopyWithImpl(this._self, this._then);

  final LoginRequest _self;
  final $Res Function(LoginRequest) _then;

/// Create a copy of LoginRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,Object? password = null,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [LoginRequest].
extension LoginRequestPatterns on LoginRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LoginRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LoginRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LoginRequest value)  $default,){
final _that = this;
switch (_that) {
case _LoginRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LoginRequest value)?  $default,){
final _that = this;
switch (_that) {
case _LoginRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String email,  String password)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LoginRequest() when $default != null:
return $default(_that.email,_that.password);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String email,  String password)  $default,) {final _that = this;
switch (_that) {
case _LoginRequest():
return $default(_that.email,_that.password);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String email,  String password)?  $default,) {final _that = this;
switch (_that) {
case _LoginRequest() when $default != null:
return $default(_that.email,_that.password);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LoginRequest with DiagnosticableTreeMixin implements LoginRequest {
  const _LoginRequest({required this.email, required this.password});
  factory _LoginRequest.fromJson(Map<String, dynamic> json) => _$LoginRequestFromJson(json);

@override final  String email;
@override final  String password;

/// Create a copy of LoginRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LoginRequestCopyWith<_LoginRequest> get copyWith => __$LoginRequestCopyWithImpl<_LoginRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LoginRequestToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'LoginRequest'))
    ..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('password', password));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoginRequest&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,password);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'LoginRequest(email: $email, password: $password)';
}


}

/// @nodoc
abstract mixin class _$LoginRequestCopyWith<$Res> implements $LoginRequestCopyWith<$Res> {
  factory _$LoginRequestCopyWith(_LoginRequest value, $Res Function(_LoginRequest) _then) = __$LoginRequestCopyWithImpl;
@override @useResult
$Res call({
 String email, String password
});




}
/// @nodoc
class __$LoginRequestCopyWithImpl<$Res>
    implements _$LoginRequestCopyWith<$Res> {
  __$LoginRequestCopyWithImpl(this._self, this._then);

  final _LoginRequest _self;
  final $Res Function(_LoginRequest) _then;

/// Create a copy of LoginRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,Object? password = null,}) {
  return _then(_LoginRequest(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$RegisterRequest implements DiagnosticableTreeMixin {

 String get name; String get email; String get password;
/// Create a copy of RegisterRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RegisterRequestCopyWith<RegisterRequest> get copyWith => _$RegisterRequestCopyWithImpl<RegisterRequest>(this as RegisterRequest, _$identity);

  /// Serializes this RegisterRequest to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'RegisterRequest'))
    ..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('password', password));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegisterRequest&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,email,password);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'RegisterRequest(name: $name, email: $email, password: $password)';
}


}

/// @nodoc
abstract mixin class $RegisterRequestCopyWith<$Res>  {
  factory $RegisterRequestCopyWith(RegisterRequest value, $Res Function(RegisterRequest) _then) = _$RegisterRequestCopyWithImpl;
@useResult
$Res call({
 String name, String email, String password
});




}
/// @nodoc
class _$RegisterRequestCopyWithImpl<$Res>
    implements $RegisterRequestCopyWith<$Res> {
  _$RegisterRequestCopyWithImpl(this._self, this._then);

  final RegisterRequest _self;
  final $Res Function(RegisterRequest) _then;

/// Create a copy of RegisterRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? email = null,Object? password = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [RegisterRequest].
extension RegisterRequestPatterns on RegisterRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RegisterRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RegisterRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RegisterRequest value)  $default,){
final _that = this;
switch (_that) {
case _RegisterRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RegisterRequest value)?  $default,){
final _that = this;
switch (_that) {
case _RegisterRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  String email,  String password)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RegisterRequest() when $default != null:
return $default(_that.name,_that.email,_that.password);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  String email,  String password)  $default,) {final _that = this;
switch (_that) {
case _RegisterRequest():
return $default(_that.name,_that.email,_that.password);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  String email,  String password)?  $default,) {final _that = this;
switch (_that) {
case _RegisterRequest() when $default != null:
return $default(_that.name,_that.email,_that.password);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RegisterRequest with DiagnosticableTreeMixin implements RegisterRequest {
  const _RegisterRequest({required this.name, required this.email, required this.password});
  factory _RegisterRequest.fromJson(Map<String, dynamic> json) => _$RegisterRequestFromJson(json);

@override final  String name;
@override final  String email;
@override final  String password;

/// Create a copy of RegisterRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RegisterRequestCopyWith<_RegisterRequest> get copyWith => __$RegisterRequestCopyWithImpl<_RegisterRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RegisterRequestToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'RegisterRequest'))
    ..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('password', password));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RegisterRequest&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,email,password);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'RegisterRequest(name: $name, email: $email, password: $password)';
}


}

/// @nodoc
abstract mixin class _$RegisterRequestCopyWith<$Res> implements $RegisterRequestCopyWith<$Res> {
  factory _$RegisterRequestCopyWith(_RegisterRequest value, $Res Function(_RegisterRequest) _then) = __$RegisterRequestCopyWithImpl;
@override @useResult
$Res call({
 String name, String email, String password
});




}
/// @nodoc
class __$RegisterRequestCopyWithImpl<$Res>
    implements _$RegisterRequestCopyWith<$Res> {
  __$RegisterRequestCopyWithImpl(this._self, this._then);

  final _RegisterRequest _self;
  final $Res Function(_RegisterRequest) _then;

/// Create a copy of RegisterRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? email = null,Object? password = null,}) {
  return _then(_RegisterRequest(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$GoogleAuthRequest implements DiagnosticableTreeMixin {

@JsonKey(name: 'id_token') String get idToken;
/// Create a copy of GoogleAuthRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GoogleAuthRequestCopyWith<GoogleAuthRequest> get copyWith => _$GoogleAuthRequestCopyWithImpl<GoogleAuthRequest>(this as GoogleAuthRequest, _$identity);

  /// Serializes this GoogleAuthRequest to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'GoogleAuthRequest'))
    ..add(DiagnosticsProperty('idToken', idToken));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GoogleAuthRequest&&(identical(other.idToken, idToken) || other.idToken == idToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,idToken);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'GoogleAuthRequest(idToken: $idToken)';
}


}

/// @nodoc
abstract mixin class $GoogleAuthRequestCopyWith<$Res>  {
  factory $GoogleAuthRequestCopyWith(GoogleAuthRequest value, $Res Function(GoogleAuthRequest) _then) = _$GoogleAuthRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'id_token') String idToken
});




}
/// @nodoc
class _$GoogleAuthRequestCopyWithImpl<$Res>
    implements $GoogleAuthRequestCopyWith<$Res> {
  _$GoogleAuthRequestCopyWithImpl(this._self, this._then);

  final GoogleAuthRequest _self;
  final $Res Function(GoogleAuthRequest) _then;

/// Create a copy of GoogleAuthRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? idToken = null,}) {
  return _then(_self.copyWith(
idToken: null == idToken ? _self.idToken : idToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [GoogleAuthRequest].
extension GoogleAuthRequestPatterns on GoogleAuthRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _GoogleAuthRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _GoogleAuthRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _GoogleAuthRequest value)  $default,){
final _that = this;
switch (_that) {
case _GoogleAuthRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _GoogleAuthRequest value)?  $default,){
final _that = this;
switch (_that) {
case _GoogleAuthRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'id_token')  String idToken)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _GoogleAuthRequest() when $default != null:
return $default(_that.idToken);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'id_token')  String idToken)  $default,) {final _that = this;
switch (_that) {
case _GoogleAuthRequest():
return $default(_that.idToken);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'id_token')  String idToken)?  $default,) {final _that = this;
switch (_that) {
case _GoogleAuthRequest() when $default != null:
return $default(_that.idToken);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _GoogleAuthRequest with DiagnosticableTreeMixin implements GoogleAuthRequest {
  const _GoogleAuthRequest({@JsonKey(name: 'id_token') required this.idToken});
  factory _GoogleAuthRequest.fromJson(Map<String, dynamic> json) => _$GoogleAuthRequestFromJson(json);

@override@JsonKey(name: 'id_token') final  String idToken;

/// Create a copy of GoogleAuthRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GoogleAuthRequestCopyWith<_GoogleAuthRequest> get copyWith => __$GoogleAuthRequestCopyWithImpl<_GoogleAuthRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GoogleAuthRequestToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'GoogleAuthRequest'))
    ..add(DiagnosticsProperty('idToken', idToken));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoogleAuthRequest&&(identical(other.idToken, idToken) || other.idToken == idToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,idToken);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'GoogleAuthRequest(idToken: $idToken)';
}


}

/// @nodoc
abstract mixin class _$GoogleAuthRequestCopyWith<$Res> implements $GoogleAuthRequestCopyWith<$Res> {
  factory _$GoogleAuthRequestCopyWith(_GoogleAuthRequest value, $Res Function(_GoogleAuthRequest) _then) = __$GoogleAuthRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'id_token') String idToken
});




}
/// @nodoc
class __$GoogleAuthRequestCopyWithImpl<$Res>
    implements _$GoogleAuthRequestCopyWith<$Res> {
  __$GoogleAuthRequestCopyWithImpl(this._self, this._then);

  final _GoogleAuthRequest _self;
  final $Res Function(_GoogleAuthRequest) _then;

/// Create a copy of GoogleAuthRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? idToken = null,}) {
  return _then(_GoogleAuthRequest(
idToken: null == idToken ? _self.idToken : idToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$RefreshTokenRequest implements DiagnosticableTreeMixin {

@JsonKey(name: 'refresh_token') String get refreshToken;
/// Create a copy of RefreshTokenRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RefreshTokenRequestCopyWith<RefreshTokenRequest> get copyWith => _$RefreshTokenRequestCopyWithImpl<RefreshTokenRequest>(this as RefreshTokenRequest, _$identity);

  /// Serializes this RefreshTokenRequest to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'RefreshTokenRequest'))
    ..add(DiagnosticsProperty('refreshToken', refreshToken));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RefreshTokenRequest&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,refreshToken);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'RefreshTokenRequest(refreshToken: $refreshToken)';
}


}

/// @nodoc
abstract mixin class $RefreshTokenRequestCopyWith<$Res>  {
  factory $RefreshTokenRequestCopyWith(RefreshTokenRequest value, $Res Function(RefreshTokenRequest) _then) = _$RefreshTokenRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'refresh_token') String refreshToken
});




}
/// @nodoc
class _$RefreshTokenRequestCopyWithImpl<$Res>
    implements $RefreshTokenRequestCopyWith<$Res> {
  _$RefreshTokenRequestCopyWithImpl(this._self, this._then);

  final RefreshTokenRequest _self;
  final $Res Function(RefreshTokenRequest) _then;

/// Create a copy of RefreshTokenRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? refreshToken = null,}) {
  return _then(_self.copyWith(
refreshToken: null == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [RefreshTokenRequest].
extension RefreshTokenRequestPatterns on RefreshTokenRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RefreshTokenRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RefreshTokenRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RefreshTokenRequest value)  $default,){
final _that = this;
switch (_that) {
case _RefreshTokenRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RefreshTokenRequest value)?  $default,){
final _that = this;
switch (_that) {
case _RefreshTokenRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'refresh_token')  String refreshToken)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RefreshTokenRequest() when $default != null:
return $default(_that.refreshToken);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'refresh_token')  String refreshToken)  $default,) {final _that = this;
switch (_that) {
case _RefreshTokenRequest():
return $default(_that.refreshToken);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'refresh_token')  String refreshToken)?  $default,) {final _that = this;
switch (_that) {
case _RefreshTokenRequest() when $default != null:
return $default(_that.refreshToken);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RefreshTokenRequest with DiagnosticableTreeMixin implements RefreshTokenRequest {
  const _RefreshTokenRequest({@JsonKey(name: 'refresh_token') required this.refreshToken});
  factory _RefreshTokenRequest.fromJson(Map<String, dynamic> json) => _$RefreshTokenRequestFromJson(json);

@override@JsonKey(name: 'refresh_token') final  String refreshToken;

/// Create a copy of RefreshTokenRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RefreshTokenRequestCopyWith<_RefreshTokenRequest> get copyWith => __$RefreshTokenRequestCopyWithImpl<_RefreshTokenRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RefreshTokenRequestToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'RefreshTokenRequest'))
    ..add(DiagnosticsProperty('refreshToken', refreshToken));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RefreshTokenRequest&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,refreshToken);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'RefreshTokenRequest(refreshToken: $refreshToken)';
}


}

/// @nodoc
abstract mixin class _$RefreshTokenRequestCopyWith<$Res> implements $RefreshTokenRequestCopyWith<$Res> {
  factory _$RefreshTokenRequestCopyWith(_RefreshTokenRequest value, $Res Function(_RefreshTokenRequest) _then) = __$RefreshTokenRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'refresh_token') String refreshToken
});




}
/// @nodoc
class __$RefreshTokenRequestCopyWithImpl<$Res>
    implements _$RefreshTokenRequestCopyWith<$Res> {
  __$RefreshTokenRequestCopyWithImpl(this._self, this._then);

  final _RefreshTokenRequest _self;
  final $Res Function(_RefreshTokenRequest) _then;

/// Create a copy of RefreshTokenRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? refreshToken = null,}) {
  return _then(_RefreshTokenRequest(
refreshToken: null == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$AuthResponse implements DiagnosticableTreeMixin {

 bool get success; Map<String, dynamic>? get data; String? get error;@JsonKey(name: 'error_code') String? get errorCode;
/// Create a copy of AuthResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthResponseCopyWith<AuthResponse> get copyWith => _$AuthResponseCopyWithImpl<AuthResponse>(this as AuthResponse, _$identity);

  /// Serializes this AuthResponse to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthResponse'))
    ..add(DiagnosticsProperty('success', success))..add(DiagnosticsProperty('data', data))..add(DiagnosticsProperty('error', error))..add(DiagnosticsProperty('errorCode', errorCode));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthResponse&&(identical(other.success, success) || other.success == success)&&const DeepCollectionEquality().equals(other.data, data)&&(identical(other.error, error) || other.error == error)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,const DeepCollectionEquality().hash(data),error,errorCode);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthResponse(success: $success, data: $data, error: $error, errorCode: $errorCode)';
}


}

/// @nodoc
abstract mixin class $AuthResponseCopyWith<$Res>  {
  factory $AuthResponseCopyWith(AuthResponse value, $Res Function(AuthResponse) _then) = _$AuthResponseCopyWithImpl;
@useResult
$Res call({
 bool success, Map<String, dynamic>? data, String? error,@JsonKey(name: 'error_code') String? errorCode
});




}
/// @nodoc
class _$AuthResponseCopyWithImpl<$Res>
    implements $AuthResponseCopyWith<$Res> {
  _$AuthResponseCopyWithImpl(this._self, this._then);

  final AuthResponse _self;
  final $Res Function(AuthResponse) _then;

/// Create a copy of AuthResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? data = freezed,Object? error = freezed,Object? errorCode = freezed,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [AuthResponse].
extension AuthResponsePatterns on AuthResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AuthResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AuthResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AuthResponse value)  $default,){
final _that = this;
switch (_that) {
case _AuthResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AuthResponse value)?  $default,){
final _that = this;
switch (_that) {
case _AuthResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool success,  Map<String, dynamic>? data,  String? error, @JsonKey(name: 'error_code')  String? errorCode)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AuthResponse() when $default != null:
return $default(_that.success,_that.data,_that.error,_that.errorCode);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool success,  Map<String, dynamic>? data,  String? error, @JsonKey(name: 'error_code')  String? errorCode)  $default,) {final _that = this;
switch (_that) {
case _AuthResponse():
return $default(_that.success,_that.data,_that.error,_that.errorCode);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool success,  Map<String, dynamic>? data,  String? error, @JsonKey(name: 'error_code')  String? errorCode)?  $default,) {final _that = this;
switch (_that) {
case _AuthResponse() when $default != null:
return $default(_that.success,_that.data,_that.error,_that.errorCode);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AuthResponse with DiagnosticableTreeMixin implements AuthResponse {
  const _AuthResponse({required this.success, final  Map<String, dynamic>? data, this.error, @JsonKey(name: 'error_code') this.errorCode}): _data = data;
  factory _AuthResponse.fromJson(Map<String, dynamic> json) => _$AuthResponseFromJson(json);

@override final  bool success;
 final  Map<String, dynamic>? _data;
@override Map<String, dynamic>? get data {
  final value = _data;
  if (value == null) return null;
  if (_data is EqualUnmodifiableMapView) return _data;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  String? error;
@override@JsonKey(name: 'error_code') final  String? errorCode;

/// Create a copy of AuthResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AuthResponseCopyWith<_AuthResponse> get copyWith => __$AuthResponseCopyWithImpl<_AuthResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AuthResponseToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthResponse'))
    ..add(DiagnosticsProperty('success', success))..add(DiagnosticsProperty('data', data))..add(DiagnosticsProperty('error', error))..add(DiagnosticsProperty('errorCode', errorCode));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AuthResponse&&(identical(other.success, success) || other.success == success)&&const DeepCollectionEquality().equals(other._data, _data)&&(identical(other.error, error) || other.error == error)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,const DeepCollectionEquality().hash(_data),error,errorCode);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthResponse(success: $success, data: $data, error: $error, errorCode: $errorCode)';
}


}

/// @nodoc
abstract mixin class _$AuthResponseCopyWith<$Res> implements $AuthResponseCopyWith<$Res> {
  factory _$AuthResponseCopyWith(_AuthResponse value, $Res Function(_AuthResponse) _then) = __$AuthResponseCopyWithImpl;
@override @useResult
$Res call({
 bool success, Map<String, dynamic>? data, String? error,@JsonKey(name: 'error_code') String? errorCode
});




}
/// @nodoc
class __$AuthResponseCopyWithImpl<$Res>
    implements _$AuthResponseCopyWith<$Res> {
  __$AuthResponseCopyWithImpl(this._self, this._then);

  final _AuthResponse _self;
  final $Res Function(_AuthResponse) _then;

/// Create a copy of AuthResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? data = freezed,Object? error = freezed,Object? errorCode = freezed,}) {
  return _then(_AuthResponse(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: freezed == data ? _self._data : data // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on

import 'auth_interfaces.dart';

/// Implementation of authentication validation
class AuthValidator implements IAuthValidator {
  /// Email validation regex pattern
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  /// Name validation regex pattern (allows Arabic and English characters)
  static final RegExp _nameRegex = RegExp(r'^[\u0600-\u06FFa-zA-Z\s]{2,50}$');

  @override
  bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    return _emailRegex.hasMatch(email.trim());
  }

  @override
  PasswordValidationResult validatePassword(String password) {
    final errors = <String>[];

    if (password.isEmpty) {
      errors.add('Password is required');
      return PasswordValidationResult(isValid: false, errors: errors);
    }

    if (password.length < 8) {
      errors.add('Password must be at least 8 characters long');
    }

    if (!password.contains(RegExp(r'[A-Z]'))) {
      errors.add('Password must contain at least one uppercase letter');
    }

    if (!password.contains(RegExp(r'[a-z]'))) {
      errors.add('Password must contain at least one lowercase letter');
    }

    if (!password.contains(RegExp(r'[0-9]'))) {
      errors.add('Password must contain at least one number');
    }

    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      errors.add('Password must contain at least one special character');
    }

    return PasswordValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  @override
  bool isValidName(String name) {
    if (name.isEmpty) return false;
    final trimmedName = name.trim();
    if (trimmedName.length < 2 || trimmedName.length > 50) return false;
    return _nameRegex.hasMatch(trimmedName);
  }

  /// Validate login request
  List<String> validateLoginRequest(String email, String password) {
    final errors = <String>[];

    if (!isValidEmail(email)) {
      errors.add('Please enter a valid email address');
    }

    if (password.isEmpty) {
      errors.add('Password is required');
    }

    return errors;
  }

  /// Validate registration request
  List<String> validateRegisterRequest(
    String name,
    String email,
    String password,
  ) {
    final errors = <String>[];

    if (!isValidName(name)) {
      errors.add('Please enter a valid name (2-50 characters)');
    }

    if (!isValidEmail(email)) {
      errors.add('Please enter a valid email address');
    }

    final passwordResult = validatePassword(password);
    if (!passwordResult.isValid) {
      errors.addAll(passwordResult.errors);
    }

    return errors;
  }
}

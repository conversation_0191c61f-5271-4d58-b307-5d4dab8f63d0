/// ============================================================================
/// AUTHENTICATION INTERFACES - Forever Plan Architecture
/// ============================================================================
///
/// واجهات المصادقة - بنية الخطة الدائمة
/// Clear system boundaries and contracts for authentication operations
///
/// Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ✅ Defines clear authentication contracts
/// ✅ Production-ready interface design
/// ✅ Comprehensive error handling
/// ✅ Type-safe operations
/// ============================================================================
library;

import 'dart:async';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
import 'auth_models.dart';

part 'auth_interfaces.freezed.dart';

// =============================================================================
// CORE AUTH SERVICE INTERFACE
// =============================================================================

/// Enhanced abstract interface for authentication operations
/// Provides comprehensive authentication functionality with type safety
abstract interface class IAuthService {
  // ---------------------------------------------------------------------------
  // Authentication Operations
  // ---------------------------------------------------------------------------
  
  /// Sign in with email and password
  /// Returns [AuthResult] with user data on success or error details on failure
  Future<AuthResult> signInWithEmail(String email, String password);

  /// Sign in with Google OAuth
  /// Returns [AuthResult] with user data on success or error details on failure
  Future<AuthResult> signInWithGoogle();

  /// Register new user with email and password
  /// Returns [AuthResult] with user data on success or error details on failure
  Future<AuthResult> signUp({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  });

  /// Sign out current user
  /// Clears all tokens and resets authentication state
  Future<void> signOut();

  /// Refresh authentication token
  /// Returns [AuthResult] with new token on success
  Future<AuthResult> refreshToken();
  
  /// Verify email address
  /// Returns [AuthResult] indicating verification status
  Future<AuthResult> verifyEmail(String verificationCode);
  
  /// Request password reset
  /// Returns [AuthResult] indicating if reset email was sent
  Future<AuthResult> requestPasswordReset(String email);
  
  /// Reset password with token
  /// Returns [AuthResult] indicating if password was reset successfully
  Future<AuthResult> resetPassword({
    required String token,
    required String newPassword,
  });
  
  /// Update user profile
  /// Returns [AuthResult] with updated user data
  Future<AuthResult> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? avatarUrl,
  });
  
  /// Change user password
  /// Returns [AuthResult] indicating success or failure
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  });
  
  /// Delete user account
  /// Returns [AuthResult] indicating success or failure
  Future<AuthResult> deleteAccount(String password);

  // ---------------------------------------------------------------------------
  // State Management
  // ---------------------------------------------------------------------------
  
  /// Get current authentication state
  AuthState get currentState;

  /// Stream of authentication state changes
  Stream<AuthState> get authStateStream;
  
  /// Get current authenticated user (null if not authenticated)
  User? get currentUser;
  
  /// Check if user is currently authenticated
  bool get isAuthenticated;
  
  /// Check if current session is valid
  bool get isSessionValid;
  
  /// Get time until token expires (null if no token or no expiry)
  Duration? get timeUntilTokenExpiry;

  // ---------------------------------------------------------------------------
  // Session Management
  // ---------------------------------------------------------------------------
  
  /// Initialize authentication service
  /// Checks for existing tokens and validates session
  Future<void> initialize();
  
  /// Validate current session
  /// Returns true if session is valid, false otherwise
  Future<bool> validateSession();
  
  /// Clear all authentication data
  /// Removes tokens, user data, and resets state
  Future<void> clearAuthData();
}

// =============================================================================
// TOKEN STORAGE INTERFACE
// =============================================================================

/// Enhanced abstract interface for secure token storage
/// Provides comprehensive token management with security features
abstract interface class ITokenStorage {
  // ---------------------------------------------------------------------------
  // Token Storage Operations
  // ---------------------------------------------------------------------------
  
  /// Store authentication token securely with encryption
  /// [token] - JWT access token to store
  /// [expiryDate] - Optional expiry date for automatic cleanup
  Future<void> storeToken(String token, {DateTime? expiryDate});

  /// Store refresh token securely with encryption
  /// [refreshToken] - Refresh token to store
  /// [expiryDate] - Optional expiry date for automatic cleanup
  Future<void> storeRefreshToken(String refreshToken, {DateTime? expiryDate});
  
  /// Store user session data securely
  /// [sessionData] - Additional session information
  Future<void> storeSessionData(Map<String, dynamic> sessionData);
  
  /// Store token expiry timestamp
  /// [expiryDate] - When the current token expires
  Future<void> storeTokenExpiry(DateTime expiryDate);

  // ---------------------------------------------------------------------------
  // Token Retrieval Operations
  // ---------------------------------------------------------------------------
  
  /// Retrieve stored authentication token
  /// Returns null if no token exists or token is expired
  Future<String?> getToken();

  /// Retrieve stored refresh token
  /// Returns null if no refresh token exists
  Future<String?> getRefreshToken();
  
  /// Retrieve stored session data
  /// Returns empty map if no session data exists
  Future<Map<String, dynamic>> getSessionData();
  
  /// Retrieve token expiry timestamp
  /// Returns null if no expiry is set
  Future<DateTime?> getTokenExpiry();

  // ---------------------------------------------------------------------------
  // Token Validation Operations
  // ---------------------------------------------------------------------------
  
  /// Check if authentication token exists and is valid
  /// Validates both existence and expiry
  Future<bool> hasValidToken();

  /// Check if refresh token exists and is valid
  /// Validates both existence and expiry
  Future<bool> hasValidRefreshToken();
  
  /// Check if token is expired
  /// Returns true if token exists but is expired
  Future<bool> isTokenExpired();
  
  /// Check if token exists and is valid (not expired)
  /// [token] - Token to validate
  /// Returns true if token is valid and not expired
  Future<bool> isTokenValid(String token);
  
  /// Get time until token expires
  /// Returns null if no token or no expiry set
  Future<Duration?> getTimeUntilExpiry();

  // ---------------------------------------------------------------------------
  // Token Management Operations
  // ---------------------------------------------------------------------------
  
  /// Clear all stored authentication data
  /// Removes tokens, session data, and expiry information
  Future<void> clearAllData();
  
  /// Clear only expired tokens
  /// Removes tokens that have passed their expiry date
  Future<void> clearExpiredTokens();
  
  /// Update token expiry without changing the token
  /// [newExpiryDate] - New expiry date for the current token
  Future<void> updateTokenExpiry(DateTime newExpiryDate);
  
  /// Rotate tokens (replace old with new)
  /// [newToken] - New access token
  /// [newRefreshToken] - New refresh token (optional)
  /// [newExpiryDate] - New expiry date (optional)
  Future<void> rotateTokens({
    required String newToken,
    String? newRefreshToken,
    DateTime? newExpiryDate,
  });

  // ---------------------------------------------------------------------------
  // Security Operations
  // ---------------------------------------------------------------------------
  
  /// Validate token storage integrity
  /// Returns true if stored data is valid and uncorrupted
  Future<bool> validateStorageIntegrity();
  
  /// Get storage metadata (last access, creation time, etc.)
  /// Returns metadata about the stored tokens
  Future<Map<String, dynamic>> getStorageMetadata();
  
  /// Check if storage is encrypted
  /// Returns true if tokens are stored with encryption
  bool get isEncrypted;
  
  /// Check if storage supports biometric protection
  /// Returns true if biometric authentication is available
  Future<bool> get supportsBiometrics;
}

// =============================================================================
// API CLIENT INTERFACE
// =============================================================================

/// Enhanced abstract interface for authentication API client
/// Provides comprehensive API communication with the Go backend
abstract interface class IAuthApiClient {
  // ---------------------------------------------------------------------------
  // Authentication Endpoints
  // ---------------------------------------------------------------------------
  
  /// Login with email and password
  /// [request] - Login credentials
  /// Returns [AuthData] with user info and tokens on success
  Future<AuthData> login(LoginRequest request);

  /// Register new user account
  /// [request] - Registration information
  /// Returns [AuthData] with user info and tokens on success
  Future<AuthData> register(RegisterRequest request);

  /// Authenticate with Google OAuth token
  /// [request] - Google OAuth ID token
  /// Returns [AuthData] with user info and tokens on success
  Future<AuthData> googleAuth(GoogleAuthRequest request);

  /// Refresh authentication token
  /// [request] - Refresh token request
  /// Returns [AuthData] with new tokens on success
  Future<AuthData> refreshToken(RefreshTokenRequest request);

  /// Logout current user session
  /// Invalidates tokens on the server side
  Future<void> logout();
  
  /// Verify email address with verification code
  /// [email] - User's email address
  /// [code] - Verification code from email
  /// Returns success status
  Future<Map<String, dynamic>> verifyEmail(String email, String code);
  
  /// Request password reset email
  /// [email] - User's email address
  /// Returns success status
  Future<Map<String, dynamic>> requestPasswordReset(String email);
  
  /// Reset password with reset token
  /// [token] - Password reset token
  /// [newPassword] - New password
  /// Returns success status
  Future<Map<String, dynamic>> resetPassword(String token, String newPassword);

  // ---------------------------------------------------------------------------
  // User Profile Endpoints
  // ---------------------------------------------------------------------------
  
  /// Get current user profile
  /// Returns [User] data for authenticated user
  Future<User> getProfile();
  
  /// Update user profile information
  /// [updates] - Map of fields to update
  /// Returns updated [User] data
  Future<User> updateProfile(Map<String, dynamic> updates);
  
  /// Change user password
  /// [currentPassword] - Current password for verification
  /// [newPassword] - New password
  /// Returns success status
  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
  });
  
  /// Delete user account
  /// [password] - Password for verification
  /// Returns success status
  Future<Map<String, dynamic>> deleteAccount(String password);

  // ---------------------------------------------------------------------------
  // Session Management Endpoints
  // ---------------------------------------------------------------------------
  
  /// Validate current session
  /// Returns session validity status
  Future<Map<String, dynamic>> validateSession();
  
  /// Get active sessions for user
  /// Returns list of active sessions
  Future<List<Map<String, dynamic>>> getActiveSessions();
  
  /// Revoke specific session
  /// [sessionId] - ID of session to revoke
  /// Returns success status
  Future<Map<String, dynamic>> revokeSession(String sessionId);
  
  /// Revoke all sessions except current
  /// Returns success status
  Future<Map<String, dynamic>> revokeAllOtherSessions();

  // ---------------------------------------------------------------------------
  // Configuration and Health
  // ---------------------------------------------------------------------------
  
  /// Get API health status
  /// Returns server health information
  Future<Map<String, dynamic>> getHealthStatus();
  
  /// Get API configuration
  /// Returns client configuration from server
  Future<Map<String, dynamic>> getConfiguration();
  
  /// Test API connectivity
  /// Returns connectivity status
  Future<bool> testConnection();
}

// =============================================================================
// GOOGLE OAUTH INTERFACE
// =============================================================================

/// Enhanced abstract interface for Google OAuth operations
/// Provides comprehensive Google authentication functionality
abstract interface class IGoogleAuthService {
  // ---------------------------------------------------------------------------
  // OAuth Operations
  // ---------------------------------------------------------------------------
  
  /// Sign in with Google OAuth
  /// Returns [GoogleAuthResult] with OAuth data on success
  Future<GoogleAuthResult> signIn();

  /// Sign out from Google OAuth
  /// Clears Google session and tokens
  Future<void> signOut();
  
  /// Silent sign in (if user previously signed in)
  /// Returns [GoogleAuthResult] if successful, null if not possible
  Future<GoogleAuthResult?> signInSilently();
  
  /// Disconnect from Google (revoke access)
  /// Completely removes app access to Google account
  Future<void> disconnect();

  // ---------------------------------------------------------------------------
  // State Management
  // ---------------------------------------------------------------------------
  
  /// Check if user is currently signed in to Google
  /// Returns true if valid Google session exists
  Future<bool> isSignedIn();

  /// Get current Google user information
  /// Returns [GoogleUserInfo] if signed in, null otherwise
  Future<GoogleUserInfo?> getCurrentUser();
  
  /// Get current Google access token
  /// Returns access token if available, null otherwise
  Future<String?> getAccessToken();
  
  /// Get current Google ID token
  /// Returns ID token if available, null otherwise
  Future<String?> getIdToken();

  // ---------------------------------------------------------------------------
  // Configuration
  // ---------------------------------------------------------------------------
  
  /// Initialize Google OAuth service
  /// [clientId] - Google OAuth client ID
  /// [scopes] - List of OAuth scopes to request
  Future<void> initialize({
    required String clientId,
    List<String> scopes = const ['email', 'profile'],
  });
  
  /// Check if Google OAuth is properly configured
  /// Returns true if service is ready to use
  bool get isConfigured;
  
  /// Get configured OAuth scopes
  /// Returns list of currently configured scopes
  List<String> get configuredScopes;
}

// =============================================================================
// GOOGLE AUTH MODELS
// =============================================================================

/// Google authentication result with pattern matching support
@freezed
abstract class GoogleAuthResult with _$GoogleAuthResult {
  /// Successful Google authentication
  const factory GoogleAuthResult.success({
    /// Google ID token
    required String idToken,
    /// Google access token (optional)
    String? accessToken,
    /// User information from Google
    required GoogleUserInfo userInfo,
    /// Token expiry date
    DateTime? expiryDate,
  }) = GoogleAuthResultSuccess;
  
  /// Cancelled Google authentication (user cancelled)
  const factory GoogleAuthResult.cancelled({
    /// Optional reason for cancellation
    String? reason,
  }) = GoogleAuthResultCancelled;
  
  /// Failed Google authentication
  const factory GoogleAuthResult.failure({
    /// Error message
    required String error,
    /// Error code for programmatic handling
    String? errorCode,
  }) = GoogleAuthResultFailure;
}

/// Google user information
class GoogleUserInfo {
  final String id;
  final String email;
  final String? displayName;
  final String? photoUrl;
  final String? firstName;
  final String? lastName;
  final String? name;
  final String? givenName;
  final String? familyName;
  final String? locale;
  
  const GoogleUserInfo({
    required this.id,
    required this.email,
    this.displayName,
    this.photoUrl,
    this.firstName,
    this.lastName,
    this.name,
    this.givenName,
    this.familyName,
    this.locale,
  });
}



/// Abstract interface for authentication validation
abstract interface class IAuthValidator {
  /// Validate email format
  bool isValidEmail(String email);

  /// Validate password strength
  PasswordValidationResult validatePassword(String password);

  /// Validate name format
  bool isValidName(String name);
}

/// Password validation result
class PasswordValidationResult {
  final bool isValid;
  final List<String> errors;

  const PasswordValidationResult({required this.isValid, required this.errors});
}

/// Abstract interface for authentication error handling
abstract interface class IAuthErrorHandler {
  /// Handle authentication errors and return user-friendly messages
  String handleError(String errorCode, [String? defaultMessage]);

  /// Get localized error message
  String getLocalizedMessage(String errorCode);

  /// Check if error is recoverable
  bool isRecoverableError(String errorCode);
}

/// Authentication event types for logging and monitoring
enum AuthEventType {
  loginAttempt,
  loginSuccess,
  loginFailure,
  registerAttempt,
  registerSuccess,
  registerFailure,
  googleAuthAttempt,
  googleAuthSuccess,
  googleAuthFailure,
  tokenRefresh,
  logout,
  sessionExpired,
}

/// Authentication event data
class AuthEvent {
  final AuthEventType type;
  final String? userId;
  final String? email;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const AuthEvent({
    required this.type,
    this.userId,
    this.email,
    required this.timestamp,
    this.metadata,
  });
}

/// Abstract interface for authentication event logging
abstract interface class IAuthEventLogger {
  /// Log authentication event
  Future<void> logEvent(AuthEvent event);

  /// Log authentication error
  Future<void> logError(String error, {Map<String, dynamic>? metadata});

  /// Log authentication success
  Future<void> logSuccess(
    AuthEventType type, {
    String? userId,
    Map<String, dynamic>? metadata,
  });
}

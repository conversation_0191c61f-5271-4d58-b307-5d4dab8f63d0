// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_interfaces.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$GoogleAuthResult implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'GoogleAuthResult'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GoogleAuthResult);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'GoogleAuthResult()';
}


}

/// @nodoc
class $GoogleAuthResultCopyWith<$Res>  {
$GoogleAuthResultCopyWith(GoogleAuthResult _, $Res Function(GoogleAuthResult) __);
}


/// Adds pattern-matching-related methods to [GoogleAuthResult].
extension GoogleAuthResultPatterns on GoogleAuthResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( GoogleAuthResultSuccess value)?  success,TResult Function( GoogleAuthResultCancelled value)?  cancelled,TResult Function( GoogleAuthResultFailure value)?  failure,required TResult orElse(),}){
final _that = this;
switch (_that) {
case GoogleAuthResultSuccess() when success != null:
return success(_that);case GoogleAuthResultCancelled() when cancelled != null:
return cancelled(_that);case GoogleAuthResultFailure() when failure != null:
return failure(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( GoogleAuthResultSuccess value)  success,required TResult Function( GoogleAuthResultCancelled value)  cancelled,required TResult Function( GoogleAuthResultFailure value)  failure,}){
final _that = this;
switch (_that) {
case GoogleAuthResultSuccess():
return success(_that);case GoogleAuthResultCancelled():
return cancelled(_that);case GoogleAuthResultFailure():
return failure(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( GoogleAuthResultSuccess value)?  success,TResult? Function( GoogleAuthResultCancelled value)?  cancelled,TResult? Function( GoogleAuthResultFailure value)?  failure,}){
final _that = this;
switch (_that) {
case GoogleAuthResultSuccess() when success != null:
return success(_that);case GoogleAuthResultCancelled() when cancelled != null:
return cancelled(_that);case GoogleAuthResultFailure() when failure != null:
return failure(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( String idToken,  String? accessToken,  GoogleUserInfo userInfo,  DateTime? expiryDate)?  success,TResult Function( String? reason)?  cancelled,TResult Function( String error,  String? errorCode)?  failure,required TResult orElse(),}) {final _that = this;
switch (_that) {
case GoogleAuthResultSuccess() when success != null:
return success(_that.idToken,_that.accessToken,_that.userInfo,_that.expiryDate);case GoogleAuthResultCancelled() when cancelled != null:
return cancelled(_that.reason);case GoogleAuthResultFailure() when failure != null:
return failure(_that.error,_that.errorCode);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( String idToken,  String? accessToken,  GoogleUserInfo userInfo,  DateTime? expiryDate)  success,required TResult Function( String? reason)  cancelled,required TResult Function( String error,  String? errorCode)  failure,}) {final _that = this;
switch (_that) {
case GoogleAuthResultSuccess():
return success(_that.idToken,_that.accessToken,_that.userInfo,_that.expiryDate);case GoogleAuthResultCancelled():
return cancelled(_that.reason);case GoogleAuthResultFailure():
return failure(_that.error,_that.errorCode);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( String idToken,  String? accessToken,  GoogleUserInfo userInfo,  DateTime? expiryDate)?  success,TResult? Function( String? reason)?  cancelled,TResult? Function( String error,  String? errorCode)?  failure,}) {final _that = this;
switch (_that) {
case GoogleAuthResultSuccess() when success != null:
return success(_that.idToken,_that.accessToken,_that.userInfo,_that.expiryDate);case GoogleAuthResultCancelled() when cancelled != null:
return cancelled(_that.reason);case GoogleAuthResultFailure() when failure != null:
return failure(_that.error,_that.errorCode);case _:
  return null;

}
}

}

/// @nodoc


class GoogleAuthResultSuccess with DiagnosticableTreeMixin implements GoogleAuthResult {
  const GoogleAuthResultSuccess({required this.idToken, this.accessToken, required this.userInfo, this.expiryDate});
  

/// Google ID token
 final  String idToken;
/// Google access token (optional)
 final  String? accessToken;
/// User information from Google
 final  GoogleUserInfo userInfo;
/// Token expiry date
 final  DateTime? expiryDate;

/// Create a copy of GoogleAuthResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GoogleAuthResultSuccessCopyWith<GoogleAuthResultSuccess> get copyWith => _$GoogleAuthResultSuccessCopyWithImpl<GoogleAuthResultSuccess>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'GoogleAuthResult.success'))
    ..add(DiagnosticsProperty('idToken', idToken))..add(DiagnosticsProperty('accessToken', accessToken))..add(DiagnosticsProperty('userInfo', userInfo))..add(DiagnosticsProperty('expiryDate', expiryDate));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GoogleAuthResultSuccess&&(identical(other.idToken, idToken) || other.idToken == idToken)&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.userInfo, userInfo) || other.userInfo == userInfo)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate));
}


@override
int get hashCode => Object.hash(runtimeType,idToken,accessToken,userInfo,expiryDate);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'GoogleAuthResult.success(idToken: $idToken, accessToken: $accessToken, userInfo: $userInfo, expiryDate: $expiryDate)';
}


}

/// @nodoc
abstract mixin class $GoogleAuthResultSuccessCopyWith<$Res> implements $GoogleAuthResultCopyWith<$Res> {
  factory $GoogleAuthResultSuccessCopyWith(GoogleAuthResultSuccess value, $Res Function(GoogleAuthResultSuccess) _then) = _$GoogleAuthResultSuccessCopyWithImpl;
@useResult
$Res call({
 String idToken, String? accessToken, GoogleUserInfo userInfo, DateTime? expiryDate
});




}
/// @nodoc
class _$GoogleAuthResultSuccessCopyWithImpl<$Res>
    implements $GoogleAuthResultSuccessCopyWith<$Res> {
  _$GoogleAuthResultSuccessCopyWithImpl(this._self, this._then);

  final GoogleAuthResultSuccess _self;
  final $Res Function(GoogleAuthResultSuccess) _then;

/// Create a copy of GoogleAuthResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? idToken = null,Object? accessToken = freezed,Object? userInfo = null,Object? expiryDate = freezed,}) {
  return _then(GoogleAuthResultSuccess(
idToken: null == idToken ? _self.idToken : idToken // ignore: cast_nullable_to_non_nullable
as String,accessToken: freezed == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String?,userInfo: null == userInfo ? _self.userInfo : userInfo // ignore: cast_nullable_to_non_nullable
as GoogleUserInfo,expiryDate: freezed == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

/// @nodoc


class GoogleAuthResultCancelled with DiagnosticableTreeMixin implements GoogleAuthResult {
  const GoogleAuthResultCancelled({this.reason});
  

/// Optional reason for cancellation
 final  String? reason;

/// Create a copy of GoogleAuthResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GoogleAuthResultCancelledCopyWith<GoogleAuthResultCancelled> get copyWith => _$GoogleAuthResultCancelledCopyWithImpl<GoogleAuthResultCancelled>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'GoogleAuthResult.cancelled'))
    ..add(DiagnosticsProperty('reason', reason));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GoogleAuthResultCancelled&&(identical(other.reason, reason) || other.reason == reason));
}


@override
int get hashCode => Object.hash(runtimeType,reason);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'GoogleAuthResult.cancelled(reason: $reason)';
}


}

/// @nodoc
abstract mixin class $GoogleAuthResultCancelledCopyWith<$Res> implements $GoogleAuthResultCopyWith<$Res> {
  factory $GoogleAuthResultCancelledCopyWith(GoogleAuthResultCancelled value, $Res Function(GoogleAuthResultCancelled) _then) = _$GoogleAuthResultCancelledCopyWithImpl;
@useResult
$Res call({
 String? reason
});




}
/// @nodoc
class _$GoogleAuthResultCancelledCopyWithImpl<$Res>
    implements $GoogleAuthResultCancelledCopyWith<$Res> {
  _$GoogleAuthResultCancelledCopyWithImpl(this._self, this._then);

  final GoogleAuthResultCancelled _self;
  final $Res Function(GoogleAuthResultCancelled) _then;

/// Create a copy of GoogleAuthResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? reason = freezed,}) {
  return _then(GoogleAuthResultCancelled(
reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class GoogleAuthResultFailure with DiagnosticableTreeMixin implements GoogleAuthResult {
  const GoogleAuthResultFailure({required this.error, this.errorCode});
  

/// Error message
 final  String error;
/// Error code for programmatic handling
 final  String? errorCode;

/// Create a copy of GoogleAuthResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GoogleAuthResultFailureCopyWith<GoogleAuthResultFailure> get copyWith => _$GoogleAuthResultFailureCopyWithImpl<GoogleAuthResultFailure>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'GoogleAuthResult.failure'))
    ..add(DiagnosticsProperty('error', error))..add(DiagnosticsProperty('errorCode', errorCode));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GoogleAuthResultFailure&&(identical(other.error, error) || other.error == error)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode));
}


@override
int get hashCode => Object.hash(runtimeType,error,errorCode);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'GoogleAuthResult.failure(error: $error, errorCode: $errorCode)';
}


}

/// @nodoc
abstract mixin class $GoogleAuthResultFailureCopyWith<$Res> implements $GoogleAuthResultCopyWith<$Res> {
  factory $GoogleAuthResultFailureCopyWith(GoogleAuthResultFailure value, $Res Function(GoogleAuthResultFailure) _then) = _$GoogleAuthResultFailureCopyWithImpl;
@useResult
$Res call({
 String error, String? errorCode
});




}
/// @nodoc
class _$GoogleAuthResultFailureCopyWithImpl<$Res>
    implements $GoogleAuthResultFailureCopyWith<$Res> {
  _$GoogleAuthResultFailureCopyWithImpl(this._self, this._then);

  final GoogleAuthResultFailure _self;
  final $Res Function(GoogleAuthResultFailure) _then;

/// Create a copy of GoogleAuthResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? error = null,Object? errorCode = freezed,}) {
  return _then(GoogleAuthResultFailure(
error: null == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on

/// ============================================================================
/// OFFLINE STATE MANAGER - Advanced Offline Handling System
/// ============================================================================
/// 
/// Offline state handling with graceful degradation and queue management
/// Task 21: Enhance error handling and user feedback - Offline handling
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../error/centralized_error_handler.dart';
import '../loading/advanced_loading_states.dart';

/// Network connectivity status
enum NetworkStatus {
  online,
  offline,
  limited, // Connected but with limited internet access
  unknown,
}

/// Offline operation types
enum OfflineOperationType {
  authentication,
  dataSync,
  fileUpload,
  apiCall,
  other,
}

/// Offline operation priority
enum OfflineOperationPriority {
  low,
  medium,
  high,
  critical,
}

/// Offline queued operation
class OfflineOperation {
  const OfflineOperation({
    required this.id,
    required this.type,
    required this.operation,
    required this.data,
    this.priority = OfflineOperationPriority.medium,
    this.maxRetries = 3,
    this.retryCount = 0,
    this.createdAt,
    this.expiresAt,
    this.metadata = const {},
  });

  final String id;
  final OfflineOperationType type;
  final String operation; // Operation name/identifier
  final Map<String, dynamic> data;
  final OfflineOperationPriority priority;
  final int maxRetries;
  final int retryCount;
  final DateTime? createdAt;
  final DateTime? expiresAt;
  final Map<String, dynamic> metadata;

  /// Check if operation is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if can retry
  bool get canRetry => retryCount < maxRetries && !isExpired;

  /// Create copy with updated values
  OfflineOperation copyWith({
    String? id,
    OfflineOperationType? type,
    String? operation,
    Map<String, dynamic>? data,
    OfflineOperationPriority? priority,
    int? maxRetries,
    int? retryCount,
    DateTime? createdAt,
    DateTime? expiresAt,
    Map<String, dynamic>? metadata,
  }) {
    return OfflineOperation(
      id: id ?? this.id,
      type: type ?? this.type,
      operation: operation ?? this.operation,
      data: data ?? this.data,
      priority: priority ?? this.priority,
      maxRetries: maxRetries ?? this.maxRetries,
      retryCount: retryCount ?? this.retryCount,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'operation': operation,
      'data': data,
      'priority': priority.name,
      'maxRetries': maxRetries,
      'retryCount': retryCount,
      'createdAt': createdAt?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory OfflineOperation.fromJson(Map<String, dynamic> json) {
    return OfflineOperation(
      id: json['id'],
      type: OfflineOperationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => OfflineOperationType.other,
      ),
      operation: json['operation'],
      data: Map<String, dynamic>.from(json['data']),
      priority: OfflineOperationPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => OfflineOperationPriority.medium,
      ),
      maxRetries: json['maxRetries'] ?? 3,
      retryCount: json['retryCount'] ?? 0,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : null,
      expiresAt: json['expiresAt'] != null 
          ? DateTime.parse(json['expiresAt']) 
          : null,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }
}

/// Offline state information
class OfflineState {
  const OfflineState({
    this.networkStatus = NetworkStatus.unknown,
    this.isOnline = false,
    this.lastOnlineAt,
    this.queuedOperations = const [],
    this.isProcessingQueue = false,
    this.offlineMessage,
  });

  final NetworkStatus networkStatus;
  final bool isOnline;
  final DateTime? lastOnlineAt;
  final List<OfflineOperation> queuedOperations;
  final bool isProcessingQueue;
  final String? offlineMessage;

  /// Get offline duration
  Duration? get offlineDuration {
    if (isOnline || lastOnlineAt == null) return null;
    return DateTime.now().difference(lastOnlineAt!);
  }

  /// Get operations by priority
  List<OfflineOperation> getOperationsByPriority(OfflineOperationPriority priority) {
    return queuedOperations.where((op) => op.priority == priority).toList();
  }

  /// Get operations by type
  List<OfflineOperation> getOperationsByType(OfflineOperationType type) {
    return queuedOperations.where((op) => op.type == type).toList();
  }

  OfflineState copyWith({
    NetworkStatus? networkStatus,
    bool? isOnline,
    DateTime? lastOnlineAt,
    List<OfflineOperation>? queuedOperations,
    bool? isProcessingQueue,
    String? offlineMessage,
  }) {
    return OfflineState(
      networkStatus: networkStatus ?? this.networkStatus,
      isOnline: isOnline ?? this.isOnline,
      lastOnlineAt: lastOnlineAt ?? this.lastOnlineAt,
      queuedOperations: queuedOperations ?? this.queuedOperations,
      isProcessingQueue: isProcessingQueue ?? this.isProcessingQueue,
      offlineMessage: offlineMessage ?? this.offlineMessage,
    );
  }
}

/// Offline state manager
class OfflineStateManager extends StateNotifier<OfflineState> {
  OfflineStateManager({
    required this.connectivity,
    required this.preferences,
    this.errorHandler,
    this.loadingNotifier,
  }) : super(const OfflineState()) {
    _initialize();
  }

  final Connectivity connectivity;
  final SharedPreferences preferences;
  final CentralizedErrorHandler? errorHandler;
  final AdvancedLoadingNotifier? loadingNotifier;

  static const String _queueKey = 'offline_operation_queue';
  static const String _lastOnlineKey = 'last_online_timestamp';

  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  Timer? _connectivityCheckTimer;

  void _initialize() {
    // Load persisted queue
    _loadPersistedQueue();
    
    // Start connectivity monitoring
    _startConnectivityMonitoring();
    
    // Perform initial connectivity check
    _checkConnectivity();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityCheckTimer?.cancel();
    super.dispose();
  }

  /// Start monitoring connectivity changes
  void _startConnectivityMonitoring() {
    _connectivitySubscription = connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        _handleConnectivityChange(results);
      },
    );

    // Periodic connectivity check for more reliable detection
    _connectivityCheckTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _checkConnectivity(),
    );
  }

  /// Handle connectivity change
  void _handleConnectivityChange(List<ConnectivityResult> results) {
    final hasConnection = results.any((result) => 
        result != ConnectivityResult.none);

    if (hasConnection) {
      _checkInternetAccess();
    } else {
      _setOfflineState();
    }
  }

  /// Check actual internet connectivity
  Future<void> _checkConnectivity() async {
    try {
      final results = await connectivity.checkConnectivity();
      final hasConnection = results.any((result) => 
          result != ConnectivityResult.none);

      if (hasConnection) {
        await _checkInternetAccess();
      } else {
        _setOfflineState();
      }
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Connectivity check failed: $error');
      }
      _setOfflineState();
    }
  }

  /// Check if device has actual internet access
  Future<void> _checkInternetAccess() async {
    try {
      // Try to reach a reliable endpoint
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 5));
      
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        _setOnlineState();
      } else {
        _setLimitedState();
      }
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Internet access check failed: $error');
      }
      _setLimitedState();
    }
  }

  /// Set online state
  void _setOnlineState() {
    final wasOffline = !state.isOnline;
    
    state = state.copyWith(
      networkStatus: NetworkStatus.online,
      isOnline: true,
      lastOnlineAt: DateTime.now(),
      offlineMessage: null,
    );

    // Save last online timestamp
    preferences.setString(_lastOnlineKey, DateTime.now().toIso8601String());

    if (wasOffline) {
      if (kDebugMode) {
        debugPrint('Device came back online');
      }
      
      // Process queued operations
      _processQueuedOperations();
    }
  }

  /// Set offline state
  void _setOfflineState() {
    final wasOnline = state.isOnline;
    
    state = state.copyWith(
      networkStatus: NetworkStatus.offline,
      isOnline: false,
      offlineMessage: 'لا يوجد اتصال بالإنترنت',
    );

    if (wasOnline) {
      if (kDebugMode) {
        debugPrint('Device went offline');
      }
      
      // Handle offline transition
      errorHandler?.handleError(
        const CarNowError(
          code: CarNowErrorCode.networkUnavailable,
          message: 'تم فقدان الاتصال بالإنترنت',
          severity: ErrorSeverity.warning,
        ),
        showToUser: true,
      );
    }
  }

  /// Set limited connectivity state
  void _setLimitedState() {
    state = state.copyWith(
      networkStatus: NetworkStatus.limited,
      isOnline: false,
      offlineMessage: 'اتصال محدود بالإنترنت',
    );

    if (kDebugMode) {
      debugPrint('Device has limited connectivity');
    }
  }

  /// Queue operation for offline execution
  Future<void> queueOperation(OfflineOperation operation) async {
    final updatedQueue = [...state.queuedOperations, operation];
    
    state = state.copyWith(queuedOperations: updatedQueue);
    
    // Persist queue
    await _persistQueue();
    
    if (kDebugMode) {
      debugPrint('Queued offline operation: ${operation.operation}');
    }
  }

  /// Remove operation from queue
  Future<void> removeOperation(String operationId) async {
    final updatedQueue = state.queuedOperations
        .where((op) => op.id != operationId)
        .toList();
    
    state = state.copyWith(queuedOperations: updatedQueue);
    
    // Persist queue
    await _persistQueue();
  }

  /// Process queued operations when online
  Future<void> _processQueuedOperations() async {
    if (state.isProcessingQueue || state.queuedOperations.isEmpty) {
      return;
    }

    state = state.copyWith(isProcessingQueue: true);
    
    loadingNotifier?.startLoading(
      message: 'مزامنة البيانات المؤجلة...',
      operation: 'offline_sync',
      canCancel: false,
    );

    try {
      // Sort operations by priority
      final sortedOperations = [...state.queuedOperations];
      sortedOperations.sort((a, b) {
        final priorityOrder = {
          OfflineOperationPriority.critical: 0,
          OfflineOperationPriority.high: 1,
          OfflineOperationPriority.medium: 2,
          OfflineOperationPriority.low: 3,
        };
        return priorityOrder[a.priority]!.compareTo(priorityOrder[b.priority]!);
      });

      int processedCount = 0;
      final totalCount = sortedOperations.length;

      for (final operation in sortedOperations) {
        if (!state.isOnline) break; // Stop if went offline
        
        if (operation.isExpired) {
          await removeOperation(operation.id);
          continue;
        }

        try {
          // Update progress
          final progress = processedCount / totalCount;
          loadingNotifier?.updateProgress(
            progress,
            message: 'معالجة العملية ${processedCount + 1} من $totalCount',
          );

          // Process operation (this would be implemented by specific operation handlers)
          await _executeQueuedOperation(operation);
          
          // Remove successful operation
          await removeOperation(operation.id);
          processedCount++;
          
        } catch (error) {
          if (kDebugMode) {
            debugPrint('Failed to process queued operation ${operation.id}: $error');
          }
          
          // Increment retry count
          if (operation.canRetry) {
            final updatedOperation = operation.copyWith(
              retryCount: operation.retryCount + 1,
            );
            
            final updatedQueue = state.queuedOperations
                .map((op) => op.id == operation.id ? updatedOperation : op)
                .toList();
            
            state = state.copyWith(queuedOperations: updatedQueue);
            await _persistQueue();
          } else {
            // Remove failed operation that can't be retried
            await removeOperation(operation.id);
          }
        }
      }

      loadingNotifier?.completeSuccess(
        message: processedCount > 0 
            ? 'تم مزامنة $processedCount عملية بنجاح'
            : 'لا توجد عمليات للمزامنة',
      );

    } catch (error) {
      loadingNotifier?.completeError(
        error,
        message: 'فشل في مزامنة البيانات المؤجلة',
      );
      
      errorHandler?.handleError(
        error,
        context: 'offline_sync',
        showToUser: true,
      );
    } finally {
      state = state.copyWith(isProcessingQueue: false);
    }
  }

  /// Execute a queued operation (to be implemented by specific handlers)
  Future<void> _executeQueuedOperation(OfflineOperation operation) async {
    // This is a placeholder - actual implementation would depend on operation type
    switch (operation.type) {
      case OfflineOperationType.authentication:
        // Handle authentication operations
        break;
      case OfflineOperationType.dataSync:
        // Handle data sync operations
        break;
      case OfflineOperationType.fileUpload:
        // Handle file upload operations
        break;
      case OfflineOperationType.apiCall:
        // Handle API call operations
        break;
      case OfflineOperationType.other:
        // Handle other operations
        break;
    }
    
    // Simulate processing time
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Load persisted operation queue
  Future<void> _loadPersistedQueue() async {
    try {
      final queueJson = preferences.getString(_queueKey);
      if (queueJson != null) {
        final queueData = jsonDecode(queueJson) as List;
        final operations = queueData
            .map((item) => OfflineOperation.fromJson(item))
            .where((op) => !op.isExpired) // Filter expired operations
            .toList();
        
        state = state.copyWith(queuedOperations: operations);
      }

      // Load last online timestamp
      final lastOnlineStr = preferences.getString(_lastOnlineKey);
      if (lastOnlineStr != null) {
        final lastOnline = DateTime.parse(lastOnlineStr);
        state = state.copyWith(lastOnlineAt: lastOnline);
      }
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Failed to load persisted queue: $error');
      }
    }
  }

  /// Persist operation queue
  Future<void> _persistQueue() async {
    try {
      final queueData = state.queuedOperations
          .where((op) => !op.isExpired) // Don't persist expired operations
          .map((op) => op.toJson())
          .toList();
      
      final queueJson = jsonEncode(queueData);
      await preferences.setString(_queueKey, queueJson);
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Failed to persist queue: $error');
      }
    }
  }

  /// Clear expired operations
  Future<void> clearExpiredOperations() async {
    final validOperations = state.queuedOperations
        .where((op) => !op.isExpired)
        .toList();
    
    if (validOperations.length != state.queuedOperations.length) {
      state = state.copyWith(queuedOperations: validOperations);
      await _persistQueue();
    }
  }

  /// Manually trigger queue processing
  Future<void> processQueueManually() async {
    if (state.isOnline) {
      await _processQueuedOperations();
    }
  }
}

/// Providers
final connectivityProvider = Provider<Connectivity>((ref) => Connectivity());

final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be overridden');
});

final offlineStateManagerProvider = StateNotifierProvider<OfflineStateManager, OfflineState>((ref) {
  final connectivity = ref.read(connectivityProvider);
  final preferences = ref.read(sharedPreferencesProvider);
  final errorHandler = ref.read(centralizedErrorHandlerProvider);
  final loadingNotifier = ref.read(advancedLoadingProvider.notifier);
  
  return OfflineStateManager(
    connectivity: connectivity,
    preferences: preferences,
    errorHandler: errorHandler,
    loadingNotifier: loadingNotifier,
  );
});

/// Offline indicator widget
class OfflineIndicator extends ConsumerWidget {
  const OfflineIndicator({
    super.key,
    this.showQueueInfo = true,
    this.showDuration = true,
    this.onRetry,
  });

  final bool showQueueInfo;
  final bool showDuration;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final offlineState = ref.watch(offlineStateManagerProvider);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (offlineState.isOnline) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getStatusColor(offlineState.networkStatus).withOpacity(0.1),
        border: Border.all(
          color: _getStatusColor(offlineState.networkStatus).withOpacity(0.3),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                _getStatusIcon(offlineState.networkStatus),
                color: _getStatusColor(offlineState.networkStatus),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      offlineState.offlineMessage ?? 'غير متصل',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (showDuration && offlineState.offlineDuration != null)
                      Text(
                        'منذ ${_formatDuration(offlineState.offlineDuration!)}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                  ],
                ),
              ),
              if (onRetry != null)
                IconButton(
                  onPressed: onRetry,
                  icon: const Icon(Icons.refresh),
                  tooltip: 'إعادة المحاولة',
                ),
            ],
          ),
          
          if (showQueueInfo && offlineState.queuedOperations.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.queue,
                    color: colorScheme.onSurface.withOpacity(0.6),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${offlineState.queuedOperations.length} عملية في الانتظار',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(NetworkStatus status) {
    switch (status) {
      case NetworkStatus.offline:
        return Colors.red;
      case NetworkStatus.limited:
        return Colors.orange;
      case NetworkStatus.online:
        return Colors.green;
      case NetworkStatus.unknown:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(NetworkStatus status) {
    switch (status) {
      case NetworkStatus.offline:
        return Icons.wifi_off;
      case NetworkStatus.limited:
        return Icons.signal_wifi_connected_no_internet_4;
      case NetworkStatus.online:
        return Icons.wifi;
      case NetworkStatus.unknown:
        return Icons.help_outline;
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} يوم';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ساعة';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} دقيقة';
    } else {
      return '${duration.inSeconds} ثانية';
    }
  }
}

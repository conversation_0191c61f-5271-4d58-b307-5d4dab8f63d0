import 'dart:io';
import 'package:dio/dio.dart';

import 'package:logging/logging.dart';
import '../errors/app_error.dart';
import '../errors/unified_error_handler.dart';


/// Interceptor موحد لمعالجة الأخطاء في جميع طلبات الشبكة
/// Unified interceptor for handling errors in all network requests
class UnifiedErrorInterceptor extends Interceptor {
  static final Logger _logger = Logger('UnifiedErrorInterceptor');

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    _logger.severe('Network error intercepted: ${err.message}');

    // تحويل DioException إلى AppError
    final appError = _handleDioError(err);

    // تسجيل الخطأ في نظام التتبع
    // TODO: Replace with proper async error logging

    // إرجاع الخطأ المحول
    handler.reject(
      DioException(
        requestOptions: err.requestOptions,
        error: appError,
        type: err.type,
        response: err.response,
      ),
    );
  }

  /// تحويل DioException إلى AppError
  AppError _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return AppError.network(
          message: 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.',
          code: 'TIMEOUT_ERROR',
          originalError: error,
          data: {
            'timeout_type': error.type.toString(),
            'url': error.requestOptions.uri.toString(),
          },
        );

      case DioExceptionType.connectionError:
        return AppError.network(
          message: 'لا يوجد اتصال بالإنترنت. يرجى التحقق من الاتصال.',
          code: 'CONNECTION_ERROR',
          originalError: error,
          data: {'url': error.requestOptions.uri.toString()},
        );

      case DioExceptionType.badResponse:
        return _handleHttpError(error);

      case DioExceptionType.cancel:
        return AppError.unexpected(
          message: 'تم إلغاء الطلب.',
          code: 'REQUEST_CANCELLED',
          originalError: error,
        );

      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return AppError.network(
            message: 'خطأ في الاتصال بالشبكة.',
            code: 'SOCKET_ERROR',
            originalError: error,
          );
        }
        return AppError.unexpected(
          message: 'حدث خطأ غير متوقع.',
          code: 'UNKNOWN_ERROR',
          originalError: error,
        );

      default:
        return AppError.unexpected(
          message: 'حدث خطأ غير متوقع.',
          originalError: error,
        );
    }
  }

  /// معالجة أخطاء HTTP بناءً على رمز الحالة
  AppError _handleHttpError(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    switch (statusCode) {
      case 400:
        return AppError.validation(
          message: _extractMessage(responseData) ?? 'البيانات المرسلة غير صحيحة.',
          code: 'BAD_REQUEST',
          originalError: error,
          data: {'response': responseData},
        );

      case 401:
        return AppError.authentication(
          message: _extractMessage(responseData) ?? 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.',
          code: 'UNAUTHORIZED',
          originalError: error,
        );

      case 403:
        return AppError.permission(
          message: _extractMessage(responseData) ?? 'ليس لديك صلاحية للوصول إلى هذا المورد.',
          code: 'FORBIDDEN',
          originalError: error,
        );

      case 404:
        return AppError.notFound(
          message: _extractMessage(responseData) ?? 'المورد المطلوب غير موجود.',
          code: 'NOT_FOUND',
          originalError: error,
        );

      case 429:
        return AppError.rateLimited(
          message: _extractMessage(responseData) ?? 'تم تجاوز عدد الطلبات المسموح. يرجى الانتظار.',
          code: 'RATE_LIMITED',
          originalError: error,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return AppError.network(
          message: _extractMessage(responseData) ?? 'خطأ في الخادم. يرجى المحاولة لاحقاً.',
          code: 'SERVER_ERROR',
          severity: ErrorSeverity.high,
          originalError: error,
          data: {'status_code': statusCode},
        );

      default:
        return AppError.network(
          message: _extractMessage(responseData) ?? 'حدث خطأ في الشبكة (${statusCode ?? 'غير معروف'}).',
          code: 'HTTP_ERROR',
          originalError: error,
          data: {'status_code': statusCode, 'response': responseData},
        );
    }
  }

  /// استخراج رسالة الخطأ من استجابة الخادم إذا وُجدت
  String? _extractMessage(dynamic responseData) {
    if (responseData == null) return null;
    if (responseData is Map<String, dynamic>) {
      return responseData['message']?.toString() ?? responseData['error']?.toString();
    }
    if (responseData is String) return responseData;
    return null;
  }
}

// SupabaseErrorInterceptor removed - Forever Plan Compliance
// All database operations now go through Go API only

/// مصنع لإنشاء Interceptors
/// Factory for creating interceptors
class ErrorInterceptorFactory {
  static UnifiedErrorInterceptor createNetworkInterceptor(
    UnifiedErrorHandler errorHandler,
  ) {
    return UnifiedErrorInterceptor();
  }

  // createSupabaseInterceptor removed - Forever Plan Compliance
  // All database operations now go through Go API only
}

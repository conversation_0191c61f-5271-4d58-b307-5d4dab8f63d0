// ============================================================================
// CarNow Unified Authentication System - Password Security Service
// ============================================================================
// File: password_security_service.dart
// Description: Advanced password strength validation and security features
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'password_security_service.g.dart';

/// Password strength levels
enum PasswordStrength {
  veryWeak,    // 0-20
  weak,        // 21-40
  fair,        // 41-60
  good,        // 61-80
  strong,      // 81-100
}

/// Password requirement types
enum PasswordRequirement {
  minLength,
  maxLength,
  uppercase,
  lowercase,
  numbers,
  symbols,
  noCommonWords,
  noPersonalInfo,
  noSequential,
  noRepeating,
  notPreviouslyUsed,
}

/// Password validation result
class PasswordValidationResult {
  final bool isValid;
  final PasswordStrength strength;
  final int score;
  final List<PasswordRequirement> failedRequirements;
  final List<String> suggestions;
  final List<String> suggestionsAr;
  final Map<String, dynamic> details;

  const PasswordValidationResult({
    required this.isValid,
    required this.strength,
    required this.score,
    required this.failedRequirements,
    required this.suggestions,
    required this.suggestionsAr,
    required this.details,
  });

  /// Get localized suggestions
  List<String> getLocalizedSuggestions(String locale) {
    return locale.startsWith('ar') ? suggestionsAr : suggestions;
  }

  /// Get strength description
  String getStrengthDescription(String locale) {
    if (locale.startsWith('ar')) {
      switch (strength) {
        case PasswordStrength.veryWeak:
          return 'ضعيفة جداً';
        case PasswordStrength.weak:
          return 'ضعيفة';
        case PasswordStrength.fair:
          return 'مقبولة';
        case PasswordStrength.good:
          return 'جيدة';
        case PasswordStrength.strong:
          return 'قوية';
      }
    } else {
      switch (strength) {
        case PasswordStrength.veryWeak:
          return 'Very Weak';
        case PasswordStrength.weak:
          return 'Weak';
        case PasswordStrength.fair:
          return 'Fair';
        case PasswordStrength.good:
          return 'Good';
        case PasswordStrength.strong:
          return 'Strong';
      }
    }
  }

  /// Get strength color
  String getStrengthColor() {
    switch (strength) {
      case PasswordStrength.veryWeak:
        return '#FF0000'; // Red
      case PasswordStrength.weak:
        return '#FF6600'; // Orange
      case PasswordStrength.fair:
        return '#FFCC00'; // Yellow
      case PasswordStrength.good:
        return '#66CC00'; // Light Green
      case PasswordStrength.strong:
        return '#00CC00'; // Green
    }
  }
}

/// Password security configuration
class PasswordSecurityConfig {
  final int minLength;
  final int maxLength;
  final bool requireUppercase;
  final bool requireLowercase;
  final bool requireNumbers;
  final bool requireSymbols;
  final int minSymbols;
  final bool checkCommonPasswords;
  final bool checkPersonalInfo;
  final bool checkSequential;
  final bool checkRepeating;
  final int maxRepeatingChars;
  final bool checkPasswordHistory;
  final int passwordHistoryCount;
  final List<String> bannedPasswords;

  const PasswordSecurityConfig({
    this.minLength = 8,
    this.maxLength = 128,
    this.requireUppercase = true,
    this.requireLowercase = true,
    this.requireNumbers = true,
    this.requireSymbols = true,
    this.minSymbols = 1,
    this.checkCommonPasswords = true,
    this.checkPersonalInfo = true,
    this.checkSequential = true,
    this.checkRepeating = true,
    this.maxRepeatingChars = 3,
    this.checkPasswordHistory = true,
    this.passwordHistoryCount = 5,
    this.bannedPasswords = const [],
  });

  /// Default configuration for CarNow
  static const carNow = PasswordSecurityConfig(
    minLength: 8,
    maxLength: 64,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSymbols: false, // More user-friendly
    minSymbols: 0,
    checkCommonPasswords: true,
    checkPersonalInfo: true,
    checkSequential: true,
    checkRepeating: true,
    maxRepeatingChars: 2,
    checkPasswordHistory: true,
    passwordHistoryCount: 3,
  );

  /// Strict configuration for admin accounts
  static const strict = PasswordSecurityConfig(
    minLength: 12,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSymbols: true,
    minSymbols: 2,
    checkCommonPasswords: true,
    checkPersonalInfo: true,
    checkSequential: true,
    checkRepeating: true,
    maxRepeatingChars: 2,
    checkPasswordHistory: true,
    passwordHistoryCount: 10,
  );
}

/// Password security service with advanced validation
class PasswordSecurityService {
  late SharedPreferences _prefs;
  
  // Common passwords list (top 1000 most common passwords)
  static const List<String> _commonPasswords = [
    'password', '123456', '********9', '********', '12345', '1234567',
    'password1', 'admin', 'qwerty', 'abc123', 'Password', '123123',
    'welcome', 'login', 'master', 'hello', 'guest', 'admin123',
    'root', 'test', 'user', '1234', 'pass', 'default', 'letmein',
    'monkey', 'dragon', 'princess', 'football', 'baseball', 'soccer',
    'jordan', 'harley', 'ranger', 'shadow', 'master', 'jennifer',
    'hunter', 'buster', 'johnny', 'killer', 'tigger', 'purple',
    'orange', 'yellow', 'maggie', 'ginger', 'flower', 'secret',
    'cookie', 'freedom', 'diamond', 'silver', 'golden', 'summer',
    'winter', 'spring', 'autumn', 'sunshine', 'rainbow', 'starlight',
  ];

  // Sequential patterns
  static const List<String> _sequentialPatterns = [
    '123', '234', '345', '456', '567', '678', '789', '890',
    'abc', 'bcd', 'cde', 'def', 'efg', 'fgh', 'ghi', 'hij',
    'qwe', 'wer', 'ert', 'rty', 'tyu', 'yui', 'uio', 'iop',
    'asd', 'sdf', 'dfg', 'fgh', 'ghj', 'hjk', 'jkl',
    'zxc', 'xcv', 'cvb', 'vbn', 'bnm',
  ];

  /// Initialize the service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Validate password with comprehensive checks
  Future<PasswordValidationResult> validatePassword(
    String password, {
    PasswordSecurityConfig config = PasswordSecurityConfig.carNow,
    Map<String, String>? personalInfo,
    String? userId,
  }) async {
    final failedRequirements = <PasswordRequirement>[];
    final suggestions = <String>[];
    final suggestionsAr = <String>[];
    final details = <String, dynamic>{};
    
    int score = 0;

    // Length checks
    if (password.length < config.minLength) {
      failedRequirements.add(PasswordRequirement.minLength);
      suggestions.add('Password must be at least ${config.minLength} characters long');
      suggestionsAr.add('يجب أن تكون كلمة المرور ${config.minLength} أحرف على الأقل');
    } else {
      score += min(25, (password.length - config.minLength) * 2);
    }

    if (password.length > config.maxLength) {
      failedRequirements.add(PasswordRequirement.maxLength);
      suggestions.add('Password must not exceed ${config.maxLength} characters');
      suggestionsAr.add('يجب ألا تتجاوز كلمة المرور ${config.maxLength} حرف');
    }

    // Character variety checks
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasNumbers = password.contains(RegExp(r'[0-9]'));
    final hasSymbols = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    final symbolCount = RegExp(r'[!@#$%^&*(),.?":{}|<>]').allMatches(password).length;

    if (config.requireUppercase && !hasUppercase) {
      failedRequirements.add(PasswordRequirement.uppercase);
      suggestions.add('Include at least one uppercase letter (A-Z)');
      suggestionsAr.add('أضف حرف كبير واحد على الأقل (A-Z)');
    } else if (hasUppercase) {
      score += 15;
    }

    if (config.requireLowercase && !hasLowercase) {
      failedRequirements.add(PasswordRequirement.lowercase);
      suggestions.add('Include at least one lowercase letter (a-z)');
      suggestionsAr.add('أضف حرف صغير واحد على الأقل (a-z)');
    } else if (hasLowercase) {
      score += 15;
    }

    if (config.requireNumbers && !hasNumbers) {
      failedRequirements.add(PasswordRequirement.numbers);
      suggestions.add('Include at least one number (0-9)');
      suggestionsAr.add('أضف رقم واحد على الأقل (0-9)');
    } else if (hasNumbers) {
      score += 15;
    }

    if (config.requireSymbols && (!hasSymbols || symbolCount < config.minSymbols)) {
      failedRequirements.add(PasswordRequirement.symbols);
      suggestions.add('Include at least ${config.minSymbols} special character(s) (!@#\$%^&*)');
      suggestionsAr.add('أضف ${config.minSymbols} رمز خاص على الأقل (!@#\$%^&*)');
    } else if (hasSymbols) {
      score += min(20, symbolCount * 5);
    }

    // Variety bonus
    final varietyCount = [hasUppercase, hasLowercase, hasNumbers, hasSymbols]
        .where((has) => has).length;
    score += varietyCount * 5;

    // Common password check
    if (config.checkCommonPasswords) {
      final lowerPassword = password.toLowerCase();
      if (_commonPasswords.contains(lowerPassword)) {
        failedRequirements.add(PasswordRequirement.noCommonWords);
        suggestions.add('Avoid using common passwords');
        suggestionsAr.add('تجنب استخدام كلمات المرور الشائعة');
        score -= 30;
      }
    }

    // Personal information check
    if (config.checkPersonalInfo && personalInfo != null) {
      final lowerPassword = password.toLowerCase();
      for (final info in personalInfo.values) {
        if (info.length >= 3 && lowerPassword.contains(info.toLowerCase())) {
          failedRequirements.add(PasswordRequirement.noPersonalInfo);
          suggestions.add('Avoid using personal information in your password');
          suggestionsAr.add('تجنب استخدام المعلومات الشخصية في كلمة المرور');
          score -= 20;
          break;
        }
      }
    }

    // Sequential pattern check
    if (config.checkSequential) {
      final lowerPassword = password.toLowerCase();
      for (final pattern in _sequentialPatterns) {
        if (lowerPassword.contains(pattern) || 
            lowerPassword.contains(pattern.split('').reversed.join())) {
          failedRequirements.add(PasswordRequirement.noSequential);
          suggestions.add('Avoid sequential characters (123, abc, qwe)');
          suggestionsAr.add('تجنب الأحرف المتسلسلة (123، abc، qwe)');
          score -= 15;
          break;
        }
      }
    }

    // Repeating characters check
    if (config.checkRepeating) {
      final repeatingPattern = RegExp(r'(.)\1{' + (config.maxRepeatingChars - 1).toString() + r',}');
      if (repeatingPattern.hasMatch(password)) {
        failedRequirements.add(PasswordRequirement.noRepeating);
        suggestions.add('Avoid repeating the same character more than ${config.maxRepeatingChars} times');
        suggestionsAr.add('تجنب تكرار نفس الحرف أكثر من ${config.maxRepeatingChars} مرات');
        score -= 10;
      }
    }

    // Password history check
    if (config.checkPasswordHistory && userId != null) {
      final isReused = await _checkPasswordHistory(password, userId, config.passwordHistoryCount);
      if (isReused) {
        failedRequirements.add(PasswordRequirement.notPreviouslyUsed);
        suggestions.add('This password was used recently. Please choose a different password');
        suggestionsAr.add('تم استخدام كلمة المرور هذه مؤخراً. يرجى اختيار كلمة مرور مختلفة');
        score -= 25;
      }
    }

    // Banned passwords check
    if (config.bannedPasswords.contains(password)) {
      failedRequirements.add(PasswordRequirement.noCommonWords);
      suggestions.add('This password is not allowed');
      suggestionsAr.add('كلمة المرور هذه غير مسموحة');
      score -= 50;
    }

    // Ensure score is within bounds
    score = score.clamp(0, 100);

    // Determine strength
    final strength = _getPasswordStrength(score);

    // Additional suggestions based on strength
    if (strength == PasswordStrength.veryWeak || strength == PasswordStrength.weak) {
      suggestions.add('Consider using a passphrase with multiple words');
      suggestionsAr.add('فكر في استخدام عبارة مرور متعددة الكلمات');
    }

    details.addAll({
      'length': password.length,
      'has_uppercase': hasUppercase,
      'has_lowercase': hasLowercase,
      'has_numbers': hasNumbers,
      'has_symbols': hasSymbols,
      'symbol_count': symbolCount,
      'variety_count': varietyCount,
      'entropy': _calculateEntropy(password),
    });

    final isValid = failedRequirements.isEmpty && score >= 40;

    return PasswordValidationResult(
      isValid: isValid,
      strength: strength,
      score: score,
      failedRequirements: failedRequirements,
      suggestions: suggestions,
      suggestionsAr: suggestionsAr,
      details: details,
    );
  }

  /// Generate a secure password suggestion
  String generateSecurePassword({
    int length = 12,
    bool includeUppercase = true,
    bool includeLowercase = true,
    bool includeNumbers = true,
    bool includeSymbols = true,
    bool avoidAmbiguous = true,
  }) {
    final random = Random.secure();
    final chars = StringBuffer();

    if (includeUppercase) {
      chars.write(avoidAmbiguous ? 'ABCDEFGHJKLMNPQRSTUVWXYZ' : 'ABCDEFGHIJKLMNOPQRSTUVWXYZ');
    }
    if (includeLowercase) {
      chars.write(avoidAmbiguous ? 'abcdefghjkmnpqrstuvwxyz' : 'abcdefghijklmnopqrstuvwxyz');
    }
    if (includeNumbers) {
      chars.write(avoidAmbiguous ? '23456789' : '0********9');
    }
    if (includeSymbols) {
      chars.write(avoidAmbiguous ? r'!@#$%^&*-_=+' : r'!@#$%^&*(),.?":{}|<>');
    }

    final charList = chars.toString().split('');
    final password = StringBuffer();

    // Ensure at least one character from each required category
    if (includeUppercase) {
      final upperChars = charList.where((c) => c.codeUnitAt(0) >= 65 && c.codeUnitAt(0) <= 90).toList();
      password.write(upperChars.elementAt(random.nextInt(upperChars.length)));
    }
    if (includeLowercase) {
      final lowerChars = charList.where((c) => c.codeUnitAt(0) >= 97 && c.codeUnitAt(0) <= 122).toList();
      password.write(lowerChars.elementAt(random.nextInt(lowerChars.length)));
    }
    if (includeNumbers) {
      final numberChars = charList.where((c) => c.codeUnitAt(0) >= 48 && c.codeUnitAt(0) <= 57).toList();
      password.write(numberChars.elementAt(random.nextInt(numberChars.length)));
    }
    if (includeSymbols) {
      final symbolChars = charList.where((c) => !RegExp(r'[a-zA-Z0-9]').hasMatch(c)).toList();
      if (symbolChars.isNotEmpty) {
        password.write(symbolChars.elementAt(random.nextInt(symbolChars.length)));
      }
    }

    // Fill remaining length with random characters
    while (password.length < length) {
      password.write(charList[random.nextInt(charList.length)]);
    }

    // Shuffle the password to avoid predictable patterns
    final passwordList = password.toString().split('');
    passwordList.shuffle(random);
    
    return passwordList.join('');
  }

  /// Store password hash in history
  Future<void> storePasswordInHistory(String password, String userId) async {
    final hashedPassword = _hashPassword(password);
    final historyKey = 'password_history_$userId';
    
    final existingHistory = _prefs.getStringList(historyKey) ?? [];
    existingHistory.insert(0, hashedPassword);
    
    // Keep only the configured number of passwords
    const maxHistory = 10;
    if (existingHistory.length > maxHistory) {
      existingHistory.removeRange(maxHistory, existingHistory.length);
    }
    
    await _prefs.setStringList(historyKey, existingHistory);
  }

  /// Check if password was used recently
  Future<bool> _checkPasswordHistory(String password, String userId, int historyCount) async {
    final hashedPassword = _hashPassword(password);
    final historyKey = 'password_history_$userId';
    
    final history = _prefs.getStringList(historyKey) ?? [];
    final checkCount = min(historyCount, history.length);
    
    return history.take(checkCount).contains(hashedPassword);
  }

  /// Hash password for storage
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Calculate password entropy
  double _calculateEntropy(String password) {
    final charSet = <String>{};
    
    for (int i = 0; i < password.length; i++) {
      charSet.add(password[i]);
    }
    
    final poolSize = _estimateCharacterPoolSize(password);
    return password.length * (log(poolSize) / log(2));
  }

  /// Estimate character pool size
  int _estimateCharacterPoolSize(String password) {
    int poolSize = 0;
    
    if (password.contains(RegExp(r'[a-z]'))) poolSize += 26;
    if (password.contains(RegExp(r'[A-Z]'))) poolSize += 26;
    if (password.contains(RegExp(r'[0-9]'))) poolSize += 10;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) poolSize += 32;
    
    return poolSize > 0 ? poolSize : 1;
  }

  /// Get password strength from score
  PasswordStrength _getPasswordStrength(int score) {
    if (score >= 81) return PasswordStrength.strong;
    if (score >= 61) return PasswordStrength.good;
    if (score >= 41) return PasswordStrength.fair;
    if (score >= 21) return PasswordStrength.weak;
    return PasswordStrength.veryWeak;
  }

  /// Clear password history for user
  Future<void> clearPasswordHistory(String userId) async {
    final historyKey = 'password_history_$userId';
    await _prefs.remove(historyKey);
  }
}

/// Riverpod provider for PasswordSecurityService
@riverpod
PasswordSecurityService passwordSecurityService(Ref ref) {
  final service = PasswordSecurityService();
  service.initialize();
  return service;
}

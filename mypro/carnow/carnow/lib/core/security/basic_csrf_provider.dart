import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Basic CSRF State - Simple implementation
class BasicCSRFState {
  final bool isEnabled;
  final String? token;
  final DateTime? expiresAt;
  final bool isLoading;

  const BasicCSRFState({
    this.isEnabled = true,
    this.token,
    this.expiresAt,
    this.isLoading = false,
  });

  BasicCSRFState copyWith({
    bool? isEnabled,
    String? token,
    DateTime? expiresAt,
    bool? isLoading,
  }) {
    return BasicCSRFState(
      isEnabled: isEnabled ?? this.isEnabled,
      token: token ?? this.token,
      expiresAt: expiresAt ?? this.expiresAt,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  /// Check if token is valid and not expired
  bool get isTokenValid {
    if (token == null || expiresAt == null) return false;
    return expiresAt!.isAfter(DateTime.now().add(const Duration(minutes: 5)));
  }
}

/// Basic CSRF Configuration
class BasicCSRFConfig {
  static const String headerName = 'X-CSRF-Token';
  static const String cookieName = 'csrf_token';
  static const List<String> exemptMethods = ['GET', 'HEAD', 'OPTIONS'];
  static const List<String> exemptPaths = [
    '/api/v1/health',
    '/api/v1/auth/login',
    '/api/v1/auth/register',
    '/api/v1/products',
    '/api/v1/categories',
  ];
}

/// Basic CSRF Provider - Simple implementation
class BasicCSRFNotifier extends StateNotifier<BasicCSRFState> {
  BasicCSRFNotifier() : super(const BasicCSRFState());

  /// Generate a simple CSRF token
  String _generateToken() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomValue = random.nextInt(999999);
    return 'csrf_${timestamp}_$randomValue';
  }

  /// Get a valid CSRF token
  Future<String?> getToken() async {
    // If CSRF is disabled, return null
    if (!state.isEnabled) {
      return null;
    }

    // Check if current token is valid
    if (state.isTokenValid) {
      return state.token;
    }

    // Generate new token
    return await _generateNewToken();
  }

  /// Generate a new CSRF token
  Future<String?> _generateNewToken() async {
    if (state.isLoading) {
      return state.token; // Return current token if already loading
    }

    state = state.copyWith(isLoading: true);

    try {
      final token = _generateToken();
      final expiresAt = DateTime.now().add(const Duration(hours: 24));

      state = state.copyWith(
        isLoading: false,
        token: token,
        expiresAt: expiresAt,
      );

      debugPrint('Generated new CSRF token: ${token.substring(0, 20)}...');
      return token;
    } catch (e) {
      state = state.copyWith(isLoading: false);
      debugPrint('Failed to generate CSRF token: $e');
      return null;
    }
  }

  /// Get CSRF headers for API requests
  Map<String, String> getHeaders() {
    if (!state.isEnabled || !state.isTokenValid) {
      return {};
    }

    return {
      BasicCSRFConfig.headerName: state.token!,
    };
  }

  /// Check if path is exempt from CSRF protection
  bool isPathExempt(String path) {
    if (!state.isEnabled) return true;
    
    return BasicCSRFConfig.exemptPaths.any((exemptPath) => 
      path.startsWith(exemptPath));
  }

  /// Check if method is exempt from CSRF protection
  bool isMethodExempt(String method) {
    if (!state.isEnabled) return true;
    
    return BasicCSRFConfig.exemptMethods.contains(method.toUpperCase());
  }

  /// Enable CSRF protection
  void enable() {
    state = state.copyWith(isEnabled: true);
    debugPrint('CSRF protection enabled');
  }

  /// Disable CSRF protection
  void disable() {
    state = state.copyWith(isEnabled: false);
    debugPrint('CSRF protection disabled');
  }

  /// Clear CSRF token
  void clearToken() {
    state = state.copyWith(token: null, expiresAt: null);
    debugPrint('CSRF token cleared');
  }

  /// Refresh token
  Future<String?> refreshToken() async {
    clearToken();
    return await getToken();
  }
}

// Providers
final basicCSRFProvider = StateNotifierProvider<BasicCSRFNotifier, BasicCSRFState>((ref) {
  return BasicCSRFNotifier();
});

// Helper provider to get CSRF token
final csrfTokenProvider = FutureProvider<String?>((ref) async {
  final csrfNotifier = ref.read(basicCSRFProvider.notifier);
  return await csrfNotifier.getToken();
});

// Helper provider to get CSRF headers
final csrfHeadersProvider = Provider<Map<String, String>>((ref) {
  final csrfNotifier = ref.read(basicCSRFProvider.notifier);
  return csrfNotifier.getHeaders();
});

// Helper provider to check if CSRF is needed for a request
final csrfRequiredProvider = Provider.family<bool, Map<String, String>>((ref, requestInfo) {
  final csrfNotifier = ref.read(basicCSRFProvider.notifier);
  final path = requestInfo['path'] ?? '';
  final method = requestInfo['method'] ?? 'GET';
  
  // CSRF is required if path and method are not exempt
  return !csrfNotifier.isPathExempt(path) && !csrfNotifier.isMethodExempt(method);
});

/// Extension to easily add CSRF headers to requests
extension CSRFHeaders on Map<String, String> {
  /// Add CSRF headers if needed
  Map<String, String> withCSRF(WidgetRef ref, {String? path, String? method}) {
    final requestInfo = {
      'path': path ?? '',
      'method': method ?? 'GET',
    };
    
    final isRequired = ref.read(csrfRequiredProvider(requestInfo));
    if (!isRequired) return this;
    
    final csrfHeaders = ref.read(csrfHeadersProvider);
    return {...this, ...csrfHeaders};
  }
}

/// Utility class for CSRF operations
class CSRFUtils {
  /// Check if CSRF protection is needed for a request
  static bool isProtectionNeeded(String path, String method) {
    // Check if method is exempt
    if (BasicCSRFConfig.exemptMethods.contains(method.toUpperCase())) {
      return false;
    }
    
    // Check if path is exempt
    return !BasicCSRFConfig.exemptPaths.any((exemptPath) => 
      path.startsWith(exemptPath));
  }
  
  /// Get CSRF header name
  static String get headerName => BasicCSRFConfig.headerName;
  
  /// Get exempt paths
  static List<String> get exemptPaths => BasicCSRFConfig.exemptPaths;
  
  /// Get exempt methods
  static List<String> get exemptMethods => BasicCSRFConfig.exemptMethods;
}

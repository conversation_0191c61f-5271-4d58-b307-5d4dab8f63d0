// ============================================================================
// CarNow Unified Authentication System - Security Headers and CORS Service
// ============================================================================
// File: security_headers_service.dart
// Description: Security headers and CORS configuration service
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../error/app_error.dart';
import '../error/app_error_factory.dart';
import '../networking/simple_api_client.dart';

part 'security_headers_service.g.dart';

/// Security header types
enum SecurityHeaderType {
  contentSecurityPolicy,    // CSP header
  strictTransportSecurity,  // HSTS header
  xFrameOptions,           // X-Frame-Options header
  xContentTypeOptions,     // X-Content-Type-Options header
  xXSSProtection,          // X-XSS-Protection header
  referrerPolicy,          // Referrer-Policy header
  permissionsPolicy,       // Permissions-Policy header
  crossOriginEmbedderPolicy, // COEP header
  crossOriginOpenerPolicy,   // COOP header
  crossOriginResourcePolicy, // CORP header
}

/// CORS policy configuration
class CORSPolicy {
  final List<String> allowedOrigins;
  final List<String> allowedMethods;
  final List<String> allowedHeaders;
  final List<String> exposedHeaders;
  final bool allowCredentials;
  final int maxAge;
  final bool preflightContinue;
  final int optionsSuccessStatus;

  const CORSPolicy({
    this.allowedOrigins = const ['*'],
    this.allowedMethods = const ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    this.allowedHeaders = const ['Content-Type', 'Authorization', 'X-Requested-With'],
    this.exposedHeaders = const [],
    this.allowCredentials = false,
    this.maxAge = 86400, // 24 hours
    this.preflightContinue = false,
    this.optionsSuccessStatus = 204,
  });

  /// Production CORS policy for CarNow
  static const carNowProduction = CORSPolicy(
    allowedOrigins: [
      'https://carnow.app',
      'https://www.carnow.app',
      'https://admin.carnow.app',
      'https://api.carnow.app',
    ],
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'X-API-Key',
      'X-Client-Version',
      'X-Device-ID',
      'Accept',
      'Origin',
    ],
    exposedHeaders: [
      'X-Total-Count',
      'X-Rate-Limit-Remaining',
      'X-Rate-Limit-Reset',
    ],
    allowCredentials: true,
    maxAge: 3600, // 1 hour
  );

  /// Development CORS policy (more permissive)
  static const development = CORSPolicy(
    allowedOrigins: [
      'http://localhost:3000',
      'http://localhost:8080',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:8080',
      'https://localhost:3000',
      'https://localhost:8080',
    ],
    allowCredentials: true,
    maxAge: 600, // 10 minutes
  );

  factory CORSPolicy.fromJson(Map<String, dynamic> json) {
    return CORSPolicy(
      allowedOrigins: List<String>.from(json['allowed_origins'] ?? ['*']),
      allowedMethods: List<String>.from(json['allowed_methods'] ?? ['GET', 'POST']),
      allowedHeaders: List<String>.from(json['allowed_headers'] ?? ['Content-Type']),
      exposedHeaders: List<String>.from(json['exposed_headers'] ?? []),
      allowCredentials: json['allow_credentials'] ?? false,
      maxAge: json['max_age'] ?? 86400,
      preflightContinue: json['preflight_continue'] ?? false,
      optionsSuccessStatus: json['options_success_status'] ?? 204,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'allowed_origins': allowedOrigins,
      'allowed_methods': allowedMethods,
      'allowed_headers': allowedHeaders,
      'exposed_headers': exposedHeaders,
      'allow_credentials': allowCredentials,
      'max_age': maxAge,
      'preflight_continue': preflightContinue,
      'options_success_status': optionsSuccessStatus,
    };
  }
}

/// Security header configuration
class SecurityHeaderConfig {
  final Map<SecurityHeaderType, String> headers;
  final bool enabled;
  final List<String> excludedPaths;
  final Map<String, dynamic> customHeaders;

  const SecurityHeaderConfig({
    required this.headers,
    this.enabled = true,
    this.excludedPaths = const [],
    this.customHeaders = const {},
  });

  /// Production security headers for CarNow
  static const carNowProduction = SecurityHeaderConfig(
    headers: {
      SecurityHeaderType.contentSecurityPolicy: 
          "default-src 'self'; "
          "script-src 'self' 'unsafe-inline' https://apis.google.com https://www.gstatic.com; "
          "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
          "font-src 'self' https://fonts.gstatic.com; "
          "img-src 'self' data: https:; "
          "connect-src 'self' https://api.carnow.app https://lpxtghyvxuenyyisrrro.supabase.co; "
          "frame-src 'none'; "
          "object-src 'none'; "
          "base-uri 'self'; "
          "form-action 'self';",
      
      SecurityHeaderType.strictTransportSecurity: 
          "max-age=31536000; includeSubDomains; preload",
      
      SecurityHeaderType.xFrameOptions: "DENY",
      
      SecurityHeaderType.xContentTypeOptions: "nosniff",
      
      SecurityHeaderType.xXSSProtection: "1; mode=block",
      
      SecurityHeaderType.referrerPolicy: "strict-origin-when-cross-origin",
      
      SecurityHeaderType.permissionsPolicy: 
          "geolocation=(), microphone=(), camera=(), payment=(self)",
      
      SecurityHeaderType.crossOriginEmbedderPolicy: "require-corp",
      
      SecurityHeaderType.crossOriginOpenerPolicy: "same-origin",
      
      SecurityHeaderType.crossOriginResourcePolicy: "same-origin",
    },
    excludedPaths: ['/health', '/metrics'],
  );

  /// Development security headers (less restrictive)
  static const development = SecurityHeaderConfig(
    headers: {
      SecurityHeaderType.contentSecurityPolicy: 
          "default-src 'self' 'unsafe-inline' 'unsafe-eval'; "
          "connect-src 'self' http://localhost:* https://localhost:* ws://localhost:* wss://localhost:*; "
          "img-src 'self' data: blob:;",
      
      SecurityHeaderType.xFrameOptions: "SAMEORIGIN",
      
      SecurityHeaderType.xContentTypeOptions: "nosniff",
      
      SecurityHeaderType.referrerPolicy: "no-referrer-when-downgrade",
    },
    excludedPaths: ['/health', '/metrics', '/debug'],
  );

  factory SecurityHeaderConfig.fromJson(Map<String, dynamic> json) {
    final headersMap = <SecurityHeaderType, String>{};
    final headersJson = json['headers'] as Map<String, dynamic>? ?? {};
    
    headersJson.forEach((key, value) {
      final headerType = SecurityHeaderType.values.firstWhere(
        (type) => type.name == key,
        orElse: () => SecurityHeaderType.contentSecurityPolicy,
      );
      headersMap[headerType] = value.toString();
    });

    return SecurityHeaderConfig(
      headers: headersMap,
      enabled: json['enabled'] ?? true,
      excludedPaths: List<String>.from(json['excluded_paths'] ?? []),
      customHeaders: json['custom_headers'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    final headersJson = <String, String>{};
    headers.forEach((type, value) {
      headersJson[type.name] = value;
    });

    return {
      'headers': headersJson,
      'enabled': enabled,
      'excluded_paths': excludedPaths,
      'custom_headers': customHeaders,
    };
  }
}

/// Security configuration
class SecurityConfig {
  final SecurityHeaderConfig headerConfig;
  final CORSPolicy corsPolicy;
  final bool enableCSRFProtection;
  final bool enableRateLimiting;
  final bool enableIPWhitelist;
  final List<String> trustedProxies;
  final Map<String, dynamic> additionalConfig;

  const SecurityConfig({
    required this.headerConfig,
    required this.corsPolicy,
    this.enableCSRFProtection = true,
    this.enableRateLimiting = true,
    this.enableIPWhitelist = false,
    this.trustedProxies = const [],
    this.additionalConfig = const {},
  });

  /// Production security configuration
  static const production = SecurityConfig(
    headerConfig: SecurityHeaderConfig.carNowProduction,
    corsPolicy: CORSPolicy.carNowProduction,
    enableCSRFProtection: true,
    enableRateLimiting: true,
    enableIPWhitelist: true,
    trustedProxies: ['127.0.0.1', '::1'],
  );

  /// Development security configuration
  static const development = SecurityConfig(
    headerConfig: SecurityHeaderConfig.development,
    corsPolicy: CORSPolicy.development,
    enableCSRFProtection: false,
    enableRateLimiting: false,
    enableIPWhitelist: false,
  );

  factory SecurityConfig.fromJson(Map<String, dynamic> json) {
    return SecurityConfig(
      headerConfig: SecurityHeaderConfig.fromJson(json['header_config'] ?? {}),
      corsPolicy: CORSPolicy.fromJson(json['cors_policy'] ?? {}),
      enableCSRFProtection: json['enable_csrf_protection'] ?? true,
      enableRateLimiting: json['enable_rate_limiting'] ?? true,
      enableIPWhitelist: json['enable_ip_whitelist'] ?? false,
      trustedProxies: List<String>.from(json['trusted_proxies'] ?? []),
      additionalConfig: json['additional_config'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'header_config': headerConfig.toJson(),
      'cors_policy': corsPolicy.toJson(),
      'enable_csrf_protection': enableCSRFProtection,
      'enable_rate_limiting': enableRateLimiting,
      'enable_ip_whitelist': enableIPWhitelist,
      'trusted_proxies': trustedProxies,
      'additional_config': additionalConfig,
    };
  }
}

/// Security headers service with CORS support
class SecurityHeadersService {
  final SimpleApiClient _apiClient;
  late SharedPreferences _prefs;
  
  // Current configuration
  SecurityConfig _currentConfig = SecurityConfig.development;
  
  // Stream controller for configuration updates
  final StreamController<SecurityConfig> _configController = 
      StreamController<SecurityConfig>.broadcast();

  SecurityHeadersService(this._apiClient);

  /// Initialize the service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadConfiguration();
  }

  /// Stream of configuration changes
  Stream<SecurityConfig> get configStream => _configController.stream;

  /// Get current security configuration
  SecurityConfig get currentConfig => _currentConfig;

  /// Update security configuration
  Future<AppResult<void>> updateSecurityConfig(SecurityConfig config) async {
    try {
      // Send to server
      await _apiClient.post('/security/config', data: config.toJson());

      // Update local config
      _currentConfig = config;
      _configController.add(config);

      // Cache locally
      await _cacheConfiguration(config);

      return const AppResult.success(null);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Get security headers for current configuration
  Map<String, String> getSecurityHeaders({String? path}) {
    final config = currentConfig.headerConfig;
    
    // Check if path is excluded
    if (path != null && config.excludedPaths.any((excluded) => path.startsWith(excluded))) {
      return {};
    }

    final headers = <String, String>{};
    
    // Add configured security headers
    config.headers.forEach((type, value) {
      headers[_getHeaderName(type)] = value;
    });

    // Add custom headers
    config.customHeaders.forEach((key, value) {
      headers[key] = value.toString();
    });

    return headers;
  }

  /// Get CORS headers for a request
  Map<String, String> getCORSHeaders({
    required String origin,
    required String method,
    List<String>? requestHeaders,
  }) {
    final policy = currentConfig.corsPolicy;
    final headers = <String, String>{};

    // Check if origin is allowed
    final isOriginAllowed = policy.allowedOrigins.contains('*') ||
        policy.allowedOrigins.contains(origin) ||
        _isOriginMatched(origin, policy.allowedOrigins);

    if (!isOriginAllowed) {
      return {}; // No CORS headers for disallowed origins
    }

    // Access-Control-Allow-Origin
    if (policy.allowedOrigins.contains('*') && !policy.allowCredentials) {
      headers['Access-Control-Allow-Origin'] = '*';
    } else {
      headers['Access-Control-Allow-Origin'] = origin;
    }

    // Access-Control-Allow-Credentials
    if (policy.allowCredentials) {
      headers['Access-Control-Allow-Credentials'] = 'true';
    }

    // Access-Control-Allow-Methods
    if (method == 'OPTIONS') {
      headers['Access-Control-Allow-Methods'] = policy.allowedMethods.join(', ');
    }

    // Access-Control-Allow-Headers
    if (requestHeaders != null && requestHeaders.isNotEmpty) {
      final allowedHeaders = requestHeaders.where((header) =>
          policy.allowedHeaders.contains('*') ||
          policy.allowedHeaders.any((allowed) => 
              header.toLowerCase() == allowed.toLowerCase())
      ).toList();
      
      if (allowedHeaders.isNotEmpty) {
        headers['Access-Control-Allow-Headers'] = allowedHeaders.join(', ');
      }
    } else if (method == 'OPTIONS') {
      headers['Access-Control-Allow-Headers'] = policy.allowedHeaders.join(', ');
    }

    // Access-Control-Expose-Headers
    if (policy.exposedHeaders.isNotEmpty) {
      headers['Access-Control-Expose-Headers'] = policy.exposedHeaders.join(', ');
    }

    // Access-Control-Max-Age
    if (method == 'OPTIONS') {
      headers['Access-Control-Max-Age'] = policy.maxAge.toString();
    }

    return headers;
  }

  /// Validate security configuration
  Future<AppResult<Map<String, dynamic>>> validateConfiguration(
    SecurityConfig config,
  ) async {
    try {
      final response = await _apiClient.post('/security/config/validate', 
          data: config.toJson());

      return AppResult.success(response.data);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Get security recommendations
  Future<AppResult<List<Map<String, dynamic>>>> getSecurityRecommendations() async {
    try {
      final response = await _apiClient.get('/security/recommendations');
      
      return AppResult.success(List<Map<String, dynamic>>.from(response.data));
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Test CORS configuration
  Future<AppResult<Map<String, dynamic>>> testCORSConfiguration({
    required String origin,
    required String method,
    List<String>? headers,
  }) async {
    try {
      final response = await _apiClient.post('/security/cors/test', data: {
        'origin': origin,
        'method': method,
        'headers': headers ?? [],
      });

      return AppResult.success(response.data);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Get security audit report
  Future<AppResult<Map<String, dynamic>>> getSecurityAuditReport() async {
    try {
      final response = await _apiClient.get('/security/audit');
      
      return AppResult.success(response.data);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Load configuration from server
  Future<void> _loadConfiguration() async {
    try {
      // Try to load from server first
      final response = await _apiClient.get('/security/config');
      if (response.isSuccess) {
        final config = SecurityConfig.fromJson(response.data);
        _currentConfig = config;
        await _cacheConfiguration(config);
      } else {
        // Fallback to cached configuration
        final cachedConfig = await _getCachedConfiguration();
        if (cachedConfig != null) {
          _currentConfig = cachedConfig;
        } else {
          // Use development config as last resort
          _currentConfig = SecurityConfig.development;
        }
      }
    } catch (e) {
      // Fallback to cached configuration
      final cachedConfig = await _getCachedConfiguration();
      if (cachedConfig != null) {
        _currentConfig = cachedConfig;
      } else {
        // Use development config as last resort
        _currentConfig = SecurityConfig.development;
      }
    }
  }

  /// Cache configuration locally
  Future<void> _cacheConfiguration(SecurityConfig config) async {
    await _prefs.setString('security_config', jsonEncode(config.toJson()));
  }

  /// Get cached configuration
  Future<SecurityConfig?> _getCachedConfiguration() async {
    final data = _prefs.getString('security_config');
    if (data != null) {
      try {
        return SecurityConfig.fromJson(jsonDecode(data));
      } catch (e) {
        // Invalid cached data, remove it
        await _prefs.remove('security_config');
      }
    }
    return null;
  }

  /// Get HTTP header name for security header type
  String _getHeaderName(SecurityHeaderType type) {
    switch (type) {
      case SecurityHeaderType.contentSecurityPolicy:
        return 'Content-Security-Policy';
      case SecurityHeaderType.strictTransportSecurity:
        return 'Strict-Transport-Security';
      case SecurityHeaderType.xFrameOptions:
        return 'X-Frame-Options';
      case SecurityHeaderType.xContentTypeOptions:
        return 'X-Content-Type-Options';
      case SecurityHeaderType.xXSSProtection:
        return 'X-XSS-Protection';
      case SecurityHeaderType.referrerPolicy:
        return 'Referrer-Policy';
      case SecurityHeaderType.permissionsPolicy:
        return 'Permissions-Policy';
      case SecurityHeaderType.crossOriginEmbedderPolicy:
        return 'Cross-Origin-Embedder-Policy';
      case SecurityHeaderType.crossOriginOpenerPolicy:
        return 'Cross-Origin-Opener-Policy';
      case SecurityHeaderType.crossOriginResourcePolicy:
        return 'Cross-Origin-Resource-Policy';
    }
  }

  /// Check if origin matches any allowed pattern
  bool _isOriginMatched(String origin, List<String> allowedOrigins) {
    for (final allowed in allowedOrigins) {
      if (allowed.contains('*')) {
        // Simple wildcard matching
        final pattern = allowed.replaceAll('*', '.*');
        final regex = RegExp('^$pattern\$');
        if (regex.hasMatch(origin)) {
          return true;
        }
      }
    }
    return false;
  }

  /// Dispose resources
  void dispose() {
    _configController.close();
  }
}

/// Riverpod provider for SecurityHeadersService
@riverpod
SecurityHeadersService securityHeadersService(Ref ref) {
  final apiClient = ref.read(simpleApiClientProvider);
  final service = SecurityHeadersService(apiClient);
  
  // Initialize the service
  service.initialize();
  
  // Dispose when provider is disposed
  ref.onDispose(() => service.dispose());
  
  return service;
}

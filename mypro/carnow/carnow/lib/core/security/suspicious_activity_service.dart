// ============================================================================
// CarNow Unified Authentication System - Suspicious Activity Detection Service
// ============================================================================
// File: suspicious_activity_service.dart
// Description: Advanced suspicious activity detection and alerting service
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'dart:async';
import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../error/app_error.dart';
import '../error/app_error_factory.dart';
import '../networking/simple_api_client.dart';

part 'suspicious_activity_service.g.dart';

/// Types of suspicious activities
enum SuspiciousActivityType {
  multipleFailedLogins,     // Multiple failed login attempts
  rapidLoginAttempts,       // Too many login attempts in short time
  unusualLocation,          // Login from unusual geographic location
  newDeviceLogin,           // Login from new/unknown device
  multipleDevices,          // Simultaneous logins from multiple devices
  offHoursActivity,         // Activity during unusual hours
  passwordSpray,            // Password spray attack pattern
  credentialStuffing,       // Credential stuffing attack pattern
  bruteForceAttack,         // Brute force attack pattern
  botActivity,              // Automated bot activity
}

/// Risk levels for suspicious activities
enum RiskLevel {
  low,      // Low risk - monitor only
  medium,   // Medium risk - alert user
  high,     // High risk - require additional verification
  critical, // Critical risk - lock account immediately
}

/// Suspicious activity record
class SuspiciousActivity {
  final String id;
  final String? userId;
  final String? email;
  final SuspiciousActivityType type;
  final RiskLevel riskLevel;
  final String description;
  final Map<String, dynamic> metadata;
  final DateTime detectedAt;
  final String ipAddress;
  final String? userAgent;
  final String? location;
  final bool resolved;

  const SuspiciousActivity({
    required this.id,
    this.userId,
    this.email,
    required this.type,
    required this.riskLevel,
    required this.description,
    required this.metadata,
    required this.detectedAt,
    required this.ipAddress,
    this.userAgent,
    this.location,
    this.resolved = false,
  });

  factory SuspiciousActivity.fromJson(Map<String, dynamic> json) {
    return SuspiciousActivity(
      id: json['id'],
      userId: json['user_id'],
      email: json['email'],
      type: SuspiciousActivityType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => SuspiciousActivityType.multipleFailedLogins,
      ),
      riskLevel: RiskLevel.values.firstWhere(
        (r) => r.name == json['risk_level'],
        orElse: () => RiskLevel.low,
      ),
      description: json['description'],
      metadata: json['metadata'] ?? {},
      detectedAt: DateTime.parse(json['detected_at']),
      ipAddress: json['ip_address'],
      userAgent: json['user_agent'],
      location: json['location'],
      resolved: json['resolved'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'email': email,
      'type': type.name,
      'risk_level': riskLevel.name,
      'description': description,
      'metadata': metadata,
      'detected_at': detectedAt.toIso8601String(),
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'location': location,
      'resolved': resolved,
    };
  }

  /// Get localized description in Arabic
  String getLocalizedDescription(String locale) {
    if (locale == 'ar') {
      return _getArabicDescription();
    }
    return description;
  }

  String _getArabicDescription() {
    switch (type) {
      case SuspiciousActivityType.multipleFailedLogins:
        return 'محاولات تسجيل دخول فاشلة متعددة';
      case SuspiciousActivityType.rapidLoginAttempts:
        return 'محاولات تسجيل دخول سريعة متتالية';
      case SuspiciousActivityType.unusualLocation:
        return 'تسجيل دخول من موقع غير معتاد';
      case SuspiciousActivityType.newDeviceLogin:
        return 'تسجيل دخول من جهاز جديد';
      case SuspiciousActivityType.multipleDevices:
        return 'تسجيل دخول من أجهزة متعددة';
      case SuspiciousActivityType.offHoursActivity:
        return 'نشاط في أوقات غير عادية';
      case SuspiciousActivityType.passwordSpray:
        return 'هجوم رش كلمات المرور';
      case SuspiciousActivityType.credentialStuffing:
        return 'هجوم حشو بيانات الاعتماد';
      case SuspiciousActivityType.bruteForceAttack:
        return 'هجوم القوة الغاشمة';
      case SuspiciousActivityType.botActivity:
        return 'نشاط آلي مشبوه';
    }
  }
}

/// Detection rule configuration
class DetectionRule {
  final SuspiciousActivityType type;
  final bool enabled;
  final RiskLevel defaultRiskLevel;
  final Duration timeWindow;
  final int threshold;
  final Map<String, dynamic> parameters;

  const DetectionRule({
    required this.type,
    this.enabled = true,
    required this.defaultRiskLevel,
    required this.timeWindow,
    required this.threshold,
    this.parameters = const {},
  });
}

/// Suspicious activity detection service
class SuspiciousActivityService {
  final SimpleApiClient _apiClient;
  
  // Detection rules
  late Map<SuspiciousActivityType, DetectionRule> _detectionRules;
  
  // Activity tracking
  final Map<String, List<Map<String, dynamic>>> _userActivities = {};
  final Map<String, List<Map<String, dynamic>>> _ipActivities = {};
  
  // Stream controllers
  final StreamController<SuspiciousActivity> _activityController = 
      StreamController<SuspiciousActivity>.broadcast();

  SuspiciousActivityService(this._apiClient);

  /// Initialize the service
  Future<void> initialize() async {
    await _loadDetectionRules();
    _startMonitoring();
  }

  /// Stream of suspicious activities
  Stream<SuspiciousActivity> get activityStream => _activityController.stream;

  /// Record user activity for analysis
  Future<void> recordActivity({
    required String activityType,
    String? userId,
    String? email,
    required String ipAddress,
    String? userAgent,
    String? location,
    Map<String, dynamic>? metadata,
  }) async {
    final activity = {
      'type': activityType,
      'user_id': userId,
      'email': email,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'location': location,
      'timestamp': DateTime.now().toIso8601String(),
      'metadata': metadata ?? {},
    };

    // Store in memory for analysis
    if (userId != null) {
      _userActivities.putIfAbsent(userId, () => []).add(activity);
    }
    if (email != null) {
      _userActivities.putIfAbsent(email, () => []).add(activity);
    }
    _ipActivities.putIfAbsent(ipAddress, () => []).add(activity);

    // Analyze for suspicious patterns
    await _analyzeActivity(activity);

    // Clean old activities
    _cleanOldActivities();
  }

  /// Analyze activity for suspicious patterns
  Future<void> _analyzeActivity(Map<String, dynamic> activity) async {
    for (final rule in _detectionRules.values) {
      if (!rule.enabled) continue;

      final suspiciousActivity = await _checkRule(activity, rule);
      if (suspiciousActivity != null) {
        await _handleSuspiciousActivity(suspiciousActivity);
      }
    }
  }

  /// Check a specific detection rule
  Future<SuspiciousActivity?> _checkRule(
    Map<String, dynamic> activity,
    DetectionRule rule,
  ) async {
    switch (rule.type) {
      case SuspiciousActivityType.multipleFailedLogins:
        return await _checkMultipleFailedLogins(activity, rule);
      case SuspiciousActivityType.rapidLoginAttempts:
        return await _checkRapidLoginAttempts(activity, rule);
      case SuspiciousActivityType.unusualLocation:
        return await _checkUnusualLocation(activity, rule);
      case SuspiciousActivityType.newDeviceLogin:
        return await _checkNewDeviceLogin(activity, rule);
      case SuspiciousActivityType.botActivity:
        return await _checkBotActivity(activity, rule);
      default:
        return null;
    }
  }

  /// Check for multiple failed logins
  Future<SuspiciousActivity?> _checkMultipleFailedLogins(
    Map<String, dynamic> activity,
    DetectionRule rule,
  ) async {
    if (activity['type'] != 'login_failed') return null;

    final email = activity['email'] as String?;
    if (email == null) return null;

    final userActivities = _userActivities[email] ?? [];
    final recentFailures = userActivities.where((a) =>
        a['type'] == 'login_failed' &&
        DateTime.now().difference(DateTime.parse(a['timestamp'])) <= rule.timeWindow
    ).length;

    if (recentFailures >= rule.threshold) {
      return SuspiciousActivity(
        id: _generateId(),
        email: email,
        type: rule.type,
        riskLevel: rule.defaultRiskLevel,
        description: 'Multiple failed login attempts detected',
        metadata: {
          'failed_attempts': recentFailures,
          'time_window_minutes': rule.timeWindow.inMinutes,
        },
        detectedAt: DateTime.now(),
        ipAddress: activity['ip_address'],
        userAgent: activity['user_agent'],
        location: activity['location'],
      );
    }

    return null;
  }

  /// Check for rapid login attempts
  Future<SuspiciousActivity?> _checkRapidLoginAttempts(
    Map<String, dynamic> activity,
    DetectionRule rule,
  ) async {
    if (!activity['type'].toString().contains('login')) return null;

    final email = activity['email'] as String?;
    if (email == null) return null;

    final userActivities = _userActivities[email] ?? [];
    final recentAttempts = userActivities.where((a) =>
        a['type'].toString().contains('login') &&
        DateTime.now().difference(DateTime.parse(a['timestamp'])) <= rule.timeWindow
    ).length;

    if (recentAttempts >= rule.threshold) {
      return SuspiciousActivity(
        id: _generateId(),
        email: email,
        type: rule.type,
        riskLevel: rule.defaultRiskLevel,
        description: 'Rapid login attempts detected',
        metadata: {
          'login_attempts': recentAttempts,
          'time_window_minutes': rule.timeWindow.inMinutes,
        },
        detectedAt: DateTime.now(),
        ipAddress: activity['ip_address'],
        userAgent: activity['user_agent'],
        location: activity['location'],
      );
    }

    return null;
  }

  /// Check for unusual location
  Future<SuspiciousActivity?> _checkUnusualLocation(
    Map<String, dynamic> activity,
    DetectionRule rule,
  ) async {
    if (activity['type'] != 'login_success') return null;

    final email = activity['email'] as String?;
    final currentLocation = activity['location'] as String?;
    if (email == null || currentLocation == null) return null;

    // Get user's typical locations
    final userActivities = _userActivities[email] ?? [];
    final recentLocations = userActivities
        .where((a) => a['location'] != null)
        .map((a) => a['location'] as String)
        .toSet();

    // Check if current location is unusual
    if (recentLocations.isNotEmpty && !recentLocations.contains(currentLocation)) {
      return SuspiciousActivity(
        id: _generateId(),
        email: email,
        type: rule.type,
        riskLevel: rule.defaultRiskLevel,
        description: 'Login from unusual location detected',
        metadata: {
          'current_location': currentLocation,
          'typical_locations': recentLocations.toList(),
        },
        detectedAt: DateTime.now(),
        ipAddress: activity['ip_address'],
        userAgent: activity['user_agent'],
        location: currentLocation,
      );
    }

    return null;
  }

  /// Check for new device login
  Future<SuspiciousActivity?> _checkNewDeviceLogin(
    Map<String, dynamic> activity,
    DetectionRule rule,
  ) async {
    if (activity['type'] != 'login_success') return null;

    final email = activity['email'] as String?;
    final currentUserAgent = activity['user_agent'] as String?;
    if (email == null || currentUserAgent == null) return null;

    // Get user's known devices
    final userActivities = _userActivities[email] ?? [];
    final knownUserAgents = userActivities
        .where((a) => a['user_agent'] != null)
        .map((a) => a['user_agent'] as String)
        .toSet();

    // Check if current device is new
    if (knownUserAgents.isNotEmpty && !knownUserAgents.contains(currentUserAgent)) {
      return SuspiciousActivity(
        id: _generateId(),
        email: email,
        type: rule.type,
        riskLevel: rule.defaultRiskLevel,
        description: 'Login from new device detected',
        metadata: {
          'new_device': currentUserAgent,
          'known_devices_count': knownUserAgents.length,
        },
        detectedAt: DateTime.now(),
        ipAddress: activity['ip_address'],
        userAgent: currentUserAgent,
        location: activity['location'],
      );
    }

    return null;
  }

  /// Check for bot activity
  Future<SuspiciousActivity?> _checkBotActivity(
    Map<String, dynamic> activity,
    DetectionRule rule,
  ) async {
    final userAgent = activity['user_agent'] as String?;
    if (userAgent == null) return null;

    // Simple bot detection based on user agent patterns
    final botPatterns = [
      'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget', 'python',
      'java', 'go-http-client', 'okhttp', 'apache-httpclient'
    ];

    final lowerUserAgent = userAgent.toLowerCase();
    final isBotUserAgent = botPatterns.any((pattern) => 
        lowerUserAgent.contains(pattern));

    if (isBotUserAgent) {
      return SuspiciousActivity(
        id: _generateId(),
        email: activity['email'],
        type: rule.type,
        riskLevel: rule.defaultRiskLevel,
        description: 'Bot activity detected',
        metadata: {
          'user_agent': userAgent,
          'detected_patterns': botPatterns.where((p) => 
              lowerUserAgent.contains(p)).toList(),
        },
        detectedAt: DateTime.now(),
        ipAddress: activity['ip_address'],
        userAgent: userAgent,
        location: activity['location'],
      );
    }

    return null;
  }

  /// Handle detected suspicious activity
  Future<void> _handleSuspiciousActivity(SuspiciousActivity activity) async {
    // Add to stream
    _activityController.add(activity);

    // Send to server for logging
    try {
      await _apiClient.post('/auth/security/suspicious-activity', data: activity.toJson());
    } catch (e) {
      print('Failed to log suspicious activity: $e');
    }

    // Handle based on risk level
    switch (activity.riskLevel) {
      case RiskLevel.low:
        break;
      case RiskLevel.medium:
        await _sendUserAlert(activity);
        break;
      case RiskLevel.high:
        await _requireAdditionalVerification(activity);
        break;
      case RiskLevel.critical:
        await _lockAccountImmediately(activity);
        break;
    }
  }

  /// Send alert to user
  Future<void> _sendUserAlert(SuspiciousActivity activity) async {
    print('Sending user alert for: ${activity.description}');
  }

  /// Require additional verification
  Future<void> _requireAdditionalVerification(SuspiciousActivity activity) async {
    print('Requiring additional verification for: ${activity.description}');
  }

  /// Lock account immediately
  Future<void> _lockAccountImmediately(SuspiciousActivity activity) async {
    if (activity.email != null) {
      try {
        await _apiClient.post('/auth/lockout', data: {
          'email': activity.email,
          'reason': 'suspicious_activity',
          'activity_id': activity.id,
          'locked_at': DateTime.now().toIso8601String(),
        });
      } catch (e) {
        print('Failed to lock account: $e');
      }
    }
  }

  /// Get recent suspicious activities
  Future<AppResult<List<SuspiciousActivity>>> getRecentActivities({
    String? userId,
    String? email,
    Duration? period,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (userId != null) queryParams['user_id'] = userId;
      if (email != null) queryParams['email'] = email;
      if (period != null) queryParams['period_hours'] = period.inHours;

      // Build URL with query parameters
      final uri = Uri.parse('/auth/security/suspicious-activities')
          .replace(queryParameters: queryParams.map((k, v) => MapEntry(k, v.toString())));
      final response = await _apiClient.get(uri.toString());

      final activities = (response.data as List)
          .map((json) => SuspiciousActivity.fromJson(json))
          .toList();

      return AppResult.success(activities);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Load detection rules
  Future<void> _loadDetectionRules() async {
    _detectionRules = {
      SuspiciousActivityType.multipleFailedLogins: const DetectionRule(
        type: SuspiciousActivityType.multipleFailedLogins,
        defaultRiskLevel: RiskLevel.medium,
        timeWindow: Duration(minutes: 15),
        threshold: 5,
      ),
      SuspiciousActivityType.rapidLoginAttempts: const DetectionRule(
        type: SuspiciousActivityType.rapidLoginAttempts,
        defaultRiskLevel: RiskLevel.medium,
        timeWindow: Duration(minutes: 5),
        threshold: 10,
      ),
      SuspiciousActivityType.unusualLocation: const DetectionRule(
        type: SuspiciousActivityType.unusualLocation,
        defaultRiskLevel: RiskLevel.high,
        timeWindow: Duration(hours: 24),
        threshold: 1,
      ),
      SuspiciousActivityType.newDeviceLogin: const DetectionRule(
        type: SuspiciousActivityType.newDeviceLogin,
        defaultRiskLevel: RiskLevel.medium,
        timeWindow: Duration(hours: 1),
        threshold: 1,
      ),
      SuspiciousActivityType.botActivity: const DetectionRule(
        type: SuspiciousActivityType.botActivity,
        defaultRiskLevel: RiskLevel.high,
        timeWindow: Duration(minutes: 1),
        threshold: 1,
      ),
    };
  }

  /// Start monitoring
  void _startMonitoring() {
    Timer.periodic(const Duration(minutes: 5), (_) {
      _cleanOldActivities();
    });
  }

  /// Clean old activities from memory
  void _cleanOldActivities() {
    final cutoff = DateTime.now().subtract(const Duration(hours: 24));
    
    _userActivities.forEach((user, activities) {
      activities.removeWhere((a) => 
          DateTime.parse(a['timestamp']).isBefore(cutoff));
    });
    
    _ipActivities.forEach((ip, activities) {
      activities.removeWhere((a) => 
          DateTime.parse(a['timestamp']).isBefore(cutoff));
    });
    
    // Remove empty entries
    _userActivities.removeWhere((_, activities) => activities.isEmpty);
    _ipActivities.removeWhere((_, activities) => activities.isEmpty);
  }

  /// Generate unique ID
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() + 
           Random().nextInt(1000).toString();
  }

  /// Dispose resources
  void dispose() {
    _activityController.close();
  }
}

/// Riverpod provider for SuspiciousActivityService
@riverpod
SuspiciousActivityService suspiciousActivityService(Ref ref) {
  final apiClient = ref.read(simpleApiClientProvider);
  final service = SuspiciousActivityService(apiClient);
  
  // Initialize the service
  service.initialize();
  
  // Dispose when provider is disposed
  ref.onDispose(() => service.dispose());
  
  return service;
}

// ============================================================================
// CarNow Unified Authentication System - GDPR Compliance Service
// ============================================================================
// File: gdpr_compliance_service.dart
// Description: GDPR compliance features for data protection and user rights
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../error/app_error.dart';
import '../error/app_error_factory.dart';
import '../networking/simple_api_client.dart';

part 'gdpr_compliance_service.g.dart';

/// GDPR user rights
enum GDPRRight {
  access,           // Right to access personal data
  rectification,    // Right to rectify inaccurate data
  erasure,          // Right to erasure (right to be forgotten)
  portability,      // Right to data portability
  restriction,      // Right to restrict processing
  objection,        // Right to object to processing
  withdraw,         // Right to withdraw consent
}

/// Data processing purposes
enum ProcessingPurpose {
  authentication,   // User authentication and login
  profileManagement, // User profile and account management
  serviceDelivery,  // Core service delivery
  analytics,        // Analytics and performance monitoring
  marketing,        // Marketing communications
  support,          // Customer support
  legal,            // Legal compliance
  security,         // Security and fraud prevention
}

/// Consent status
enum ConsentStatus {
  granted,          // Consent granted
  denied,           // Consent denied
  withdrawn,        // Consent withdrawn
  pending,          // Consent pending
  expired,          // Consent expired
}

/// User consent record
class UserConsent {
  final String id;
  final String userId;
  final ProcessingPurpose purpose;
  final ConsentStatus status;
  final DateTime grantedAt;
  final DateTime? withdrawnAt;
  final DateTime? expiresAt;
  final String? withdrawalReason;
  final Map<String, dynamic> metadata;

  const UserConsent({
    required this.id,
    required this.userId,
    required this.purpose,
    required this.status,
    required this.grantedAt,
    this.withdrawnAt,
    this.expiresAt,
    this.withdrawalReason,
    this.metadata = const {},
  });

  bool get isActive => status == ConsentStatus.granted && 
      (expiresAt == null || DateTime.now().isBefore(expiresAt!));

  factory UserConsent.fromJson(Map<String, dynamic> json) {
    return UserConsent(
      id: json['id'],
      userId: json['user_id'],
      purpose: ProcessingPurpose.values.firstWhere(
        (p) => p.name == json['purpose'],
      ),
      status: ConsentStatus.values.firstWhere(
        (s) => s.name == json['status'],
      ),
      grantedAt: DateTime.parse(json['granted_at']),
      withdrawnAt: json['withdrawn_at'] != null 
          ? DateTime.parse(json['withdrawn_at']) 
          : null,
      expiresAt: json['expires_at'] != null 
          ? DateTime.parse(json['expires_at']) 
          : null,
      withdrawalReason: json['withdrawal_reason'],
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'purpose': purpose.name,
      'status': status.name,
      'granted_at': grantedAt.toIso8601String(),
      'withdrawn_at': withdrawnAt?.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'withdrawal_reason': withdrawalReason,
      'metadata': metadata,
    };
  }
}

/// Data export request
class DataExportRequest {
  final String id;
  final String userId;
  final String email;
  final DateTime requestedAt;
  final DateTime? completedAt;
  final String status; // pending, processing, completed, failed
  final String? downloadUrl;
  final DateTime? expiresAt;
  final Map<String, dynamic> metadata;

  const DataExportRequest({
    required this.id,
    required this.userId,
    required this.email,
    required this.requestedAt,
    this.completedAt,
    required this.status,
    this.downloadUrl,
    this.expiresAt,
    this.metadata = const {},
  });

  bool get isCompleted => status == 'completed';
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);

  factory DataExportRequest.fromJson(Map<String, dynamic> json) {
    return DataExportRequest(
      id: json['id'],
      userId: json['user_id'],
      email: json['email'],
      requestedAt: DateTime.parse(json['requested_at']),
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at']) 
          : null,
      status: json['status'],
      downloadUrl: json['download_url'],
      expiresAt: json['expires_at'] != null 
          ? DateTime.parse(json['expires_at']) 
          : null,
      metadata: json['metadata'] ?? {},
    );
  }
}

/// Data deletion request
class DataDeletionRequest {
  final String id;
  final String userId;
  final String email;
  final DateTime requestedAt;
  final DateTime? scheduledAt;
  final DateTime? completedAt;
  final String status; // pending, scheduled, processing, completed, cancelled
  final String? reason;
  final Map<String, dynamic> metadata;

  const DataDeletionRequest({
    required this.id,
    required this.userId,
    required this.email,
    required this.requestedAt,
    this.scheduledAt,
    this.completedAt,
    required this.status,
    this.reason,
    this.metadata = const {},
  });

  bool get isCompleted => status == 'completed';
  bool get isPending => status == 'pending';
  bool get isScheduled => status == 'scheduled';

  factory DataDeletionRequest.fromJson(Map<String, dynamic> json) {
    return DataDeletionRequest(
      id: json['id'],
      userId: json['user_id'],
      email: json['email'],
      requestedAt: DateTime.parse(json['requested_at']),
      scheduledAt: json['scheduled_at'] != null 
          ? DateTime.parse(json['scheduled_at']) 
          : null,
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at']) 
          : null,
      status: json['status'],
      reason: json['reason'],
      metadata: json['metadata'] ?? {},
    );
  }
}

/// GDPR compliance service
class GDPRComplianceService {
  final SimpleApiClient _apiClient;
  late SharedPreferences _prefs;
  
  // Stream controllers
  final StreamController<UserConsent> _consentController = 
      StreamController<UserConsent>.broadcast();
  final StreamController<DataExportRequest> _exportController = 
      StreamController<DataExportRequest>.broadcast();
  final StreamController<DataDeletionRequest> _deletionController = 
      StreamController<DataDeletionRequest>.broadcast();

  GDPRComplianceService(this._apiClient);

  /// Initialize the service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Streams
  Stream<UserConsent> get consentStream => _consentController.stream;
  Stream<DataExportRequest> get exportStream => _exportController.stream;
  Stream<DataDeletionRequest> get deletionStream => _deletionController.stream;

  /// Grant consent for data processing
  Future<AppResult<UserConsent>> grantConsent({
    required String userId,
    required ProcessingPurpose purpose,
    Duration? validity,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final expiresAt = validity != null ? DateTime.now().add(validity) : null;
      
      final response = await _apiClient.post<Map<String, dynamic>>('/gdpr/consent', data: {
        'user_id': userId,
        'purpose': purpose.name,
        'status': ConsentStatus.granted.name,
        'granted_at': DateTime.now().toIso8601String(),
        'expires_at': expiresAt?.toIso8601String(),
        'metadata': metadata ?? {},
      });

      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to grant consent');
      }

      final consent = UserConsent.fromJson(response.data!);
      _consentController.add(consent);

      // Cache locally
      await _cacheConsent(consent);

      return AppResult.success(consent);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Withdraw consent for data processing
  Future<AppResult<void>> withdrawConsent({
    required String userId,
    required ProcessingPurpose purpose,
    String? reason,
  }) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>('/gdpr/consent/withdraw', data: {
        'user_id': userId,
        'purpose': purpose.name,
        'withdrawn_at': DateTime.now().toIso8601String(),
        'withdrawal_reason': reason,
      });

      if (!response.isSuccess) {
        throw Exception(response.error ?? 'Failed to withdraw consent');
      }

      // Remove from local cache
      await _removeCachedConsent(userId, purpose);

      return const AppResult.success(null);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Get user consents
  Future<AppResult<List<UserConsent>>> getUserConsents(String userId) async {
    try {
      final response = await _apiClient.get<List<dynamic>>('/gdpr/consent/$userId');
      
      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to get user consents');
      }

      final consents = response.data!
          .map((json) => UserConsent.fromJson(json as Map<String, dynamic>))
          .toList();

      return AppResult.success(consents);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Check if user has granted consent for specific purpose
  Future<AppResult<bool>> hasConsent({
    required String userId,
    required ProcessingPurpose purpose,
  }) async {
    try {
      // Check cache first
      final cachedConsent = await _getCachedConsent(userId, purpose);
      if (cachedConsent != null) {
        return AppResult.success(cachedConsent.isActive);
      }

      // Check with server
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/gdpr/consent/$userId/${purpose.name}',
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to check consent status');
      }

      final hasConsent = response.data!['has_consent'] ?? false;
      return AppResult.success(hasConsent);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Request data export (Right to Data Portability)
  Future<AppResult<DataExportRequest>> requestDataExport({
    required String userId,
    required String email,
    List<String>? dataTypes,
    String? format = 'json',
  }) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>('/gdpr/export', data: {
        'user_id': userId,
        'email': email,
        'data_types': dataTypes,
        'format': format,
        'requested_at': DateTime.now().toIso8601String(),
      });

      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to request data export');
      }

      final exportRequest = DataExportRequest.fromJson(response.data!);
      _exportController.add(exportRequest);

      return AppResult.success(exportRequest);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Get data export status
  Future<AppResult<DataExportRequest>> getDataExportStatus(String requestId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>('/gdpr/export/$requestId');
      
      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to get export status');
      }

      final exportRequest = DataExportRequest.fromJson(response.data!);
      return AppResult.success(exportRequest);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Request data deletion (Right to be Forgotten)
  Future<AppResult<DataDeletionRequest>> requestDataDeletion({
    required String userId,
    required String email,
    String? reason,
    DateTime? scheduledAt,
    bool immediate = false,
  }) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>('/gdpr/deletion', data: {
        'user_id': userId,
        'email': email,
        'reason': reason,
        'scheduled_at': scheduledAt?.toIso8601String(),
        'immediate': immediate,
        'requested_at': DateTime.now().toIso8601String(),
      });

      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to request data deletion');
      }

      final deletionRequest = DataDeletionRequest.fromJson(response.data!);
      _deletionController.add(deletionRequest);

      return AppResult.success(deletionRequest);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Cancel data deletion request
  Future<AppResult<void>> cancelDataDeletion(String requestId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>('/gdpr/deletion/$requestId/cancel');
      
      if (!response.isSuccess) {
        throw Exception(response.error ?? 'Failed to cancel data deletion');
      }

      return const AppResult.success(null);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Get data deletion status
  Future<AppResult<DataDeletionRequest>> getDataDeletionStatus(String requestId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>('/gdpr/deletion/$requestId');
      
      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to get deletion status');
      }

      final deletionRequest = DataDeletionRequest.fromJson(response.data!);
      return AppResult.success(deletionRequest);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Get privacy policy and terms
  Future<AppResult<Map<String, dynamic>>> getPrivacyPolicy({
    String locale = 'en',
  }) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/gdpr/privacy-policy',
        queryParameters: {'locale': locale},
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to get privacy policy');
      }

      return AppResult.success(response.data!);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Record data processing activity
  Future<void> recordProcessingActivity({
    required String userId,
    required ProcessingPurpose purpose,
    required String activity,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>('/gdpr/processing-activity', data: {
        'user_id': userId,
        'purpose': purpose.name,
        'activity': activity,
        'timestamp': DateTime.now().toIso8601String(),
        'metadata': metadata ?? {},
      });
      
      if (!response.isSuccess) {
        print('Failed to record processing activity: ${response.error}');
      }
    } catch (e) {
      // Log but don't fail the main operation
      print('Failed to record processing activity: $e');
    }
  }

  /// Get user's data processing log
  Future<AppResult<List<Map<String, dynamic>>>> getProcessingLog({
    required String userId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (fromDate != null) queryParams['from'] = fromDate.toIso8601String();
      if (toDate != null) queryParams['to'] = toDate.toIso8601String();

      final response = await _apiClient.get<List<dynamic>>(
        '/gdpr/processing-log/$userId',
        queryParameters: queryParams,
      );

      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to get processing log');
      }

      return AppResult.success(
        response.data!.map((item) => Map<String, dynamic>.from(item)).toList(),
      );
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Cache consent locally
  Future<void> _cacheConsent(UserConsent consent) async {
    final key = 'consent_${consent.userId}_${consent.purpose.name}';
    await _prefs.setString(key, jsonEncode(consent.toJson()));
  }

  /// Get cached consent
  Future<UserConsent?> _getCachedConsent(String userId, ProcessingPurpose purpose) async {
    final key = 'consent_${userId}_${purpose.name}';
    final data = _prefs.getString(key);
    if (data != null) {
      try {
        return UserConsent.fromJson(jsonDecode(data));
      } catch (e) {
        // Invalid cached data, remove it
        await _prefs.remove(key);
      }
    }
    return null;
  }

  /// Remove cached consent
  Future<void> _removeCachedConsent(String userId, ProcessingPurpose purpose) async {
    final key = 'consent_${userId}_${purpose.name}';
    await _prefs.remove(key);
  }

  /// Get localized GDPR right description
  String getGDPRRightDescription(GDPRRight right, String locale) {
    if (locale == 'ar') {
      return _getArabicGDPRDescription(right);
    }
    return _getEnglishGDPRDescription(right);
  }

  String _getArabicGDPRDescription(GDPRRight right) {
    switch (right) {
      case GDPRRight.access:
        return 'الحق في الوصول إلى البيانات الشخصية';
      case GDPRRight.rectification:
        return 'الحق في تصحيح البيانات غير الدقيقة';
      case GDPRRight.erasure:
        return 'الحق في محو البيانات (الحق في النسيان)';
      case GDPRRight.portability:
        return 'الحق في نقل البيانات';
      case GDPRRight.restriction:
        return 'الحق في تقييد المعالجة';
      case GDPRRight.objection:
        return 'الحق في الاعتراض على المعالجة';
      case GDPRRight.withdraw:
        return 'الحق في سحب الموافقة';
    }
  }

  String _getEnglishGDPRDescription(GDPRRight right) {
    switch (right) {
      case GDPRRight.access:
        return 'Right to access your personal data';
      case GDPRRight.rectification:
        return 'Right to rectify inaccurate data';
      case GDPRRight.erasure:
        return 'Right to erasure (right to be forgotten)';
      case GDPRRight.portability:
        return 'Right to data portability';
      case GDPRRight.restriction:
        return 'Right to restrict processing';
      case GDPRRight.objection:
        return 'Right to object to processing';
      case GDPRRight.withdraw:
        return 'Right to withdraw consent';
    }
  }

  /// Dispose resources
  void dispose() {
    _consentController.close();
    _exportController.close();
    _deletionController.close();
  }
}

/// Riverpod provider for GDPRComplianceService
@riverpod
GDPRComplianceService gdprComplianceService(Ref ref) {
  final apiClient = ref.read(simpleApiClientProvider);
  final service = GDPRComplianceService(apiClient);
  
  // Initialize the service
  service.initialize();
  
  // Dispose when provider is disposed
  ref.onDispose(() => service.dispose());
  
  return service;
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gdpr_compliance_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$gdprComplianceServiceHash() =>
    r'efb4d9759cfc747e87631e66dfa6a2e5b009133f';

/// Riverpod provider for GDPRComplianceService
///
/// Copied from [gdprComplianceService].
@ProviderFor(gdprComplianceService)
final gdprComplianceServiceProvider =
    AutoDisposeProvider<GDPRComplianceService>.internal(
      gdprComplianceService,
      name: r'gdprComplianceServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$gdprComplianceServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GdprComplianceServiceRef =
    AutoDisposeProviderRef<GDPRComplianceService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

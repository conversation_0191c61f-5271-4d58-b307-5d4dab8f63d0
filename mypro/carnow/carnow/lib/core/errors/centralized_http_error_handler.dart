import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../../core/auth/unified_auth_provider.dart';
import 'app_error.dart';

final _logger = Logger('CentralizedHttpErrorHandler');

/// معالج أخطاء HTTP مركزي لمعمارية Forever Plan
/// Centralized HTTP Error Handler for Forever Plan Architecture
/// 
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// ✅ معالجة شاملة لجميع أخطاء HTTP
/// ✅ رسائل خطأ باللغة العربية صديقة للمستخدم
/// ✅ آلية استرداد تلقائية من الأخطاء
/// ✅ تكامل مع نظام المصادقة UnifiedAuthProvider
/// ✅ تسجيل مفصل للأخطاء
/// ✅ عرض أخطاء منظم للمستخدم
class CentralizedHttpErrorHandler {
  const CentralizedHttpErrorHandler(this._ref);
  
  final Ref _ref;

  /// معالجة خطأ HTTP من Go Backend وإرجاع خطأ منظم
  /// Handle HTTP error from Go Backend and return structured error
  Future<AppError> handleHttpError(
    dynamic error, {
    String? context,
    Map<String, dynamic>? additionalData,
    bool shouldRetry = false,
  }) async {
    final errorContext = context ?? 'عملية API';
    
    _logger.severe('HTTP Error in $errorContext: $error');

    // معالجة أخطاء Dio المختلفة
    if (error is DioException) {
      return await _handleDioError(error, errorContext, additionalData, shouldRetry);
    }

    // معالجة أخطاء الشبكة العامة
    if (error is SocketException) {
      return _handleNetworkError(error, errorContext, additionalData);
    }

    // معالجة أخطاء انتهاء المهلة
    if (error is TimeoutException) {
      return _handleTimeoutError(error, errorContext, additionalData);
    }

    // خطأ غير متوقع
    return AppError.unexpected(
      message: 'حدث خطأ غير متوقع في $errorContext',
      originalError: error,
      data: {
        'context': errorContext,
        if (additionalData != null) ...additionalData,
      },
    );
  }

  /// معالجة أخطاء Dio المتخصصة
  Future<AppError> _handleDioError(
    DioException error,
    String context,
    Map<String, dynamic>? additionalData,
    bool shouldRetry,
  ) async {
    final statusCode = error.response?.statusCode ?? 0;
    final responseData = error.response?.data;
    final backendMessage = responseData is Map ? responseData['message'] as String? : null;

    _logger.warning(
      'DioException - Status: $statusCode, Type: ${error.type}, Message: ${error.message}',
    );

    // معالجة حسب نوع الخطأ
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return _handleTimeoutDioError(error, context, additionalData);

      case DioExceptionType.badResponse:
        return await _handleBadResponseError(error, context, additionalData, shouldRetry);

      case DioExceptionType.connectionError:
        return _handleConnectionError(error, context, additionalData);

      case DioExceptionType.cancel:
        return AppError.network(
          message: 'تم إلغاء العملية',
          code: 'REQUEST_CANCELLED',
          originalError: error,
          data: {
            'context': context,
            if (additionalData != null) ...additionalData,
          },
        );

      default:
        return AppError.network(
          message: backendMessage ?? 'خطأ في الاتصال مع الخادم',
          code: 'UNKNOWN_DIO_ERROR',
          originalError: error,
          data: {
            'context': context,
            'statusCode': statusCode,
            'errorType': error.type.toString(),
            if (additionalData != null) ...additionalData,
          },
        );
    }
  }

  /// معالجة أخطاء الاستجابة السيئة (4xx, 5xx)
  Future<AppError> _handleBadResponseError(
    DioException error,
    String context,
    Map<String, dynamic>? additionalData,
    bool shouldRetry,
  ) async {
    final statusCode = error.response?.statusCode ?? 0;
    final responseData = error.response?.data;
    final backendMessage = responseData is Map ? responseData['message'] as String? : null;

    switch (statusCode) {
      // 400 - Bad Request
      case 400:
        return AppError.validation(
          message: backendMessage ?? 'البيانات المرسلة غير صحيحة',
          code: 'BAD_REQUEST',
          originalError: error,
          data: {
            'context': context,
            'statusCode': statusCode,
            'validationErrors': responseData is Map ? responseData['errors'] : null,
            if (additionalData != null) ...additionalData,
          },
        );

      // 401 - Unauthorized
      case 401:
        await _handleAuthenticationError();
        return AppError.authentication(
          message: backendMessage ?? 'انتهت جلسة المصادقة، يرجى تسجيل الدخول مرة أخرى',
          code: 'UNAUTHORIZED',
          originalError: error,
          data: {
            'context': context,
            'statusCode': statusCode,
            'requiresReauth': true,
            if (additionalData != null) ...additionalData,
          },
        );

      // 403 - Forbidden
      case 403:
        return AppError.permission(
          message: backendMessage ?? 'ليس لديك صلاحية للوصول إلى هذا المورد',
          code: 'FORBIDDEN',
          originalError: error,
          data: {
            'context': context,
            'statusCode': statusCode,
            if (additionalData != null) ...additionalData,
          },
        );

      // 404 - Not Found
      case 404:
        return AppError.notFound(
          message: backendMessage ?? 'المورد المطلوب غير موجود',
          code: 'NOT_FOUND',
          originalError: error,
          data: {
            'context': context,
            'statusCode': statusCode,
            if (additionalData != null) ...additionalData,
          },
        );

      // 422 - Unprocessable Entity
      case 422:
        return AppError.validation(
          message: backendMessage ?? 'البيانات غير قابلة للمعالجة',
          code: 'UNPROCESSABLE_ENTITY',
          originalError: error,
          data: {
            'context': context,
            'statusCode': statusCode,
            'validationErrors': responseData is Map ? responseData['errors'] : null,
            if (additionalData != null) ...additionalData,
          },
        );

      // 429 - Too Many Requests
      case 429:
        final retryAfter = error.response?.headers['retry-after']?.first;
        return AppError.rateLimited(
          message: backendMessage ?? 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً',
          code: 'RATE_LIMITED',
          originalError: error,
          data: {
            'context': context,
            'statusCode': statusCode,
            'retryAfter': retryAfter,
            if (additionalData != null) ...additionalData,
          },
        );

      // 500+ - Server Errors
      case 500:
      case 502:
      case 503:
        return AppError.network(
          message: _getServerErrorMessage(statusCode, backendMessage),
          code: _getServerErrorCode(statusCode),
          severity: ErrorSeverity.high,
          originalError: error,
          data: {
            'context': context,
            'statusCode': statusCode,
            'canRetry': shouldRetry || statusCode >= 500,
            if (additionalData != null) ...additionalData,
          },
        );

      // Other status codes
      default:
        if (statusCode >= 400 && statusCode < 500) {
          return AppError.validation(
            message: backendMessage ?? 'خطأ في البيانات المرسلة',
            code: 'CLIENT_ERROR',
            originalError: error,
            data: {
              'context': context,
              'statusCode': statusCode,
              if (additionalData != null) ...additionalData,
            },
          );
        } else if (statusCode >= 500) {
          return AppError.network(
            message: backendMessage ?? 'خطأ في الخادم',
            code: 'SERVER_ERROR',
            severity: ErrorSeverity.high,
            originalError: error,
            data: {
              'context': context,
              'statusCode': statusCode,
              'canRetry': shouldRetry,
              if (additionalData != null) ...additionalData,
            },
          );
        } else {
          return AppError.unexpected(
            message: backendMessage ?? 'حدث خطأ غير متوقع',
            originalError: error,
            data: {
              'context': context,
              'statusCode': statusCode,
              if (additionalData != null) ...additionalData,
            },
          );
        }
    }
  }

  /// الحصول على رسالة خطأ الخادم المناسبة
  String _getServerErrorMessage(int statusCode, String? backendMessage) {
    if (backendMessage != null && backendMessage.isNotEmpty) {
      return backendMessage;
    }
    
    switch (statusCode) {
      case 500:
        return 'خطأ في الخادم، يرجى المحاولة لاحقاً';
      case 502:
        return 'خطأ في البوابة، يرجى المحاولة لاحقاً';
      case 503:
        return 'الخدمة غير متاحة حالياً، يرجى المحاولة لاحقاً';
      default:
        return 'خطأ في الخادم';
    }
  }

  /// الحصول على رمز خطأ الخادم المناسب
  String _getServerErrorCode(int statusCode) {
    switch (statusCode) {
      case 500:
        return 'INTERNAL_SERVER_ERROR';
      case 502:
        return 'BAD_GATEWAY';
      case 503:
        return 'SERVICE_UNAVAILABLE';
      default:
        return 'SERVER_ERROR';
    }
  }

  /// معالجة أخطاء انتهاء المهلة من Dio
  AppError _handleTimeoutDioError(
    DioException error,
    String context,
    Map<String, dynamic>? additionalData,
  ) {
    String message;
    String code;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        message = 'انتهت مهلة الاتصال بالخادم';
        code = 'CONNECTION_TIMEOUT';
        break;
      case DioExceptionType.sendTimeout:
        message = 'انتهت مهلة إرسال البيانات';
        code = 'SEND_TIMEOUT';
        break;
      case DioExceptionType.receiveTimeout:
        message = 'انتهت مهلة استقبال البيانات';
        code = 'RECEIVE_TIMEOUT';
        break;
      default:
        message = 'انتهت مهلة العملية';
        code = 'TIMEOUT';
    }

    return AppError.network(
      message: message,
      code: code,
      severity: ErrorSeverity.medium,
      originalError: error,
      data: {
        'context': context,
        'errorType': error.type.toString(),
        'canRetry': true,
        if (additionalData != null) ...additionalData,
      },
    );
  }

  /// معالجة أخطاء الاتصال
  AppError _handleConnectionError(
    DioException error,
    String context,
    Map<String, dynamic>? additionalData,
  ) {
    return AppError.network(
      message: 'فشل الاتصال بالخادم، تحقق من اتصال الإنترنت',
      code: 'CONNECTION_ERROR',
      severity: ErrorSeverity.high,
      originalError: error,
      data: {
        'context': context,
        'errorType': error.type.toString(),
        'canRetry': true,
        if (additionalData != null) ...additionalData,
      },
    );
  }

  /// معالجة أخطاء الشبكة العامة
  AppError _handleNetworkError(
    SocketException error,
    String context,
    Map<String, dynamic>? additionalData,
  ) {
    return AppError.network(
      message: 'خطأ في الشبكة، تحقق من اتصال الإنترنت',
      code: 'NETWORK_ERROR',
      severity: ErrorSeverity.high,
      originalError: error,
      data: {
        'context': context,
        'canRetry': true,
        if (additionalData != null) ...additionalData,
      },
    );
  }

  /// معالجة أخطاء انتهاء المهلة
  AppError _handleTimeoutError(
    TimeoutException error,
    String context,
    Map<String, dynamic>? additionalData,
  ) {
    return AppError.network(
      message: 'انتهت مهلة العملية، يرجى المحاولة مرة أخرى',
      code: 'TIMEOUT',
      severity: ErrorSeverity.medium,
      originalError: error,
      data: {
        'context': context,
        'canRetry': true,
        if (additionalData != null) ...additionalData,
      },
    );
  }

  /// معالجة خطأ المصادقة - تنظيف الجلسة وإعادة التوجيه
  Future<void> _handleAuthenticationError() async {
    try {
      _logger.warning('Authentication error detected - clearing session');
      
      // إشارة لنظام المصادقة لتنظيف الجلسة
      _ref.invalidate(unifiedAuthProviderProvider);
      
    } catch (e) {
      _logger.severe('Error during authentication cleanup: $e');
    }
  }

  /// عرض خطأ للمستخدم مع إمكانية إعادة المحاولة
  void showError(
    BuildContext context,
    AppError error, {
    VoidCallback? onRetry,
    bool showRetryButton = false,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          error.message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: _getErrorColor(error.severity),
        duration: Duration(
          seconds: error.severity == ErrorSeverity.high ? 8 : 4,
        ),
        action: showRetryButton && onRetry != null
            ? SnackBarAction(
                label: 'إعادة المحاولة',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  /// الحصول على لون الخطأ حسب الشدة
  Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return Colors.orange;
      case ErrorSeverity.medium:
        return Colors.deepOrange;
      case ErrorSeverity.high:
        return Colors.red.shade700;
      case ErrorSeverity.critical:
        return Colors.red.shade900;
    }
  }

  /// فحص ما إذا كان الخطأ قابل لإعادة المحاولة
  bool isRetryableError(AppError error) {
    final canRetry = error.data?['canRetry'] as bool?;
    if (canRetry != null) return canRetry;

    // قواعد افتراضية لإعادة المحاولة
    switch (error.type) {
      case AppErrorType.network:
        return true;
      case AppErrorType.rateLimited:
        return true;
      default:
        return false;
    }
  }

  /// عرض رسالة خطأ مبسطة
  static void showQuickError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.red.shade700,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// عرض رسالة نجاح
  static void showSuccess(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green.shade600,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

/// Provider لمعالج أخطاء HTTP المركزي
final centralizedHttpErrorHandlerProvider = Provider<CentralizedHttpErrorHandler>((ref) {
  return CentralizedHttpErrorHandler(ref);
});

/// Provider مساعد للحصول على معالج الأخطاء بسهولة
final httpErrorHandlerProvider = Provider<CentralizedHttpErrorHandler>((ref) {
  return ref.watch(centralizedHttpErrorHandlerProvider);
}); 
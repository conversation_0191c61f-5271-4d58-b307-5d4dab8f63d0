import 'failure.dart';

/// Authentication-related failure base class
class AuthFailure extends Failure {
  /// Creates a new AuthFailure instance
  const AuthFailure({required super.message, super.details});
}

/// Failure for invalid credentials (wrong email/password)
class InvalidCredentialsFailure extends AuthFailure {
  /// Creates a new InvalidCredentialsFailure instance
  const InvalidCredentialsFailure({
    super.message = 'Invalid credentials',
    super.details,
  });
}

/// Failure for when a user doesn't exist
class UserNotFoundFailure extends AuthFailure {
  /// Creates a new UserNotFoundFailure instance
  const UserNotFoundFailure({super.message = 'User not found', super.details});
}

/// Failure for when a user already exists (during registration)
class UserExistsFailure extends AuthFailure {
  /// Creates a new UserExistsFailure instance
  const UserExistsFailure({
    super.message = 'User already exists',
    super.details,
  });
}

/// Failure for when a user is not authenticated but tries to access protected resources
class NotAuthenticatedFailure extends AuthFailure {
  /// Creates a new NotAuthenticatedFailure instance
  const NotAuthenticatedFailure({
    super.message = 'Not authenticated',
    super.details,
  });
}

/// Failure for when a token is expired or invalid
class TokenFailure extends AuthFailure {
  /// Creates a new TokenFailure instance
  const TokenFailure({
    super.message = 'Token is invalid or expired',
    super.details,
  });
}

/// Failure for when social authentication (Google, Facebook, etc.) fails
class SocialAuthFailure extends AuthFailure {
  /// Creates a new SocialAuthFailure instance
  const SocialAuthFailure({
    required this.provider,
    required super.message,
    super.details,
  });

  /// The social provider that failed (google, facebook, etc.)
  final String provider;

  @override
  String toString() => 'Social auth failure ($provider): $message';
}

/// Failure for when the user cancels the authentication process
class AuthCancelledFailure extends AuthFailure {
  /// Creates a new AuthCancelledFailure instance
  const AuthCancelledFailure({
    super.message = 'Authentication was cancelled',
    super.details,
  });
}

/// Failure for email verification issues
class EmailVerificationFailure extends AuthFailure {
  /// Creates a new EmailVerificationFailure instance
  const EmailVerificationFailure({
    super.message = 'Email verification failed',
    super.details,
  });
}

/// Failure for when a password reset fails
class PasswordResetFailure extends AuthFailure {
  /// Creates a new PasswordResetFailure instance
  const PasswordResetFailure({
    super.message = 'Password reset failed',
    super.details,
  });
}

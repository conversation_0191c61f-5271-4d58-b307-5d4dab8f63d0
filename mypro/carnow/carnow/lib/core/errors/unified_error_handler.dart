import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';


import '../utils/error_handler.dart';
import 'app_error.dart';
import 'result.dart';

part 'unified_error_handler.g.dart';

/// خدمة موحدة لمعالجة الأخطاء في التطبيق
/// Unified error handling service for the application
///
/// هذه الخدمة توفر:
/// This service provides:
/// - تحويل الأخطاء العامة إلى أخطاء التطبيق
/// - Convert general errors to app errors
/// - تسجيل الأخطاء بشكل منظم
/// - Structured error logging
/// - عرض الأخطاء للمستخدم
/// - Display errors to user
/// - إعادة المحاولة التلقائية
/// - Automatic retry logic
class UnifiedErrorHandler {
  UnifiedErrorHandler({Logger? logger})
    : _logger = logger ?? Logger('UnifiedErrorHandler');

  final Logger _logger;

  /// معالجة خطأ وإرجاع AppError
  /// Handle error and return AppError
  AppError handleError(
    dynamic error, {
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalData,
  }) {
    _logger.severe(
      'Error occurred${context != null ? ' in $context' : ''}: $error',
      error,
      stackTrace,
    );

    // تحويل الأخطاء المعروفة
    // Convert known errors
    if (error is AppError) {
      return error;
    }

    // Supabase-specific error handling removed - Forever Plan Compliance
    // All API errors now handled generically through Go backend

    if (error is SocketException || error is HttpException) {
      return _handleNetworkError(error, additionalData);
    }

    if (error is TimeoutException) {
      return _handleTimeoutError(error, additionalData);
    }

    if (error is FormatException) {
      return _handleFormatError(error, additionalData);
    }

    // خطأ غير معروف
    // Unknown error
    return AppError.unexpected(
      message: error?.toString() ?? 'Unknown error occurred',
      originalError: error,
      data: {
        if (additionalData != null) ...additionalData,
        if (stackTrace != null) 'stackTrace': stackTrace.toString(),
        if (context != null) 'context': context,
      },
    );
  }

  // _handleAuthError removed - Forever Plan Compliance
  // All authentication errors now handled through Go API

  // _handlePostgrestError removed - Forever Plan Compliance
  // All database errors now handled through Go API

  /// معالجة أخطاء الشبكة
  /// Handle network errors
  AppError _handleNetworkError(
    dynamic error,
    Map<String, dynamic>? additionalData,
  ) {
    String message;
    String? code;

    if (error is SocketException) {
      message = 'لا يوجد اتصال بالإنترنت';
      code = 'NO_INTERNET';
    } else if (error is HttpException) {
      message = 'خطأ في الاتصال بالخادم';
      code = 'SERVER_ERROR';
    } else {
      message = 'خطأ في الشبكة';
      code = 'NETWORK_ERROR';
    }

    return AppError.network(
      message: message,
      code: code,
      originalError: error,
      data: additionalData,
    );
  }

  /// معالجة أخطاء انتهاء المهلة
  /// Handle timeout errors
  AppError _handleTimeoutError(
    TimeoutException error,
    Map<String, dynamic>? additionalData,
  ) => AppError.network(
    message: 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى',
    code: 'TIMEOUT',
    originalError: error,
    data: {
      if (additionalData != null) ...additionalData,
      'timeout': error.duration?.inSeconds,
    },
  );

  /// معالجة أخطاء التنسيق
  /// Handle format errors
  AppError _handleFormatError(
    FormatException error,
    Map<String, dynamic>? additionalData,
  ) => AppError.validation(
    message: 'تنسيق البيانات غير صحيح',
    code: 'INVALID_FORMAT',
    originalError: error,
    data: {
      if (additionalData != null) ...additionalData,
      'source': error.source,
      'offset': error.offset,
    },
  );

  /// تنفيذ عملية مع معالجة الأخطاء
  /// Execute operation with error handling
  Future<Result<T>> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    String? context,
    Map<String, dynamic>? additionalData,
    bool shouldRetry = false,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    var attempts = 0;

    while (attempts < (shouldRetry ? maxRetries : 1)) {
      try {
        attempts++;
        final result = await operation();
        return Result.success(result);
      } catch (error, stackTrace) {
        final appError = handleError(
          error,
          stackTrace: stackTrace,
          context: context,
          additionalData: {
            if (additionalData != null) ...additionalData,
            'attempt': attempts,
            'maxRetries': maxRetries,
          },
        );

        // تحديد ما إذا كان يجب إعادة المحاولة
        // Determine if we should retry
        final shouldRetryThis =
            shouldRetry && appError.canRetry && attempts < maxRetries;

        if (!shouldRetryThis) {
          return Result.failure(appError);
        }

        // انتظار قبل إعادة المحاولة
        // Wait before retry
        if (attempts < maxRetries) {
          await Future<void>.delayed(retryDelay * attempts);
        }
      }
    }

    // لا يجب الوصول هنا
    // Should not reach here
    return Result.failure(
      AppError.unexpected(
        message: 'Unexpected error in retry logic',
        data: {'context': context, 'attempts': attempts},
      ),
    );
  }

  /// عرض الخطأ للمستخدم
  /// Display error to user
  void showErrorToUser(
    BuildContext context,
    AppError error, {
    VoidCallback? onRetry,
    bool showSnackBar = true,
  }) {
    if (showSnackBar && context.mounted) {
      ErrorHandler.handleError(
        context,
        error,
        customMessage: error.userMessage,
        onRetry: onRetry,
      );
    }
  }
}

/// Provider للخدمة الموحدة لمعالجة الأخطاء
/// Provider for unified error handling service
@riverpod
UnifiedErrorHandler unifiedErrorHandler(Ref ref) => UnifiedErrorHandler();

/// امتدادات مفيدة للعمل مع الأخطاء
/// Useful extensions for working with errors
extension ErrorHandlingExtensions on Ref {
  /// معالجة خطأ باستخدام الخدمة الموحدة
  /// Handle error using unified service
  AppError handleError(
    dynamic error, {
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalData,
  }) {
    final handler = read(unifiedErrorHandlerProvider);
    return handler.handleError(
      error,
      stackTrace: stackTrace,
      context: context,
      additionalData: additionalData,
    );
  }

  /// تنفيذ عملية مع معالجة الأخطاء
  /// Execute operation with error handling
  Future<Result<T>> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    String? context,
    Map<String, dynamic>? additionalData,
    bool shouldRetry = false,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) {
    final handler = read(unifiedErrorHandlerProvider);
    return handler.executeWithErrorHandling(
      operation,
      context: context,
      additionalData: additionalData,
      shouldRetry: shouldRetry,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
    );
  }
}

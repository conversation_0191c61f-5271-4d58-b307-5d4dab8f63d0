/// نظام معالجة الأخطاء الموحد
/// Unified Error Handling System
library;

/// هذا الملف يصدر جميع مكونات نظام معالجة الأخطاء الموحد
/// This file exports all components of the unified error handling system
///
/// الاستخدام:
/// Usage:
/// ```dart
/// import 'package:carnow/core/errors/index.dart';
///
/// // استخدام Result
/// // Using Result
/// final result = await someOperation();
/// result.when(
///   success: (data) => handleSuccess(data),
///   failure: (error) => handleError(error),
/// );
///
/// // استخدام UnifiedErrorHandler
/// // Using UnifiedErrorHandler
/// final handler = ref.read(unifiedErrorHandlerProvider);
/// final result = await handler.executeWithErrorHandling(() async {
///   return await riskyOperation();
/// });
/// ```

// Import required classes for use in helper classes
import 'app_error.dart';
import 'result.dart';

// تصدير الأخطاء الأساسية
// Export core errors
export 'app_error.dart';
export 'auth_failure.dart';
// export 'example_usage.dart'; // تم حذف الملف مؤقتاً
export 'failure.dart' hide Failure;
export 'result.dart';
export 'unified_error_handler.dart';

/// مساعدات سريعة لمعالجة الأخطاء
/// Quick helpers for error handling
class ErrorHelpers {
  /// تحويل سريع من خطأ عام إلى AppError
  /// Quick conversion from general error to AppError
  static AppError toAppError(dynamic error, {StackTrace? stackTrace}) =>
      AppErrorFactory.fromError(error, stackTrace: stackTrace);

  /// إنشاء نتيجة ناجحة
  /// Create successful result
  static Result<T> success<T>(T data) => Result.success(data);

  /// إنشاء نتيجة فاشلة
  /// Create failure result
  static Result<T> failure<T>(AppError error) => Result.failure(error);

  /// إنشاء نتيجة فاشلة من خطأ عام
  /// Create failure result from general error
  static Result<T> failureFromError<T>(
    dynamic error, {
    StackTrace? stackTrace,
  }) => Result.failure(toAppError(error, stackTrace: stackTrace));

  /// تنفيذ عملية مع معالجة الأخطاء
  /// Execute operation with error handling
  static Future<Result<T>> tryAsync<T>(Future<T> Function() operation) async {
    try {
      final data = await operation();
      return Result.success(data);
    } catch (error, stackTrace) {
      return Result.failure(toAppError(error, stackTrace: stackTrace));
    }
  }

  /// تنفيذ عملية متزامنة مع معالجة الأخطاء
  /// Execute synchronous operation with error handling
  static Result<T> trySync<T>(T Function() operation) {
    try {
      final data = operation();
      return Result.success(data);
    } catch (error, stackTrace) {
      return Result.failure(toAppError(error, stackTrace: stackTrace));
    }
  }
}

/// امتدادات مفيدة للعمل مع Future
/// Useful extensions for working with Future
extension FutureErrorHandling<T> on Future<T> {
  /// تحويل `Future<T>` إلى `Future<Result<T>>`
  /// Convert `Future<T>` to `Future<Result<T>>`
  Future<Result<T>> toResult() => ErrorHelpers.tryAsync(() => this);

  /// معالجة الأخطاء مع callback
  /// Handle errors with callback
  Future<T> handleErrors({
    T Function(dynamic error)? onError,
    void Function(dynamic error, StackTrace stackTrace)? onErrorCallback,
  }) async {
    try {
      return await this;
    } catch (error, stackTrace) {
      onErrorCallback?.call(error, stackTrace);
      if (onError != null) {
        return onError(error);
      }
      rethrow;
    }
  }
}

/// امتدادات مفيدة للعمل مع Stream
/// Useful extensions for working with Stream
extension StreamErrorHandling<T> on Stream<T> {
  /// معالجة الأخطاء في Stream
  /// Handle errors in Stream
  Stream<Result<T>> toResultStream() => map(Result.success).handleError(
    (Object error, StackTrace stackTrace) => Result<T>.failure(
      ErrorHelpers.toAppError(error, stackTrace: stackTrace),
    ),
  );

  /// معالجة الأخطاء مع callback
  /// Handle errors with callback
  Stream<T> handleStreamErrors({
    T Function(dynamic error)? onError,
    void Function(Object error, StackTrace stackTrace)? onErrorCallback,
  }) => handleError((Object error, StackTrace stackTrace) {
    onErrorCallback?.call(error, stackTrace);
    if (onError != null) {
      return onError(error);
    }
    throw Exception(error);
  });
}

/// ثوابت مفيدة لمعالجة الأخطاء
/// Useful constants for error handling
class ErrorConstants {
  /// رسائل الأخطاء الافتراضية
  /// Default error messages
  static const String defaultNetworkError = 'مشكلة في الاتصال بالإنترنت';
  static const String defaultDatabaseError = 'مشكلة في قاعدة البيانات';
  static const String defaultAuthError = 'مشكلة في المصادقة';
  static const String defaultValidationError = 'بيانات غير صحيحة';
  static const String defaultUnexpectedError = 'حدث خطأ غير متوقع';

  /// أكواد الأخطاء الشائعة
  /// Common error codes
  static const String networkErrorCode = 'NETWORK_ERROR';
  static const String databaseErrorCode = 'DATABASE_ERROR';
  static const String authErrorCode = 'AUTH_ERROR';
  static const String validationErrorCode = 'VALIDATION_ERROR';
  static const String unexpectedErrorCode = 'UNEXPECTED_ERROR';

  /// مدد إعادة المحاولة
  /// Retry durations
  static const Duration shortRetryDelay = Duration(seconds: 1);
  static const Duration mediumRetryDelay = Duration(seconds: 3);
  static const Duration longRetryDelay = Duration(seconds: 5);

  /// عدد محاولات إعادة المحاولة
  /// Retry attempt counts
  static const int defaultMaxRetries = 3;
  static const int networkMaxRetries = 5;
  static const int databaseMaxRetries = 2;
}

/// نصائح للاستخدام الأمثل
/// Tips for optimal usage
///
/// 1. استخدم Result<T> في جميع العمليات التي قد تفشل
///    Use Result<T> for all operations that may fail
///
/// 2. استخدم UnifiedErrorHandler للعمليات المعقدة
///    Use UnifiedErrorHandler for complex operations
///
/// 3. استخدم ErrorHelpers للعمليات البسيطة
///    Use ErrorHelpers for simple operations
///
/// 4. تأكد من معالجة جميع أنواع الأخطاء
///    Make sure to handle all error types
///
/// 5. استخدم الـ extensions للتبسيط
///    Use extensions for simplification
///
/// 6. اختبر سيناريوهات الأخطاء
///    Test error scenarios
///
/// 7. سجل الأخطاء المهمة
///    Log important errors
///
/// 8. اعرض رسائل مفيدة للمستخدم
///    Show helpful messages to users

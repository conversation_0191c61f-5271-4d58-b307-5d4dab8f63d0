import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_failure.freezed.dart';

/// Unified failure model for async operations across the app.
@freezed
abstract class AppFailure with _$AppFailure {
  const factory AppFailure.network([String? message]) = _NetworkFailure;
  const factory AppFailure.database([String? message]) = _DatabaseFailure;
  const factory AppFailure.unauthorized([String? message]) =
      _UnauthorizedFailure;
  const factory AppFailure.unexpected([String? message]) = _UnexpectedFailure;
}

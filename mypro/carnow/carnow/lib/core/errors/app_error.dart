/// نظام إدارة الأخطاء الموحد للتطبيق
/// Unified error management system for the application
class AppError implements Exception {
  const AppError({
    required this.message,
    required this.type,
    this.code,
    this.severity = ErrorSeverity.medium,
    this.originalError,
    this.data,
    this.stackTrace,
  });

  /// خطأ في الشبكة
  /// Network related errors
  const AppError.network({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.medium,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.network,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في قاعدة البيانات
  /// Database related errors
  const AppError.database({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.high,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.database,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في المصادقة
  /// Authentication related errors
  const AppError.authentication({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.high,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.authentication,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في التحقق من صحة البيانات
  /// Validation errors
  const AppError.validation({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.low,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.validation,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ غير متوقع
  /// Unexpected errors
  const AppError.unexpected({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.critical,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.unexpected,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في الأذونات
  /// Permission related errors
  const AppError.permission({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.medium,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.permission,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في المحتوى غير الموجود
  /// Content not found errors
  const AppError.notFound({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.low,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.notFound,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في معدل الطلبات
  /// Rate limiting errors
  const AppError.rateLimited({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.medium,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.rateLimited,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في الخادم
  /// Server errors
  const AppError.server({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.high,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.server,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في التفويض
  /// Authorization errors
  const AppError.authorization({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.medium,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.authorization,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في التضارب
  /// Conflict errors
  const AppError.conflict({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.medium,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.conflict,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في انتهاء المهلة
  /// Timeout errors
  const AppError.timeout({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.medium,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.timeout,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );

  /// خطأ في عدم توفر الميزة
  /// Feature unavailable errors
  const AppError.featureUnavailable({
    required String message,
    String? code,
    ErrorSeverity severity = ErrorSeverity.low,
    dynamic originalError,
    Map<String, dynamic>? data,
    StackTrace? stackTrace,
  }) : this(
         message: message,
         type: AppErrorType.featureUnavailable,
         code: code,
         severity: severity,
         originalError: originalError,
         data: data,
         stackTrace: stackTrace,
       );
  final String message;
  final String? code;
  final ErrorSeverity severity;
  final dynamic originalError;
  final AppErrorType type;
  final Map<String, dynamic>? data;
  final StackTrace? stackTrace;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppError &&
          runtimeType == other.runtimeType &&
          message == other.message &&
          code == other.code &&
          severity == other.severity &&
          type == other.type;

  @override
  int get hashCode =>
      message.hashCode ^ code.hashCode ^ severity.hashCode ^ type.hashCode;

  @override
  String toString() =>
      'AppError.$type(message: $message, code: $code, '
      'severity: $severity)';
}

/// أنواع الأخطاء
/// Error types
enum AppErrorType {
  network,
  database,
  authentication,
  validation,
  unexpected,
  permission,
  notFound,
  rateLimited,
  server,
  authorization,
  conflict,
  timeout,
  featureUnavailable,
}

/// Alias for AppErrorType for compatibility
typedef ErrorType = AppErrorType;

/// مستوى خطورة الخطأ
/// Error severity levels
enum ErrorSeverity {
  /// منخفض - يمكن للمستخدم الاستمرار عادة
  /// Low - User can continue normally
  low,

  /// متوسط - يمكن أن يؤثر على التجربة
  /// Medium - May affect user experience
  medium,

  /// عالي - يحتاج تدخل فوري
  /// High - Requires immediate attention
  high,

  /// حرج - قد يؤدي لتعطل التطبيق
  /// Critical - May cause app failure
  critical,
}

/// مساعد لتحويل الأخطاء العامة إلى أخطاء التطبيق
/// Helper for converting general errors to app errors
extension AppErrorExtension on AppError {
  /// الحصول على رسالة صديقة للمستخدم
  /// Get user-friendly message
  String get userMessage {
    switch (type) {
      case AppErrorType.network:
        return 'مشكلة في الاتصال. يرجى التحقق من الإنترنت والمحاولة مرة أخرى.';
      case AppErrorType.database:
        return 'مشكلة في حفظ البيانات. يرجى المحاولة مرة أخرى.';
      case AppErrorType.authentication:
        return 'مشكلة في تسجيل الدخول. يرجى المحاولة مرة أخرى.';
      case AppErrorType.validation:
        return message;
      case AppErrorType.unexpected:
        return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
      case AppErrorType.permission:
        return 'تحتاج إلى إذن للوصول لهذه الميزة.';
      case AppErrorType.notFound:
        return 'العنصر المطلوب غير موجود.';
      case AppErrorType.rateLimited:
        return 'تم تجاوز عدد المحاولات المسموح. '
            'يرجى الانتظار والمحاولة مرة أخرى.';
      case AppErrorType.server:
        return 'مشكلة في الخادم. يرجى المحاولة مرة أخرى لاحقاً.';
      case AppErrorType.authorization:
        return 'ليس لديك صلاحية للقيام بهذا الإجراء.';
      case AppErrorType.conflict:
        return 'البيانات موجودة مسبقاً أو متضاربة.';
      case AppErrorType.timeout:
        return 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
      case AppErrorType.featureUnavailable:
        return message;
    }
  }

  /// الحصول على رمز الخطأ
  /// Get error code
  String get errorCode {
    switch (type) {
      case AppErrorType.network:
        return code ?? 'NETWORK_ERROR';
      case AppErrorType.database:
        return code ?? 'DATABASE_ERROR';
      case AppErrorType.authentication:
        return code ?? 'AUTH_ERROR';
      case AppErrorType.validation:
        return code ?? 'VALIDATION_ERROR';
      case AppErrorType.unexpected:
        return code ?? 'UNEXPECTED_ERROR';
      case AppErrorType.permission:
        return code ?? 'PERMISSION_ERROR';
      case AppErrorType.notFound:
        return code ?? 'NOT_FOUND_ERROR';
      case AppErrorType.rateLimited:
        return code ?? 'RATE_LIMITED_ERROR';
      case AppErrorType.server:
        return code ?? 'SERVER_ERROR';
      case AppErrorType.authorization:
        return code ?? 'AUTHORIZATION_ERROR';
      case AppErrorType.conflict:
        return code ?? 'CONFLICT_ERROR';
      case AppErrorType.timeout:
        return code ?? 'TIMEOUT_ERROR';
      case AppErrorType.featureUnavailable:
        return code ?? 'FEATURE_UNAVAILABLE_ERROR';
    }
  }

  /// هل يمكن إعادة المحاولة؟
  /// Can this error be retried?
  bool get canRetry {
    switch (type) {
      case AppErrorType.network:
      case AppErrorType.database:
      case AppErrorType.unexpected:
      case AppErrorType.rateLimited:
      case AppErrorType.server:
      case AppErrorType.timeout:
        return true;
      case AppErrorType.authentication:
      case AppErrorType.validation:
      case AppErrorType.permission:
      case AppErrorType.notFound:
      case AppErrorType.authorization:
      case AppErrorType.conflict:
      case AppErrorType.featureUnavailable:
        return false;
    }
  }

  /// هل يجب تسجيل هذا الخطأ؟
  /// Should this error be logged?
  bool get shouldLog =>
      severity == ErrorSeverity.high || severity == ErrorSeverity.critical;
}

/// مصنع الأخطاء للتحويل من الأخطاء العامة
/// Error factory for converting from general errors
class AppErrorFactory {
  /// تحويل من خطأ عام
  /// Convert from general error
  static AppError fromError(dynamic error, {StackTrace? stackTrace}) {
    if (error is AppError) {
      return error;
    }

    final message = error?.toString() ?? 'Unknown error';

    // تحليل نوع الخطأ بناءً على النص
    // Analyze error type based on text
    if (message.toLowerCase().contains('network') ||
        message.toLowerCase().contains('connection') ||
        message.toLowerCase().contains('timeout')) {
      return AppError.network(
        message: message,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    if (message.toLowerCase().contains('auth') ||
        message.toLowerCase().contains('login') ||
        message.toLowerCase().contains('unauthorized')) {
      return AppError.authentication(
        message: message,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    if (message.toLowerCase().contains('database') ||
        message.toLowerCase().contains('sql') ||
        message.toLowerCase().contains('supabase')) {
      return AppError.database(
        message: message,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    return AppError.unexpected(
      message: message,
      originalError: error,
      stackTrace: stackTrace,
      data: stackTrace != null ? {'stackTrace': stackTrace.toString()} : null,
    );
  }
}

/// Extension to format error details as a bullet list
extension AppErrorDetailsFormatter on AppError {
  String formatDetails() {
    if (data == null || data!.isEmpty) return '';
    return data!.entries.map((e) => '• ${e.key}: ${e.value}').join('\n');
  }
}

/// Abstract base class for all failures in the application
///
/// All failure classes should extend this class to ensure
/// consistent error handling throughout the application.
abstract class Failure implements Exception {
  /// Creates a new Failure instance
  const Failure({required this.message, this.details});

  /// Brief description of the failure
  final String message;

  /// Optional detailed error information, useful for debugging
  final dynamic details;

  @override
  String toString() {
    if (details != null) {
      return '$message\nDetails: $details';
    }
    return message;
  }
}

/// General server failure that doesn't fit other specific categories
class ServerFailure extends Failure {
  /// Creates a new ServerFailure instance
  const ServerFailure({required super.message, super.details});
}

/// Network-related failures (connectivity issues, timeouts, etc.)
class NetworkFailure extends Failure {
  /// Creates a new NetworkFailure instance
  const NetworkFailure({required super.message, super.details});
}

/// Specific network exception class for connection issues
class NetworkException implements Exception {
  /// Creates a new NetworkException
  NetworkException(this.message, [this.originalError]);

  /// Description of the network issue
  final String message;

  /// Original error that caused this exception
  final dynamic originalError;

  @override
  String toString() {
    if (originalError != null) {
      return '$message\nOriginal error: $originalError';
    }
    return message;
  }
}

/// For storing and handling issues related to cache or local storage
class CacheFailure extends Failure {
  /// Creates a new CacheFailure instance
  const CacheFailure({required super.message, super.details});
}

/// For handling unexpected or unhandled exceptions
class UnexpectedFailure extends Failure {
  /// Creates a new UnexpectedFailure instance
  const UnexpectedFailure({required super.message, super.details});
}

/// For validation errors (form validation, input validation)
class ValidationFailure extends Failure {
  /// Creates a new ValidationFailure instance
  const ValidationFailure({
    required super.message,
    this.fieldErrors,
    super.details,
  });

  /// Field-specific validation errors map
  final Map<String, String>? fieldErrors;

  @override
  String toString() {
    if (fieldErrors != null && fieldErrors!.isNotEmpty) {
      final fieldsStr = fieldErrors!.entries
          .map((e) => '${e.key}: ${e.value}')
          .join('\n');
      return '$message\nField errors:\n$fieldsStr';
    }
    return super.toString();
  }
}

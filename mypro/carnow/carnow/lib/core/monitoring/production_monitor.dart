import 'dart:async';
import 'dart:developer' as developer;
import 'package:dio/dio.dart';
import '../config/production_config.dart';

/// Production monitoring system for CarNow app
class ProductionMonitor {
  factory ProductionMonitor() => _instance;
  ProductionMonitor._internal();
  static final ProductionMonitor _instance = ProductionMonitor._internal();

  // Monitoring state
  bool _isMonitoring = false;
  Timer? _healthCheckTimer;
  Timer? _metricsReportTimer;

  // Performance metrics
  final List<PerformanceMetric> _metrics = [];
  final List<ErrorMetric> _errors = [];
  static const int _maxMetrics = 1000;
  static const int _maxErrors = 100;

  // Health status
  bool _isHealthy = true;
  DateTime? _lastHealthCheck;
  String? _lastHealthError;

  /// Start production monitoring
  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    developer.log('🚀 ProductionMonitor: Starting production monitoring');
    
    // Start health checks
    if (ProductionConfig.enableHealthChecks) {
      _startHealthChecks();
    }
    
    // Start metrics reporting
    _startMetricsReporting();
    
    developer.log('✅ ProductionMonitor: Monitoring started successfully');
  }

  /// Stop production monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    _isMonitoring = false;
    _healthCheckTimer?.cancel();
    _metricsReportTimer?.cancel();
    
    developer.log('🛑 ProductionMonitor: Monitoring stopped');
  }

  /// Start health check monitoring
  void _startHealthChecks() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(
      ProductionConfig.healthCheckInterval,
      (_) => _performHealthCheck(),
    );
    
    // Perform initial health check
    _performHealthCheck();
  }

  /// Perform backend health check
  Future<void> _performHealthCheck() async {
    try {
      final dio = Dio();
      final response = await dio.get(
        ProductionCarnowConfig.buildUrl('/health'),
        options: Options(
          receiveTimeout: const Duration(seconds: 10),
          headers: ProductionCarnowConfig.getProductionHeaders(),
        ),
      );
      
      _isHealthy = response.statusCode == 200;
      _lastHealthCheck = DateTime.now();
      _lastHealthError = null;
      
      if (_isHealthy) {
        developer.log('💚 PROD_HEALTH: Backend healthy (${response.statusCode})');
      } else {
        developer.log('💛 PROD_HEALTH: Backend unhealthy (${response.statusCode})');
      }
    } catch (e) {
      _isHealthy = false;
      _lastHealthCheck = DateTime.now();
      _lastHealthError = e.toString();
      
      developer.log('💔 PROD_HEALTH: Backend unreachable - $e', level: 1000);
    }
  }

  /// Start metrics reporting
  void _startMetricsReporting() {
    _metricsReportTimer?.cancel();
    _metricsReportTimer = Timer.periodic(
      const Duration(minutes: 15),
      (_) => _reportMetrics(),
    );
  }

  /// Track API performance
  void trackApiPerformance(
    String operation,
    int responseTime,
    bool isSuccess, {
    String? endpoint,
    int? statusCode,
    String? errorMessage,
  }) {
    final metric = PerformanceMetric(
      operation: operation,
      responseTime: responseTime,
      isSuccess: isSuccess,
      timestamp: DateTime.now(),
      endpoint: endpoint,
      statusCode: statusCode,
      errorMessage: errorMessage,
    );

    _metrics.add(metric);
    
    // Keep only recent metrics
    if (_metrics.length > _maxMetrics) {
      _metrics.removeAt(0);
    }

    // Log performance warnings
    if (responseTime > ProductionConfig.performanceCriticalThreshold.inMilliseconds) {
      developer.log(
        '🚨 PROD_CRITICAL: Very slow operation $operation: ${responseTime}ms',
        level: 1000,
      );
    } else if (responseTime > ProductionConfig.performanceWarningThreshold.inMilliseconds) {
      developer.log(
        '⚠️ PROD_WARNING: Slow operation $operation: ${responseTime}ms',
        level: 900,
      );
    }

    // Track errors
    if (!isSuccess) {
      trackError(operation, errorMessage ?? 'Unknown error', responseTime);
    }
  }

  /// Track application errors
  void trackError(String operation, String error, [int? responseTime]) {
    final errorMetric = ErrorMetric(
      operation: operation,
      error: error,
      timestamp: DateTime.now(),
      responseTime: responseTime,
    );

    _errors.add(errorMetric);
    
    // Keep only recent errors
    if (_errors.length > _maxErrors) {
      _errors.removeAt(0);
    }

    developer.log(
      '❌ PROD_ERROR: $operation failed - $error',
      level: 1000,
    );
  }

  /// Report metrics summary
  void _reportMetrics() {
    if (_metrics.isEmpty) return;

    final now = DateTime.now();
    final last15Minutes = now.subtract(const Duration(minutes: 15));
    
    final recentMetrics = _metrics
        .where((m) => m.timestamp.isAfter(last15Minutes))
        .toList();

    if (recentMetrics.isEmpty) return;

    final totalRequests = recentMetrics.length;
    final successfulRequests = recentMetrics.where((m) => m.isSuccess).length;
    final failedRequests = totalRequests - successfulRequests;
    
    final avgResponseTime = recentMetrics
        .map((m) => m.responseTime)
        .reduce((a, b) => a + b) / totalRequests;
    
    final successRate = successfulRequests / totalRequests * 100;
    
    developer.log(
      '📊 PROD_METRICS (15min): '
      'Requests: $totalRequests, '
      'Success: ${successRate.toStringAsFixed(1)}%, '
      'Avg Response: ${avgResponseTime.toStringAsFixed(0)}ms, '
      'Failures: $failedRequests'
    );

    // Alert on poor performance
    if (successRate < 95.0) {
      developer.log(
        '🚨 PROD_ALERT: Low success rate: ${successRate.toStringAsFixed(1)}%',
        level: 1000,
      );
    }
    
    if (avgResponseTime > 2000) {
      developer.log(
        '🚨 PROD_ALERT: High response time: ${avgResponseTime.toStringAsFixed(0)}ms',
        level: 1000,
      );
    }
  }

  /// Get current health status
  Map<String, dynamic> getHealthStatus() {
    return {
      'is_healthy': _isHealthy,
      'last_check': _lastHealthCheck?.toIso8601String(),
      'last_error': _lastHealthError,
      'monitoring_active': _isMonitoring,
    };
  }

  /// Get performance summary
  Map<String, dynamic> getPerformanceSummary() {
    if (_metrics.isEmpty) {
      return {
        'total_requests': 0,
        'success_rate': 0.0,
        'avg_response_time': 0.0,
        'error_count': 0,
      };
    }

    final totalRequests = _metrics.length;
    final successfulRequests = _metrics.where((m) => m.isSuccess).length;
    final avgResponseTime = _metrics
        .map((m) => m.responseTime)
        .reduce((a, b) => a + b) / totalRequests;
    
    return {
      'total_requests': totalRequests,
      'success_rate': (successfulRequests / totalRequests * 100),
      'avg_response_time': avgResponseTime,
      'error_count': _errors.length,
      'health_status': getHealthStatus(),
      'environment': ProductionConfig.getEnvironmentInfo(),
    };
  }

  /// Get recent errors
  List<Map<String, dynamic>> getRecentErrors({int limit = 10}) {
    return _errors
        .take(limit)
        .map((e) => e.toMap())
        .toList()
        .reversed
        .toList();
  }

  /// Get performance trends
  Map<String, dynamic> getPerformanceTrends() {
    if (_metrics.length < 10) {
      return {'insufficient_data': true};
    }

    final recentMetrics = _metrics.length > 100
        ? _metrics.sublist(_metrics.length - 100)
        : _metrics;
    final olderMetrics = _metrics.length > 200 
        ? _metrics.sublist(_metrics.length - 200, _metrics.length - 100)
        : recentMetrics;

    final recentAvg = recentMetrics
        .map((m) => m.responseTime)
        .reduce((a, b) => a + b) / recentMetrics.length;
    
    final olderAvg = olderMetrics
        .map((m) => m.responseTime)
        .reduce((a, b) => a + b) / olderMetrics.length;

    final trend = recentAvg - olderAvg;
    
    return {
      'recent_avg_response_time': recentAvg,
      'older_avg_response_time': olderAvg,
      'trend': trend,
      'trend_direction': trend > 0 ? 'slower' : 'faster',
      'trend_magnitude': trend.abs(),
    };
  }

  /// Clear all metrics (for testing)
  void clearMetrics() {
    _metrics.clear();
    _errors.clear();
    developer.log('🧹 ProductionMonitor: Metrics cleared');
  }

  /// Export metrics for external analysis
  Map<String, dynamic> exportMetrics() {
    return {
      'metrics': _metrics.map((m) => m.toMap()).toList(),
      'errors': _errors.map((e) => e.toMap()).toList(),
      'health_status': getHealthStatus(),
      'performance_summary': getPerformanceSummary(),
      'exported_at': DateTime.now().toIso8601String(),
    };
  }

  /// Check if system is ready for production
  Map<String, dynamic> checkProductionReadiness() {
    final healthStatus = getHealthStatus();
    final performanceSummary = getPerformanceSummary();
    final configCheck = ProductionConfig.validateProductionReadiness();
    
    final isReady = healthStatus['is_healthy'] == true &&
        performanceSummary['success_rate'] > 95.0 &&
        performanceSummary['avg_response_time'] < 2000 &&
        configCheck.values.every((check) => check == true);

    return {
      'is_ready': isReady,
      'health_check': healthStatus['is_healthy'],
      'performance_acceptable': performanceSummary['avg_response_time'] < 2000,
      'success_rate_good': performanceSummary['success_rate'] > 95.0,
      'config_valid': configCheck.values.every((check) => check == true),
      'detailed_config_check': configCheck,
      'recommendations': isReady 
          ? ['System ready for production deployment'] 
          : _getProductionRecommendations(healthStatus, performanceSummary, configCheck),
    };
  }

  List<String> _getProductionRecommendations(
    Map<String, dynamic> health,
    Map<String, dynamic> performance,
    Map<String, bool> config,
  ) {
    final recommendations = <String>[];
    
    if (health['is_healthy'] != true) {
      recommendations.add('Fix backend health issues before deployment');
    }
    
    if (performance['avg_response_time'] >= 2000) {
      recommendations.add('Optimize response times - currently ${performance['avg_response_time'].toStringAsFixed(0)}ms');
    }
    
    if (performance['success_rate'] <= 95.0) {
      recommendations.add('Improve success rate - currently ${performance['success_rate'].toStringAsFixed(1)}%');
    }
    
    config.forEach((check, passed) {
      if (!passed) {
        recommendations.add('Fix configuration: $check');
      }
    });
    
    return recommendations;
  }
}

/// Performance metric data class
class PerformanceMetric {

  PerformanceMetric({
    required this.operation,
    required this.responseTime,
    required this.isSuccess,
    required this.timestamp,
    this.endpoint,
    this.statusCode,
    this.errorMessage,
  });
  final String operation;
  final int responseTime;
  final bool isSuccess;
  final DateTime timestamp;
  final String? endpoint;
  final int? statusCode;
  final String? errorMessage;

  Map<String, dynamic> toMap() {
    return {
      'operation': operation,
      'response_time': responseTime,
      'is_success': isSuccess,
      'timestamp': timestamp.toIso8601String(),
      'endpoint': endpoint,
      'status_code': statusCode,
      'error_message': errorMessage,
    };
  }
}

/// Error metric data class
class ErrorMetric {

  ErrorMetric({
    required this.operation,
    required this.error,
    required this.timestamp,
    this.responseTime,
  });
  final String operation;
  final String error;
  final DateTime timestamp;
  final int? responseTime;

  Map<String, dynamic> toMap() {
    return {
      'operation': operation,
      'error': error,
      'timestamp': timestamp.toIso8601String(),
      'response_time': responseTime,
    };
  }
} 
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';
import '../../features/categories/models/category_model.dart';

final _logger = Logger('CategoryRepository');

/// Category Repository - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// Repository for category operations using Go backend ONLY
class CategoryRepository {
  final SimpleApiClient _apiClient;

  CategoryRepository(this._apiClient);

  /// Fetch all categories
  Future<List<CategoryModel>> getAllCategories() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>('/categories');
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to fetch categories: ${response.error}');
      }

      final data = response.data!;
      final categoriesData = data['data'] as List?;
      
      if (categoriesData == null) return [];
      
      return categoriesData
          .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.severe('Error fetching all categories: $e');
      rethrow;
    }
  }

  /// Fetch category by ID
  Future<CategoryModel?> getCategoryById(String id) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>('/categories/$id');
      
      if (!response.isSuccess || response.data == null) {
        return null;
      }

      final data = response.data!;
      final categoryData = data['data'] as Map<String, dynamic>?;
      
      if (categoryData == null) return null;
      
      return CategoryModel.fromJson(categoryData);
    } catch (e) {
      _logger.warning('Error fetching category $id: $e');
      return null;
    }
  }

  /// Fetch parent categories
  Future<List<CategoryModel>> getParentCategories() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>('/categories/parents');
      
      if (!response.isSuccess || response.data == null) {
        return [];
      }

      final data = response.data!;
      final categoriesData = data['data'] as List?;
      
      if (categoriesData == null) return [];
      
      return categoriesData
          .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.warning('Error fetching parent categories: $e');
      return [];
    }
  }

  /// Fetch subcategories for a parent
  Future<List<CategoryModel>> getSubcategories(String parentId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>('/categories/$parentId/subcategories');
      
      if (!response.isSuccess || response.data == null) {
        return [];
      }

      final data = response.data!;
      final categoriesData = data['data'] as List?;
      
      if (categoriesData == null) return [];
      
      return categoriesData
          .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.warning('Error fetching subcategories for $parentId: $e');
      return [];
    }
  }

  /// Create new category
  Future<CategoryModel> createCategory(Map<String, dynamic> categoryData) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>('/categories', data: categoryData);
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to create category: ${response.error}');
      }

      final data = response.data!;
      final newCategoryData = data['data'] as Map<String, dynamic>?;
      
      if (newCategoryData == null) {
        throw Exception('Invalid response format for created category');
      }
      
      return CategoryModel.fromJson(newCategoryData);
    } catch (e) {
      _logger.severe('Error creating category: $e');
      rethrow;
    }
  }

  /// Update existing category
  Future<CategoryModel> updateCategory(String id, Map<String, dynamic> updateData) async {
    try {
      final response = await _apiClient.put<Map<String, dynamic>>('/categories/$id', data: updateData);
      
      if (!response.isSuccess || response.data == null) {
        throw Exception('Failed to update category: ${response.error}');
      }

      final data = response.data!;
      final updatedCategoryData = data['data'] as Map<String, dynamic>?;
      
      if (updatedCategoryData == null) {
        throw Exception('Invalid response format for updated category');
      }
      
      return CategoryModel.fromJson(updatedCategoryData);
    } catch (e) {
      _logger.severe('Error updating category $id: $e');
      rethrow;
    }
  }

  /// Delete category
  Future<bool> deleteCategory(String id) async {
    try {
      final response = await _apiClient.delete<Map<String, dynamic>>('/categories/$id');
      
      if (!response.isSuccess) {
        throw Exception('Failed to delete category: ${response.error}');
      }

      return true;
    } catch (e) {
      _logger.severe('Error deleting category $id: $e');
      return false;
    }
  }

  /// Search categories by name
  Future<List<CategoryModel>> searchCategories(String query) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/categories/search',
        queryParameters: {'q': query},
      );
      
      if (!response.isSuccess || response.data == null) {
        return [];
      }

      final data = response.data!;
      final categoriesData = data['data'] as List?;
      
      if (categoriesData == null) return [];
      
      return categoriesData
          .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.warning('Error searching categories with query "$query": $e');
      return [];
    }
  }

  /// Get popular categories
  Future<List<CategoryModel>> getPopularCategories() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>('/categories/popular');
      
      if (!response.isSuccess || response.data == null) {
        return [];
      }

      final data = response.data!;
      final categoriesData = data['data'] as List?;
      
      if (categoriesData == null) return [];
      
      return categoriesData
          .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.warning('Error fetching popular categories: $e');
      return [];
    }
  }

  /// Get category statistics
  Future<Map<String, dynamic>?> getCategoryStats(String categoryId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>('/categories/$categoryId/stats');
      
      if (!response.isSuccess || response.data == null) {
        return null;
      }

      final data = response.data!;
      return data['data'] as Map<String, dynamic>?;
    } catch (e) {
      _logger.warning('Error fetching category stats for $categoryId: $e');
      return null;
    }
  }
}

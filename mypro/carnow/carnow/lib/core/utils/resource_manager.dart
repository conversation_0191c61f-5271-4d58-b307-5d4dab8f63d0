import 'dart:async';

import '../utils/unified_logger.dart';

/// مدير الموارد لضمان تنظيف Timer و StreamController
class ResourceManager {
  static final Map<String, Timer> _timers = {};
  static final Map<String, StreamSubscription> _subscriptions = {};
  static final Map<String, StreamController> _controllers = {};

  static void registerTimer(String name, Timer timer) {
    UnifiedLogger.info('Registering timer: $name');
    _timers[name] = timer;
  }

  static void cancelTimer(String name) {
    final timer = _timers.remove(name);
    if (timer != null) {
      UnifiedLogger.info('Cancelling timer: $name');
      timer.cancel();
    }
  }

  static void registerSubscription(String name, StreamSubscription subscription) {
    UnifiedLogger.info('Registering subscription: $name');
    _subscriptions[name] = subscription;
  }

  static void cancelSubscription(String name) {
    final subscription = _subscriptions.remove(name);
    if (subscription != null) {
      UnifiedLogger.info('Cancelling subscription: $name');
      subscription.cancel();
    }
  }

  static T registerController<T extends StreamController>(T controller) {
    final name = controller.runtimeType.toString();
    UnifiedLogger.info('Registering controller: $name');
    _controllers[name] = controller;
    return controller;
  }

  static void closeController(StreamController controller) {
    final name = controller.runtimeType.toString();
    UnifiedLogger.info('Closing controller: $name');
    _controllers.remove(name);
    if (!controller.isClosed) {
      controller.close();
    }
  }

  static void dispose() {
    UnifiedLogger.info('Disposing ResourceManager');
    
    for (final timer in _timers.values) {
      try {
        timer.cancel();
      } catch (e) {
        UnifiedLogger.error('Error cancelling timer: $e');
      }
    }
    _timers.clear();

    for (final subscription in _subscriptions.values) {
      try {
        subscription.cancel();
      } catch (e) {
        UnifiedLogger.error('Error cancelling subscription: $e');
      }
    }
    _subscriptions.clear();

    for (final controller in _controllers.values) {
      try {
        if (!controller.isClosed) {
          controller.close();
        }
      } catch (e) {
        UnifiedLogger.error('Error closing controller: $e');
      }
    }
    _controllers.clear();
  }

  static Map<String, dynamic> getResourceStats() {
    final stats = {
      'activeTimers': _timers.length,
      'activeSubscriptions': _subscriptions.length,
      'activeControllers': _controllers.length,
    };
    
    for (final key in stats.keys) {
      UnifiedLogger.info('Resource stats - $key: ${stats[key]}');
    }
    
    return stats;
  }
}

/// Extension لتسهيل استخدام ResourceManager مع Timer
extension TimerResourceExtension on Timer {
  /// تسجيل Timer في ResourceManager
  Timer register() {
    ResourceManager.registerTimer(toString(), this);
    return this;
  }

  /// إلغاء Timer وإزالته من ResourceManager
  void dispose() {
    ResourceManager.cancelTimer(toString());
  }
}

/// Extension لتسهيل استخدام ResourceManager مع StreamController
extension StreamControllerResourceExtension<T> on StreamController<T> {
  /// تسجيل StreamController في ResourceManager
  StreamController<T> register() {
    return ResourceManager.registerController(this);
  }

  /// إغلاق StreamController وإزالته من ResourceManager
  void dispose() {
    ResourceManager.closeController(this);
  }
}

/// Extension لتسهيل استخدام ResourceManager مع StreamSubscription
extension StreamSubscriptionResourceExtension<T> on StreamSubscription<T> {
  /// تسجيل StreamSubscription في ResourceManager
  StreamSubscription<T> register() {
    ResourceManager.registerSubscription(toString(), this);
    return this;
  }

  /// إلغاء StreamSubscription وإزالته من ResourceManager
  void dispose() {
    ResourceManager.cancelSubscription(toString());
  }
}
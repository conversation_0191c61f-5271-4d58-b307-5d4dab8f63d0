import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

final _logger = Logger('SafeUIOperations');

/// Utility class for safe UI operations that prevent NaN/Infinity errors
class SafeUIOperations {
  SafeUIOperations._();

  /// Safe withAlpha operation that prevents crashes
  static Color safeWithAlpha(Color color, double alpha) {
    try {
      var alphaValue = alpha;

      // Check for NaN or Infinity
      if (alphaValue.isNaN || alphaValue.isInfinite) {
        _logger.warning(
          'Invalid alpha value: $alphaValue, using 1.0 as default',
        );
        alphaValue = 1.0;
      }

      // Clamp alpha to valid range
      alphaValue = alphaValue.clamp(0.0, 1.0);

      // Convert to int safely (0–255)
      final alphaInt = (alphaValue * 255).round().clamp(0, 255);

      return color.withAlpha(alphaInt);
    } catch (e) {
      _logger.severe('Error in safeWithAlpha: $e');
      return color; // Return original color on error
    }
  }

  /// Safe withOpacity operation
  static Color safeWithOpacity(Color color, double opacity) {
    try {
      var opacityValue = opacity;

      if (opacityValue.isNaN || opacityValue.isInfinite) {
        _logger.warning(
          'Invalid opacity value: $opacityValue, using 1.0 as default',
        );
        opacityValue = 1.0;
      }

      opacityValue = opacityValue.clamp(0.0, 1.0);

      // Use new Color API to avoid precision loss.
      return color.withValues(alpha: opacityValue);
    } catch (e) {
      _logger.severe('Error in safeWithOpacity: $e');
      return color;
    }
  }

  /// Safe double to int conversion
  static int safeDoubleToInt(double value, {int fallback = 0}) {
    try {
      if (value.isNaN || value.isInfinite) {
        _logger.warning(
          'Invalid double value: $value, using fallback: $fallback',
        );
        return fallback;
      }
      return value.round();
    } catch (e) {
      _logger.severe('Error converting double to int: $e');
      return fallback;
    }
  }

  /// Safe Size operations
  static Size safeSize(double width, double height) {
    try {
      var w = width;
      var h = height;

      // Check for invalid values
      if (w.isNaN || w.isInfinite) {
        _logger.warning('Invalid width: $w, using 0.0 as default');
        w = 0.0;
      }
      if (h.isNaN || h.isInfinite) {
        _logger.warning('Invalid height: $h, using 0.0 as default');
        h = 0.0;
      }

      // Ensure non-negative values
      w = w.abs();
      h = h.abs();

      return Size(w, h);
    } catch (e) {
      _logger.severe('Error creating Size: $e');
      return Size.zero;
    }
  }

  /// Safe BorderRadius operations
  static BorderRadius safeBorderRadius(double radius) {
    try {
      var r = radius;

      if (r.isNaN || r.isInfinite) {
        _logger.warning('Invalid radius: $r, using 0.0 as default');
        r = 0.0;
      }

      r = r.abs(); // Ensure positive
      return BorderRadius.circular(r);
    } catch (e) {
      _logger.severe('Error creating BorderRadius: $e');
      return BorderRadius.zero;
    }
  }

  /// Safe Offset operations
  static Offset safeOffset(double dx, double dy) {
    try {
      var offsetX = dx;
      var offsetY = dy;

      if (offsetX.isNaN || offsetX.isInfinite) {
        _logger.warning('Invalid dx: $offsetX, using 0.0 as default');
        offsetX = 0.0;
      }
      if (offsetY.isNaN || offsetY.isInfinite) {
        _logger.warning('Invalid dy: $offsetY, using 0.0 as default');
        offsetY = 0.0;
      }

      return Offset(offsetX, offsetY);
    } catch (e) {
      _logger.severe('Error creating Offset: $e');
      return Offset.zero;
    }
  }
}

/// Extension on Color for safe operations
extension SafeColorExtension on Color {
  /// Safe withAlpha extension
  Color safeAlpha(double alpha) => SafeUIOperations.safeWithAlpha(this, alpha);

  /// Safe withOpacity extension
  Color safeOpacity(double opacity) =>
      SafeUIOperations.safeWithOpacity(this, opacity);
}

/// Extension on double for safe operations
extension SafeDoubleExtension on double {
  /// Safe toInt conversion
  int safeToInt({int fallback = 0}) =>
      SafeUIOperations.safeDoubleToInt(this, fallback: fallback);

  /// Check if value is safe for UI operations
  bool get isSafeForUI => !isNaN && !isInfinite;

  /// Get safe value for UI operations
  double getSafeValue({double fallback = 0.0}) {
    if (isSafeForUI) return this;
    _logger.warning('Unsafe double value: $this, using fallback: $fallback');
    return fallback;
  }
}

import 'package:intl/intl.dart';

/// UI utility functions for common operations
class UIUtils {
  /// Format a DateTime to a readable Arabic date string
  static String formatDate(DateTime date) {
    final formatter = DateFormat('dd/MM/yyyy', 'ar');
    return formatter.format(date);
  }

  /// Format a DateTime to a readable Arabic date and time string
  static String formatDateTime(DateTime dateTime) {
    final formatter = DateFormat('dd/MM/yyyy HH:mm', 'ar');
    return formatter.format(dateTime);
  }

  /// Format a DateTime to a relative time string (e.g., "منذ ساعتين")
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  /// Format a price in Libyan Dinars
  static String formatPrice(double price) {
    final formatter = NumberFormat.currency(
      locale: 'ar',
      symbol: 'د.ل',
      decimalDigits: 2,
    );
    return formatter.format(price);
  }

  /// Format a number with Arabic locale
  static String formatNumber(num number) {
    final formatter = NumberFormat('#,##0', 'ar');
    return formatter.format(number);
  }

  /// Get a color for status badges
  static String getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'pending_approval':
        return 'orange';
      case 'approved':
      case 'active':
        return 'green';
      case 'rejected':
        return 'red';
      case 'cancelled':
      case 'canceled':
        return 'grey';
      case 'expired':
        return 'grey';
      default:
        return 'grey';
    }
  }

  /// Truncate text to a specific length
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  /// Check if a string is a valid email
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Check if a string is a valid phone number
  static bool isValidPhone(String phone) {
    return RegExp(r'^[0-9+\-\s()]+$').hasMatch(phone);
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// مجموعة من الـ Extensions المفيدة لتحسين الكود وتسهيل التطوير

/// Extension على String لإضافة وظائف مفيدة
extension StringExtensions on String {
  /// تحقق من كون النص عنوان بريد إلكتروني صحيح
  bool get isValidEmail {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(this);
  }

  /// تحقق من كون النص رقم هاتف صحيح
  bool get isValidPhone {
    return RegExp(r'^[0-9+\-\s()]+$').hasMatch(this);
  }

  /// إزالة المسافات الزائدة من بداية ونهاية النص
  String get trimmed => trim();

  /// تحويل أول حرف إلى capital
  String get capitalizeFirst {
    if (isEmpty) return this;
    return this[0].toUpperCase() + substring(1);
  }

  /// تحويل كل كلمة لتبدأ بحرف capital
  String get titleCase {
    return split(' ').map((word) => word.capitalizeFirst).join(' ');
  }

  /// تحقق من كون النص فارغ أو يحتوي على مسافات فقط
  bool get isNullOrWhiteSpace {
    return trim().isEmpty;
  }

  /// تحويل النص إلى رقم بأمان
  int? get toIntOrNull {
    return int.tryParse(this);
  }

  /// تحويل النص إلى رقم عشري بأمان
  double? get toDoubleOrNull {
    return double.tryParse(this);
  }
}

/// Extension على DateTime لإضافة وظائف مفيدة
extension DateTimeExtensions on DateTime {
  /// تنسيق التاريخ بالشكل المطلوب
  String get formattedDate {
    return DateFormat('dd/MM/yyyy').format(this);
  }

  /// تنسيق الوقت بالشكل المطلوب
  String get formattedTime {
    return DateFormat('HH:mm').format(this);
  }

  /// تنسيق التاريخ والوقت معاً
  String get formattedDateTime {
    return DateFormat('dd/MM/yyyy HH:mm').format(this);
  }

  /// تحقق من كون التاريخ اليوم
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  /// تحقق من كون التاريخ أمس
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year &&
        month == yesterday.month &&
        day == yesterday.day;
  }

  /// الحصول على التاريخ النسبي (منذ ساعة، منذ يوم، إلخ)
  String get relativeTime {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }
}

/// Extension على BuildContext لإضافة وظائف مفيدة
extension BuildContextExtensions on BuildContext {
  /// الحصول على ThemeData الحالي
  ThemeData get theme => Theme.of(this);

  /// الحصول على ColorScheme الحالي
  ColorScheme get colorScheme => theme.colorScheme;

  /// الحصول على TextTheme الحالي
  TextTheme get textTheme => theme.textTheme;

  /// الحصول على MediaQueryData الحالي
  MediaQueryData get mediaQuery => MediaQuery.of(this);

  /// الحصول على حجم الشاشة
  Size get screenSize => mediaQuery.size;

  /// الحصول على عرض الشاشة
  double get screenWidth => screenSize.width;

  /// الحصول على ارتفاع الشاشة
  double get screenHeight => screenSize.height;

  /// تحقق من كون الشاشة صغيرة (موبايل)
  bool get isSmallScreen => screenWidth < 600;

  /// تحقق من كون الشاشة متوسطة (تابلت)
  bool get isMediumScreen => screenWidth >= 600 && screenWidth < 1200;

  /// تحقق من كون الشاشة كبيرة (ديسكتوب)
  bool get isLargeScreen => screenWidth >= 1200;

  /// إظهار SnackBar
  void showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? colorScheme.error : colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// إخفاء لوحة المفاتيح
  void hideKeyboard() {
    FocusScope.of(this).unfocus();
  }

  /// التنقل إلى صفحة جديدة
  Future<T?> pushNamed<T>(String routeName, {Object? arguments}) {
    return Navigator.of(this).pushNamed<T>(routeName, arguments: arguments);
  }

  /// العودة للصفحة السابقة
  void pop<T>([T? result]) {
    Navigator.of(this).pop<T>(result);
  }
}

/// Extension على List لإضافة وظائف مفيدة
extension ListExtensions<T> on List<T> {
  /// الحصول على عنصر بأمان (يعيد null إذا كان المؤشر خارج النطاق)
  T? elementAtOrNull(int index) {
    if (index >= 0 && index < length) {
      return this[index];
    }
    return null;
  }

  /// تحقق من كون القائمة فارغة أو null
  bool get isNullOrEmpty => isEmpty;

  /// تحقق من كون القائمة تحتوي على عناصر
  bool get isNotNullOrEmpty => isNotEmpty;

  /// تقسيم القائمة إلى مجموعات بحجم محدد
  List<List<T>> chunked(int chunkSize) {
    final chunks = <List<T>>[];
    for (int i = 0; i < length; i += chunkSize) {
      chunks.add(sublist(i, (i + chunkSize).clamp(0, length)));
    }
    return chunks;
  }
}

/// Extension على double لإضافة وظائف مفيدة
extension DoubleExtensions on double {
  /// تحويل الرقم إلى نص مع تنسيق العملة
  String get toCurrency {
    final formatter = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'LYD',
      decimalDigits: 2,
    );
    return formatter.format(this);
  }

  /// تقريب الرقم إلى منزلة عشرية محددة
  double roundTo(int places) {
    num factor = 1;
    for (int i = 0; i < places; i++) {
      factor *= 10;
    }
    return (this * factor).round() / factor;
  }
}

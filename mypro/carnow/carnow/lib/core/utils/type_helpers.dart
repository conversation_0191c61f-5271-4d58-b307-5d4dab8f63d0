/// Helper functions for type conversions and null safety
class TypeHelpers {
  /// Safely converts dynamic to String
  static String? dynamicToString(dynamic value) {
    if (value == null) return null;
    if (value is String) return value;
    return value.toString();
  }

  /// Safely converts dynamic to int
  static int? dynamicToInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    if (value is double) return value.toInt();
    return null;
  }

  /// Safely converts dynamic to double
  static double? dynamicToDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  /// Safely converts dynamic to `Map<String, dynamic>`
  static Map<String, dynamic>? dynamicToMap(dynamic value) {
    if (value == null) return null;
    if (value is Map<String, dynamic>) return value;
    if (value is Map) return Map<String, dynamic>.from(value);
    return null;
  }

  /// Safely converts dynamic to List
  static List<T>? dynamicToList<T>(dynamic value) {
    if (value == null) return null;
    if (value is List<T>) return value;
    if (value is List) return value.cast<T>();
    return null;
  }

  /// Safely converts dynamic to `Iterable<T>`
  static Iterable<T>? dynamicToIterable<T>(dynamic value) {
    if (value == null) return null;
    if (value is Iterable<T>) return value;
    if (value is Iterable) return value.cast<T>();
    if (value is List) return value.cast<T>();
    return null;
  }
}

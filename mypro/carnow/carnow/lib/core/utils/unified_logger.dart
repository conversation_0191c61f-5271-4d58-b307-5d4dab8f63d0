import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';

/// مستويات التسجيل
enum LogLevel { debug, info, warning, error, critical }

/// نظام التسجيل الموحد لتطبيق CarNow
class UnifiedLogger {
  factory UnifiedLogger() => _instance;
  UnifiedLogger._internal();
  static final UnifiedLogger _instance = UnifiedLogger._internal();

  static final Logger _logger = Logger('CarNow');
  static bool _isInitialized = false;

  /// تهيئة نظام التسجيل
  static void initialize() {
    if (_isInitialized) return;

    Logger.root.level = kDebugMode ? Level.ALL : Level.WARNING;
    Logger.root.onRecord.listen((record) {
      if (kDebugMode) {
        developer.log(
          record.message,
          time: record.time,
          level: record.level.value,
          name: record.loggerName,
          error: record.error,
          stackTrace: record.stackTrace,
        );
      }
    });

    _isInitialized = true;
  }

  /// تسجيل معلومات تطوير
  static void debug(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (!kDebugMode) return;
    _log(LogLevel.debug, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// تسجيل معلومات عامة
  static void info(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.info, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// تسجيل تحذيرات
  static void warning(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.warning, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// تسجيل أخطاء
  static void error(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// تسجيل أخطاء حرجة
  static void critical(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.critical, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// تسجيل بداية عملية
  static void startOperation(String operation, {String? tag}) {
    debug('🚀 Starting: $operation', tag: tag);
  }

  /// تسجيل انتهاء عملية بنجاح
  static void endOperation(String operation, {Duration? duration, String? tag}) {
    final durationText = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    debug('✅ Completed: $operation$durationText', tag: tag);
  }

  /// تسجيل فشل عملية
  static void failOperation(String operation, Object error, {StackTrace? stackTrace, String? tag}) {
    UnifiedLogger.error('❌ Failed: $operation', error: error, stackTrace: stackTrace, tag: tag);
  }

  /// تسجيل التنقل في التطبيق
  static void navigation(String from, String to, {String? tag}) {
    debug('🧭 Navigation: $from → $to', tag: tag);
  }

  /// تسجيل أحداث المستخدم
  static void userAction(String action, {Map<String, dynamic>? data, String? tag}) {
    final dataText = data != null ? ' Data: $data' : '';
    debug('👤 User Action: $action$dataText', tag: tag);
  }

  /// تسجيل أحداث الشبكة
  static void network(
    String method,
    String url, {
    int? statusCode,
    Duration? duration,
    String? tag,
  }) {
    final statusText = statusCode != null ? ' [$statusCode]' : '';
    final durationText = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    debug('🌐 $method $url$statusText$durationText', tag: tag);
  }

  /// تسجيل أحداث قاعدة البيانات
  static void database(
    String operation, {
    String? table,
    Duration? duration,
    String? tag,
  }) {
    final tableText = table != null ? ' on $table' : '';
    final durationText = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    debug('💾 DB $operation$tableText$durationText', tag: tag);
  }

  /// تسجيل أحداث الأداء
  static void performance(
    String metric,
    dynamic value, {
    String? unit,
    String? tag,
  }) {
    final unitText = unit != null ? ' $unit' : '';
    debug('⚡ Performance: $metric = $value$unitText', tag: tag);
  }

  /// التسجيل الداخلي
  static void _log(
    LogLevel level,
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    final formattedMessage = _formatMessage(level, message, tag: tag);

    switch (level) {
      case LogLevel.debug:
        _logger.fine(formattedMessage, error, stackTrace);
        break;
      case LogLevel.info:
        _logger.info(formattedMessage, error, stackTrace);
        break;
      case LogLevel.warning:
        _logger.warning(formattedMessage, error, stackTrace);
        break;
      case LogLevel.error:
        _logger.severe(formattedMessage, error, stackTrace);
        break;
      case LogLevel.critical:
        _logger.shout(formattedMessage, error, stackTrace);
        break;
    }
  }

  /// تنسيق الرسالة
  static String _formatMessage(LogLevel level, String message, {String? tag}) {
    final levelPrefix = _getLevelPrefix(level);
    final tagPrefix = tag != null ? '[$tag] ' : '';
    final timestamp = DateTime.now().toIso8601String().substring(11, 19);

    return '$timestamp $levelPrefix $tagPrefix$message';
  }

  /// الحصول على بادئة المستوى
  static String _getLevelPrefix(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '🐛 DEBUG';
      case LogLevel.info:
        return 'ℹ️  INFO';
      case LogLevel.warning:
        return '⚠️  WARN';
      case LogLevel.error:
        return '❌ ERROR';
      case LogLevel.critical:
        return '🚨 CRITICAL';
    }
  }
}

/// اختصارات للاستخدام السريع
const log = UnifiedLogger;

/// Extension لتسهيل الاستخدام على أي كائن
extension LoggerExtension on Object {
  void logDebug(String message) =>
      UnifiedLogger.debug(message, tag: runtimeType.toString());
  
  void logInfo(String message) =>
      UnifiedLogger.info(message, tag: runtimeType.toString());
  
  void logWarning(String message) =>
      UnifiedLogger.warning(message, tag: runtimeType.toString());
  
  void logError(String message, {Object? error, StackTrace? stackTrace}) =>
      UnifiedLogger.error(
        message,
        tag: runtimeType.toString(),
        error: error,
        stackTrace: stackTrace,
      );
}

/// دوال عامة للتوافق العكسي
void safePrint(Object? object) {
  UnifiedLogger.info(object?.toString() ?? 'null');
}

void safeDebugPrint(String message, {String? tag}) {
  UnifiedLogger.debug(message, tag: tag);
}

void logError(String message, {Object? error, String? tag}) {
  UnifiedLogger.error(message, error: error, tag: tag);
}

void logWarning(String message, {String? tag}) {
  UnifiedLogger.warning(message, tag: tag);
}

void logInfo(String message, {String? tag}) {
  UnifiedLogger.info(message, tag: tag);
}

void logNavigation(String from, String to) {
  UnifiedLogger.navigation(from, to);
}

void logUserAction(String action, {Map<String, dynamic>? data}) {
  UnifiedLogger.userAction(action, data: data);
}

void logNetworkRequest(String method, String url, {Map<String, dynamic>? data}) {
  UnifiedLogger.network(method, url);
}

void logDatabaseOperation(String operation, String table, {Map<String, dynamic>? data}) {
  UnifiedLogger.database(operation, table: table);
}

void logPerformance(String operation, Duration duration, {Map<String, dynamic>? metadata}) {
  UnifiedLogger.performance(operation, duration.inMilliseconds, unit: 'ms');
}

/// === التوافق العكسي ===
/// Backward compatibility aliases

/// Alias للتوافق مع AppLogger القديم
@Deprecated('Use UnifiedLogger instead')
class AppLogger {
  static void debug(String message, {String? tag, Object? error, StackTrace? stackTrace}) =>
      UnifiedLogger.debug(message, tag: tag, error: error, stackTrace: stackTrace);
  
  static void info(String message, {String? tag, Object? error, StackTrace? stackTrace}) =>
      UnifiedLogger.info(message, tag: tag, error: error, stackTrace: stackTrace);
  
  static void warning(String message, {String? tag, Object? error, StackTrace? stackTrace}) =>
      UnifiedLogger.warning(message, tag: tag, error: error, stackTrace: stackTrace);
  
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) =>
      UnifiedLogger.error(message, tag: tag, error: error, stackTrace: stackTrace);
  
  static void critical(String message, {String? tag, Object? error, StackTrace? stackTrace}) =>
      UnifiedLogger.critical(message, tag: tag, error: error, stackTrace: stackTrace);
} 
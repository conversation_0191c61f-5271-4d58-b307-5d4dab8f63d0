import 'package:flutter/material.dart';

/// Theme extension methods for easy access to unified Material 3 theme properties
extension UnifiedThemeExtension on BuildContext {
  /// Quick access to color scheme
  ColorScheme get colorScheme => Theme.of(this).colorScheme;

  /// Quick access to text theme
  TextTheme get textTheme => Theme.of(this).textTheme;

  // Primary colors
  Color get primaryColor => colorScheme.primary;
  Color get secondaryColor => colorScheme.secondary;
  Color get tertiaryColor => colorScheme.tertiary;
  Color get surfaceColor => colorScheme.surface;
  Color get backgroundColor => colorScheme.surface;

  // Text colors
  Color get onPrimaryColor => colorScheme.onPrimary;
  Color get onSecondaryColor => colorScheme.onSecondary;
  Color get onSurfaceColor => colorScheme.onSurface;

  // Typography shortcuts
  TextStyle? get displayLarge => textTheme.displayLarge;
  TextStyle? get displayMedium => textTheme.displayMedium;
  TextStyle? get displaySmall => textTheme.displaySmall;
  TextStyle? get headlineLarge => textTheme.headlineLarge;
  TextStyle? get headlineMedium => textTheme.headlineMedium;
  TextStyle? get headlineSmall => textTheme.headlineSmall;
  TextStyle? get titleLarge => textTheme.titleLarge;
  TextStyle? get titleMedium => textTheme.titleMedium;
  TextStyle? get titleSmall => textTheme.titleSmall;
  TextStyle? get bodyLarge => textTheme.bodyLarge;
  TextStyle? get bodyMedium => textTheme.bodyMedium;
  TextStyle? get bodySmall => textTheme.bodySmall;
  TextStyle? get labelLarge => textTheme.labelLarge;
  TextStyle? get labelMedium => textTheme.labelMedium;
  TextStyle? get labelSmall => textTheme.labelSmall;

  // Standard spacing values (double values for height/width)
  double get paddingTiny => 4;
  double get paddingXSmall => 6;
  double get paddingSmall => 8;
  double get paddingMedium => 16;
  double get paddingLarge => 24;
  double get paddingXLarge => 32;
  double get paddingXXLarge => 48;

  // EdgeInsets shortcuts for padding property
  EdgeInsets get paddingAllTiny => const EdgeInsets.all(4);
  EdgeInsets get paddingAllSmall => const EdgeInsets.all(8);
  EdgeInsets get paddingAllMedium => const EdgeInsets.all(16);
  EdgeInsets get paddingAllLarge => const EdgeInsets.all(24);
  EdgeInsets get paddingAllXLarge => const EdgeInsets.all(32);

  // Horizontal padding
  EdgeInsets get paddingHorizontalSmall =>
      const EdgeInsets.symmetric(horizontal: 8);
  EdgeInsets get paddingHorizontalMedium =>
      const EdgeInsets.symmetric(horizontal: 16);
  EdgeInsets get paddingHorizontalLarge =>
      const EdgeInsets.symmetric(horizontal: 24);

  // Vertical padding
  EdgeInsets get paddingVerticalSmall =>
      const EdgeInsets.symmetric(vertical: 8);
  EdgeInsets get paddingVerticalMedium =>
      const EdgeInsets.symmetric(vertical: 16);
  EdgeInsets get paddingVerticalLarge =>
      const EdgeInsets.symmetric(vertical: 24);

  // Border radius values
  double get radiusSmall => 8;
  double get radiusMedium => 12;
  double get radiusLarge => 16;
  double get radiusXLarge => 24;

  // Device type helpers
  bool get isMobile => MediaQuery.of(this).size.width < 600;
  bool get isTablet =>
      MediaQuery.of(this).size.width >= 600 &&
      MediaQuery.of(this).size.width < 1200;
  bool get isDesktop => MediaQuery.of(this).size.width >= 1200;

  // Screen size helpers
  Size get screenSize => MediaQuery.of(this).size;
  double get screenWidth => screenSize.width;
  double get screenHeight => screenSize.height;

  // Safe area helpers
  EdgeInsets get safeAreaPadding => MediaQuery.of(this).padding;
  double get statusBarHeight => MediaQuery.of(this).padding.top;
  double get bottomSafeArea => MediaQuery.of(this).padding.bottom;

  // Built-in SnackBar helpers
  void showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
      ),
    );
  }

  void showErrorSnackBar(String message) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: colorScheme.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
      ),
    );
  }

  void showInfoSnackBar(String message) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: colorScheme.surfaceContainerHighest,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
      ),
    );
  }

  void showWarningSnackBar(String message) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: colorScheme.tertiary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
      ),
    );
  }
}

/// Extension للألوان مع دعم الشفافية
extension ColorExtension on Color {
  /// إضافة شفافية آمنة
  Color withSafeAlpha(double alpha) {
    final alphaInt = (alpha * 255).round().clamp(0, 255);
    return withAlpha(alphaInt);
  }

  /// إنشاء لون فاتح من اللون الحالي
  Color get lightVariant => withSafeAlpha(0.1);

  /// إنشاء لون متوسط من اللون الحالي
  Color get mediumVariant => withSafeAlpha(0.3);

  /// إنشاء لون داكن من اللون الحالي
  Color get darkVariant => withSafeAlpha(0.7);
}

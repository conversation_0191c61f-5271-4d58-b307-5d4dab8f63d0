import 'package:freezed_annotation/freezed_annotation.dart';
import '../errors/app_error.dart';

part 'result.freezed.dart';

/// نمط Result للتعامل مع النتائج بشكل آمن
/// Result pattern for safe handling of operations
@freezed
sealed class Result<T> with _$Result<T> {
  /// نجح العملية
  /// Success case
  const factory Result.success(T data) = Success<T>;

  /// فشل العملية
  /// Failure case
  const factory Result.failure(AppError error) = Failure<T>;
}

/// امتدادات مفيدة لنمط Result
/// Useful extensions for Result pattern
extension ResultExtensions<T> on Result<T> {
  /// هل العملية نجحت؟
  /// Is the operation successful?
  bool get isSuccess => this is Success<T>;

  /// هل العملية فشلت؟
  /// Is the operation failed?
  bool get isFailure => this is Failure<T>;

  /// الحصول على البيانات (null إذا فشلت)
  /// Get data (null if failed)
  T? get dataOrNull => switch (this) {
    Success<T>(data: final data) => data,
    Failure<T>() => null,
  };

  /// الحصول على الخطأ (null إذا نجحت)
  /// Get error (null if successful)
  AppError? get errorOrNull => switch (this) {
    Success<T>() => null,
    Failure<T>(error: final error) => error,
  };

  /// الحصول على البيانات أو قيمة افتراضية
  /// Get data or default value
  T getOrElse(T defaultValue) => switch (this) {
    Success<T>(data: final data) => data,
    Failure<T>() => defaultValue,
  };

  /// تحويل البيانات في حالة النجاح
  /// Transform data on success
  Result<R> map<R>(R Function(T data) transform) => switch (this) {
    Success<T>(data: final data) => Result.success(transform(data)),
    Failure<T>(error: final error) => Result.failure(error),
  };

  /// تحويل البيانات مع إمكانية الفشل
  /// Transform data with possible failure
  Result<R> flatMap<R>(Result<R> Function(T data) transform) => switch (this) {
    Success<T>(data: final data) => transform(data),
    Failure<T>(error: final error) => Result.failure(error),
  };

  /// تنفيذ عملية جانبية على النجاح
  /// Perform side effect on success
  Result<T> onSuccess(void Function(T data) action) => switch (this) {
    Success<T>(data: final data) => () {
      action(data);
      return this;
    }(),
    Failure<T>() => this,
  };

  /// تنفيذ عملية جانبية على الفشل
  /// Perform side effect on failure
  Result<T> onFailure(void Function(AppError error) action) => switch (this) {
    Success<T>() => this,
    Failure<T>(error: final error) => () {
      action(error);
      return this;
    }(),
  };
}

/// مساعدات لإنشاء Result من العمليات العامة
/// Helpers for creating Result from common operations
class ResultHelpers {
  /// تنفيذ عملية مع حماية من الأخطاء
  /// Execute operation with error protection
  static Result<T> tryCall<T>(T Function() operation) {
    try {
      return Result.success(operation());
    } catch (error, stackTrace) {
      return Result.failure(
        AppErrorFactory.fromError(error, stackTrace: stackTrace),
      );
    }
  }

  /// تنفيذ عملية غير متزامنة مع حماية من الأخطاء
  /// Execute async operation with error protection
  static Future<Result<T>> tryCallAsync<T>(
    Future<T> Function() operation,
  ) async {
    try {
      final result = await operation();
      return Result.success(result);
    } catch (error, stackTrace) {
      return Result.failure(
        AppErrorFactory.fromError(error, stackTrace: stackTrace),
      );
    }
  }

  /// دمج عدة نتائج
  /// Combine multiple results
  static Result<List<T>> combine<T>(List<Result<T>> results) {
    final successData = <T>[];

    for (final result in results) {
      if (result.isFailure) {
        return Result.failure(result.errorOrNull!);
      }
      successData.add(result.dataOrNull as T);
    }

    return Result.success(successData);
  }

  /// تحويل من nullable إلى Result
  /// Convert from nullable to Result
  static Result<T> fromNullable<T>(T? value, {required AppError errorIfNull}) {
    if (value != null) {
      return Result.success(value);
    } else {
      return Result.failure(errorIfNull);
    }
  }
}

/// نوع عملي للعمليات التي لا تُرجع بيانات
/// Convenient type for operations that don't return data
typedef VoidResult = Result<void>;

/// مساعدات للعمليات التي لا تُرجع بيانات
/// Helpers for void operations
class VoidResultHelpers {
  /// نجح العملية بدون بيانات
  /// Success without data
  static VoidResult success() => const Result.success(null);

  /// فشل العملية
  /// Failure
  static VoidResult failure(AppError error) => Result.failure(error);

  /// تنفيذ عملية بدون بيانات مع حماية من الأخطاء
  /// Execute void operation with error protection
  static VoidResult tryCall(void Function() operation) {
    try {
      operation();
      return success();
    } catch (error, stackTrace) {
      return Result.failure(
        AppErrorFactory.fromError(error, stackTrace: stackTrace),
      );
    }
  }

  /// تنفيذ عملية غير متزامنة بدون بيانات مع حماية من الأخطاء
  /// Execute async void operation with error protection
  static Future<VoidResult> tryCallAsync(
    Future<void> Function() operation,
  ) async {
    try {
      await operation();
      return success();
    } catch (error, stackTrace) {
      return Result.failure(
        AppErrorFactory.fromError(error, stackTrace: stackTrace),
      );
    }
  }
}

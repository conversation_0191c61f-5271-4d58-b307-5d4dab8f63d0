import 'dart:io';

import 'package:flutter/material.dart';
import 'package:logging/logging.dart';


import '../../l10n/app_localizations.dart';

final _logger = Logger('ErrorHandler');

/// Global error handler for the CarNow application
class ErrorHandler {
  static const String _defaultErrorMessage = 'حدث خطأ غير متوقع';

  /// Handle and display errors to the user
  static void handleError(
    BuildContext context,
    dynamic error, {
    String? customMessage,
    bool showSnackBar = true,
    VoidCallback? onRetry,
  }) {
    final l10n = AppLocalizations.of(context);
    final errorMessage = _getErrorMessage(error, l10n, customMessage);

    _logger.severe('Error occurred: $error');

    if (showSnackBar && context.mounted) {
      _showErrorSnackBar(context, errorMessage, onRetry);
    }
  }

  /// Get user-friendly error message
  static String _getErrorMessage(
    dynamic error,
    AppLocalizations? l10n,
    String? customMessage,
  ) {
    if (customMessage != null) return customMessage;

    // Supabase-specific error handling removed - Forever Plan Compliance
    // All errors now handled generically through Go API

    // Handle network errors
    if (error is SocketException) {
      return l10n?.networkError ?? 'خطأ في الاتصال بالإنترنت';
    }

    if (error is HttpException) {
      return l10n?.serverError ?? 'خطأ في الخادم';
    }

    // Handle timeout errors
    if (error.toString().contains('timeout')) {
      return l10n?.timeoutError ?? 'انتهت مهلة الاتصال';
    }

    // Default error message
    return l10n?.unknownError ?? _defaultErrorMessage;
  }

  // _handleAuthError removed - Forever Plan Compliance
  // All authentication errors now handled through Go API

  // _handlePostgrestError removed - Forever Plan Compliance
  // All database errors now handled through Go API

  /// Show error snackbar with retry option
  static void _showErrorSnackBar(
    BuildContext context,
    String message,
    VoidCallback? onRetry,
  ) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.red.shade600,
        action: onRetry != null
            ? SnackBarAction(
                label: 'إعادة المحاولة',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Show success message
  static void showSuccessMessage(BuildContext context, String message) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.green.shade600,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Show warning message
  static void showWarning(BuildContext context, String message) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.orange.shade600,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Show info message
  static void showInfo(BuildContext context, String message) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.blue.shade600,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Handle async operations with error handling
  static Future<T?> handleAsyncOperation<T>(
    BuildContext context,
    Future<T> operation, {
    String? loadingMessage,
    String? successMessage,
    String? errorMessage,
    bool showLoading = false,
    bool showSuccess = false,
    VoidCallback? onError,
  }) async {
    try {
      if (showLoading && loadingMessage != null) {
        showInfo(context, loadingMessage);
      }

      final result = await operation;

      if (showSuccess && successMessage != null && context.mounted) {
        showSuccessMessage(context, successMessage);
      }

      return result;
    } catch (error) {
      _logger.severe('Async operation failed: $error');

      if (context.mounted) {
        handleError(context, error, customMessage: errorMessage);
      }

      onError?.call();
      return null;
    }
  }

  /// Validate network connectivity
  static Future<bool> checkConnectivity() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// Show no internet connection dialog
  static void showNoInternetDialog(BuildContext context) {
    if (!context.mounted) return;

    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('لا يوجد اتصال بالإنترنت'),
        content: const Text(
          'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}

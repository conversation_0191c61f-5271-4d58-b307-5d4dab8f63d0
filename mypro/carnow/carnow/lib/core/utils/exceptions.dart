// Exceptions used throughout the application

/// Authentication related exceptions
class AppAuthException implements Exception {
  const AppAuthException({required this.message, this.stackTrace});
  final String message;
  final StackTrace? stackTrace;

  @override
  String toString() => 'AppAuthException: $message';
}

/// Network related exceptions
class NetworkException implements Exception {
  const NetworkException({
    required this.message,
    this.stackTrace,
    this.isRetryable = true,
  });
  final String message;
  final StackTrace? stackTrace;
  final bool isRetryable;

  @override
  String toString() => 'NetworkException: $message';
}

/// Data related exceptions
class DataException implements Exception {
  const DataException({required this.message, this.stackTrace});
  final String message;
  final StackTrace? stackTrace;

  @override
  String toString() => 'DataException: $message';
}

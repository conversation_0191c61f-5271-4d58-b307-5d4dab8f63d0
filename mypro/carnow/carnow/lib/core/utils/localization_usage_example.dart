import 'package:flutter/material.dart';
import 'package:carnow/core/utils/localization_helper.dart';

/// Example widget showing different ways to safely access localizations
class LocalizationUsageExample extends StatelessWidget {
  const LocalizationUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Localization Examples'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Method 1: Safe access with fallback (recommended)
            Text('Safe with fallback: ${context.l10nSafe.appTitle}'),
            
            // Method 2: Null-safe access
            if (context.hasLocalization)
              Text('Available: ${context.l10n!.appTitle}')
            else
              Text('Localization not available'),
            
            // Method 3: With error handling
            Builder(
              builder: (context) {
                try {
                  return Text('With error handling: ${context.l10nOrThrow.appTitle}');
                } catch (e) {
                  return Text('Error: $e');
                }
              },
            ),
            
            // Method 4: Direct helper usage
            Text('Helper method: ${LocalizationHelper.ofWithFallback(context).appTitle}'),
            
            // Check current locale
            Text('Current locale: ${context.currentLocale?.languageCode ?? 'unknown'}'),
            Text('Is Arabic: ${context.isArabic}'),
            Text('Is English: ${context.isEnglish}'),
          ],
        ),
      ),
    );
  }
}

/// Example of migrating from the old null-check operator approach
class MigrationExample extends StatelessWidget {
  const MigrationExample({super.key});

  @override
  Widget build(BuildContext context) {
    // OLD WAY (can cause null check operator error):
    // final l10n = AppLocalizations.of(context)!;
    
    // NEW WAY (safe with fallback):
    final l10n = context.l10nSafe;
    
    return Text(l10n.appTitle);
  }
} 
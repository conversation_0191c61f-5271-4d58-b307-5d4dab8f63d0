import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:logging/logging.dart';

/// مساعد لتنفيذ العمليات الثقيلة خارج الخيط الرئيسي مع أدوات التحسين
class ComputeUtils {
  static final _logger = Logger('ComputeUtils');

  // === أدوات ISOLATE OPERATIONS ===

  /// تنفيذ مهمة في خيط منفصل (Isolate) وإرجاع النتيجة
  static Future<R> execute<P, R>({
    required P params,
    required ComputeCallback<P, R> callback,
  }) async {
    // استخدام compute المضمنة في Flutter للأجهزة ذات المواصفات العالية
    if (!kIsWeb) {
      return compute(callback, params);
    }

    // خيارات إضافية للويب أو الأجهزة الضعيفة
    final completer = Completer<R>();
    final receivePort = ReceivePort();

    // مراقبة أي أخطاء
    final errorPort = ReceivePort();
    errorPort.listen((error) {
      errorPort.close();
      receivePort.close();
      if (!completer.isCompleted) {
        completer.completeError(error[0] as Object, error[1] as StackTrace?);
      }
    });

    try {
      final isolate = await Isolate.spawn<_IsolateData<P, R>>(
        _isolateHandler,
        _IsolateData<P, R>(
          callback: callback,
          params: params,
          responsePort: receivePort.sendPort,
        ),
        onError: errorPort.sendPort,
      );

      // الاستماع للنتيجة
      receivePort.listen((message) {
        receivePort.close();
        errorPort.close();
        isolate.kill();
        completer.complete(message as R);
      });

      return completer.future;
    } catch (e, stack) {
      receivePort.close();
      errorPort.close();
      return Future.error(e, stack);
    }
  }

  // === أدوات التحسين المدمجة ===

  // Debounce utility for search and input optimization
  static Timer? _debounceTimer;

  static void debounce(Duration delay, VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  // Throttle utility for scroll and frequent events
  static DateTime? _lastThrottleTime;

  static void throttle(Duration delay, VoidCallback callback) {
    final now = DateTime.now();
    if (_lastThrottleTime == null ||
        now.difference(_lastThrottleTime!) >= delay) {
      _lastThrottleTime = now;
      callback();
    }
  }

  // Batch operations utility
  static final Map<String, List<Function()>> _batchOperations = {};
  static final Map<String, Timer> _batchTimers = {};

  static void batchOperation(
    String batchKey,
    Function() operation, {
    Duration delay = const Duration(milliseconds: 500),
  }) {
    _batchOperations[batchKey] ??= [];
    _batchOperations[batchKey]!.add(operation);

    _batchTimers[batchKey]?.cancel();
    _batchTimers[batchKey] = Timer(delay, () {
      final operations = _batchOperations[batchKey] ?? [];
      _batchOperations.remove(batchKey);
      _batchTimers.remove(batchKey);

      // Execute all batched operations
      for (final operation in operations) {
        try {
          operation();
        } catch (e) {
          if (kDebugMode) {
            _logger.severe('Batch operation error: $e');
          }
        }
      }
    });
  }

  // Memory optimization for large lists
  static List<T> optimizeList<T>(List<T> list, {int maxSize = 1000}) {
    if (list.length <= maxSize) return list;

    // Keep first half and last quarter
    final firstHalf = list.take(maxSize ~/ 2).toList();
    final lastQuarter = list.skip(list.length - (maxSize ~/ 4)).toList();

    return [...firstHalf, ...lastQuarter];
  }

  // Widget build optimization
  static Widget optimizeWidget(Widget child, String widgetName) {
    if (kDebugMode) {
      return Builder(
        builder: (context) {
          final stopwatch = Stopwatch()..start();
          final result = child;
          stopwatch.stop();

          if (stopwatch.elapsedMilliseconds > 16) {
            _logger.warning(
              'Slow widget build: $widgetName took ${stopwatch.elapsedMilliseconds}ms',
            );
          }

          return result;
        },
      );
    }
    return child;
  }

  // Cleanup utilities
  static void cleanup() {
    _debounceTimer?.cancel();
    for (final timer in _batchTimers.values) {
      timer.cancel();
    }
    _batchTimers.clear();
    _batchOperations.clear();
  }

  /// معالج العمليات داخل الـ Isolate
  static void _isolateHandler<P, R>(_IsolateData<P, R> data) {
    try {
      final result = data.callback(data.params);
      data.responsePort.send(result);
    } catch (e, stackTrace) {
      data.responsePort.send([e, stackTrace]);
    }
  }
}

/// بيانات الـ Isolate
class _IsolateData<P, R> {
  const _IsolateData({
    required this.callback,
    required this.params,
    required this.responsePort,
  });

  final ComputeCallback<P, R> callback;
  final P params;
  final SendPort responsePort;
}

// === Extensions للسهولة ===

/// Extension for easy optimization
extension OptimizedFuture<T> on Future<T> {
  Future<T> optimized(String operationName) {
    return _measurePerformance(operationName, this);
  }
}

/// Mixin for optimized widgets
mixin OptimizedWidget {
  Widget optimizeChild(Widget child, String name) {
    return ComputeUtils.optimizeWidget(child, name);
  }
}

/// قياس الأداء للعمليات
Future<T> _measurePerformance<T>(String operationName, Future<T> operation) async {
  final stopwatch = Stopwatch()..start();
  try {
    final result = await operation;
    stopwatch.stop();
    
    if (kDebugMode) {
      Logger('Performance').info('$operationName completed in ${stopwatch.elapsedMilliseconds}ms');
    }
    
    return result;
  } catch (e) {
    stopwatch.stop();
    if (kDebugMode) {
      Logger('Performance').severe('$operationName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
    }
    rethrow;
  }
}

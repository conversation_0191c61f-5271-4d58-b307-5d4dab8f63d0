import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;

/// Utility class for formatting various data types
class Formatters {
  // Private constructor to prevent instantiation
  Formatters._();

  /// Formats a double as Libyan Dinar (LYD)
  static String formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: 'LYD ', decimalDigits: 2);
    return formatter.format(amount);
  }

  /// Formats a DateTime as a readable date
  static String formatDate(DateTime date) => DateFormat.yMMMd().format(date);

  /// Formats a DateTime as a readable time
  static String formatTime(DateTime date) => DateFormat.jm().format(date);

  /// Formats a DateTime as a readable date and time
  static String formatDateTime(DateTime date) =>
      DateFormat.yMMMd().add_jm().format(date);

  /// Formats a DateTime as a "time ago" string (e.g. "5 minutes ago")
  static String timeAgo(DateTime date) => timeago.format(date);

  /// Formats a phone number in Libyan format
  static String formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.length != 10) return phoneNumber;

    return '+218 ${phoneNumber.substring(0, 2)} ${phoneNumber.substring(2, 5)} ${phoneNumber.substring(5)}';
  }

  /// Adds thousands separators to a number
  static String formatNumber(int number) =>
      NumberFormat.decimalPattern().format(number);
}

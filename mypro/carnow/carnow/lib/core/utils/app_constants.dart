/// Constants and configuration values for CarNow App
class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  /// Application Information
  static const String appName = 'CarNow';
  static const String appVersion = '1.0';
  static const String appDescription = 'Car Parts Marketplace';

  /// API Configuration
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration longTimeout = Duration(minutes: 2);
  static const int maxRetryAttempts = 3;

  /// UI Configuration
  static const int itemsPerPage = 20;
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const double maxImageWidth = 1920;
  static const double maxImageHeight = 1080;

  /// Cache Configuration
  static const Duration cacheValidityDuration = Duration(minutes: 5);
  static const int maxCacheItems = 100;

  /// Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  /// Validation Rules
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;

  /// Supported Languages
  static const List<String> supportedLanguageCodes = ['ar', 'en'];
  static const String defaultLanguageCode = 'ar';

  /// File Upload Limits
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];

  /// URL Patterns
  static const String emailPattern =
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^[0-9+\-\s()]+$';

  /// Error Messages
  static const String genericErrorMessage =
      'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
  static const String networkErrorMessage =
      'خطأ في الاتصال. تحقق من اتصال الإنترنت.';
  static const String timeoutErrorMessage =
      'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.';

  /// Feature Flags
  static const bool enablePushNotifications = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  static const bool enableLocationServices = true;

  /// Storage Keys
  static const String themeKey = 'app_theme';
  static const String languageKey = 'app_language';
  static const String onboardingKey = 'onboarding_completed';
  static const String userTokenKey = 'user_token';
  static const String userProfileKey = 'user_profile';

  /// Routes
  static const String homeRoute = '/';
  static const String loginRoute = '/login';
  static const String registerRoute = '/register';
  static const String profileRoute = '/profile';
  static const String settingsRoute = '/settings';

  /// Assets Paths
  static const String logoAsset = 'assets/images/logo.png';
  static const String placeholderAsset = 'assets/images/placeholder.png';
  static const String noDataAsset = 'assets/images/no_data.png';
}

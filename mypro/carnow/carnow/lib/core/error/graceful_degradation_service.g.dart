// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'graceful_degradation_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$gracefulDegradationServiceHash() =>
    r'20e81c8be73b20964d4a105f5beb6f231052b28c';

/// Riverpod provider for GracefulDegradationService
///
/// Copied from [gracefulDegradationService].
@ProviderFor(gracefulDegradationService)
final gracefulDegradationServiceProvider =
    AutoDisposeProvider<GracefulDegradationService>.internal(
      gracefulDegradationService,
      name: r'gracefulDegradationServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$gracefulDegradationServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GracefulDegradationServiceRef =
    AutoDisposeProviderRef<GracefulDegradationService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

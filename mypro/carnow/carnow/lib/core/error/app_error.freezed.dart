// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AppError {

 AppErrorType get type; String get code; String get message; String get messageAr;// Arabic message
 AppErrorSeverity get severity; String? get details; String? get detailsAr;// Arabic details
 Object? get originalError; StackTrace? get stackTrace; Map<String, dynamic>? get metadata; DateTime? get timestamp; String? get correlationId; bool get isRetryable; int get retryCount; Duration get retryAfter;
/// Create a copy of AppError
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppErrorCopyWith<AppError> get copyWith => _$AppErrorCopyWithImpl<AppError>(this as AppError, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppError&&(identical(other.type, type) || other.type == type)&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message)&&(identical(other.messageAr, messageAr) || other.messageAr == messageAr)&&(identical(other.severity, severity) || other.severity == severity)&&(identical(other.details, details) || other.details == details)&&(identical(other.detailsAr, detailsAr) || other.detailsAr == detailsAr)&&const DeepCollectionEquality().equals(other.originalError, originalError)&&(identical(other.stackTrace, stackTrace) || other.stackTrace == stackTrace)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.correlationId, correlationId) || other.correlationId == correlationId)&&(identical(other.isRetryable, isRetryable) || other.isRetryable == isRetryable)&&(identical(other.retryCount, retryCount) || other.retryCount == retryCount)&&(identical(other.retryAfter, retryAfter) || other.retryAfter == retryAfter));
}


@override
int get hashCode => Object.hash(runtimeType,type,code,message,messageAr,severity,details,detailsAr,const DeepCollectionEquality().hash(originalError),stackTrace,const DeepCollectionEquality().hash(metadata),timestamp,correlationId,isRetryable,retryCount,retryAfter);

@override
String toString() {
  return 'AppError(type: $type, code: $code, message: $message, messageAr: $messageAr, severity: $severity, details: $details, detailsAr: $detailsAr, originalError: $originalError, stackTrace: $stackTrace, metadata: $metadata, timestamp: $timestamp, correlationId: $correlationId, isRetryable: $isRetryable, retryCount: $retryCount, retryAfter: $retryAfter)';
}


}

/// @nodoc
abstract mixin class $AppErrorCopyWith<$Res>  {
  factory $AppErrorCopyWith(AppError value, $Res Function(AppError) _then) = _$AppErrorCopyWithImpl;
@useResult
$Res call({
 AppErrorType type, String code, String message, String messageAr, AppErrorSeverity severity, String? details, String? detailsAr, Object? originalError, StackTrace? stackTrace, Map<String, dynamic>? metadata, DateTime? timestamp, String? correlationId, bool isRetryable, int retryCount, Duration retryAfter
});




}
/// @nodoc
class _$AppErrorCopyWithImpl<$Res>
    implements $AppErrorCopyWith<$Res> {
  _$AppErrorCopyWithImpl(this._self, this._then);

  final AppError _self;
  final $Res Function(AppError) _then;

/// Create a copy of AppError
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? code = null,Object? message = null,Object? messageAr = null,Object? severity = null,Object? details = freezed,Object? detailsAr = freezed,Object? originalError = freezed,Object? stackTrace = freezed,Object? metadata = freezed,Object? timestamp = freezed,Object? correlationId = freezed,Object? isRetryable = null,Object? retryCount = null,Object? retryAfter = null,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as AppErrorType,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,messageAr: null == messageAr ? _self.messageAr : messageAr // ignore: cast_nullable_to_non_nullable
as String,severity: null == severity ? _self.severity : severity // ignore: cast_nullable_to_non_nullable
as AppErrorSeverity,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as String?,detailsAr: freezed == detailsAr ? _self.detailsAr : detailsAr // ignore: cast_nullable_to_non_nullable
as String?,originalError: freezed == originalError ? _self.originalError : originalError ,stackTrace: freezed == stackTrace ? _self.stackTrace : stackTrace // ignore: cast_nullable_to_non_nullable
as StackTrace?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,timestamp: freezed == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,correlationId: freezed == correlationId ? _self.correlationId : correlationId // ignore: cast_nullable_to_non_nullable
as String?,isRetryable: null == isRetryable ? _self.isRetryable : isRetryable // ignore: cast_nullable_to_non_nullable
as bool,retryCount: null == retryCount ? _self.retryCount : retryCount // ignore: cast_nullable_to_non_nullable
as int,retryAfter: null == retryAfter ? _self.retryAfter : retryAfter // ignore: cast_nullable_to_non_nullable
as Duration,
  ));
}

}


/// Adds pattern-matching-related methods to [AppError].
extension AppErrorPatterns on AppError {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AppError value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AppError() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AppError value)  $default,){
final _that = this;
switch (_that) {
case _AppError():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AppError value)?  $default,){
final _that = this;
switch (_that) {
case _AppError() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( AppErrorType type,  String code,  String message,  String messageAr,  AppErrorSeverity severity,  String? details,  String? detailsAr,  Object? originalError,  StackTrace? stackTrace,  Map<String, dynamic>? metadata,  DateTime? timestamp,  String? correlationId,  bool isRetryable,  int retryCount,  Duration retryAfter)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AppError() when $default != null:
return $default(_that.type,_that.code,_that.message,_that.messageAr,_that.severity,_that.details,_that.detailsAr,_that.originalError,_that.stackTrace,_that.metadata,_that.timestamp,_that.correlationId,_that.isRetryable,_that.retryCount,_that.retryAfter);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( AppErrorType type,  String code,  String message,  String messageAr,  AppErrorSeverity severity,  String? details,  String? detailsAr,  Object? originalError,  StackTrace? stackTrace,  Map<String, dynamic>? metadata,  DateTime? timestamp,  String? correlationId,  bool isRetryable,  int retryCount,  Duration retryAfter)  $default,) {final _that = this;
switch (_that) {
case _AppError():
return $default(_that.type,_that.code,_that.message,_that.messageAr,_that.severity,_that.details,_that.detailsAr,_that.originalError,_that.stackTrace,_that.metadata,_that.timestamp,_that.correlationId,_that.isRetryable,_that.retryCount,_that.retryAfter);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( AppErrorType type,  String code,  String message,  String messageAr,  AppErrorSeverity severity,  String? details,  String? detailsAr,  Object? originalError,  StackTrace? stackTrace,  Map<String, dynamic>? metadata,  DateTime? timestamp,  String? correlationId,  bool isRetryable,  int retryCount,  Duration retryAfter)?  $default,) {final _that = this;
switch (_that) {
case _AppError() when $default != null:
return $default(_that.type,_that.code,_that.message,_that.messageAr,_that.severity,_that.details,_that.detailsAr,_that.originalError,_that.stackTrace,_that.metadata,_that.timestamp,_that.correlationId,_that.isRetryable,_that.retryCount,_that.retryAfter);case _:
  return null;

}
}

}

/// @nodoc


class _AppError extends AppError {
  const _AppError({required this.type, required this.code, required this.message, required this.messageAr, this.severity = AppErrorSeverity.medium, this.details, this.detailsAr, this.originalError, this.stackTrace, final  Map<String, dynamic>? metadata, this.timestamp, this.correlationId, this.isRetryable = false, this.retryCount = 0, this.retryAfter = Duration.zero}): _metadata = metadata,super._();
  

@override final  AppErrorType type;
@override final  String code;
@override final  String message;
@override final  String messageAr;
// Arabic message
@override@JsonKey() final  AppErrorSeverity severity;
@override final  String? details;
@override final  String? detailsAr;
// Arabic details
@override final  Object? originalError;
@override final  StackTrace? stackTrace;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  DateTime? timestamp;
@override final  String? correlationId;
@override@JsonKey() final  bool isRetryable;
@override@JsonKey() final  int retryCount;
@override@JsonKey() final  Duration retryAfter;

/// Create a copy of AppError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppErrorCopyWith<_AppError> get copyWith => __$AppErrorCopyWithImpl<_AppError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AppError&&(identical(other.type, type) || other.type == type)&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message)&&(identical(other.messageAr, messageAr) || other.messageAr == messageAr)&&(identical(other.severity, severity) || other.severity == severity)&&(identical(other.details, details) || other.details == details)&&(identical(other.detailsAr, detailsAr) || other.detailsAr == detailsAr)&&const DeepCollectionEquality().equals(other.originalError, originalError)&&(identical(other.stackTrace, stackTrace) || other.stackTrace == stackTrace)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.correlationId, correlationId) || other.correlationId == correlationId)&&(identical(other.isRetryable, isRetryable) || other.isRetryable == isRetryable)&&(identical(other.retryCount, retryCount) || other.retryCount == retryCount)&&(identical(other.retryAfter, retryAfter) || other.retryAfter == retryAfter));
}


@override
int get hashCode => Object.hash(runtimeType,type,code,message,messageAr,severity,details,detailsAr,const DeepCollectionEquality().hash(originalError),stackTrace,const DeepCollectionEquality().hash(_metadata),timestamp,correlationId,isRetryable,retryCount,retryAfter);

@override
String toString() {
  return 'AppError(type: $type, code: $code, message: $message, messageAr: $messageAr, severity: $severity, details: $details, detailsAr: $detailsAr, originalError: $originalError, stackTrace: $stackTrace, metadata: $metadata, timestamp: $timestamp, correlationId: $correlationId, isRetryable: $isRetryable, retryCount: $retryCount, retryAfter: $retryAfter)';
}


}

/// @nodoc
abstract mixin class _$AppErrorCopyWith<$Res> implements $AppErrorCopyWith<$Res> {
  factory _$AppErrorCopyWith(_AppError value, $Res Function(_AppError) _then) = __$AppErrorCopyWithImpl;
@override @useResult
$Res call({
 AppErrorType type, String code, String message, String messageAr, AppErrorSeverity severity, String? details, String? detailsAr, Object? originalError, StackTrace? stackTrace, Map<String, dynamic>? metadata, DateTime? timestamp, String? correlationId, bool isRetryable, int retryCount, Duration retryAfter
});




}
/// @nodoc
class __$AppErrorCopyWithImpl<$Res>
    implements _$AppErrorCopyWith<$Res> {
  __$AppErrorCopyWithImpl(this._self, this._then);

  final _AppError _self;
  final $Res Function(_AppError) _then;

/// Create a copy of AppError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? code = null,Object? message = null,Object? messageAr = null,Object? severity = null,Object? details = freezed,Object? detailsAr = freezed,Object? originalError = freezed,Object? stackTrace = freezed,Object? metadata = freezed,Object? timestamp = freezed,Object? correlationId = freezed,Object? isRetryable = null,Object? retryCount = null,Object? retryAfter = null,}) {
  return _then(_AppError(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as AppErrorType,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,messageAr: null == messageAr ? _self.messageAr : messageAr // ignore: cast_nullable_to_non_nullable
as String,severity: null == severity ? _self.severity : severity // ignore: cast_nullable_to_non_nullable
as AppErrorSeverity,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as String?,detailsAr: freezed == detailsAr ? _self.detailsAr : detailsAr // ignore: cast_nullable_to_non_nullable
as String?,originalError: freezed == originalError ? _self.originalError : originalError ,stackTrace: freezed == stackTrace ? _self.stackTrace : stackTrace // ignore: cast_nullable_to_non_nullable
as StackTrace?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,timestamp: freezed == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,correlationId: freezed == correlationId ? _self.correlationId : correlationId // ignore: cast_nullable_to_non_nullable
as String?,isRetryable: null == isRetryable ? _self.isRetryable : isRetryable // ignore: cast_nullable_to_non_nullable
as bool,retryCount: null == retryCount ? _self.retryCount : retryCount // ignore: cast_nullable_to_non_nullable
as int,retryAfter: null == retryAfter ? _self.retryAfter : retryAfter // ignore: cast_nullable_to_non_nullable
as Duration,
  ));
}


}

/// @nodoc
mixin _$AppResult<T> {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppResult<T>);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AppResult<$T>()';
}


}

/// @nodoc
class $AppResultCopyWith<T,$Res>  {
$AppResultCopyWith(AppResult<T> _, $Res Function(AppResult<T>) __);
}


/// Adds pattern-matching-related methods to [AppResult].
extension AppResultPatterns<T> on AppResult<T> {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( AppSuccess<T> value)?  success,TResult Function( AppFailure<T> value)?  failure,TResult Function( AppLoading<T> value)?  loading,TResult Function( AppCancelled<T> value)?  cancelled,required TResult orElse(),}){
final _that = this;
switch (_that) {
case AppSuccess() when success != null:
return success(_that);case AppFailure() when failure != null:
return failure(_that);case AppLoading() when loading != null:
return loading(_that);case AppCancelled() when cancelled != null:
return cancelled(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( AppSuccess<T> value)  success,required TResult Function( AppFailure<T> value)  failure,required TResult Function( AppLoading<T> value)  loading,required TResult Function( AppCancelled<T> value)  cancelled,}){
final _that = this;
switch (_that) {
case AppSuccess():
return success(_that);case AppFailure():
return failure(_that);case AppLoading():
return loading(_that);case AppCancelled():
return cancelled(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( AppSuccess<T> value)?  success,TResult? Function( AppFailure<T> value)?  failure,TResult? Function( AppLoading<T> value)?  loading,TResult? Function( AppCancelled<T> value)?  cancelled,}){
final _that = this;
switch (_that) {
case AppSuccess() when success != null:
return success(_that);case AppFailure() when failure != null:
return failure(_that);case AppLoading() when loading != null:
return loading(_that);case AppCancelled() when cancelled != null:
return cancelled(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( T data)?  success,TResult Function( AppError error)?  failure,TResult Function()?  loading,TResult Function()?  cancelled,required TResult orElse(),}) {final _that = this;
switch (_that) {
case AppSuccess() when success != null:
return success(_that.data);case AppFailure() when failure != null:
return failure(_that.error);case AppLoading() when loading != null:
return loading();case AppCancelled() when cancelled != null:
return cancelled();case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( T data)  success,required TResult Function( AppError error)  failure,required TResult Function()  loading,required TResult Function()  cancelled,}) {final _that = this;
switch (_that) {
case AppSuccess():
return success(_that.data);case AppFailure():
return failure(_that.error);case AppLoading():
return loading();case AppCancelled():
return cancelled();case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( T data)?  success,TResult? Function( AppError error)?  failure,TResult? Function()?  loading,TResult? Function()?  cancelled,}) {final _that = this;
switch (_that) {
case AppSuccess() when success != null:
return success(_that.data);case AppFailure() when failure != null:
return failure(_that.error);case AppLoading() when loading != null:
return loading();case AppCancelled() when cancelled != null:
return cancelled();case _:
  return null;

}
}

}

/// @nodoc


class AppSuccess<T> extends AppResult<T> {
  const AppSuccess(this.data): super._();
  

 final  T data;

/// Create a copy of AppResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppSuccessCopyWith<T, AppSuccess<T>> get copyWith => _$AppSuccessCopyWithImpl<T, AppSuccess<T>>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppSuccess<T>&&const DeepCollectionEquality().equals(other.data, data));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(data));

@override
String toString() {
  return 'AppResult<$T>.success(data: $data)';
}


}

/// @nodoc
abstract mixin class $AppSuccessCopyWith<T,$Res> implements $AppResultCopyWith<T, $Res> {
  factory $AppSuccessCopyWith(AppSuccess<T> value, $Res Function(AppSuccess<T>) _then) = _$AppSuccessCopyWithImpl;
@useResult
$Res call({
 T data
});




}
/// @nodoc
class _$AppSuccessCopyWithImpl<T,$Res>
    implements $AppSuccessCopyWith<T, $Res> {
  _$AppSuccessCopyWithImpl(this._self, this._then);

  final AppSuccess<T> _self;
  final $Res Function(AppSuccess<T>) _then;

/// Create a copy of AppResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? data = freezed,}) {
  return _then(AppSuccess<T>(
freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as T,
  ));
}


}

/// @nodoc


class AppFailure<T> extends AppResult<T> {
  const AppFailure(this.error): super._();
  

 final  AppError error;

/// Create a copy of AppResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppFailureCopyWith<T, AppFailure<T>> get copyWith => _$AppFailureCopyWithImpl<T, AppFailure<T>>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppFailure<T>&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,error);

@override
String toString() {
  return 'AppResult<$T>.failure(error: $error)';
}


}

/// @nodoc
abstract mixin class $AppFailureCopyWith<T,$Res> implements $AppResultCopyWith<T, $Res> {
  factory $AppFailureCopyWith(AppFailure<T> value, $Res Function(AppFailure<T>) _then) = _$AppFailureCopyWithImpl;
@useResult
$Res call({
 AppError error
});


$AppErrorCopyWith<$Res> get error;

}
/// @nodoc
class _$AppFailureCopyWithImpl<T,$Res>
    implements $AppFailureCopyWith<T, $Res> {
  _$AppFailureCopyWithImpl(this._self, this._then);

  final AppFailure<T> _self;
  final $Res Function(AppFailure<T>) _then;

/// Create a copy of AppResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? error = null,}) {
  return _then(AppFailure<T>(
null == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as AppError,
  ));
}

/// Create a copy of AppResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AppErrorCopyWith<$Res> get error {
  
  return $AppErrorCopyWith<$Res>(_self.error, (value) {
    return _then(_self.copyWith(error: value));
  });
}
}

/// @nodoc


class AppLoading<T> extends AppResult<T> {
  const AppLoading(): super._();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppLoading<T>);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AppResult<$T>.loading()';
}


}




/// @nodoc


class AppCancelled<T> extends AppResult<T> {
  const AppCancelled(): super._();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppCancelled<T>);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AppResult<$T>.cancelled()';
}


}




// dart format on

// ============================================================================
// CarNow Unified Authentication System - Retry Service
// ============================================================================
// File: retry_service.dart
// Description: Retry service with exponential backoff for transient errors
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'dart:async';
import 'dart:math';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'app_error.dart';
import 'app_error_factory.dart';

part 'retry_service.g.dart';

/// Configuration for retry behavior
class RetryConfig {
  final int maxRetries;
  final Duration initialDelay;
  final Duration maxDelay;
  final double backoffMultiplier;
  final double jitterFactor;
  final List<AppErrorType> retryableErrorTypes;

  const RetryConfig({
    this.maxRetries = 3,
    this.initialDelay = const Duration(milliseconds: 500),
    this.maxDelay = const Duration(seconds: 30),
    this.backoffMultiplier = 2.0,
    this.jitterFactor = 0.1,
    this.retryableErrorTypes = const [
      AppErrorType.networkError,
      AppErrorType.connectionTimeout,
      AppErrorType.noInternetConnection,
      AppErrorType.serverUnavailable,
      AppErrorType.badGateway,
      AppErrorType.gatewayTimeout,
      AppErrorType.internalServerError,
      AppErrorType.serviceUnavailable,
      AppErrorType.tokenExpired,
    ],
  });

  /// Default configuration for authentication operations
  static const auth = RetryConfig(
    maxRetries: 2,
    initialDelay: Duration(milliseconds: 1000),
    maxDelay: Duration(seconds: 10),
    backoffMultiplier: 2.0,
    jitterFactor: 0.2,
  );

  /// Default configuration for network operations
  static const network = RetryConfig(
    maxRetries: 3,
    initialDelay: Duration(milliseconds: 500),
    maxDelay: Duration(seconds: 15),
    backoffMultiplier: 1.5,
    jitterFactor: 0.1,
  );

  /// Default configuration for critical operations
  static const critical = RetryConfig(
    maxRetries: 5,
    initialDelay: Duration(milliseconds: 200),
    maxDelay: Duration(seconds: 60),
    backoffMultiplier: 2.5,
    jitterFactor: 0.15,
  );
}

/// Retry service with exponential backoff and jitter
class RetryService {
  final Random _random = Random();

  /// Execute operation with retry logic
  Future<AppResult<T>> executeWithRetry<T>({
    required Future<T> Function() operation,
    required String operationName,
    RetryConfig config = const RetryConfig(),
    Map<String, dynamic>? metadata,
    void Function(int attempt, AppError error)? onRetry,
  }) async {
    int attempt = 0;
    AppError? lastError;

    while (attempt <= config.maxRetries) {
      try {
        final result = await operation();
        return AppResult.success(result);
      } catch (e, stackTrace) {
        attempt++;
        
        // Convert exception to AppError
        final error = e is AppException 
            ? e.error 
            : AppErrorFactory.fromException(
                e is Exception ? e : Exception(e.toString()),
                stackTrace: stackTrace,
              );

        // Add metadata and attempt info
        final enrichedError = error.copyWith(
          retryCount: attempt - 1,
          metadata: {
            ...?error.metadata,
            ...?metadata,
            'operation_name': operationName,
            'attempt': attempt,
            'max_retries': config.maxRetries,
          },
        );

        lastError = enrichedError;

        // Check if error is retryable
        if (!_isRetryableError(enrichedError, config) || attempt > config.maxRetries) {
          return AppResult.failure(enrichedError);
        }

        // Call retry callback if provided
        onRetry?.call(attempt, enrichedError);

        // Calculate delay with exponential backoff and jitter
        final delay = _calculateDelay(attempt, config);
        
        // Wait before retry
        await Future.delayed(delay);
      }
    }

    // This should never be reached, but just in case
    return AppResult.failure(
      lastError ?? AppErrorFactory.fromException(
        Exception('Maximum retries exceeded for $operationName'),
      ),
    );
  }

  /// Execute operation with timeout and retry
  Future<AppResult<T>> executeWithTimeoutAndRetry<T>({
    required Future<T> Function() operation,
    required String operationName,
    required Duration timeout,
    RetryConfig config = const RetryConfig(),
    Map<String, dynamic>? metadata,
    void Function(int attempt, AppError error)? onRetry,
  }) async {
    return executeWithRetry<T>(
      operation: () => operation().timeout(
        timeout,
        onTimeout: () => throw TimeoutException(
          'Operation $operationName timed out after ${timeout.inSeconds}s',
          timeout,
        ),
      ),
      operationName: operationName,
      config: config,
      metadata: {
        ...?metadata,
        'timeout_seconds': timeout.inSeconds,
      },
      onRetry: onRetry,
    );
  }

  /// Check if error should be retried
  bool _isRetryableError(AppError error, RetryConfig config) {
    // Check if error type is in retryable list
    if (!config.retryableErrorTypes.contains(error.type)) {
      return false;
    }

    // Check if error explicitly says it's retryable
    if (!error.isRetryable) {
      return false;
    }

    // Don't retry authentication errors (except token expired)
    if (error.isAuthError && error.type != AppErrorType.tokenExpired) {
      return false;
    }

    return true;
  }

  /// Calculate delay with exponential backoff and jitter
  Duration _calculateDelay(int attempt, RetryConfig config) {
    // Calculate base delay with exponential backoff
    final baseDelay = config.initialDelay.inMilliseconds * 
        pow(config.backoffMultiplier, attempt - 1);

    // Add jitter to prevent thundering herd
    final jitter = baseDelay * config.jitterFactor * (_random.nextDouble() - 0.5);
    final delayWithJitter = (baseDelay + jitter).round();

    // Ensure delay doesn't exceed maximum
    final finalDelay = min(delayWithJitter, config.maxDelay.inMilliseconds);

    return Duration(milliseconds: max(0, finalDelay));
  }

  /// Create a retry wrapper for a specific operation
  Future<T> Function() createRetryWrapper<T>({
    required Future<T> Function() operation,
    required String operationName,
    RetryConfig config = const RetryConfig(),
    Map<String, dynamic>? metadata,
  }) {
    return () async {
      final result = await executeWithRetry<T>(
        operation: operation,
        operationName: operationName,
        config: config,
        metadata: metadata,
      );

      return result.when(
        success: (data) => data,
        failure: (error) => throw AppException(error),
        loading: () => throw AppException(
          AppErrorFactory.fromException(Exception('Unexpected loading state')),
        ),
        cancelled: () => throw AppException(
          AppErrorFactory.fromException(Exception('Operation was cancelled')),
        ),
      );
    };
  }
}

/// Riverpod provider for RetryService
@riverpod
RetryService retryService(RetryServiceRef ref) {
  return RetryService();
}

/// Extension methods for easier retry usage
extension RetryExtension<T> on Future<T> {
  /// Add retry capability to any Future
  Future<AppResult<T>> withRetry({
    required String operationName,
    RetryConfig config = const RetryConfig(),
    Map<String, dynamic>? metadata,
    void Function(int attempt, AppError error)? onRetry,
  }) {
    final retryService = RetryService();
    return retryService.executeWithRetry<T>(
      operation: () => this,
      operationName: operationName,
      config: config,
      metadata: metadata,
      onRetry: onRetry,
    );
  }

  /// Add timeout and retry capability to any Future
  Future<AppResult<T>> withTimeoutAndRetry({
    required String operationName,
    required Duration timeout,
    RetryConfig config = const RetryConfig(),
    Map<String, dynamic>? metadata,
    void Function(int attempt, AppError error)? onRetry,
  }) {
    final retryService = RetryService();
    return retryService.executeWithTimeoutAndRetry<T>(
      operation: () => this,
      operationName: operationName,
      timeout: timeout,
      config: config,
      metadata: metadata,
      onRetry: onRetry,
    );
  }
}

/// Timeout exception for operations that exceed time limits
class TimeoutException implements Exception {
  final String message;
  final Duration timeout;

  const TimeoutException(this.message, this.timeout);

  @override
  String toString() => 'TimeoutException: $message';
}

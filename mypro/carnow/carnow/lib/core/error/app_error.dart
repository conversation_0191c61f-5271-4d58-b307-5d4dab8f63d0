// ============================================================================
// CarNow Unified Authentication System - Comprehensive Error Handling
// ============================================================================
// File: app_error.dart
// Description: Centralized error handling system with error codes and localization
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_error.freezed.dart';

/// Comprehensive error types for the CarNow application
enum AppErrorType {
  // Network and connectivity errors
  networkError,
  connectionTimeout,
  noInternetConnection,
  serverUnavailable,
  
  // Authentication errors
  invalidCredentials,
  accountNotFound,
  accountDisabled,
  accountLocked,
  emailNotVerified,
  passwordExpired,
  tokenExpired,
  tokenInvalid,
  refreshTokenExpired,
  sessionExpired,
  
  // Authorization errors
  unauthorized,
  forbidden,
  insufficientPermissions,
  
  // Validation errors
  invalidEmail,
  invalidPassword,
  passwordTooWeak,
  emailAlreadyExists,
  invalidPhoneNumber,
  invalidName,
  
  // Rate limiting errors
  rateLimitExceeded,
  tooManyRequests,
  temporarilyBlocked,
  
  // Server errors
  internalServerError,
  serviceUnavailable,
  badGateway,
  gatewayTimeout,
  
  // Data errors
  dataNotFound,
  dataCorrupted,
  invalidData,
  duplicateData,
  
  // Google OAuth errors
  googleAuthCancelled,
  googleAuthFailed,
  googleAuthNetworkError,
  googleAuthInvalidToken,
  
  // Storage errors
  storageError,
  storagePermissionDenied,
  storageFull,
  
  // General errors
  unknown,
  cancelled,
  timeout,
  parseError,
  configurationError,
}

/// Error severity levels for logging and handling
enum AppErrorSeverity {
  low,      // Minor issues, user can continue
  medium,   // Noticeable issues, some functionality affected
  high,     // Major issues, core functionality affected
  critical, // Critical issues, app cannot function properly
}

/// Comprehensive application error with detailed information
@freezed
abstract class AppError with _$AppError {
  const factory AppError({
    required AppErrorType type,
    required String code,
    required String message,
    required String messageAr, // Arabic message
    @Default(AppErrorSeverity.medium) AppErrorSeverity severity,
    String? details,
    String? detailsAr, // Arabic details
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
    String? correlationId,
    @Default(false) bool isRetryable,
    @Default(0) int retryCount,
    @Default(Duration.zero) Duration retryAfter,
  }) = _AppError;

  const AppError._();

  /// Check if this error is a network-related error
  bool get isNetworkError => [
    AppErrorType.networkError,
    AppErrorType.connectionTimeout,
    AppErrorType.noInternetConnection,
    AppErrorType.serverUnavailable,
    AppErrorType.badGateway,
    AppErrorType.gatewayTimeout,
  ].contains(type);

  /// Check if this error is authentication-related
  bool get isAuthError => [
    AppErrorType.invalidCredentials,
    AppErrorType.accountNotFound,
    AppErrorType.accountDisabled,
    AppErrorType.accountLocked,
    AppErrorType.emailNotVerified,
    AppErrorType.passwordExpired,
    AppErrorType.tokenExpired,
    AppErrorType.tokenInvalid,
    AppErrorType.refreshTokenExpired,
    AppErrorType.sessionExpired,
    AppErrorType.unauthorized,
    AppErrorType.forbidden,
  ].contains(type);

  /// Check if this error should trigger automatic retry
  bool get shouldRetry => isRetryable && retryCount < 3 && isNetworkError;

  /// Get user-friendly message based on locale
  String getLocalizedMessage(String locale) {
    return locale.startsWith('ar') ? messageAr : message;
  }

  /// Get user-friendly details based on locale
  String? getLocalizedDetails(String locale) {
    if (locale.startsWith('ar')) {
      return detailsAr ?? details;
    }
    return details ?? detailsAr;
  }

  /// Create a copy with incremented retry count
  AppError withRetry() {
    return copyWith(
      retryCount: retryCount + 1,
      timestamp: DateTime.now(),
    );
  }

  /// Create a copy with correlation ID for tracking
  AppError withCorrelationId(String id) {
    return copyWith(correlationId: id);
  }

  /// Convert to JSON for logging
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'code': code,
      'message': message,
      'messageAr': messageAr,
      'severity': severity.name,
      'details': details,
      'detailsAr': detailsAr,
      'isRetryable': isRetryable,
      'retryCount': retryCount,
      'retryAfter': retryAfter.inMilliseconds,
      'timestamp': timestamp?.toIso8601String(),
      'correlationId': correlationId,
      'metadata': metadata,
    };
  }
}

/// Result wrapper for operations that can fail
@freezed
class AppResult<T> with _$AppResult<T> {
  const factory AppResult.success(T data) = AppSuccess<T>;
  const factory AppResult.failure(AppError error) = AppFailure<T>;
  const factory AppResult.loading() = AppLoading<T>;
  const factory AppResult.cancelled() = AppCancelled<T>;

  const AppResult._();

  /// Check if the result is successful
  bool get isSuccess => this is AppSuccess<T>;

  /// Check if the result is a failure
  bool get isFailure => this is AppFailure<T>;

  /// Check if the result is loading
  bool get isLoading => this is AppLoading<T>;

  /// Check if the result was cancelled
  bool get isCancelled => this is AppCancelled<T>;

  /// Get the data if successful, null otherwise
  T? get data => maybeWhen(
    success: (data) => data,
    orElse: () => null,
  );

  /// Get the error if failed, null otherwise
  AppError? get error => maybeWhen(
    failure: (error) => error,
    orElse: () => null,
  );

  /// Transform the success data
  AppResult<R> map<R>(R Function(T) transform) {
    return when(
      success: (data) => AppResult.success(transform(data)),
      failure: (error) => AppResult.failure(error),
      loading: () => AppResult.loading(),
      cancelled: () => AppResult.cancelled(),
    );
  }

  /// Handle the result with callbacks
  R fold<R>({
    required R Function(T data) onSuccess,
    required R Function(AppError error) onFailure,
    required R Function() onLoading,
    required R Function() onCancelled,
  }) {
    return when(
      success: onSuccess,
      failure: onFailure,
      loading: onLoading,
      cancelled: onCancelled,
    );
  }
}

/// Exception wrapper for AppError
class AppException implements Exception {
  final AppError error;

  const AppException(this.error);

  @override
  String toString() => 'AppException: ${error.message}';
}

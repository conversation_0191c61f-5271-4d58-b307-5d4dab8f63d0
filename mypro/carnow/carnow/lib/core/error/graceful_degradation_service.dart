// ============================================================================
// CarNow Unified Authentication System - Graceful Degradation Service
// ============================================================================
// File: graceful_degradation_service.dart
// Description: Graceful degradation for network failures with offline support
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'app_error.dart';
import 'app_error_factory.dart';
import 'retry_service.dart';

part 'graceful_degradation_service.g.dart';

/// Network connectivity status
enum NetworkStatus {
  online,
  offline,
  limited, // Poor connection
}

/// Offline cache entry
class CacheEntry<T> {
  final T data;
  final DateTime timestamp;
  final Duration ttl;
  final String key;

  const CacheEntry({
    required this.data,
    required this.timestamp,
    required this.ttl,
    required this.key,
  });

  bool get isExpired => DateTime.now().difference(timestamp) > ttl;

  Map<String, dynamic> toJson() => {
    'data': data,
    'timestamp': timestamp.toIso8601String(),
    'ttl_seconds': ttl.inSeconds,
    'key': key,
  };

  factory CacheEntry.fromJson(Map<String, dynamic> json, T data) {
    return CacheEntry<T>(
      data: data,
      timestamp: DateTime.parse(json['timestamp']),
      ttl: Duration(seconds: json['ttl_seconds']),
      key: json['key'],
    );
  }
}

/// Configuration for graceful degradation
class DegradationConfig {
  final Duration cacheTimeout;
  final Duration networkTimeout;
  final int maxCacheSize;
  final bool enableOfflineMode;
  final List<String> criticalOperations;

  const DegradationConfig({
    this.cacheTimeout = const Duration(hours: 24),
    this.networkTimeout = const Duration(seconds: 10),
    this.maxCacheSize = 100,
    this.enableOfflineMode = true,
    this.criticalOperations = const ['auth', 'user_profile'],
  });

  static const auth = DegradationConfig(
    cacheTimeout: Duration(minutes: 30),
    networkTimeout: Duration(seconds: 5),
    maxCacheSize: 50,
    enableOfflineMode: true,
    criticalOperations: ['login', 'token_refresh', 'user_data'],
  );
}

/// Graceful degradation service with offline support
class GracefulDegradationService {
  final Connectivity _connectivity = Connectivity();
  final RetryService _retryService;
  late SharedPreferences _prefs;
  
  NetworkStatus _currentStatus = NetworkStatus.online;
  final StreamController<NetworkStatus> _statusController = 
      StreamController<NetworkStatus>.broadcast();

  // Cache for offline data
  final Map<String, CacheEntry> _memoryCache = {};
  
  GracefulDegradationService(this._retryService);

  /// Initialize the service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    
    // Monitor connectivity changes
    _connectivity.onConnectivityChanged.listen((List<ConnectivityResult> results) {
      _handleConnectivityChange(results.isNotEmpty ? results.first : ConnectivityResult.none);
    });
    
    // Check initial connectivity
    final results = await _connectivity.checkConnectivity();
    _handleConnectivityChange(results.isNotEmpty ? results.first : ConnectivityResult.none);
    
    // Load cached data from persistent storage
    await _loadCacheFromStorage();
  }

  /// Stream of network status changes
  Stream<NetworkStatus> get networkStatusStream => _statusController.stream;

  /// Current network status
  NetworkStatus get currentStatus => _currentStatus;

  /// Check if currently online
  bool get isOnline => _currentStatus == NetworkStatus.online;

  /// Check if currently offline
  bool get isOffline => _currentStatus == NetworkStatus.offline;

  /// Execute operation with graceful degradation
  Future<AppResult<T>> executeWithGracefulDegradation<T>({
    required Future<T> Function() primaryOperation,
    required String operationName,
    Future<T> Function()? fallbackOperation,
    T? defaultValue,
    DegradationConfig config = const DegradationConfig(),
    bool enableCaching = true,
    Duration? customTimeout,
  }) async {
    final cacheKey = 'degradation_$operationName';
    
    try {
      // Try primary operation first if online
      if (isOnline) {
        final result = await _retryService.executeWithTimeoutAndRetry<T>(
          operation: primaryOperation,
          operationName: operationName,
          timeout: customTimeout ?? config.networkTimeout,
          config: RetryConfig.network,
          metadata: {
            'degradation_enabled': true,
            'cache_enabled': enableCaching,
          },
        );

        return result.when(
          success: (data) {
            // Cache successful result
            if (enableCaching) {
              _cacheData(cacheKey, data, config.cacheTimeout);
            }
            return AppResult.success(data);
          },
          failure: (error) => _handlePrimaryFailure<T>(
            error: error,
            cacheKey: cacheKey,
            fallbackOperation: fallbackOperation,
            defaultValue: defaultValue,
            operationName: operationName,
          ),
          loading: () => AppResult.loading(),
          cancelled: () => AppResult.cancelled(),
        );
      } else {
        // Offline mode - try cache or fallback
        return _handleOfflineMode<T>(
          cacheKey: cacheKey,
          fallbackOperation: fallbackOperation,
          defaultValue: defaultValue,
          operationName: operationName,
        );
      }
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );

      return _handlePrimaryFailure<T>(
        error: error,
        cacheKey: cacheKey,
        fallbackOperation: fallbackOperation,
        defaultValue: defaultValue,
        operationName: operationName,
      );
    }
  }

  /// Handle primary operation failure
  Future<AppResult<T>> _handlePrimaryFailure<T>({
    required AppError error,
    required String cacheKey,
    Future<T> Function()? fallbackOperation,
    T? defaultValue,
    required String operationName,
  }) async {
    // Try cached data first
    final cachedData = _getCachedData<T>(cacheKey);
    if (cachedData != null) {
      return AppResult.success(cachedData);
    }

    // Try fallback operation
    if (fallbackOperation != null) {
      try {
        final fallbackResult = await fallbackOperation();
        return AppResult.success(fallbackResult);
      } catch (fallbackError) {
        // Fallback also failed, continue to default value
      }
    }

    // Use default value if available
    if (defaultValue != null) {
      return AppResult.success(defaultValue);
    }

    // All degradation options exhausted
    return AppResult.failure(
      error.copyWith(
        details: 'Primary operation failed and no fallback available',
        detailsAr: 'فشلت العملية الأساسية ولا يوجد بديل متاح',
        metadata: {
          ...?error.metadata,
          'degradation_attempted': true,
          'cache_checked': true,
          'fallback_available': fallbackOperation != null,
          'default_available': defaultValue != null,
        },
      ),
    );
  }

  /// Handle offline mode
  Future<AppResult<T>> _handleOfflineMode<T>({
    required String cacheKey,
    Future<T> Function()? fallbackOperation,
    T? defaultValue,
    required String operationName,
  }) async {
    // Try cached data first
    final cachedData = _getCachedData<T>(cacheKey);
    if (cachedData != null) {
      return AppResult.success(cachedData);
    }

    // Try fallback operation
    if (fallbackOperation != null) {
      try {
        final fallbackResult = await fallbackOperation();
        return AppResult.success(fallbackResult);
      } catch (fallbackError) {
        // Fallback failed in offline mode
      }
    }

    // Use default value if available
    if (defaultValue != null) {
      return AppResult.success(defaultValue);
    }

    // No offline data available
    return AppResult.failure(
      AppErrorFactory.noInternetConnection().copyWith(
        details: 'No cached data available for offline mode',
        detailsAr: 'لا توجد بيانات مخزنة متاحة للوضع غير المتصل',
        metadata: {
          'operation_name': operationName,
          'offline_mode': true,
          'cache_checked': true,
          'fallback_available': fallbackOperation != null,
        },
      ),
    );
  }

  /// Cache data in memory and persistent storage
  void _cacheData<T>(String key, T data, Duration ttl) {
    try {
      final entry = CacheEntry<T>(
        data: data,
        timestamp: DateTime.now(),
        ttl: ttl,
        key: key,
      );

      // Store in memory cache
      _memoryCache[key] = entry;

      // Store in persistent cache (JSON serializable data only)
      if (data is Map || data is List || data is String || data is num || data is bool) {
        _prefs.setString('cache_$key', jsonEncode(entry.toJson()));
      }

      // Clean up old cache entries
      _cleanupCache();
    } catch (e) {
      // Cache storage failed, but don't break the main operation
      print('Failed to cache data for key $key: $e');
    }
  }

  /// Get cached data
  T? _getCachedData<T>(String key) {
    try {
      // Check memory cache first
      final memoryEntry = _memoryCache[key];
      if (memoryEntry != null && !memoryEntry.isExpired) {
        return memoryEntry.data as T?;
      }

      // Check persistent cache
      final cachedJson = _prefs.getString('cache_$key');
      if (cachedJson != null) {
        final json = jsonDecode(cachedJson) as Map<String, dynamic>;
        final timestamp = DateTime.parse(json['timestamp']);
        final ttl = Duration(seconds: json['ttl_seconds']);
        
        if (DateTime.now().difference(timestamp) <= ttl) {
          return json['data'] as T?;
        } else {
          // Remove expired cache
          _prefs.remove('cache_$key');
        }
      }
    } catch (e) {
      // Cache retrieval failed
      print('Failed to retrieve cached data for key $key: $e');
    }

    return null;
  }

  /// Handle connectivity changes
  void _handleConnectivityChange(ConnectivityResult result) {
    NetworkStatus newStatus;
    
    switch (result) {
      case ConnectivityResult.wifi:
      case ConnectivityResult.ethernet:
        newStatus = NetworkStatus.online;
        break;
      case ConnectivityResult.mobile:
        newStatus = NetworkStatus.online; // Could be limited, but assume online
        break;
      case ConnectivityResult.none:
        newStatus = NetworkStatus.offline;
        break;
      default:
        newStatus = NetworkStatus.limited;
    }

    if (newStatus != _currentStatus) {
      _currentStatus = newStatus;
      _statusController.add(newStatus);
    }
  }

  /// Load cache from persistent storage
  Future<void> _loadCacheFromStorage() async {
    try {
      final keys = _prefs.getKeys().where((key) => key.startsWith('cache_'));
      
      for (final key in keys) {
        final cachedJson = _prefs.getString(key);
        if (cachedJson != null) {
          final json = jsonDecode(cachedJson) as Map<String, dynamic>;
          final timestamp = DateTime.parse(json['timestamp']);
          final ttl = Duration(seconds: json['ttl_seconds']);
          
          // Remove expired entries
          if (DateTime.now().difference(timestamp) > ttl) {
            _prefs.remove(key);
          }
        }
      }
    } catch (e) {
      print('Failed to load cache from storage: $e');
    }
  }

  /// Clean up old cache entries
  void _cleanupCache() {
    if (_memoryCache.length > 100) { // Max cache size
      final sortedEntries = _memoryCache.entries.toList()
        ..sort((a, b) => a.value.timestamp.compareTo(b.value.timestamp));
      
      // Remove oldest 20% of entries
      final removeCount = (_memoryCache.length * 0.2).round();
      for (int i = 0; i < removeCount; i++) {
        final key = sortedEntries[i].key;
        _memoryCache.remove(key);
        _prefs.remove('cache_$key');
      }
    }
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    _memoryCache.clear();
    
    final keys = _prefs.getKeys().where((key) => key.startsWith('cache_'));
    for (final key in keys) {
      await _prefs.remove(key);
    }
  }

  /// Dispose resources
  void dispose() {
    _statusController.close();
  }
}

/// Riverpod provider for GracefulDegradationService
@riverpod
GracefulDegradationService gracefulDegradationService(
  Ref ref,
) {
  final retryService = ref.read(retryServiceProvider);
  final service = GracefulDegradationService(retryService);
  
  // Initialize the service
  service.initialize();
  
  // Dispose when provider is disposed
  ref.onDispose(() => service.dispose());
  
  return service;
}

/// Extension for easier graceful degradation usage
extension GracefulDegradationExtension<T> on Future<T> {
  /// Add graceful degradation to any Future
  Future<AppResult<T>> withGracefulDegradation({
    required String operationName,
    Future<T> Function()? fallbackOperation,
    T? defaultValue,
    DegradationConfig config = const DegradationConfig(),
    bool enableCaching = true,
    Duration? customTimeout,
  }) {
    // This would need to be injected in real usage
    final service = GracefulDegradationService(RetryService());
    
    return service.executeWithGracefulDegradation<T>(
      primaryOperation: () => this,
      operationName: operationName,
      fallbackOperation: fallbackOperation,
      defaultValue: defaultValue,
      config: config,
      enableCaching: enableCaching,
      customTimeout: customTimeout,
    );
  }
}

import 'package:flutter/foundation.dart';

@immutable
class City {
  const City({
    required this.id,
    required this.nameArabic,
    required this.nameEnglish,
    this.region,
    this.population,
    this.hasMajorMarket,
    this.coordinates,
    this.postalCode,
    required this.createdAt,
  });

  factory City.fromJson(Map<String, dynamic> json) {
    return City(
      id: json['id'] as int,
      nameArabic: json['name_arabic'] as String,
      nameEnglish: json['name_english'] as String,
      region: json['region'] as String?,
      population: json['population'] as int?,
      hasMajorMarket: json['has_major_market'] as bool?,
      coordinates: json['coordinates'] as String?,
      postalCode: json['postal_code'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }
  final int id;
  final String nameArabic;
  final String nameEnglish;
  final String? region;
  final int? population;
  final bool? hasMajorMarket;
  final String? coordinates;
  final String? postalCode;
  final DateTime createdAt;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name_arabic': nameArabic,
      'name_english': nameEnglish,
      'region': region,
      'population': population,
      'has_major_market': hasMajorMarket,
      'coordinates': coordinates,
      'postal_code': postalCode,
      'created_at': createdAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'City(id: $id, nameArabic: $nameArabic, nameEnglish: $nameEnglish)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is City &&
        other.id == id &&
        other.nameArabic == nameArabic &&
        other.nameEnglish == nameEnglish;
  }

  @override
  int get hashCode => id.hashCode ^ nameArabic.hashCode ^ nameEnglish.hashCode;
}

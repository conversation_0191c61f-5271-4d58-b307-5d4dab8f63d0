/// Generic API response wrapper for handling success, error, and loading states
class ApiResponse<T> {
  const ApiResponse._({
    this.data,
    this.error,
    this.message,
    bool isSuccess = false,
    bool isLoading = false,
  }) : _isSuccess = isSuccess,
       _isLoading = isLoading;

  /// Create a successful response with data
  factory ApiResponse.success(T data, {String? message}) {
    return ApiResponse._(data: data, message: message, isSuccess: true);
  }

  /// Create an error response
  factory ApiResponse.error(String error, {String? message}) {
    return ApiResponse._(error: error, message: message);
  }

  /// Create a loading response
  factory ApiResponse.loading({String? message}) {
    return ApiResponse._(message: message, isLoading: true);
  }
  final T? data;
  final String? error;
  final String? message;
  final bool _isSuccess;
  final bool _isLoading;

  /// Check if the response is successful
  bool get isSuccess => _isSuccess;

  /// Check if the response is in error state
  bool get isFailure => !_isSuccess && !_isLoading;

  /// Check if the response is in loading state
  bool get isLoading => _isLoading;

  @override
  String toString() {
    if (isLoading) {
      return 'ApiResponse.loading(message: $message)';
    } else if (isSuccess) {
      return 'ApiResponse.success(data: $data, message: $message)';
    } else {
      return 'ApiResponse.error(error: $error, message: $message)';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ApiResponse<T> &&
          runtimeType == other.runtimeType &&
          data == other.data &&
          error == other.error &&
          message == other.message &&
          _isSuccess == other._isSuccess &&
          _isLoading == other._isLoading;

  @override
  int get hashCode =>
      data.hashCode ^
      error.hashCode ^
      message.hashCode ^
      _isSuccess.hashCode ^
      _isLoading.hashCode;
}

import 'package:freezed_annotation/freezed_annotation.dart';

import 'enums.dart';

part 'order_status_history_model.freezed.dart';
part 'order_status_history_model.g.dart';

@freezed
abstract class OrderStatusHistoryModel with _$OrderStatusHistoryModel {
  @JsonSerializable(explicitToJson: true, fieldRename: FieldRename.snake)
  const factory OrderStatusHistoryModel({
    required OrderStatus status,
    required DateTime timestamp,
    String? note,
  }) = _OrderStatusHistoryModel;

  factory OrderStatusHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$OrderStatusHistoryModelFromJson(json);
}

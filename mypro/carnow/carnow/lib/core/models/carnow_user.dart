import 'package:freezed_annotation/freezed_annotation.dart';

part 'carnow_user.freezed.dart';
part 'carnow_user.g.dart';

@freezed
abstract class CarnowUser with _$CarnowUser {
  const factory CarnowUser({
    required String id,
    required String email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name') required String firstName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name') required String lastName,
    @J<PERSON><PERSON><PERSON>(name: 'phone_number') String? phoneNumber,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'avatar_url') String? avatarUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active') required bool isActive,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at') required DateTime createdAt,
    @J<PERSON><PERSON>ey(name: 'updated_at') required DateTime updatedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'carnow_user_id') String? carnowUserId, // Link to Carnow database user
  }) = _CarnowUser;

  factory CarnowUser.fromJson(Map<String, dynamic> json) => _$CarnowUserFromJson(json);
}

@freezed
abstract class CreateCarnowUserRequest with _$CreateCarnowUserRequest {
  const factory CreateCarnowUserRequest({
    required String email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name') required String firstName,
    @JsonKey(name: 'last_name') required String lastName,
    @JsonKey(name: 'phone_number') String? phoneNumber,
    @JsonKey(name: 'avatar_url') String? avatarUrl,
  }) = _CreateCarnowUserRequest;

  factory CreateCarnowUserRequest.fromJson(Map<String, dynamic> json) => _$CreateCarnowUserRequestFromJson(json);
}

@freezed  
abstract class UpdateCarnowUserRequest with _$UpdateCarnowUserRequest {
  const factory UpdateCarnowUserRequest({
    @JsonKey(name: 'first_name') String? firstName,
    @JsonKey(name: 'last_name') String? lastName,
    @JsonKey(name: 'phone_number') String? phoneNumber,
    @JsonKey(name: 'avatar_url') String? avatarUrl,
  }) = _UpdateCarnowUserRequest;

  factory UpdateCarnowUserRequest.fromJson(Map<String, dynamic> json) => _$UpdateCarnowUserRequestFromJson(json);
}
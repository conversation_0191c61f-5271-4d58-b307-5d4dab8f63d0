// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subscription_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SubscriptionResponse {

 String get id; String get status;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt; String? get message;@JsonKey(name: 'store_name') String? get storeName; String? get phone; String? get city; String? get address; String? get description;@JsonKey(name: 'plan_type') String? get planType; double? get price;@JsonKey(name: 'user_id') String? get userId;@JsonKey(name: 'error_code') String? get errorCode;@JsonKey(name: 'error_details') Map<String, dynamic>? get errorDetails;
/// Create a copy of SubscriptionResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubscriptionResponseCopyWith<SubscriptionResponse> get copyWith => _$SubscriptionResponseCopyWithImpl<SubscriptionResponse>(this as SubscriptionResponse, _$identity);

  /// Serializes this SubscriptionResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubscriptionResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.message, message) || other.message == message)&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.city, city) || other.city == city)&&(identical(other.address, address) || other.address == address)&&(identical(other.description, description) || other.description == description)&&(identical(other.planType, planType) || other.planType == planType)&&(identical(other.price, price) || other.price == price)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode)&&const DeepCollectionEquality().equals(other.errorDetails, errorDetails));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,status,createdAt,updatedAt,message,storeName,phone,city,address,description,planType,price,userId,errorCode,const DeepCollectionEquality().hash(errorDetails));

@override
String toString() {
  return 'SubscriptionResponse(id: $id, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, message: $message, storeName: $storeName, phone: $phone, city: $city, address: $address, description: $description, planType: $planType, price: $price, userId: $userId, errorCode: $errorCode, errorDetails: $errorDetails)';
}


}

/// @nodoc
abstract mixin class $SubscriptionResponseCopyWith<$Res>  {
  factory $SubscriptionResponseCopyWith(SubscriptionResponse value, $Res Function(SubscriptionResponse) _then) = _$SubscriptionResponseCopyWithImpl;
@useResult
$Res call({
 String id, String status,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt, String? message,@JsonKey(name: 'store_name') String? storeName, String? phone, String? city, String? address, String? description,@JsonKey(name: 'plan_type') String? planType, double? price,@JsonKey(name: 'user_id') String? userId,@JsonKey(name: 'error_code') String? errorCode,@JsonKey(name: 'error_details') Map<String, dynamic>? errorDetails
});




}
/// @nodoc
class _$SubscriptionResponseCopyWithImpl<$Res>
    implements $SubscriptionResponseCopyWith<$Res> {
  _$SubscriptionResponseCopyWithImpl(this._self, this._then);

  final SubscriptionResponse _self;
  final $Res Function(SubscriptionResponse) _then;

/// Create a copy of SubscriptionResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? status = null,Object? createdAt = null,Object? updatedAt = freezed,Object? message = freezed,Object? storeName = freezed,Object? phone = freezed,Object? city = freezed,Object? address = freezed,Object? description = freezed,Object? planType = freezed,Object? price = freezed,Object? userId = freezed,Object? errorCode = freezed,Object? errorDetails = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,storeName: freezed == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,planType: freezed == planType ? _self.planType : planType // ignore: cast_nullable_to_non_nullable
as String?,price: freezed == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,errorDetails: freezed == errorDetails ? _self.errorDetails : errorDetails // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [SubscriptionResponse].
extension SubscriptionResponsePatterns on SubscriptionResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SubscriptionResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SubscriptionResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SubscriptionResponse value)  $default,){
final _that = this;
switch (_that) {
case _SubscriptionResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SubscriptionResponse value)?  $default,){
final _that = this;
switch (_that) {
case _SubscriptionResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String status, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt,  String? message, @JsonKey(name: 'store_name')  String? storeName,  String? phone,  String? city,  String? address,  String? description, @JsonKey(name: 'plan_type')  String? planType,  double? price, @JsonKey(name: 'user_id')  String? userId, @JsonKey(name: 'error_code')  String? errorCode, @JsonKey(name: 'error_details')  Map<String, dynamic>? errorDetails)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SubscriptionResponse() when $default != null:
return $default(_that.id,_that.status,_that.createdAt,_that.updatedAt,_that.message,_that.storeName,_that.phone,_that.city,_that.address,_that.description,_that.planType,_that.price,_that.userId,_that.errorCode,_that.errorDetails);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String status, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt,  String? message, @JsonKey(name: 'store_name')  String? storeName,  String? phone,  String? city,  String? address,  String? description, @JsonKey(name: 'plan_type')  String? planType,  double? price, @JsonKey(name: 'user_id')  String? userId, @JsonKey(name: 'error_code')  String? errorCode, @JsonKey(name: 'error_details')  Map<String, dynamic>? errorDetails)  $default,) {final _that = this;
switch (_that) {
case _SubscriptionResponse():
return $default(_that.id,_that.status,_that.createdAt,_that.updatedAt,_that.message,_that.storeName,_that.phone,_that.city,_that.address,_that.description,_that.planType,_that.price,_that.userId,_that.errorCode,_that.errorDetails);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String status, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt,  String? message, @JsonKey(name: 'store_name')  String? storeName,  String? phone,  String? city,  String? address,  String? description, @JsonKey(name: 'plan_type')  String? planType,  double? price, @JsonKey(name: 'user_id')  String? userId, @JsonKey(name: 'error_code')  String? errorCode, @JsonKey(name: 'error_details')  Map<String, dynamic>? errorDetails)?  $default,) {final _that = this;
switch (_that) {
case _SubscriptionResponse() when $default != null:
return $default(_that.id,_that.status,_that.createdAt,_that.updatedAt,_that.message,_that.storeName,_that.phone,_that.city,_that.address,_that.description,_that.planType,_that.price,_that.userId,_that.errorCode,_that.errorDetails);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SubscriptionResponse implements SubscriptionResponse {
  const _SubscriptionResponse({required this.id, required this.status, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, this.message, @JsonKey(name: 'store_name') this.storeName, this.phone, this.city, this.address, this.description, @JsonKey(name: 'plan_type') this.planType, this.price, @JsonKey(name: 'user_id') this.userId, @JsonKey(name: 'error_code') this.errorCode, @JsonKey(name: 'error_details') final  Map<String, dynamic>? errorDetails}): _errorDetails = errorDetails;
  factory _SubscriptionResponse.fromJson(Map<String, dynamic> json) => _$SubscriptionResponseFromJson(json);

@override final  String id;
@override final  String status;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override final  String? message;
@override@JsonKey(name: 'store_name') final  String? storeName;
@override final  String? phone;
@override final  String? city;
@override final  String? address;
@override final  String? description;
@override@JsonKey(name: 'plan_type') final  String? planType;
@override final  double? price;
@override@JsonKey(name: 'user_id') final  String? userId;
@override@JsonKey(name: 'error_code') final  String? errorCode;
 final  Map<String, dynamic>? _errorDetails;
@override@JsonKey(name: 'error_details') Map<String, dynamic>? get errorDetails {
  final value = _errorDetails;
  if (value == null) return null;
  if (_errorDetails is EqualUnmodifiableMapView) return _errorDetails;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SubscriptionResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubscriptionResponseCopyWith<_SubscriptionResponse> get copyWith => __$SubscriptionResponseCopyWithImpl<_SubscriptionResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SubscriptionResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubscriptionResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.message, message) || other.message == message)&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.city, city) || other.city == city)&&(identical(other.address, address) || other.address == address)&&(identical(other.description, description) || other.description == description)&&(identical(other.planType, planType) || other.planType == planType)&&(identical(other.price, price) || other.price == price)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode)&&const DeepCollectionEquality().equals(other._errorDetails, _errorDetails));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,status,createdAt,updatedAt,message,storeName,phone,city,address,description,planType,price,userId,errorCode,const DeepCollectionEquality().hash(_errorDetails));

@override
String toString() {
  return 'SubscriptionResponse(id: $id, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, message: $message, storeName: $storeName, phone: $phone, city: $city, address: $address, description: $description, planType: $planType, price: $price, userId: $userId, errorCode: $errorCode, errorDetails: $errorDetails)';
}


}

/// @nodoc
abstract mixin class _$SubscriptionResponseCopyWith<$Res> implements $SubscriptionResponseCopyWith<$Res> {
  factory _$SubscriptionResponseCopyWith(_SubscriptionResponse value, $Res Function(_SubscriptionResponse) _then) = __$SubscriptionResponseCopyWithImpl;
@override @useResult
$Res call({
 String id, String status,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt, String? message,@JsonKey(name: 'store_name') String? storeName, String? phone, String? city, String? address, String? description,@JsonKey(name: 'plan_type') String? planType, double? price,@JsonKey(name: 'user_id') String? userId,@JsonKey(name: 'error_code') String? errorCode,@JsonKey(name: 'error_details') Map<String, dynamic>? errorDetails
});




}
/// @nodoc
class __$SubscriptionResponseCopyWithImpl<$Res>
    implements _$SubscriptionResponseCopyWith<$Res> {
  __$SubscriptionResponseCopyWithImpl(this._self, this._then);

  final _SubscriptionResponse _self;
  final $Res Function(_SubscriptionResponse) _then;

/// Create a copy of SubscriptionResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? status = null,Object? createdAt = null,Object? updatedAt = freezed,Object? message = freezed,Object? storeName = freezed,Object? phone = freezed,Object? city = freezed,Object? address = freezed,Object? description = freezed,Object? planType = freezed,Object? price = freezed,Object? userId = freezed,Object? errorCode = freezed,Object? errorDetails = freezed,}) {
  return _then(_SubscriptionResponse(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,storeName: freezed == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,planType: freezed == planType ? _self.planType : planType // ignore: cast_nullable_to_non_nullable
as String?,price: freezed == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,errorDetails: freezed == errorDetails ? _self._errorDetails : errorDetails // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on

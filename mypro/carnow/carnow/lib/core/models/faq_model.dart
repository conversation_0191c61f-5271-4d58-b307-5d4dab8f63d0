import 'package:freezed_annotation/freezed_annotation.dart';

part 'faq_model.freezed.dart';
part 'faq_model.g.dart';

/// FAQ data model
@freezed
abstract class FaqModel with _$FaqModel {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory FaqModel({
    required String id,
    required String question,
    required String answer,
    String? category, // Optional: to group FAQs by category later
    int? displayOrder,
    @Default(true) bool isVisible,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt,
  }) = _FaqModel;

  factory FaqModel.fromJson(Map<String, dynamic> json) =>
      _$FaqModelFromJson(json);
}

import 'package:freezed_annotation/freezed_annotation.dart';

part 'order_item_details_model.freezed.dart';
part 'order_item_details_model.g.dart';

@freezed
abstract class OrderItemDetailsModel with _$OrderItemDetailsModel {
  @JsonSerializable(explicitToJson: true, fieldRename: FieldRename.snake)
  const factory OrderItemDetailsModel({
    required String id,
    required String productName,
    required int quantity,
    required double unitPrice,
    required double totalPrice,
    String? productImage,
    String? sku,
  }) = _OrderItemDetailsModel;

  factory OrderItemDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$OrderItemDetailsModelFromJson(json);
}

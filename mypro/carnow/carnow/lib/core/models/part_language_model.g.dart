// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'part_language_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PartTermModel _$PartTermModelFromJson(Map<String, dynamic> json) =>
    _PartTermModel(
      id: json['id'] as String,
      arabicName: json['arabicName'] as String,
      englishName: json['englishName'] as String,
      italianName: json['italianName'] as String,
      category: json['category'] as String?,
      isCommonInLibya: json['isCommonInLibya'] as bool? ?? true,
      usageFrequency: (json['usageFrequency'] as num?)?.toInt() ?? 1,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PartTermModelToJson(_PartTermModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'arabicName': instance.arabicName,
      'englishName': instance.englishName,
      'italianName': instance.italianName,
      'category': instance.category,
      'isCommonInLibya': instance.isCommonInLibya,
      'usageFrequency': instance.usageFrequency,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_PartLanguageSettings _$PartLanguageSettingsFromJson(
  Map<String, dynamic> json,
) => _PartLanguageSettings(
  primaryLanguage:
      $enumDecodeNullable(_$PartLanguageEnumMap, json['primaryLanguage']) ??
      PartLanguage.arabic,
  secondaryLanguage:
      $enumDecodeNullable(_$PartLanguageEnumMap, json['secondaryLanguage']) ??
      PartLanguage.italian,
  showSecondaryInParentheses:
      json['showSecondaryInParentheses'] as bool? ?? true,
  enableAutoTranslation: json['enableAutoTranslation'] as bool? ?? true,
  preferLibyanTerms: json['preferLibyanTerms'] as bool? ?? false,
);

Map<String, dynamic> _$PartLanguageSettingsToJson(
  _PartLanguageSettings instance,
) => <String, dynamic>{
  'primaryLanguage': _$PartLanguageEnumMap[instance.primaryLanguage]!,
  'secondaryLanguage': _$PartLanguageEnumMap[instance.secondaryLanguage]!,
  'showSecondaryInParentheses': instance.showSecondaryInParentheses,
  'enableAutoTranslation': instance.enableAutoTranslation,
  'preferLibyanTerms': instance.preferLibyanTerms,
};

const _$PartLanguageEnumMap = {
  PartLanguage.arabic: 'arabic',
  PartLanguage.english: 'english',
  PartLanguage.italian: 'italian',
};

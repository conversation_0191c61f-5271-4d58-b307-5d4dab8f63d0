// ignore_for_file: invalid_annotation_target
// ignore_for_file: annotate_overrides

import 'package:freezed_annotation/freezed_annotation.dart';

part 'car_brand_model.freezed.dart';
part 'car_brand_model.g.dart';

@freezed
abstract class CarBrandModel with _$CarBrandModel {
  const factory CarBrandModel({int? id, String? name, String? imageUrl}) =
      _CarBrandModel;

  factory CarBrandModel.fromJson(Map<String, dynamic> json) =>
      _$CarBrandModelFromJson(json);
}

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'car_model_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CarModelModel {

 int? get id; String? get name; int? get brandId;
/// Create a copy of CarModelModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CarModelModelCopyWith<CarModelModel> get copyWith => _$CarModelModelCopyWithImpl<CarModelModel>(this as CarModelModel, _$identity);

  /// Serializes this CarModelModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CarModelModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.brandId, brandId) || other.brandId == brandId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,brandId);

@override
String toString() {
  return 'CarModelModel(id: $id, name: $name, brandId: $brandId)';
}


}

/// @nodoc
abstract mixin class $CarModelModelCopyWith<$Res>  {
  factory $CarModelModelCopyWith(CarModelModel value, $Res Function(CarModelModel) _then) = _$CarModelModelCopyWithImpl;
@useResult
$Res call({
 int? id, String? name, int? brandId
});




}
/// @nodoc
class _$CarModelModelCopyWithImpl<$Res>
    implements $CarModelModelCopyWith<$Res> {
  _$CarModelModelCopyWithImpl(this._self, this._then);

  final CarModelModel _self;
  final $Res Function(CarModelModel) _then;

/// Create a copy of CarModelModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = freezed,Object? brandId = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,brandId: freezed == brandId ? _self.brandId : brandId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// Adds pattern-matching-related methods to [CarModelModel].
extension CarModelModelPatterns on CarModelModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CarModelModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CarModelModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CarModelModel value)  $default,){
final _that = this;
switch (_that) {
case _CarModelModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CarModelModel value)?  $default,){
final _that = this;
switch (_that) {
case _CarModelModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  String? name,  int? brandId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CarModelModel() when $default != null:
return $default(_that.id,_that.name,_that.brandId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  String? name,  int? brandId)  $default,) {final _that = this;
switch (_that) {
case _CarModelModel():
return $default(_that.id,_that.name,_that.brandId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  String? name,  int? brandId)?  $default,) {final _that = this;
switch (_that) {
case _CarModelModel() when $default != null:
return $default(_that.id,_that.name,_that.brandId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CarModelModel implements CarModelModel {
  const _CarModelModel({this.id, this.name, this.brandId});
  factory _CarModelModel.fromJson(Map<String, dynamic> json) => _$CarModelModelFromJson(json);

@override final  int? id;
@override final  String? name;
@override final  int? brandId;

/// Create a copy of CarModelModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CarModelModelCopyWith<_CarModelModel> get copyWith => __$CarModelModelCopyWithImpl<_CarModelModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CarModelModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CarModelModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.brandId, brandId) || other.brandId == brandId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,brandId);

@override
String toString() {
  return 'CarModelModel(id: $id, name: $name, brandId: $brandId)';
}


}

/// @nodoc
abstract mixin class _$CarModelModelCopyWith<$Res> implements $CarModelModelCopyWith<$Res> {
  factory _$CarModelModelCopyWith(_CarModelModel value, $Res Function(_CarModelModel) _then) = __$CarModelModelCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? name, int? brandId
});




}
/// @nodoc
class __$CarModelModelCopyWithImpl<$Res>
    implements _$CarModelModelCopyWith<$Res> {
  __$CarModelModelCopyWithImpl(this._self, this._then);

  final _CarModelModel _self;
  final $Res Function(_CarModelModel) _then;

/// Create a copy of CarModelModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = freezed,Object? brandId = freezed,}) {
  return _then(_CarModelModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,brandId: freezed == brandId ? _self.brandId : brandId // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on

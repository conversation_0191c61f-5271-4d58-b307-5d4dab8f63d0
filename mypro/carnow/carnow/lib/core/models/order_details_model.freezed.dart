// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_details_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OrderDetailsModel {

 String get id; String get orderNumber; OrderStatus get status; DateTime get createdAt; double get totalAmount; double get subtotal; double get taxAmount; double get shippingCost; String get currency; String get buyerName; String get buyerEmail; String get buyerPhone; String get sellerName; String get sellerEmail; String get shippingAddress; List<OrderItemDetailsModel> get items; List<OrderStatusHistoryModel> get statusHistory; DateTime? get updatedAt; String? get trackingNumber; String? get notes;
/// Create a copy of OrderDetailsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderDetailsModelCopyWith<OrderDetailsModel> get copyWith => _$OrderDetailsModelCopyWithImpl<OrderDetailsModel>(this as OrderDetailsModel, _$identity);

  /// Serializes this OrderDetailsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderDetailsModel&&(identical(other.id, id) || other.id == id)&&(identical(other.orderNumber, orderNumber) || other.orderNumber == orderNumber)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.subtotal, subtotal) || other.subtotal == subtotal)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.shippingCost, shippingCost) || other.shippingCost == shippingCost)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.buyerName, buyerName) || other.buyerName == buyerName)&&(identical(other.buyerEmail, buyerEmail) || other.buyerEmail == buyerEmail)&&(identical(other.buyerPhone, buyerPhone) || other.buyerPhone == buyerPhone)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName)&&(identical(other.sellerEmail, sellerEmail) || other.sellerEmail == sellerEmail)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&const DeepCollectionEquality().equals(other.items, items)&&const DeepCollectionEquality().equals(other.statusHistory, statusHistory)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.trackingNumber, trackingNumber) || other.trackingNumber == trackingNumber)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,orderNumber,status,createdAt,totalAmount,subtotal,taxAmount,shippingCost,currency,buyerName,buyerEmail,buyerPhone,sellerName,sellerEmail,shippingAddress,const DeepCollectionEquality().hash(items),const DeepCollectionEquality().hash(statusHistory),updatedAt,trackingNumber,notes]);

@override
String toString() {
  return 'OrderDetailsModel(id: $id, orderNumber: $orderNumber, status: $status, createdAt: $createdAt, totalAmount: $totalAmount, subtotal: $subtotal, taxAmount: $taxAmount, shippingCost: $shippingCost, currency: $currency, buyerName: $buyerName, buyerEmail: $buyerEmail, buyerPhone: $buyerPhone, sellerName: $sellerName, sellerEmail: $sellerEmail, shippingAddress: $shippingAddress, items: $items, statusHistory: $statusHistory, updatedAt: $updatedAt, trackingNumber: $trackingNumber, notes: $notes)';
}


}

/// @nodoc
abstract mixin class $OrderDetailsModelCopyWith<$Res>  {
  factory $OrderDetailsModelCopyWith(OrderDetailsModel value, $Res Function(OrderDetailsModel) _then) = _$OrderDetailsModelCopyWithImpl;
@useResult
$Res call({
 String id, String orderNumber, OrderStatus status, DateTime createdAt, double totalAmount, double subtotal, double taxAmount, double shippingCost, String currency, String buyerName, String buyerEmail, String buyerPhone, String sellerName, String sellerEmail, String shippingAddress, List<OrderItemDetailsModel> items, List<OrderStatusHistoryModel> statusHistory, DateTime? updatedAt, String? trackingNumber, String? notes
});




}
/// @nodoc
class _$OrderDetailsModelCopyWithImpl<$Res>
    implements $OrderDetailsModelCopyWith<$Res> {
  _$OrderDetailsModelCopyWithImpl(this._self, this._then);

  final OrderDetailsModel _self;
  final $Res Function(OrderDetailsModel) _then;

/// Create a copy of OrderDetailsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? orderNumber = null,Object? status = null,Object? createdAt = null,Object? totalAmount = null,Object? subtotal = null,Object? taxAmount = null,Object? shippingCost = null,Object? currency = null,Object? buyerName = null,Object? buyerEmail = null,Object? buyerPhone = null,Object? sellerName = null,Object? sellerEmail = null,Object? shippingAddress = null,Object? items = null,Object? statusHistory = null,Object? updatedAt = freezed,Object? trackingNumber = freezed,Object? notes = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,orderNumber: null == orderNumber ? _self.orderNumber : orderNumber // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,subtotal: null == subtotal ? _self.subtotal : subtotal // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,shippingCost: null == shippingCost ? _self.shippingCost : shippingCost // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,buyerName: null == buyerName ? _self.buyerName : buyerName // ignore: cast_nullable_to_non_nullable
as String,buyerEmail: null == buyerEmail ? _self.buyerEmail : buyerEmail // ignore: cast_nullable_to_non_nullable
as String,buyerPhone: null == buyerPhone ? _self.buyerPhone : buyerPhone // ignore: cast_nullable_to_non_nullable
as String,sellerName: null == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String,sellerEmail: null == sellerEmail ? _self.sellerEmail : sellerEmail // ignore: cast_nullable_to_non_nullable
as String,shippingAddress: null == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as String,items: null == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<OrderItemDetailsModel>,statusHistory: null == statusHistory ? _self.statusHistory : statusHistory // ignore: cast_nullable_to_non_nullable
as List<OrderStatusHistoryModel>,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,trackingNumber: freezed == trackingNumber ? _self.trackingNumber : trackingNumber // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [OrderDetailsModel].
extension OrderDetailsModelPatterns on OrderDetailsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderDetailsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderDetailsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderDetailsModel value)  $default,){
final _that = this;
switch (_that) {
case _OrderDetailsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderDetailsModel value)?  $default,){
final _that = this;
switch (_that) {
case _OrderDetailsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String orderNumber,  OrderStatus status,  DateTime createdAt,  double totalAmount,  double subtotal,  double taxAmount,  double shippingCost,  String currency,  String buyerName,  String buyerEmail,  String buyerPhone,  String sellerName,  String sellerEmail,  String shippingAddress,  List<OrderItemDetailsModel> items,  List<OrderStatusHistoryModel> statusHistory,  DateTime? updatedAt,  String? trackingNumber,  String? notes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderDetailsModel() when $default != null:
return $default(_that.id,_that.orderNumber,_that.status,_that.createdAt,_that.totalAmount,_that.subtotal,_that.taxAmount,_that.shippingCost,_that.currency,_that.buyerName,_that.buyerEmail,_that.buyerPhone,_that.sellerName,_that.sellerEmail,_that.shippingAddress,_that.items,_that.statusHistory,_that.updatedAt,_that.trackingNumber,_that.notes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String orderNumber,  OrderStatus status,  DateTime createdAt,  double totalAmount,  double subtotal,  double taxAmount,  double shippingCost,  String currency,  String buyerName,  String buyerEmail,  String buyerPhone,  String sellerName,  String sellerEmail,  String shippingAddress,  List<OrderItemDetailsModel> items,  List<OrderStatusHistoryModel> statusHistory,  DateTime? updatedAt,  String? trackingNumber,  String? notes)  $default,) {final _that = this;
switch (_that) {
case _OrderDetailsModel():
return $default(_that.id,_that.orderNumber,_that.status,_that.createdAt,_that.totalAmount,_that.subtotal,_that.taxAmount,_that.shippingCost,_that.currency,_that.buyerName,_that.buyerEmail,_that.buyerPhone,_that.sellerName,_that.sellerEmail,_that.shippingAddress,_that.items,_that.statusHistory,_that.updatedAt,_that.trackingNumber,_that.notes);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String orderNumber,  OrderStatus status,  DateTime createdAt,  double totalAmount,  double subtotal,  double taxAmount,  double shippingCost,  String currency,  String buyerName,  String buyerEmail,  String buyerPhone,  String sellerName,  String sellerEmail,  String shippingAddress,  List<OrderItemDetailsModel> items,  List<OrderStatusHistoryModel> statusHistory,  DateTime? updatedAt,  String? trackingNumber,  String? notes)?  $default,) {final _that = this;
switch (_that) {
case _OrderDetailsModel() when $default != null:
return $default(_that.id,_that.orderNumber,_that.status,_that.createdAt,_that.totalAmount,_that.subtotal,_that.taxAmount,_that.shippingCost,_that.currency,_that.buyerName,_that.buyerEmail,_that.buyerPhone,_that.sellerName,_that.sellerEmail,_that.shippingAddress,_that.items,_that.statusHistory,_that.updatedAt,_that.trackingNumber,_that.notes);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(explicitToJson: true, fieldRename: FieldRename.snake)
class _OrderDetailsModel implements OrderDetailsModel {
  const _OrderDetailsModel({required this.id, required this.orderNumber, required this.status, required this.createdAt, required this.totalAmount, required this.subtotal, required this.taxAmount, required this.shippingCost, required this.currency, required this.buyerName, required this.buyerEmail, required this.buyerPhone, required this.sellerName, required this.sellerEmail, required this.shippingAddress, required final  List<OrderItemDetailsModel> items, required final  List<OrderStatusHistoryModel> statusHistory, this.updatedAt, this.trackingNumber, this.notes}): _items = items,_statusHistory = statusHistory;
  factory _OrderDetailsModel.fromJson(Map<String, dynamic> json) => _$OrderDetailsModelFromJson(json);

@override final  String id;
@override final  String orderNumber;
@override final  OrderStatus status;
@override final  DateTime createdAt;
@override final  double totalAmount;
@override final  double subtotal;
@override final  double taxAmount;
@override final  double shippingCost;
@override final  String currency;
@override final  String buyerName;
@override final  String buyerEmail;
@override final  String buyerPhone;
@override final  String sellerName;
@override final  String sellerEmail;
@override final  String shippingAddress;
 final  List<OrderItemDetailsModel> _items;
@override List<OrderItemDetailsModel> get items {
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_items);
}

 final  List<OrderStatusHistoryModel> _statusHistory;
@override List<OrderStatusHistoryModel> get statusHistory {
  if (_statusHistory is EqualUnmodifiableListView) return _statusHistory;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_statusHistory);
}

@override final  DateTime? updatedAt;
@override final  String? trackingNumber;
@override final  String? notes;

/// Create a copy of OrderDetailsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderDetailsModelCopyWith<_OrderDetailsModel> get copyWith => __$OrderDetailsModelCopyWithImpl<_OrderDetailsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderDetailsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderDetailsModel&&(identical(other.id, id) || other.id == id)&&(identical(other.orderNumber, orderNumber) || other.orderNumber == orderNumber)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.subtotal, subtotal) || other.subtotal == subtotal)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.shippingCost, shippingCost) || other.shippingCost == shippingCost)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.buyerName, buyerName) || other.buyerName == buyerName)&&(identical(other.buyerEmail, buyerEmail) || other.buyerEmail == buyerEmail)&&(identical(other.buyerPhone, buyerPhone) || other.buyerPhone == buyerPhone)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName)&&(identical(other.sellerEmail, sellerEmail) || other.sellerEmail == sellerEmail)&&(identical(other.shippingAddress, shippingAddress) || other.shippingAddress == shippingAddress)&&const DeepCollectionEquality().equals(other._items, _items)&&const DeepCollectionEquality().equals(other._statusHistory, _statusHistory)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.trackingNumber, trackingNumber) || other.trackingNumber == trackingNumber)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,orderNumber,status,createdAt,totalAmount,subtotal,taxAmount,shippingCost,currency,buyerName,buyerEmail,buyerPhone,sellerName,sellerEmail,shippingAddress,const DeepCollectionEquality().hash(_items),const DeepCollectionEquality().hash(_statusHistory),updatedAt,trackingNumber,notes]);

@override
String toString() {
  return 'OrderDetailsModel(id: $id, orderNumber: $orderNumber, status: $status, createdAt: $createdAt, totalAmount: $totalAmount, subtotal: $subtotal, taxAmount: $taxAmount, shippingCost: $shippingCost, currency: $currency, buyerName: $buyerName, buyerEmail: $buyerEmail, buyerPhone: $buyerPhone, sellerName: $sellerName, sellerEmail: $sellerEmail, shippingAddress: $shippingAddress, items: $items, statusHistory: $statusHistory, updatedAt: $updatedAt, trackingNumber: $trackingNumber, notes: $notes)';
}


}

/// @nodoc
abstract mixin class _$OrderDetailsModelCopyWith<$Res> implements $OrderDetailsModelCopyWith<$Res> {
  factory _$OrderDetailsModelCopyWith(_OrderDetailsModel value, $Res Function(_OrderDetailsModel) _then) = __$OrderDetailsModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String orderNumber, OrderStatus status, DateTime createdAt, double totalAmount, double subtotal, double taxAmount, double shippingCost, String currency, String buyerName, String buyerEmail, String buyerPhone, String sellerName, String sellerEmail, String shippingAddress, List<OrderItemDetailsModel> items, List<OrderStatusHistoryModel> statusHistory, DateTime? updatedAt, String? trackingNumber, String? notes
});




}
/// @nodoc
class __$OrderDetailsModelCopyWithImpl<$Res>
    implements _$OrderDetailsModelCopyWith<$Res> {
  __$OrderDetailsModelCopyWithImpl(this._self, this._then);

  final _OrderDetailsModel _self;
  final $Res Function(_OrderDetailsModel) _then;

/// Create a copy of OrderDetailsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? orderNumber = null,Object? status = null,Object? createdAt = null,Object? totalAmount = null,Object? subtotal = null,Object? taxAmount = null,Object? shippingCost = null,Object? currency = null,Object? buyerName = null,Object? buyerEmail = null,Object? buyerPhone = null,Object? sellerName = null,Object? sellerEmail = null,Object? shippingAddress = null,Object? items = null,Object? statusHistory = null,Object? updatedAt = freezed,Object? trackingNumber = freezed,Object? notes = freezed,}) {
  return _then(_OrderDetailsModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,orderNumber: null == orderNumber ? _self.orderNumber : orderNumber // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,subtotal: null == subtotal ? _self.subtotal : subtotal // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,shippingCost: null == shippingCost ? _self.shippingCost : shippingCost // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,buyerName: null == buyerName ? _self.buyerName : buyerName // ignore: cast_nullable_to_non_nullable
as String,buyerEmail: null == buyerEmail ? _self.buyerEmail : buyerEmail // ignore: cast_nullable_to_non_nullable
as String,buyerPhone: null == buyerPhone ? _self.buyerPhone : buyerPhone // ignore: cast_nullable_to_non_nullable
as String,sellerName: null == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String,sellerEmail: null == sellerEmail ? _self.sellerEmail : sellerEmail // ignore: cast_nullable_to_non_nullable
as String,shippingAddress: null == shippingAddress ? _self.shippingAddress : shippingAddress // ignore: cast_nullable_to_non_nullable
as String,items: null == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<OrderItemDetailsModel>,statusHistory: null == statusHistory ? _self._statusHistory : statusHistory // ignore: cast_nullable_to_non_nullable
as List<OrderStatusHistoryModel>,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,trackingNumber: freezed == trackingNumber ? _self.trackingNumber : trackingNumber // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on

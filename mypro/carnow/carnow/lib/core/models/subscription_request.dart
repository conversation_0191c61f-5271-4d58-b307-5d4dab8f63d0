import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_request.freezed.dart';
part 'subscription_request.g.dart';

@freezed
abstract class SubscriptionRequest with _$SubscriptionRequest {
  const factory SubscriptionRequest({
    @JsonKey(name: 'store_name') required String storeName,
    required String phone,
    required String city,
    required String address,
    required String description,
    @JsonKey(name: 'plan_type') required String planType,
    required double price,
    @JsonKey(name: 'user_id') required String userId,
  }) = _SubscriptionRequest;

  factory SubscriptionRequest.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionRequestFromJson(json);
}

extension SubscriptionRequestValidation on SubscriptionRequest {
  /// Validates the subscription request and returns a map of field errors in Arabic
  Map<String, String> validate() {
    final errors = <String, String>{};

    // Store name validation
    if (storeName.trim().isEmpty) {
      errors['storeName'] = 'اسم المتجر مطلوب';
    } else if (storeName.trim().length < 2) {
      errors['storeName'] = 'اسم المتجر يجب أن يكون أكثر من حرفين';
    } else if (storeName.trim().length > 100) {
      errors['storeName'] = 'اسم المتجر يجب أن يكون أقل من 100 حرف';
    }

    // Phone validation
    if (phone.trim().isEmpty) {
      errors['phone'] = 'رقم الهاتف مطلوب';
    } else if (!_isValidPhoneNumber(phone.trim())) {
      errors['phone'] = 'رقم الهاتف غير صحيح';
    }

    // City validation
    if (city.trim().isEmpty) {
      errors['city'] = 'المدينة مطلوبة';
    } else if (city.trim().length < 2) {
      errors['city'] = 'اسم المدينة يجب أن يكون أكثر من حرفين';
    }

    // Address validation
    if (address.trim().isEmpty) {
      errors['address'] = 'العنوان مطلوب';
    } else if (address.trim().length < 10) {
      errors['address'] =
          'العنوان يجب أن يكون أكثر تفصيلاً (على الأقل 10 أحرف)';
    } else if (address.trim().length > 500) {
      errors['address'] = 'العنوان طويل جداً (أقل من 500 حرف)';
    }

    // Description validation
    if (description.trim().isEmpty) {
      errors['description'] = 'وصف المتجر مطلوب';
    } else if (description.trim().length < 10) {
      errors['description'] =
          'وصف المتجر يجب أن يكون أكثر تفصيلاً (على الأقل 10 أحرف)';
    } else if (description.trim().length > 1000) {
      errors['description'] = 'وصف المتجر طويل جداً (أقل من 1000 حرف)';
    }

    // Plan type validation
    if (planType.trim().isEmpty) {
      errors['planType'] = 'نوع الخطة مطلوب';
    } else if (!_isValidPlanType(planType.trim())) {
      errors['planType'] = 'نوع الخطة غير صحيح';
    }

    // Price validation
    if (price <= 0) {
      errors['price'] = 'السعر يجب أن يكون أكبر من صفر';
    } else if (price > 10000) {
      errors['price'] = 'السعر مرتفع جداً';
    }

    // User ID validation
    if (userId.trim().isEmpty) {
      errors['userId'] = 'معرف المستخدم مطلوب';
    } else if (!_isValidUUID(userId.trim())) {
      errors['userId'] = 'معرف المستخدم غير صحيح';
    }

    return errors;
  }

  /// Checks if the subscription request is valid
  bool get isValid => validate().isEmpty;

  /// Gets validation error message for a specific field
  String? getFieldError(String fieldName) {
    final errors = validate();
    return errors[fieldName];
  }

  /// Validates phone number format (Saudi Arabia format)
  bool _isValidPhoneNumber(String phone) {
    // Remove any spaces, dashes, or parentheses
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Check for Saudi Arabia phone number patterns
    // Mobile: +966 5X XXX XXXX or 05X XXX XXXX (10 digits total with leading 0)
    // Landline: +966 1X XXX XXXX or 01X XXX XXXX (10 digits total with leading 0)
    final saudiMobileRegex = RegExp(r'^(\+966|966)?0?5[0-9]{8}$');
    final saudiLandlineRegex = RegExp(r'^(\+966|966)?0?[1-4][0-9]{8}$');

    return saudiMobileRegex.hasMatch(cleanPhone) ||
        saudiLandlineRegex.hasMatch(cleanPhone);
  }

  /// Validates plan type
  bool _isValidPlanType(String planType) {
    const validPlanTypes = [
      'basic',
      'premium',
      'enterprise',
      'starter',
      'professional',
    ];
    return validPlanTypes.contains(planType.toLowerCase());
  }

  /// Validates UUID format
  bool _isValidUUID(String uuid) {
    final uuidRegex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
    );
    return uuidRegex.hasMatch(uuid);
  }
}

import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_error.freezed.dart';

@freezed
abstract class SubscriptionError with _$SubscriptionError {
  const factory SubscriptionError.networkError({
    required String message,
    String? code,
    Map<String, dynamic>? details,
  }) = NetworkError;

  const factory SubscriptionError.databaseError({
    required String message,
    String? code,
    Map<String, dynamic>? details,
  }) = DatabaseError;

  const factory SubscriptionError.validationError({
    required String message,
    required Map<String, String> fieldErrors,
    String? code,
  }) = ValidationError;

  const factory SubscriptionError.navigationError({
    required String message,
    required String attemptedRoute,
    String? code,
  }) = NavigationError;

  const factory SubscriptionError.authenticationError({
    required String message,
    String? code,
    Map<String, dynamic>? details,
  }) = AuthenticationError;

  const factory SubscriptionError.serverError({
    required String message,
    String? code,
    int? statusCode,
    Map<String, dynamic>? details,
  }) = ServerError;

  const factory SubscriptionError.paymentError({
    required String message,
    String? code,
    String? paymentMethod,
    Map<String, dynamic>? details,
  }) = PaymentError;

  const factory SubscriptionError.businessLogicError({
    required String message,
    String? code,
    String? businessRule,
    Map<String, dynamic>? details,
  }) = BusinessLogicError;

  const factory SubscriptionError.unknownError({
    required String message,
    String? code,
    Map<String, dynamic>? details,
  }) = UnknownError;
}

extension SubscriptionErrorHelpers on SubscriptionError {
  /// Gets user-friendly error message in Arabic
  String get userFriendlyMessageArabic {
    return when(
      networkError: (message, code, details) =>
          'خطأ في الاتصال: تحقق من اتصال الإنترنت وحاول مرة أخرى',
      databaseError: (message, code, details) =>
          'خطأ في قاعدة البيانات: حاول مرة أخرى لاحقاً',
      validationError: (message, fieldErrors, code) =>
          'بيانات غير صحيحة: يرجى مراجعة البيانات المدخلة',
      navigationError: (message, attemptedRoute, code) =>
          'خطأ في التنقل: حاول مرة أخرى',
      authenticationError: (message, code, details) =>
          'خطأ في المصادقة: يرجى تسجيل الدخول مرة أخرى',
      serverError: (message, code, statusCode, details) =>
          'خطأ في الخادم: حاول مرة أخرى لاحقاً',
      paymentError: (message, code, paymentMethod, details) =>
          'خطأ في عملية الدفع: تحقق من طريقة الدفع',
      businessLogicError: (message, code, businessRule, details) =>
          'خطأ في العملية: $message',
      unknownError: (message, code, details) =>
          'حدث خطأ غير متوقع: حاول مرة أخرى',
    );
  }

  /// Gets technical error message for logging
  String get technicalMessage {
    return when(
      networkError: (message, code, details) =>
          'Network Error: $message (Code: $code)',
      databaseError: (message, code, details) =>
          'Database Error: $message (Code: $code)',
      validationError: (message, fieldErrors, code) =>
          'Validation Error: $message (Fields: ${fieldErrors.keys.join(', ')}) (Code: $code)',
      navigationError: (message, attemptedRoute, code) =>
          'Navigation Error: $message (Route: $attemptedRoute) (Code: $code)',
      authenticationError: (message, code, details) =>
          'Authentication Error: $message (Code: $code)',
      serverError: (message, code, statusCode, details) =>
          'Server Error: $message (Status: $statusCode) (Code: $code)',
      paymentError: (message, code, paymentMethod, details) =>
          'Payment Error: $message (Method: $paymentMethod) (Code: $code)',
      businessLogicError: (message, code, businessRule, details) =>
          'Business Logic Error: $message (Rule: $businessRule) (Code: $code)',
      unknownError: (message, code, details) =>
          'Unknown Error: $message (Code: $code)',
    );
  }

  /// Gets error severity level
  ErrorSeverity get severity {
    return when(
      networkError: (_, __, ___) => ErrorSeverity.medium,
      databaseError: (_, __, ___) => ErrorSeverity.high,
      validationError: (_, __, ___) => ErrorSeverity.low,
      navigationError: (_, __, ___) => ErrorSeverity.medium,
      authenticationError: (_, __, ___) => ErrorSeverity.high,
      serverError: (_, __, ___, ____) => ErrorSeverity.high,
      paymentError: (_, __, ___, ____) => ErrorSeverity.high,
      businessLogicError: (_, __, ___, ____) => ErrorSeverity.medium,
      unknownError: (_, __, ___) => ErrorSeverity.high,
    );
  }

  /// Checks if the error is retryable
  bool get isRetryable {
    return when(
      networkError: (_, __, ___) => true,
      databaseError: (_, __, ___) => true,
      validationError: (_, __, ___) => false,
      navigationError: (_, __, ___) => true,
      authenticationError: (_, __, ___) => false,
      serverError: (message, code, statusCode, details) =>
          statusCode != null && statusCode >= 500,
      paymentError: (_, __, ___, ____) => false,
      businessLogicError: (_, __, ___, ____) => false,
      unknownError: (_, __, ___) => true,
    );
  }

  /// Gets suggested action for the user in Arabic
  String get suggestedActionArabic {
    return when(
      networkError: (_, __, ___) => 'تحقق من اتصال الإنترنت وحاول مرة أخرى',
      databaseError: (_, __, ___) => 'حاول مرة أخرى بعد قليل',
      validationError: (_, fieldErrors, __) =>
          'راجع البيانات المدخلة وصحح الأخطاء',
      navigationError: (_, __, ___) => 'أعد تحميل الصفحة وحاول مرة أخرى',
      authenticationError: (_, __, ___) => 'سجل الدخول مرة أخرى',
      serverError: (_, __, ___, ____) => 'حاول مرة أخرى بعد قليل',
      paymentError: (_, __, ___, ____) => 'تحقق من طريقة الدفع وحاول مرة أخرى',
      businessLogicError: (_, __, ___, ____) => 'راجع البيانات وحاول مرة أخرى',
      unknownError: (_, __, ___) => 'أعد تشغيل التطبيق وحاول مرة أخرى',
    );
  }

  /// Creates a SubscriptionError from an exception
  static SubscriptionError fromException(
    Exception exception, {
    String? context,
  }) {
    final message = exception.toString();

    if (message.contains('SocketException') ||
        message.contains('NetworkException')) {
      return SubscriptionError.networkError(
        message:
            'Network connection failed${context != null ? ' in $context' : ''}',
        code: 'NETWORK_ERROR',
        details: {'originalError': message},
      );
    }

    if (message.contains('FormatException') ||
        message.contains('ValidationException')) {
      return SubscriptionError.validationError(
        message:
            'Data validation failed${context != null ? ' in $context' : ''}',
        fieldErrors: {'general': 'Invalid data format'},
        code: 'VALIDATION_ERROR',
      );
    }

    if (message.contains('AuthException') || message.contains('Unauthorized')) {
      return SubscriptionError.authenticationError(
        message:
            'Authentication failed${context != null ? ' in $context' : ''}',
        code: 'AUTH_ERROR',
        details: {'originalError': message},
      );
    }

    return SubscriptionError.unknownError(
      message: 'Unknown error occurred${context != null ? ' in $context' : ''}',
      code: 'UNKNOWN_ERROR',
      details: {'originalError': message},
    );
  }

  /// Creates a SubscriptionError from HTTP status code
  static SubscriptionError fromHttpStatusCode(
    int statusCode,
    String message, {
    String? context,
  }) {
    switch (statusCode) {
      case 400:
        return SubscriptionError.validationError(
          message: message,
          fieldErrors: {'general': 'Bad request'},
          code: 'BAD_REQUEST',
        );
      case 401:
        return SubscriptionError.authenticationError(
          message: message,
          code: 'UNAUTHORIZED',
        );
      case 403:
        return SubscriptionError.authenticationError(
          message: message,
          code: 'FORBIDDEN',
        );
      case 404:
        return SubscriptionError.serverError(
          message: message,
          code: 'NOT_FOUND',
          statusCode: statusCode,
        );
      case 422:
        return SubscriptionError.validationError(
          message: message,
          fieldErrors: {'general': 'Unprocessable entity'},
          code: 'UNPROCESSABLE_ENTITY',
        );
      case 429:
        return SubscriptionError.serverError(
          message: message,
          code: 'RATE_LIMIT_EXCEEDED',
          statusCode: statusCode,
        );
      case >= 500:
        return SubscriptionError.serverError(
          message: message,
          code: 'SERVER_ERROR',
          statusCode: statusCode,
        );
      default:
        return SubscriptionError.unknownError(
          message: message,
          code: 'HTTP_ERROR_$statusCode',
          details: {'statusCode': statusCode},
        );
    }
  }
}

enum ErrorSeverity { low, medium, high, critical }

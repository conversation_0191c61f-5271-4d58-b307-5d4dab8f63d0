// =============================================================================
// توحيد نماذج المنتجات - Unified Product Model Export
// =============================================================================
//
// يوفر هذا الملف نقطة وصول موحدة لجميع نماذج المنتجات في تطبيق كار ناو
// This file provides a single point of access for all product models
//
// الميزات:
// - إعادة تصدير النموذج الرئيسي من وحدة المنتجات
// - نوع مُستعار للتوافق العكسي
// - أدوات مساعدة لتحويل النماذج والتوافق
// - دعم التحويل بين أنواع البيانات المختلفة
// =============================================================================

// إعادة تصدير النموذج الرئيسي من وحدة المنتجات
// Re-export the main product model from products module
export '../../features/products/models/product_model.dart';

// نوع مُستعار للتوافق العكسي
// Type alias for backward compatibility
import '../../features/products/models/product_model.dart' as product_models;

// تعريف نوع ProductModel الرئيسي
// Main ProductModel type definition
typedef ProductModel = product_models.ProductModel;

/// فئة مساعدة لتحويل نماذج المنتجات والتوافق
/// Utility class for product model conversions and compatibility
///
/// توفر هذه الفئة:
/// - تحويل من خرائط البيانات إلى النماذج القياسية
/// - تحويل camelCase إلى snake_case
/// - إنشاء خرائط JSON متوافقة مع الأنظمة القديمة
class ProductModelUtils {
  /// Convert from data map to standard ProductModel
  static ProductModel fromMap(Map<String, dynamic> json) {
    // Convert camelCase to snake_case for compatibility
    final Map<String, dynamic> convertedJson = {};
    json.forEach((key, value) {
      convertedJson[_camelToSnake(key)] = value;
    });
    return ProductModel.fromJson(convertedJson);
  }

  /// Convert camelCase to snake_case
  static String _camelToSnake(String camelCase) {
    return camelCase
        .replaceAll(RegExp('([A-Z])'), '_\$1')
        .toLowerCase()
        .replaceFirst(RegExp('^_'), '');
  }

  /// Create a legacy-compatible JSON map
  static Map<String, dynamic> toLegacyJson(ProductModel product) {
    final json = product.toJson();
    // Add any legacy field mappings here if needed
    return json;
  }
}

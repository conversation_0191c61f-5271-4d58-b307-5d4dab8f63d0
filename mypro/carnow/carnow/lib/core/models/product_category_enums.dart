import 'package:freezed_annotation/freezed_annotation.dart';

/// الأقسام الرئيسية للمنتجات
enum MainProductCategory {
  @JsonValue('vehicles_automotive')
  vehiclesAutomotive, // السيارات والمركبات

  @JsonValue('auto_parts')
  autoParts, // قطع غيار السيارات

  @JsonValue('electronics')
  electronics, // الإلكترونيات

  @JsonValue('tools_equipment')
  toolsEquipment, // الأدوات والمعدات

  @JsonValue('accessories')
  accessories, // الإكسسوارات

  @JsonValue('maintenance')
  maintenance, // مواد الصيانة

  @JsonValue('other')
  other, // أخرى
}

/// فئات السيارات والمركبات
enum VehicleSubCategory {
  @JsonValue('passenger_cars')
  passengerCars, // السيارات الشخصية

  @JsonValue('commercial_vehicles')
  commercialVehicles, // المركبات التجارية

  @JsonValue('motorcycles')
  motorcycles, // الدراجات النارية

  @JsonValue('trucks')
  trucks, // الشاحنات

  @JsonValue('buses')
  buses, // الحافلات

  @JsonValue('special_vehicles')
  specialVehicles, // المركبات الخاصة

  @JsonValue('trailers')
  trailers, // المقطورات

  @JsonValue('other')
  other,
}

/// فئات قطع غيار السيارات الرئيسية
enum AutoPartsMainCategory {
  @JsonValue('engine_parts')
  engineParts, // قطع المحرك

  @JsonValue('transmission_parts')
  transmissionParts, // قطع نقل الحركة

  @JsonValue('brake_system')
  brakeSystem, // نظام الفرامل

  @JsonValue('suspension_steering')
  suspensionSteering, // التعليق والتوجيه

  @JsonValue('electrical_system')
  electricalSystem, // النظام الكهربائي

  @JsonValue('body_exterior')
  bodyExterior, // الهيكل الخارجي

  @JsonValue('interior_parts')
  interiorParts, // الأجزاء الداخلية

  @JsonValue('tires_wheels')
  tiresWheels, // الإطارات والعجلات

  @JsonValue('exhaust_system')
  exhaustSystem, // نظام العادم

  @JsonValue('cooling_system')
  coolingSystem, // نظام التبريد

  @JsonValue('fuel_system')
  fuelSystem, // نظام الوقود

  @JsonValue('air_conditioning')
  airConditioning, // نظام التكييف

  @JsonValue('lighting_system')
  lightingSystem, // نظام الإضاءة

  @JsonValue('filters_fluids')
  filtersFluids, // الفلاتر والسوائل

  @JsonValue('belts_chains')
  beltsChains, // الأحزمة والسلاسل

  @JsonValue('sensors_gauges')
  sensorsGauges, // الحساسات وأجهزة القياس

  @JsonValue('other')
  other,
}

/// فئات قطع المحرك الفرعية
enum EnginePartsSubCategory {
  @JsonValue('pistons_rings')
  pistonsRings, // المكابس والحلقات

  @JsonValue('cylinder_head')
  cylinderHead, // رأس الأسطوانة

  @JsonValue('engine_block')
  engineBlock, // كتلة المحرك

  @JsonValue('valves_springs')
  valvesSprings, // الصمامات والنوابض

  @JsonValue('camshaft_crankshaft')
  camshaftCrankshaft, // عمود الكامات وعمود المرفق

  @JsonValue('connecting_rods')
  connectingRods, // أذرع التوصيل

  @JsonValue('engine_gaskets')
  engineGaskets, // جوانات المحرك

  @JsonValue('timing_components')
  timingComponents, // مكونات التوقيت

  @JsonValue('oil_pump')
  oilPump, // مضخة الزيت

  @JsonValue('water_pump')
  waterPump, // مضخة الماء

  @JsonValue('other')
  other,
}

/// فئات نظام الفرامل الفرعية
enum BrakeSystemSubCategory {
  @JsonValue('brake_pads')
  brakePads, // تيل الفرامل

  @JsonValue('brake_discs')
  brakeDiscs, // أقراص الفرامل

  @JsonValue('brake_drums')
  brakeDrums, // طبول الفرامل

  @JsonValue('brake_shoes')
  brakeShoes, // حذاء الفرامل

  @JsonValue('brake_calipers')
  brakeCalipers, // كماشة الفرامل

  @JsonValue('brake_fluid')
  brakeFluid, // سائل الفرامل

  @JsonValue('brake_lines')
  brakeLines, // خطوط الفرامل

  @JsonValue('brake_master_cylinder')
  brakeMasterCylinder, // أسطوانة الفرامل الرئيسية

  @JsonValue('brake_booster')
  brakeBooster, // معزز الفرامل

  @JsonValue('handbrake_parts')
  handbrakeParts, // قطع فرامل اليد

  @JsonValue('other')
  other,
}

/// فئات الإطارات والعجلات الفرعية
enum TiresWheelsSubCategory {
  @JsonValue('car_tires')
  carTires, // إطارات السيارات

  @JsonValue('truck_tires')
  truckTires, // إطارات الشاحنات

  @JsonValue('motorcycle_tires')
  motorcycleTires, // إطارات الدراجات النارية

  @JsonValue('alloy_wheels')
  alloyWheels, // العجلات المعدنية

  @JsonValue('steel_wheels')
  steelWheels, // العجلات الحديدية

  @JsonValue('wheel_covers')
  wheelCovers, // أغطية العجلات

  @JsonValue('tire_tubes')
  tireTubes, // الأنابيب الداخلية

  @JsonValue('tire_valves')
  tireValves, // صمامات الإطارات

  @JsonValue('wheel_bearings')
  wheelBearings, // كراسي العجلات

  @JsonValue('tire_accessories')
  tireAccessories, // إكسسوارات الإطارات

  @JsonValue('other')
  other,
}

/// فئات النظام الكهربائي الفرعية
enum ElectricalSystemSubCategory {
  @JsonValue('batteries')
  batteries, // البطاريات

  @JsonValue('alternators')
  alternators, // المولدات

  @JsonValue('starters')
  starters, // بادئ الحركة

  @JsonValue('ignition_system')
  ignitionSystem, // نظام الإشعال

  @JsonValue('wiring_harness')
  wiringHarness, // حزمة الأسلاك

  @JsonValue('fuses_relays')
  fusesRelays, // الفيوزات والريليهات

  @JsonValue('ecu_modules')
  ecuModules, // وحدات التحكم الإلكترونية

  @JsonValue('sensors')
  sensors, // الحساسات

  @JsonValue('switches_buttons')
  switchesButtons, // المفاتيح والأزرار

  @JsonValue('charging_system')
  chargingSystem, // نظام الشحن

  @JsonValue('other')
  other,
}

/// نموذج فئة المنتج الشامل
class ProductCategoryModel {
  const ProductCategoryModel({
    required this.mainCategory,
    this.subCategory,
    this.detailedCategory,
  });

  /// إنشاء فئة منتج للسيارات والمركبات
  factory ProductCategoryModel.vehicle({
    required VehicleSubCategory subCategory,
  }) {
    return ProductCategoryModel(
      mainCategory: MainProductCategory.vehiclesAutomotive,
      subCategory: subCategory,
    );
  }

  /// إنشاء فئة منتج لقطع الغيار
  factory ProductCategoryModel.autoPart({
    required AutoPartsMainCategory mainPartsCategory,
    dynamic subPartsCategory,
  }) {
    return ProductCategoryModel(
      mainCategory: MainProductCategory.autoParts,
      subCategory: mainPartsCategory,
      detailedCategory: subPartsCategory,
    );
  }
  final MainProductCategory mainCategory;
  final dynamic subCategory;
  final dynamic detailedCategory;

  /// الحصول على نص الفئة الرئيسية
  String getMainCategoryDisplayName() {
    switch (mainCategory) {
      case MainProductCategory.vehiclesAutomotive:
        return 'السيارات والمركبات';
      case MainProductCategory.autoParts:
        return 'قطع غيار السيارات';
      case MainProductCategory.electronics:
        return 'الإلكترونيات';
      case MainProductCategory.toolsEquipment:
        return 'الأدوات والمعدات';
      case MainProductCategory.accessories:
        return 'الإكسسوارات';
      case MainProductCategory.maintenance:
        return 'مواد الصيانة';
      case MainProductCategory.other:
        return 'أخرى';
    }
  }

  /// الحصول على نص الفئة الفرعية
  String? getSubCategoryDisplayName() {
    if (subCategory == null) return null;

    // فئات السيارات
    if (subCategory is VehicleSubCategory) {
      switch (subCategory as VehicleSubCategory) {
        case VehicleSubCategory.passengerCars:
          return 'السيارات الشخصية';
        case VehicleSubCategory.commercialVehicles:
          return 'المركبات التجارية';
        case VehicleSubCategory.motorcycles:
          return 'الدراجات النارية';
        case VehicleSubCategory.trucks:
          return 'الشاحنات';
        case VehicleSubCategory.buses:
          return 'الحافلات';
        case VehicleSubCategory.specialVehicles:
          return 'المركبات الخاصة';
        case VehicleSubCategory.trailers:
          return 'المقطورات';
        case VehicleSubCategory.other:
          return 'أخرى';
      }
    }

    // فئات قطع الغيار الرئيسية
    if (subCategory is AutoPartsMainCategory) {
      switch (subCategory as AutoPartsMainCategory) {
        case AutoPartsMainCategory.engineParts:
          return 'قطع المحرك';
        case AutoPartsMainCategory.transmissionParts:
          return 'قطع نقل الحركة';
        case AutoPartsMainCategory.brakeSystem:
          return 'نظام الفرامل';
        case AutoPartsMainCategory.suspensionSteering:
          return 'التعليق والتوجيه';
        case AutoPartsMainCategory.electricalSystem:
          return 'النظام الكهربائي';
        case AutoPartsMainCategory.bodyExterior:
          return 'الهيكل الخارجي';
        case AutoPartsMainCategory.interiorParts:
          return 'الأجزاء الداخلية';
        case AutoPartsMainCategory.tiresWheels:
          return 'الإطارات والعجلات';
        case AutoPartsMainCategory.exhaustSystem:
          return 'نظام العادم';
        case AutoPartsMainCategory.coolingSystem:
          return 'نظام التبريد';
        case AutoPartsMainCategory.fuelSystem:
          return 'نظام الوقود';
        case AutoPartsMainCategory.airConditioning:
          return 'نظام التكييف';
        case AutoPartsMainCategory.lightingSystem:
          return 'نظام الإضاءة';
        case AutoPartsMainCategory.filtersFluids:
          return 'الفلاتر والسوائل';
        case AutoPartsMainCategory.beltsChains:
          return 'الأحزمة والسلاسل';
        case AutoPartsMainCategory.sensorsGauges:
          return 'الحساسات وأجهزة القياس';
        case AutoPartsMainCategory.other:
          return 'أخرى';
      }
    }

    return null;
  }
}

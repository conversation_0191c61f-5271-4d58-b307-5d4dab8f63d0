import 'package:freezed_annotation/freezed_annotation.dart';

part 'part_language_model.freezed.dart';
part 'part_language_model.g.dart';

/// أنواع اللغات المدعومة لأسماء قطع الغيار
enum PartLanguage {
  /// العربية (الافتراضية)
  arabic('ar', 'العربية', 'Arabic'),

  /// الإنجليزية
  english('en', 'الإنجليزية', 'English'),

  /// الإيطالية (المصطلحات الليبية الشائعة، تكتب بالعربية)
  italian('it', 'الإيطالية', 'Italiano');

  const PartLanguage(this.code, this.nameAr, this.nameEn);

  final String code;
  final String nameAr;
  final String nameEn;

  /// الحصول على اللغة من الكود
  static PartLanguage fromCode(String code) {
    return PartLanguage.values.firstWhere(
      (lang) => lang.code == code,
      orElse: () => PartLanguage.arabic,
    );
  }

  /// الحصول على الاسم المعروض حسب اللغة الحالية
  String getDisplayName(String currentLangCode) {
    switch (currentLangCode) {
      case 'en':
        return nameEn;
      default:
        return nameAr;
    }
  }
}

/// نموذج المصطلحات المتعددة اللغات لقطع الغيار
@freezed
abstract class PartTermModel with _$PartTermModel {
  const factory PartTermModel({
    required String id,
    required String arabicName,
    required String englishName,
    required String italianName,
    String? category,
    @Default(true) bool isCommonInLibya,
    @Default(1) int usageFrequency,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _PartTermModel;

  factory PartTermModel.fromJson(Map<String, dynamic> json) =>
      _$PartTermModelFromJson(json);
}

/// امتداد للحصول على الاسم حسب اللغة
extension PartTermModelX on PartTermModel {
  /// الحصول على الاسم بناءً على اللغة المحددة
  String getNameByLanguage(PartLanguage language) {
    switch (language) {
      case PartLanguage.arabic:
        return arabicName;
      case PartLanguage.english:
        return englishName;
      case PartLanguage.italian:
        return italianName;
    }
  }

  /// الحصول على الاسم بناءً على كود اللغة
  String getNameByCode(String languageCode) {
    final language = PartLanguage.fromCode(languageCode);
    return getNameByLanguage(language);
  }

  /// التحقق من وجود المصطلح في لغة معينة
  bool hasTranslation(PartLanguage language) {
    final name = getNameByLanguage(language);
    return name.isNotEmpty;
  }
}

/// نموذج إعدادات لغة قطع الغيار
@freezed
abstract class PartLanguageSettings with _$PartLanguageSettings {
  const factory PartLanguageSettings({
    @Default(PartLanguage.arabic) PartLanguage primaryLanguage,
    @Default(PartLanguage.italian) PartLanguage secondaryLanguage,
    @Default(true) bool showSecondaryInParentheses,
    @Default(true) bool enableAutoTranslation,
    @Default(false) bool preferLibyanTerms,
  }) = _PartLanguageSettings;

  factory PartLanguageSettings.fromJson(Map<String, dynamic> json) =>
      _$PartLanguageSettingsFromJson(json);
}

/// امتداد لإعدادات اللغة
extension PartLanguageSettingsX on PartLanguageSettings {
  /// تنسيق اسم قطعة الغيار حسب الإعدادات
  String formatPartName(PartTermModel term) {
    final primaryName = term.getNameByLanguage(primaryLanguage);

    if (!showSecondaryInParentheses) {
      return primaryName;
    }

    final secondaryName = term.getNameByLanguage(secondaryLanguage);
    if (secondaryName.isEmpty || secondaryName == primaryName) {
      return primaryName;
    }

    return '$primaryName ($secondaryName)';
  }

  /// الحصول على اللغات بترتيب الأولوية
  List<PartLanguage> get languagePriority => [
    primaryLanguage,
    if (secondaryLanguage != primaryLanguage) secondaryLanguage,
    ...PartLanguage.values.where(
      (lang) => lang != primaryLanguage && lang != secondaryLanguage,
    ),
  ];
}

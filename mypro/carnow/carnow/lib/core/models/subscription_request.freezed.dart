// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subscription_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SubscriptionRequest {

@JsonKey(name: 'store_name') String get storeName; String get phone; String get city; String get address; String get description;@JsonKey(name: 'plan_type') String get planType; double get price;@JsonKey(name: 'user_id') String get userId;
/// Create a copy of SubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubscriptionRequestCopyWith<SubscriptionRequest> get copyWith => _$SubscriptionRequestCopyWithImpl<SubscriptionRequest>(this as SubscriptionRequest, _$identity);

  /// Serializes this SubscriptionRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubscriptionRequest&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.city, city) || other.city == city)&&(identical(other.address, address) || other.address == address)&&(identical(other.description, description) || other.description == description)&&(identical(other.planType, planType) || other.planType == planType)&&(identical(other.price, price) || other.price == price)&&(identical(other.userId, userId) || other.userId == userId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,storeName,phone,city,address,description,planType,price,userId);

@override
String toString() {
  return 'SubscriptionRequest(storeName: $storeName, phone: $phone, city: $city, address: $address, description: $description, planType: $planType, price: $price, userId: $userId)';
}


}

/// @nodoc
abstract mixin class $SubscriptionRequestCopyWith<$Res>  {
  factory $SubscriptionRequestCopyWith(SubscriptionRequest value, $Res Function(SubscriptionRequest) _then) = _$SubscriptionRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'store_name') String storeName, String phone, String city, String address, String description,@JsonKey(name: 'plan_type') String planType, double price,@JsonKey(name: 'user_id') String userId
});




}
/// @nodoc
class _$SubscriptionRequestCopyWithImpl<$Res>
    implements $SubscriptionRequestCopyWith<$Res> {
  _$SubscriptionRequestCopyWithImpl(this._self, this._then);

  final SubscriptionRequest _self;
  final $Res Function(SubscriptionRequest) _then;

/// Create a copy of SubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? storeName = null,Object? phone = null,Object? city = null,Object? address = null,Object? description = null,Object? planType = null,Object? price = null,Object? userId = null,}) {
  return _then(_self.copyWith(
storeName: null == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,planType: null == planType ? _self.planType : planType // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [SubscriptionRequest].
extension SubscriptionRequestPatterns on SubscriptionRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SubscriptionRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SubscriptionRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SubscriptionRequest value)  $default,){
final _that = this;
switch (_that) {
case _SubscriptionRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SubscriptionRequest value)?  $default,){
final _that = this;
switch (_that) {
case _SubscriptionRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'store_name')  String storeName,  String phone,  String city,  String address,  String description, @JsonKey(name: 'plan_type')  String planType,  double price, @JsonKey(name: 'user_id')  String userId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SubscriptionRequest() when $default != null:
return $default(_that.storeName,_that.phone,_that.city,_that.address,_that.description,_that.planType,_that.price,_that.userId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'store_name')  String storeName,  String phone,  String city,  String address,  String description, @JsonKey(name: 'plan_type')  String planType,  double price, @JsonKey(name: 'user_id')  String userId)  $default,) {final _that = this;
switch (_that) {
case _SubscriptionRequest():
return $default(_that.storeName,_that.phone,_that.city,_that.address,_that.description,_that.planType,_that.price,_that.userId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'store_name')  String storeName,  String phone,  String city,  String address,  String description, @JsonKey(name: 'plan_type')  String planType,  double price, @JsonKey(name: 'user_id')  String userId)?  $default,) {final _that = this;
switch (_that) {
case _SubscriptionRequest() when $default != null:
return $default(_that.storeName,_that.phone,_that.city,_that.address,_that.description,_that.planType,_that.price,_that.userId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SubscriptionRequest implements SubscriptionRequest {
  const _SubscriptionRequest({@JsonKey(name: 'store_name') required this.storeName, required this.phone, required this.city, required this.address, required this.description, @JsonKey(name: 'plan_type') required this.planType, required this.price, @JsonKey(name: 'user_id') required this.userId});
  factory _SubscriptionRequest.fromJson(Map<String, dynamic> json) => _$SubscriptionRequestFromJson(json);

@override@JsonKey(name: 'store_name') final  String storeName;
@override final  String phone;
@override final  String city;
@override final  String address;
@override final  String description;
@override@JsonKey(name: 'plan_type') final  String planType;
@override final  double price;
@override@JsonKey(name: 'user_id') final  String userId;

/// Create a copy of SubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubscriptionRequestCopyWith<_SubscriptionRequest> get copyWith => __$SubscriptionRequestCopyWithImpl<_SubscriptionRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SubscriptionRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubscriptionRequest&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.city, city) || other.city == city)&&(identical(other.address, address) || other.address == address)&&(identical(other.description, description) || other.description == description)&&(identical(other.planType, planType) || other.planType == planType)&&(identical(other.price, price) || other.price == price)&&(identical(other.userId, userId) || other.userId == userId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,storeName,phone,city,address,description,planType,price,userId);

@override
String toString() {
  return 'SubscriptionRequest(storeName: $storeName, phone: $phone, city: $city, address: $address, description: $description, planType: $planType, price: $price, userId: $userId)';
}


}

/// @nodoc
abstract mixin class _$SubscriptionRequestCopyWith<$Res> implements $SubscriptionRequestCopyWith<$Res> {
  factory _$SubscriptionRequestCopyWith(_SubscriptionRequest value, $Res Function(_SubscriptionRequest) _then) = __$SubscriptionRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'store_name') String storeName, String phone, String city, String address, String description,@JsonKey(name: 'plan_type') String planType, double price,@JsonKey(name: 'user_id') String userId
});




}
/// @nodoc
class __$SubscriptionRequestCopyWithImpl<$Res>
    implements _$SubscriptionRequestCopyWith<$Res> {
  __$SubscriptionRequestCopyWithImpl(this._self, this._then);

  final _SubscriptionRequest _self;
  final $Res Function(_SubscriptionRequest) _then;

/// Create a copy of SubscriptionRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? storeName = null,Object? phone = null,Object? city = null,Object? address = null,Object? description = null,Object? planType = null,Object? price = null,Object? userId = null,}) {
  return _then(_SubscriptionRequest(
storeName: null == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,planType: null == planType ? _self.planType : planType // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on

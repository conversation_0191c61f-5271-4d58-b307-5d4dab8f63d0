// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TicketMessageModel _$TicketMessageModelFromJson(Map<String, dynamic> json) =>
    _TicketMessageModel(
      message: json['message'] as String,
      id: (json['id'] as num?)?.toInt(),
      ticketId: (json['ticket_id'] as num?)?.toInt(),
      senderId: (json['sender_id'] as num?)?.toInt(),
      sender: json['sender'] == null
          ? null
          : UserModel.fromJson(json['sender'] as Map<String, dynamic>),
      senderType:
          $enumDecodeNullable(_$MessageSenderEnumMap, json['sender_type']) ??
          MessageSender.user,
      isInternal: json['is_internal'] as bool? ?? false,
      readAt: json['read_at'] == null
          ? null
          : DateTime.parse(json['read_at'] as String),
      sentAt: json['sent_at'] == null
          ? null
          : DateTime.parse(json['sent_at'] as String),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      isDeleted: json['is_deleted'] as bool? ?? false,
    );

Map<String, dynamic> _$TicketMessageModelToJson(_TicketMessageModel instance) =>
    <String, dynamic>{
      'message': instance.message,
      'id': instance.id,
      'ticket_id': instance.ticketId,
      'sender_id': instance.senderId,
      'sender': instance.sender,
      'sender_type': _$MessageSenderEnumMap[instance.senderType]!,
      'is_internal': instance.isInternal,
      'read_at': instance.readAt?.toIso8601String(),
      'sent_at': instance.sentAt?.toIso8601String(),
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
    };

const _$MessageSenderEnumMap = {
  MessageSender.user: 'user',
  MessageSender.agent: 'agent',
  MessageSender.system: 'system',
};

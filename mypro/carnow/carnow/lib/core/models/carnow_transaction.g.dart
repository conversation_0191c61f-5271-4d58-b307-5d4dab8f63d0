// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'carnow_transaction.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CarnowTransaction _$CarnowTransactionFromJson(Map<String, dynamic> json) =>
    _CarnowTransaction(
      id: json['id'] as String,
      walletId: json['wallet_id'] as String,
      userId: json['user_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      type: json['type'] as String,
      status: json['status'] as String,
      description: json['description'] as String,
      reference: json['reference'] as String?,
      fromWalletId: json['from_wallet_id'] as String?,
      toWalletId: json['to_wallet_id'] as String?,
      fee: (json['fee'] as num?)?.toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      processedAt: json['processed_at'] == null
          ? null
          : DateTime.parse(json['processed_at'] as String),
    );

Map<String, dynamic> _$CarnowTransactionToJson(_CarnowTransaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'wallet_id': instance.walletId,
      'user_id': instance.userId,
      'amount': instance.amount,
      'type': instance.type,
      'status': instance.status,
      'description': instance.description,
      'reference': instance.reference,
      'from_wallet_id': instance.fromWalletId,
      'to_wallet_id': instance.toWalletId,
      'fee': instance.fee,
      'metadata': instance.metadata,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'processed_at': instance.processedAt?.toIso8601String(),
    };

_CarnowFinancialOperation _$CarnowFinancialOperationFromJson(
  Map<String, dynamic> json,
) => _CarnowFinancialOperation(
  id: json['id'] as String,
  userId: json['userId'] as String,
  operationType: json['operationType'] as String,
  amount: (json['amount'] as num).toDouble(),
  currency: json['currency'] as String,
  status: json['status'] as String,
  description: json['description'] as String,
  reference: json['reference'] as String?,
  orderId: json['orderId'] as String?,
  productId: json['productId'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  processedAt: json['processedAt'] == null
      ? null
      : DateTime.parse(json['processedAt'] as String),
  failureReason: json['failureReason'] as String?,
);

Map<String, dynamic> _$CarnowFinancialOperationToJson(
  _CarnowFinancialOperation instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'operationType': instance.operationType,
  'amount': instance.amount,
  'currency': instance.currency,
  'status': instance.status,
  'description': instance.description,
  'reference': instance.reference,
  'orderId': instance.orderId,
  'productId': instance.productId,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'processedAt': instance.processedAt?.toIso8601String(),
  'failureReason': instance.failureReason,
};

_CreateFinancialOperationRequest _$CreateFinancialOperationRequestFromJson(
  Map<String, dynamic> json,
) => _CreateFinancialOperationRequest(
  userId: json['userId'] as String,
  operationType: json['operationType'] as String,
  amount: (json['amount'] as num).toDouble(),
  currency: json['currency'] as String? ?? 'SAR',
  description: json['description'] as String,
  reference: json['reference'] as String?,
  orderId: json['orderId'] as String?,
  productId: json['productId'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$CreateFinancialOperationRequestToJson(
  _CreateFinancialOperationRequest instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'operationType': instance.operationType,
  'amount': instance.amount,
  'currency': instance.currency,
  'description': instance.description,
  'reference': instance.reference,
  'orderId': instance.orderId,
  'productId': instance.productId,
  'metadata': instance.metadata,
};

_ProcessOperationRequest _$ProcessOperationRequestFromJson(
  Map<String, dynamic> json,
) => _ProcessOperationRequest(
  action: json['action'] as String,
  reason: json['reason'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ProcessOperationRequestToJson(
  _ProcessOperationRequest instance,
) => <String, dynamic>{
  'action': instance.action,
  'reason': instance.reason,
  'metadata': instance.metadata,
};

// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_message_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketMessageModel {

 String get message; int? get id;@JsonKey(name: 'ticket_id') int? get ticketId;@JsonKey(name: 'sender_id') int? get senderId; UserModel? get sender;@JsonKey(name: 'sender_type') MessageSender get senderType;@JsonKey(name: 'is_internal') bool get isInternal;@JsonKey(name: 'read_at') DateTime? get readAt;@JsonKey(name: 'sent_at') DateTime? get sentAt;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;@JsonKey(name: 'is_deleted', defaultValue: false) bool get isDeleted;// UI state
@JsonKey(includeFromJson: false, includeToJson: false) bool get isSending;@JsonKey(includeFromJson: false, includeToJson: false) bool get isError;@JsonKey(includeFromJson: false, includeToJson: false) String? get errorMessage;
/// Create a copy of TicketMessageModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TicketMessageModelCopyWith<TicketMessageModel> get copyWith => _$TicketMessageModelCopyWithImpl<TicketMessageModel>(this as TicketMessageModel, _$identity);

  /// Serializes this TicketMessageModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TicketMessageModel&&(identical(other.message, message) || other.message == message)&&(identical(other.id, id) || other.id == id)&&(identical(other.ticketId, ticketId) || other.ticketId == ticketId)&&(identical(other.senderId, senderId) || other.senderId == senderId)&&(identical(other.sender, sender) || other.sender == sender)&&(identical(other.senderType, senderType) || other.senderType == senderType)&&(identical(other.isInternal, isInternal) || other.isInternal == isInternal)&&(identical(other.readAt, readAt) || other.readAt == readAt)&&(identical(other.sentAt, sentAt) || other.sentAt == sentAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.isSending, isSending) || other.isSending == isSending)&&(identical(other.isError, isError) || other.isError == isError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,message,id,ticketId,senderId,sender,senderType,isInternal,readAt,sentAt,createdAt,updatedAt,isDeleted,isSending,isError,errorMessage);

@override
String toString() {
  return 'TicketMessageModel(message: $message, id: $id, ticketId: $ticketId, senderId: $senderId, sender: $sender, senderType: $senderType, isInternal: $isInternal, readAt: $readAt, sentAt: $sentAt, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, isSending: $isSending, isError: $isError, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $TicketMessageModelCopyWith<$Res>  {
  factory $TicketMessageModelCopyWith(TicketMessageModel value, $Res Function(TicketMessageModel) _then) = _$TicketMessageModelCopyWithImpl;
@useResult
$Res call({
 String message, int? id,@JsonKey(name: 'ticket_id') int? ticketId,@JsonKey(name: 'sender_id') int? senderId, UserModel? sender,@JsonKey(name: 'sender_type') MessageSender senderType,@JsonKey(name: 'is_internal') bool isInternal,@JsonKey(name: 'read_at') DateTime? readAt,@JsonKey(name: 'sent_at') DateTime? sentAt,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted', defaultValue: false) bool isDeleted,@JsonKey(includeFromJson: false, includeToJson: false) bool isSending,@JsonKey(includeFromJson: false, includeToJson: false) bool isError,@JsonKey(includeFromJson: false, includeToJson: false) String? errorMessage
});


$UserModelCopyWith<$Res>? get sender;

}
/// @nodoc
class _$TicketMessageModelCopyWithImpl<$Res>
    implements $TicketMessageModelCopyWith<$Res> {
  _$TicketMessageModelCopyWithImpl(this._self, this._then);

  final TicketMessageModel _self;
  final $Res Function(TicketMessageModel) _then;

/// Create a copy of TicketMessageModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? message = null,Object? id = freezed,Object? ticketId = freezed,Object? senderId = freezed,Object? sender = freezed,Object? senderType = null,Object? isInternal = null,Object? readAt = freezed,Object? sentAt = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? isSending = null,Object? isError = null,Object? errorMessage = freezed,}) {
  return _then(_self.copyWith(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,ticketId: freezed == ticketId ? _self.ticketId : ticketId // ignore: cast_nullable_to_non_nullable
as int?,senderId: freezed == senderId ? _self.senderId : senderId // ignore: cast_nullable_to_non_nullable
as int?,sender: freezed == sender ? _self.sender : sender // ignore: cast_nullable_to_non_nullable
as UserModel?,senderType: null == senderType ? _self.senderType : senderType // ignore: cast_nullable_to_non_nullable
as MessageSender,isInternal: null == isInternal ? _self.isInternal : isInternal // ignore: cast_nullable_to_non_nullable
as bool,readAt: freezed == readAt ? _self.readAt : readAt // ignore: cast_nullable_to_non_nullable
as DateTime?,sentAt: freezed == sentAt ? _self.sentAt : sentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,isSending: null == isSending ? _self.isSending : isSending // ignore: cast_nullable_to_non_nullable
as bool,isError: null == isError ? _self.isError : isError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of TicketMessageModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserModelCopyWith<$Res>? get sender {
    if (_self.sender == null) {
    return null;
  }

  return $UserModelCopyWith<$Res>(_self.sender!, (value) {
    return _then(_self.copyWith(sender: value));
  });
}
}


/// Adds pattern-matching-related methods to [TicketMessageModel].
extension TicketMessageModelPatterns on TicketMessageModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TicketMessageModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TicketMessageModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TicketMessageModel value)  $default,){
final _that = this;
switch (_that) {
case _TicketMessageModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TicketMessageModel value)?  $default,){
final _that = this;
switch (_that) {
case _TicketMessageModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String message,  int? id, @JsonKey(name: 'ticket_id')  int? ticketId, @JsonKey(name: 'sender_id')  int? senderId,  UserModel? sender, @JsonKey(name: 'sender_type')  MessageSender senderType, @JsonKey(name: 'is_internal')  bool isInternal, @JsonKey(name: 'read_at')  DateTime? readAt, @JsonKey(name: 'sent_at')  DateTime? sentAt, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted', defaultValue: false)  bool isDeleted, @JsonKey(includeFromJson: false, includeToJson: false)  bool isSending, @JsonKey(includeFromJson: false, includeToJson: false)  bool isError, @JsonKey(includeFromJson: false, includeToJson: false)  String? errorMessage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TicketMessageModel() when $default != null:
return $default(_that.message,_that.id,_that.ticketId,_that.senderId,_that.sender,_that.senderType,_that.isInternal,_that.readAt,_that.sentAt,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.isSending,_that.isError,_that.errorMessage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String message,  int? id, @JsonKey(name: 'ticket_id')  int? ticketId, @JsonKey(name: 'sender_id')  int? senderId,  UserModel? sender, @JsonKey(name: 'sender_type')  MessageSender senderType, @JsonKey(name: 'is_internal')  bool isInternal, @JsonKey(name: 'read_at')  DateTime? readAt, @JsonKey(name: 'sent_at')  DateTime? sentAt, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted', defaultValue: false)  bool isDeleted, @JsonKey(includeFromJson: false, includeToJson: false)  bool isSending, @JsonKey(includeFromJson: false, includeToJson: false)  bool isError, @JsonKey(includeFromJson: false, includeToJson: false)  String? errorMessage)  $default,) {final _that = this;
switch (_that) {
case _TicketMessageModel():
return $default(_that.message,_that.id,_that.ticketId,_that.senderId,_that.sender,_that.senderType,_that.isInternal,_that.readAt,_that.sentAt,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.isSending,_that.isError,_that.errorMessage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String message,  int? id, @JsonKey(name: 'ticket_id')  int? ticketId, @JsonKey(name: 'sender_id')  int? senderId,  UserModel? sender, @JsonKey(name: 'sender_type')  MessageSender senderType, @JsonKey(name: 'is_internal')  bool isInternal, @JsonKey(name: 'read_at')  DateTime? readAt, @JsonKey(name: 'sent_at')  DateTime? sentAt, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted', defaultValue: false)  bool isDeleted, @JsonKey(includeFromJson: false, includeToJson: false)  bool isSending, @JsonKey(includeFromJson: false, includeToJson: false)  bool isError, @JsonKey(includeFromJson: false, includeToJson: false)  String? errorMessage)?  $default,) {final _that = this;
switch (_that) {
case _TicketMessageModel() when $default != null:
return $default(_that.message,_that.id,_that.ticketId,_that.senderId,_that.sender,_that.senderType,_that.isInternal,_that.readAt,_that.sentAt,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.isSending,_that.isError,_that.errorMessage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TicketMessageModel extends TicketMessageModel {
  const _TicketMessageModel({required this.message, this.id, @JsonKey(name: 'ticket_id') this.ticketId, @JsonKey(name: 'sender_id') this.senderId, this.sender, @JsonKey(name: 'sender_type') this.senderType = MessageSender.user, @JsonKey(name: 'is_internal') this.isInternal = false, @JsonKey(name: 'read_at') this.readAt, @JsonKey(name: 'sent_at') this.sentAt, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'is_deleted', defaultValue: false) this.isDeleted = false, @JsonKey(includeFromJson: false, includeToJson: false) this.isSending = false, @JsonKey(includeFromJson: false, includeToJson: false) this.isError = false, @JsonKey(includeFromJson: false, includeToJson: false) this.errorMessage}): super._();
  factory _TicketMessageModel.fromJson(Map<String, dynamic> json) => _$TicketMessageModelFromJson(json);

@override final  String message;
@override final  int? id;
@override@JsonKey(name: 'ticket_id') final  int? ticketId;
@override@JsonKey(name: 'sender_id') final  int? senderId;
@override final  UserModel? sender;
@override@JsonKey(name: 'sender_type') final  MessageSender senderType;
@override@JsonKey(name: 'is_internal') final  bool isInternal;
@override@JsonKey(name: 'read_at') final  DateTime? readAt;
@override@JsonKey(name: 'sent_at') final  DateTime? sentAt;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override@JsonKey(name: 'is_deleted', defaultValue: false) final  bool isDeleted;
// UI state
@override@JsonKey(includeFromJson: false, includeToJson: false) final  bool isSending;
@override@JsonKey(includeFromJson: false, includeToJson: false) final  bool isError;
@override@JsonKey(includeFromJson: false, includeToJson: false) final  String? errorMessage;

/// Create a copy of TicketMessageModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TicketMessageModelCopyWith<_TicketMessageModel> get copyWith => __$TicketMessageModelCopyWithImpl<_TicketMessageModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TicketMessageModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TicketMessageModel&&(identical(other.message, message) || other.message == message)&&(identical(other.id, id) || other.id == id)&&(identical(other.ticketId, ticketId) || other.ticketId == ticketId)&&(identical(other.senderId, senderId) || other.senderId == senderId)&&(identical(other.sender, sender) || other.sender == sender)&&(identical(other.senderType, senderType) || other.senderType == senderType)&&(identical(other.isInternal, isInternal) || other.isInternal == isInternal)&&(identical(other.readAt, readAt) || other.readAt == readAt)&&(identical(other.sentAt, sentAt) || other.sentAt == sentAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.isSending, isSending) || other.isSending == isSending)&&(identical(other.isError, isError) || other.isError == isError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,message,id,ticketId,senderId,sender,senderType,isInternal,readAt,sentAt,createdAt,updatedAt,isDeleted,isSending,isError,errorMessage);

@override
String toString() {
  return 'TicketMessageModel(message: $message, id: $id, ticketId: $ticketId, senderId: $senderId, sender: $sender, senderType: $senderType, isInternal: $isInternal, readAt: $readAt, sentAt: $sentAt, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, isSending: $isSending, isError: $isError, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class _$TicketMessageModelCopyWith<$Res> implements $TicketMessageModelCopyWith<$Res> {
  factory _$TicketMessageModelCopyWith(_TicketMessageModel value, $Res Function(_TicketMessageModel) _then) = __$TicketMessageModelCopyWithImpl;
@override @useResult
$Res call({
 String message, int? id,@JsonKey(name: 'ticket_id') int? ticketId,@JsonKey(name: 'sender_id') int? senderId, UserModel? sender,@JsonKey(name: 'sender_type') MessageSender senderType,@JsonKey(name: 'is_internal') bool isInternal,@JsonKey(name: 'read_at') DateTime? readAt,@JsonKey(name: 'sent_at') DateTime? sentAt,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted', defaultValue: false) bool isDeleted,@JsonKey(includeFromJson: false, includeToJson: false) bool isSending,@JsonKey(includeFromJson: false, includeToJson: false) bool isError,@JsonKey(includeFromJson: false, includeToJson: false) String? errorMessage
});


@override $UserModelCopyWith<$Res>? get sender;

}
/// @nodoc
class __$TicketMessageModelCopyWithImpl<$Res>
    implements _$TicketMessageModelCopyWith<$Res> {
  __$TicketMessageModelCopyWithImpl(this._self, this._then);

  final _TicketMessageModel _self;
  final $Res Function(_TicketMessageModel) _then;

/// Create a copy of TicketMessageModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? id = freezed,Object? ticketId = freezed,Object? senderId = freezed,Object? sender = freezed,Object? senderType = null,Object? isInternal = null,Object? readAt = freezed,Object? sentAt = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? isSending = null,Object? isError = null,Object? errorMessage = freezed,}) {
  return _then(_TicketMessageModel(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,ticketId: freezed == ticketId ? _self.ticketId : ticketId // ignore: cast_nullable_to_non_nullable
as int?,senderId: freezed == senderId ? _self.senderId : senderId // ignore: cast_nullable_to_non_nullable
as int?,sender: freezed == sender ? _self.sender : sender // ignore: cast_nullable_to_non_nullable
as UserModel?,senderType: null == senderType ? _self.senderType : senderType // ignore: cast_nullable_to_non_nullable
as MessageSender,isInternal: null == isInternal ? _self.isInternal : isInternal // ignore: cast_nullable_to_non_nullable
as bool,readAt: freezed == readAt ? _self.readAt : readAt // ignore: cast_nullable_to_non_nullable
as DateTime?,sentAt: freezed == sentAt ? _self.sentAt : sentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,isSending: null == isSending ? _self.isSending : isSending // ignore: cast_nullable_to_non_nullable
as bool,isError: null == isError ? _self.isError : isError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of TicketMessageModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserModelCopyWith<$Res>? get sender {
    if (_self.sender == null) {
    return null;
  }

  return $UserModelCopyWith<$Res>(_self.sender!, (value) {
    return _then(_self.copyWith(sender: value));
  });
}
}

// dart format on

import 'package:freezed_annotation/freezed_annotation.dart';
import 'enums.dart';

part 'product_category_model.freezed.dart';
part 'product_category_model.g.dart';

@freezed
abstract class ProductCategoryModel with _$ProductCategoryModel {
  const factory ProductCategoryModel({
    required String id,
    required String name,
    String? nameAr,
    String? nameEn,
    String? nameIt,
    String? description,
    String? descriptionAr,
    String? descriptionEn,
    String? descriptionIt,
    String? parentId,
    @Default(ProductType.other) ProductType productType,
    String? iconUrl,
    String? imageUrl,
    String? colorCode,
    @Default(true) bool isActive,
    @Default(0) int sortOrder,
    @Default(0) int level,
    @Default([]) List<String> requiredFields,
    @Default([]) List<String> optionalFields,
    Map<String, dynamic>? formTemplate,
    @Default([]) List<ProductCategoryModel> subcategories,
    DateTime? createdAt,
    DateTime? updatedAt,
    // الحقول الجديدة للنظام المحدث
    VehicleCategory? vehicleCategory,
    AutoPartsCategory? autoPartsCategory,
    AutomotiveElectronicsCategory? electronicsCategory,
    ToolsCategory? toolsCategory,
    AccessoriesCategory? accessoriesCategory,
  }) = _ProductCategoryModel;

  factory ProductCategoryModel.fromJson(Map<String, dynamic> json) =>
      _$ProductCategoryModelFromJson(json);
}

// Extension for category helpers
extension ProductCategoryModelX on ProductCategoryModel {
  /// Returns the localized name based on the provided language code
  String getLocalizedName(String langCode) {
    switch (langCode) {
      case 'ar':
        return nameAr ?? name;
      case 'en':
        return nameEn ?? name;
      case 'it':
        return nameIt ?? name;
      default:
        return name;
    }
  }

  /// Returns the localized description based on the provided language code
  String? getLocalizedDescription(String langCode) {
    switch (langCode) {
      case 'ar':
        return descriptionAr ?? description;
      case 'en':
        return descriptionEn ?? description;
      case 'it':
        return descriptionIt ?? description;
      default:
        return description;
    }
  }

  /// Check if this category has subcategories
  bool get hasSubcategories => subcategories.isNotEmpty;

  /// Check if this is a root category (no parent)
  bool get isRoot => parentId == null;

  /// Get all descendant categories (recursive)
  List<ProductCategoryModel> getAllDescendants() {
    final List<ProductCategoryModel> descendants = [];
    for (final subcategory in subcategories) {
      descendants.add(subcategory);
      descendants.addAll(subcategory.getAllDescendants());
    }
    return descendants;
  }

  /// Get category path as string (e.g., "السيارات > قطع الغيار > المحرك")
  String getCategoryPath([String separator = ' > ']) {
    // This would need parent information to build full path
    // For now, just return the name
    return name;
  }

  /// Get category display icon
  String get displayIcon {
    switch (productType) {
      case ProductType.vehicles:
        return '🚗';
      case ProductType.autoParts:
        return '⚙️';
      case ProductType.electronics:
        return '📱';
      case ProductType.tools:
        return '🔧';
      case ProductType.accessories:
        return '✨';
      case ProductType.maintenance:
        return '🛠️';
      case ProductType.other:
        return '📦';
    }
  }

  /// Get category color code
  String get displayColor {
    switch (productType) {
      case ProductType.vehicles:
        return '#1976D2';
      case ProductType.autoParts:
        return '#FF6B35';
      case ProductType.electronics:
        return '#00C853';
      case ProductType.tools:
        return '#9C27B0';
      case ProductType.accessories:
        return '#FF9800';
      case ProductType.maintenance:
        return '#795548';
      case ProductType.other:
        return '#607D8B';
    }
  }
}

// إعدادات الفئات المحدثة والمتخصصة
class CategoryConfigurations {
  // إعدادات فئات السيارات والمركبات
  static const Map<VehicleCategory, Map<String, dynamic>> vehicleConfigs = {
    VehicleCategory.passengerCars: {
      'name': 'السيارات الشخصية',
      'nameEn': 'Passenger Cars',
      'icon': '🚗',
      'colorCode': '#1976D2',
      'requiredFields': ['make', 'model', 'year', 'mileage', 'fuelType'],
      'optionalFields': [
        'transmissionType',
        'engineSize',
        'color',
        'condition',
      ],
    },
    VehicleCategory.commercialVehicles: {
      'name': 'المركبات التجارية',
      'nameEn': 'Commercial Vehicles',
      'icon': '🚛',
      'colorCode': '#FF6B35',
      'requiredFields': ['make', 'model', 'year', 'loadCapacity', 'fuelType'],
      'optionalFields': ['mileage', 'transmissionType', 'condition'],
    },
    VehicleCategory.motorcycles: {
      'name': 'الدراجات النارية',
      'nameEn': 'Motorcycles',
      'icon': '🏍️',
      'colorCode': '#F44336',
      'requiredFields': ['make', 'model', 'year', 'engineSize', 'fuelType'],
      'optionalFields': ['mileage', 'color', 'condition'],
    },
  };

  // إعدادات فئات قطع الغيار المفصلة
  static const Map<AutoPartsCategory, Map<String, dynamic>> autoPartsConfigs = {
    AutoPartsCategory.engineParts: {
      'name': 'قطع المحرك',
      'nameEn': 'Engine Parts',
      'icon': '⚙️',
      'colorCode': '#FF6B35',
      'requiredFields': [
        'partName',
        'partNumber',
        'brand',
        'compatibleVehicles',
      ],
      'optionalFields': ['manufacturer', 'warranty', 'condition', 'oem'],
      'subcategories': [
        'مكابس المحرك',
        'صبابات المحرك',
        'حلقات المكابس',
        'سلاسل التوقيت',
        'رؤوس المحرك',
        'بلوكات المحرك',
      ],
    },
    AutoPartsCategory.transmissionParts: {
      'name': 'قطع ناقل الحركة',
      'nameEn': 'Transmission Parts',
      'icon': '⚡',
      'colorCode': '#2196F3',
      'requiredFields': [
        'partName',
        'partNumber',
        'transmissionType',
        'compatibleVehicles',
      ],
      'optionalFields': ['brand', 'warranty', 'condition'],
      'subcategories': [
        'فلاتر زيت القير',
        'طقم الكلتش',
        'ديسك الكلتش',
        'مضخة القير',
        'حساس القير',
      ],
    },
    AutoPartsCategory.brakeSystem: {
      'name': 'نظام الفرامل',
      'nameEn': 'Brake System',
      'icon': '🔴',
      'colorCode': '#F44336',
      'requiredFields': [
        'partName',
        'partNumber',
        'brakeType',
        'compatibleVehicles',
      ],
      'optionalFields': ['brand', 'warranty', 'condition'],
      'subcategories': [
        'فحمات الفرامل',
        'أسطوانات الفرامل',
        'أقراص الفرامل',
        'خراطيم الفرامل',
        'سائل الفرامل',
      ],
    },
    AutoPartsCategory.electricalElectronic: {
      'name': 'النظام الكهربائي والإلكتروني',
      'nameEn': 'Electrical & Electronic System',
      'icon': '⚡',
      'colorCode': '#FF9800',
      'requiredFields': [
        'partName',
        'partNumber',
        'voltage',
        'compatibleVehicles',
      ],
      'optionalFields': ['brand', 'warranty', 'condition', 'amperage'],
      'subcategories': [
        'البطاريات',
        'المولدات (الدينامو)',
        'البادئات (السلف)',
        'الأسلاك الكهربائية',
        'الحساسات الإلكترونية',
        'وحدات التحكم',
      ],
    },
    AutoPartsCategory.tiresWheels: {
      'name': 'الإطارات والعجلات',
      'nameEn': 'Tires & Wheels',
      'icon': '⚫',
      'colorCode': '#424242',
      'requiredFields': ['tireSize', 'brand', 'tireType', 'compatibleVehicles'],
      'optionalFields': ['seasonType', 'loadIndex', 'speedRating', 'condition'],
      'subcategories': [
        'إطارات صيفية',
        'إطارات شتوية',
        'إطارات رياضية',
        'جنوط المنيوم',
        'جنوط حديد',
        'أغطية الجنوط',
      ],
    },
  };

  // إعدادات فئات الإلكترونيات
  static const Map<AutomotiveElectronicsCategory, Map<String, dynamic>>
  electronicsConfigs = {
    AutomotiveElectronicsCategory.audioVideo: {
      'name': 'أنظمة الصوت والمرئيات',
      'nameEn': 'Audio & Video Systems',
      'icon': '🔊',
      'colorCode': '#9C27B0',
      'requiredFields': ['productName', 'brand', 'model', 'compatibility'],
      'optionalFields': ['warranty', 'condition', 'features'],
    },
    AutomotiveElectronicsCategory.navigationGPS: {
      'name': 'أنظمة الملاحة والخرائط',
      'nameEn': 'Navigation & GPS Systems',
      'icon': '🗺️',
      'colorCode': '#00BCD4',
      'requiredFields': ['productName', 'brand', 'screenSize', 'mapCoverage'],
      'optionalFields': ['updateFrequency', 'warranty', 'condition'],
    },
    AutomotiveElectronicsCategory.securitySafety: {
      'name': 'أنظمة الأمان والحماية',
      'nameEn': 'Security & Safety Systems',
      'icon': '🛡️',
      'colorCode': '#4CAF50',
      'requiredFields': ['productName', 'brand', 'securityType', 'coverage'],
      'optionalFields': ['remoteControl', 'warranty', 'installation'],
    },
  };

  // إعدادات فئات الأدوات
  static const Map<ToolsCategory, Map<String, dynamic>> toolsConfigs = {
    ToolsCategory.handTools: {
      'name': 'الأدوات اليدوية',
      'nameEn': 'Hand Tools',
      'icon': '🔧',
      'colorCode': '#607D8B',
      'requiredFields': ['toolName', 'brand', 'size', 'material'],
      'optionalFields': ['warranty', 'condition', 'set'],
    },
    ToolsCategory.powerTools: {
      'name': 'الأدوات الكهربائية',
      'nameEn': 'Power Tools',
      'icon': '⚡',
      'colorCode': '#FF5722',
      'requiredFields': ['toolName', 'brand', 'powerRating', 'voltage'],
      'optionalFields': ['batteryType', 'warranty', 'accessories'],
    },
    ToolsCategory.diagnosticEquipment: {
      'name': 'معدات التشخيص',
      'nameEn': 'Diagnostic Equipment',
      'icon': '📊',
      'colorCode': '#3F51B5',
      'requiredFields': ['equipmentName', 'brand', 'compatibility', 'features'],
      'optionalFields': ['softwareVersion', 'updateSupport', 'warranty'],
    },
  };
}

// مساعدات بناء الفئات المحدثة
class CategoryHelper {
  /// إنشاء فئة رئيسية للمنتجات
  static ProductCategoryModel createMainCategory({
    required String id,
    required ProductType productType,
    required String name,
    String? nameEn,
    String? description,
    String? imageUrl,
    int sortOrder = 0,
  }) => ProductCategoryModel(
    id: id,
    name: name,
    nameEn: nameEn ?? name,
    description: description,
    productType: productType,
    imageUrl: imageUrl,
    colorCode: _getColorForProductType(productType),
    sortOrder: sortOrder,
  );

  /// إنشاء فئة فرعية لقطع الغيار
  static ProductCategoryModel createAutoPartsSubCategory({
    required String id,
    required String parentId,
    required AutoPartsCategory category,
    required String name,
    String? nameEn,
    String? description,
    int sortOrder = 0,
  }) => ProductCategoryModel(
    id: id,
    name: name,
    nameEn: nameEn ?? name,
    description: description,
    parentId: parentId,
    productType: ProductType.autoParts,
    autoPartsCategory: category,
    sortOrder: sortOrder,
    level: 1,
    requiredFields:
        CategoryConfigurations.autoPartsConfigs[category]?['requiredFields'] ??
        [],
    optionalFields:
        CategoryConfigurations.autoPartsConfigs[category]?['optionalFields'] ??
        [],
  );

  /// إنشاء فئة فرعية للمركبات
  static ProductCategoryModel createVehicleSubCategory({
    required String id,
    required String parentId,
    required VehicleCategory category,
    required String name,
    String? nameEn,
    String? description,
    int sortOrder = 0,
  }) => ProductCategoryModel(
    id: id,
    name: name,
    nameEn: nameEn ?? name,
    description: description,
    parentId: parentId,
    productType: ProductType.vehicles,
    vehicleCategory: category,
    sortOrder: sortOrder,
    level: 1,
    requiredFields:
        CategoryConfigurations.vehicleConfigs[category]?['requiredFields'] ??
        [],
    optionalFields:
        CategoryConfigurations.vehicleConfigs[category]?['optionalFields'] ??
        [],
  );

  /// الحصول على جميع الفئات الرئيسية
  static List<ProductType> getMainCategories() => ProductType.values;

  /// الحصول على الفئات الفرعية لنوع منتج معين
  static List<dynamic> getSubCategories(ProductType productType) {
    switch (productType) {
      case ProductType.vehicles:
        return VehicleCategory.values;
      case ProductType.autoParts:
        return AutoPartsCategory.values;
      case ProductType.electronics:
        return AutomotiveElectronicsCategory.values;
      case ProductType.tools:
        return ToolsCategory.values;
      case ProductType.accessories:
        return AccessoriesCategory.values;
      case ProductType.maintenance:
      case ProductType.other:
        return [];
    }
  }

  /// الحصول على لون الفئة
  static String _getColorForProductType(ProductType type) {
    switch (type) {
      case ProductType.vehicles:
        return '#1976D2';
      case ProductType.autoParts:
        return '#FF6B35';
      case ProductType.electronics:
        return '#00C853';
      case ProductType.tools:
        return '#9C27B0';
      case ProductType.accessories:
        return '#FF9800';
      case ProductType.maintenance:
        return '#795548';
      case ProductType.other:
        return '#607D8B';
    }
  }

  /// التحقق من صحة فئة المنتج
  static bool isValidCategoryForProduct(
    ProductType productType,
    dynamic subCategory,
  ) {
    switch (productType) {
      case ProductType.vehicles:
        return subCategory is VehicleCategory;
      case ProductType.autoParts:
        return subCategory is AutoPartsCategory;
      case ProductType.electronics:
        return subCategory is AutomotiveElectronicsCategory;
      case ProductType.tools:
        return subCategory is ToolsCategory;
      case ProductType.accessories:
        return subCategory is AccessoriesCategory;
      case ProductType.maintenance:
      case ProductType.other:
        return true;
    }
  }
}

/// A class representing a status message with an error code
/// Used for providing standardized error messages across the app
class AsyncValueStatus implements Exception {
  /// Creates a new AsyncValueStatus
  const AsyncValueStatus({
    required this.code,
    required this.message,
    this.data,
  });

  /// The error code for categorizing the error
  final String code;

  /// The human-readable error message
  final String message;

  /// Optional additional data related to the error
  final Map<String, dynamic>? data;

  @override
  String toString() => 'AsyncValueStatus: [$code] $message';
}

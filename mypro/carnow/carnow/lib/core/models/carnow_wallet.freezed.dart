// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'carnow_wallet.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CarnowWallet {

 String get id;@JsonKey(name: 'user_id') String get userId; double get balance;@JsonKey(name: 'available_balance') double get availableBalance;@JsonKey(name: 'frozen_balance') double get frozenBalance; String get currency;@JsonKey(name: 'is_active') bool get isActive;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime get updatedAt;@JsonKey(name: 'last_transaction_id') String? get lastTransactionId;@JsonKey(name: 'last_transaction_date') DateTime? get lastTransactionDate;
/// Create a copy of CarnowWallet
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CarnowWalletCopyWith<CarnowWallet> get copyWith => _$CarnowWalletCopyWithImpl<CarnowWallet>(this as CarnowWallet, _$identity);

  /// Serializes this CarnowWallet to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CarnowWallet&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.availableBalance, availableBalance) || other.availableBalance == availableBalance)&&(identical(other.frozenBalance, frozenBalance) || other.frozenBalance == frozenBalance)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.lastTransactionId, lastTransactionId) || other.lastTransactionId == lastTransactionId)&&(identical(other.lastTransactionDate, lastTransactionDate) || other.lastTransactionDate == lastTransactionDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,balance,availableBalance,frozenBalance,currency,isActive,createdAt,updatedAt,lastTransactionId,lastTransactionDate);

@override
String toString() {
  return 'CarnowWallet(id: $id, userId: $userId, balance: $balance, availableBalance: $availableBalance, frozenBalance: $frozenBalance, currency: $currency, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, lastTransactionId: $lastTransactionId, lastTransactionDate: $lastTransactionDate)';
}


}

/// @nodoc
abstract mixin class $CarnowWalletCopyWith<$Res>  {
  factory $CarnowWalletCopyWith(CarnowWallet value, $Res Function(CarnowWallet) _then) = _$CarnowWalletCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'user_id') String userId, double balance,@JsonKey(name: 'available_balance') double availableBalance,@JsonKey(name: 'frozen_balance') double frozenBalance, String currency,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'last_transaction_id') String? lastTransactionId,@JsonKey(name: 'last_transaction_date') DateTime? lastTransactionDate
});




}
/// @nodoc
class _$CarnowWalletCopyWithImpl<$Res>
    implements $CarnowWalletCopyWith<$Res> {
  _$CarnowWalletCopyWithImpl(this._self, this._then);

  final CarnowWallet _self;
  final $Res Function(CarnowWallet) _then;

/// Create a copy of CarnowWallet
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? balance = null,Object? availableBalance = null,Object? frozenBalance = null,Object? currency = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,Object? lastTransactionId = freezed,Object? lastTransactionDate = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as double,availableBalance: null == availableBalance ? _self.availableBalance : availableBalance // ignore: cast_nullable_to_non_nullable
as double,frozenBalance: null == frozenBalance ? _self.frozenBalance : frozenBalance // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,lastTransactionId: freezed == lastTransactionId ? _self.lastTransactionId : lastTransactionId // ignore: cast_nullable_to_non_nullable
as String?,lastTransactionDate: freezed == lastTransactionDate ? _self.lastTransactionDate : lastTransactionDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [CarnowWallet].
extension CarnowWalletPatterns on CarnowWallet {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CarnowWallet value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CarnowWallet() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CarnowWallet value)  $default,){
final _that = this;
switch (_that) {
case _CarnowWallet():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CarnowWallet value)?  $default,){
final _that = this;
switch (_that) {
case _CarnowWallet() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'user_id')  String userId,  double balance, @JsonKey(name: 'available_balance')  double availableBalance, @JsonKey(name: 'frozen_balance')  double frozenBalance,  String currency, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'last_transaction_id')  String? lastTransactionId, @JsonKey(name: 'last_transaction_date')  DateTime? lastTransactionDate)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CarnowWallet() when $default != null:
return $default(_that.id,_that.userId,_that.balance,_that.availableBalance,_that.frozenBalance,_that.currency,_that.isActive,_that.createdAt,_that.updatedAt,_that.lastTransactionId,_that.lastTransactionDate);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'user_id')  String userId,  double balance, @JsonKey(name: 'available_balance')  double availableBalance, @JsonKey(name: 'frozen_balance')  double frozenBalance,  String currency, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'last_transaction_id')  String? lastTransactionId, @JsonKey(name: 'last_transaction_date')  DateTime? lastTransactionDate)  $default,) {final _that = this;
switch (_that) {
case _CarnowWallet():
return $default(_that.id,_that.userId,_that.balance,_that.availableBalance,_that.frozenBalance,_that.currency,_that.isActive,_that.createdAt,_that.updatedAt,_that.lastTransactionId,_that.lastTransactionDate);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'user_id')  String userId,  double balance, @JsonKey(name: 'available_balance')  double availableBalance, @JsonKey(name: 'frozen_balance')  double frozenBalance,  String currency, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'last_transaction_id')  String? lastTransactionId, @JsonKey(name: 'last_transaction_date')  DateTime? lastTransactionDate)?  $default,) {final _that = this;
switch (_that) {
case _CarnowWallet() when $default != null:
return $default(_that.id,_that.userId,_that.balance,_that.availableBalance,_that.frozenBalance,_that.currency,_that.isActive,_that.createdAt,_that.updatedAt,_that.lastTransactionId,_that.lastTransactionDate);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CarnowWallet implements CarnowWallet {
  const _CarnowWallet({required this.id, @JsonKey(name: 'user_id') required this.userId, this.balance = 0.0, @JsonKey(name: 'available_balance') this.availableBalance = 0.0, @JsonKey(name: 'frozen_balance') this.frozenBalance = 0.0, this.currency = 'LYD', @JsonKey(name: 'is_active') this.isActive = true, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt, @JsonKey(name: 'last_transaction_id') this.lastTransactionId, @JsonKey(name: 'last_transaction_date') this.lastTransactionDate});
  factory _CarnowWallet.fromJson(Map<String, dynamic> json) => _$CarnowWalletFromJson(json);

@override final  String id;
@override@JsonKey(name: 'user_id') final  String userId;
@override@JsonKey() final  double balance;
@override@JsonKey(name: 'available_balance') final  double availableBalance;
@override@JsonKey(name: 'frozen_balance') final  double frozenBalance;
@override@JsonKey() final  String currency;
@override@JsonKey(name: 'is_active') final  bool isActive;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;
@override@JsonKey(name: 'last_transaction_id') final  String? lastTransactionId;
@override@JsonKey(name: 'last_transaction_date') final  DateTime? lastTransactionDate;

/// Create a copy of CarnowWallet
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CarnowWalletCopyWith<_CarnowWallet> get copyWith => __$CarnowWalletCopyWithImpl<_CarnowWallet>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CarnowWalletToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CarnowWallet&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.availableBalance, availableBalance) || other.availableBalance == availableBalance)&&(identical(other.frozenBalance, frozenBalance) || other.frozenBalance == frozenBalance)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.lastTransactionId, lastTransactionId) || other.lastTransactionId == lastTransactionId)&&(identical(other.lastTransactionDate, lastTransactionDate) || other.lastTransactionDate == lastTransactionDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,balance,availableBalance,frozenBalance,currency,isActive,createdAt,updatedAt,lastTransactionId,lastTransactionDate);

@override
String toString() {
  return 'CarnowWallet(id: $id, userId: $userId, balance: $balance, availableBalance: $availableBalance, frozenBalance: $frozenBalance, currency: $currency, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, lastTransactionId: $lastTransactionId, lastTransactionDate: $lastTransactionDate)';
}


}

/// @nodoc
abstract mixin class _$CarnowWalletCopyWith<$Res> implements $CarnowWalletCopyWith<$Res> {
  factory _$CarnowWalletCopyWith(_CarnowWallet value, $Res Function(_CarnowWallet) _then) = __$CarnowWalletCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'user_id') String userId, double balance,@JsonKey(name: 'available_balance') double availableBalance,@JsonKey(name: 'frozen_balance') double frozenBalance, String currency,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'last_transaction_id') String? lastTransactionId,@JsonKey(name: 'last_transaction_date') DateTime? lastTransactionDate
});




}
/// @nodoc
class __$CarnowWalletCopyWithImpl<$Res>
    implements _$CarnowWalletCopyWith<$Res> {
  __$CarnowWalletCopyWithImpl(this._self, this._then);

  final _CarnowWallet _self;
  final $Res Function(_CarnowWallet) _then;

/// Create a copy of CarnowWallet
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? balance = null,Object? availableBalance = null,Object? frozenBalance = null,Object? currency = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,Object? lastTransactionId = freezed,Object? lastTransactionDate = freezed,}) {
  return _then(_CarnowWallet(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as double,availableBalance: null == availableBalance ? _self.availableBalance : availableBalance // ignore: cast_nullable_to_non_nullable
as double,frozenBalance: null == frozenBalance ? _self.frozenBalance : frozenBalance // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,lastTransactionId: freezed == lastTransactionId ? _self.lastTransactionId : lastTransactionId // ignore: cast_nullable_to_non_nullable
as String?,lastTransactionDate: freezed == lastTransactionDate ? _self.lastTransactionDate : lastTransactionDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$CreateWalletRequest {

 String get userId; String get currency;
/// Create a copy of CreateWalletRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateWalletRequestCopyWith<CreateWalletRequest> get copyWith => _$CreateWalletRequestCopyWithImpl<CreateWalletRequest>(this as CreateWalletRequest, _$identity);

  /// Serializes this CreateWalletRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateWalletRequest&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.currency, currency) || other.currency == currency));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,currency);

@override
String toString() {
  return 'CreateWalletRequest(userId: $userId, currency: $currency)';
}


}

/// @nodoc
abstract mixin class $CreateWalletRequestCopyWith<$Res>  {
  factory $CreateWalletRequestCopyWith(CreateWalletRequest value, $Res Function(CreateWalletRequest) _then) = _$CreateWalletRequestCopyWithImpl;
@useResult
$Res call({
 String userId, String currency
});




}
/// @nodoc
class _$CreateWalletRequestCopyWithImpl<$Res>
    implements $CreateWalletRequestCopyWith<$Res> {
  _$CreateWalletRequestCopyWithImpl(this._self, this._then);

  final CreateWalletRequest _self;
  final $Res Function(CreateWalletRequest) _then;

/// Create a copy of CreateWalletRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? currency = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [CreateWalletRequest].
extension CreateWalletRequestPatterns on CreateWalletRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CreateWalletRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CreateWalletRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CreateWalletRequest value)  $default,){
final _that = this;
switch (_that) {
case _CreateWalletRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CreateWalletRequest value)?  $default,){
final _that = this;
switch (_that) {
case _CreateWalletRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  String currency)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CreateWalletRequest() when $default != null:
return $default(_that.userId,_that.currency);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  String currency)  $default,) {final _that = this;
switch (_that) {
case _CreateWalletRequest():
return $default(_that.userId,_that.currency);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  String currency)?  $default,) {final _that = this;
switch (_that) {
case _CreateWalletRequest() when $default != null:
return $default(_that.userId,_that.currency);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CreateWalletRequest implements CreateWalletRequest {
  const _CreateWalletRequest({required this.userId, this.currency = 'LYD'});
  factory _CreateWalletRequest.fromJson(Map<String, dynamic> json) => _$CreateWalletRequestFromJson(json);

@override final  String userId;
@override@JsonKey() final  String currency;

/// Create a copy of CreateWalletRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreateWalletRequestCopyWith<_CreateWalletRequest> get copyWith => __$CreateWalletRequestCopyWithImpl<_CreateWalletRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CreateWalletRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CreateWalletRequest&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.currency, currency) || other.currency == currency));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,currency);

@override
String toString() {
  return 'CreateWalletRequest(userId: $userId, currency: $currency)';
}


}

/// @nodoc
abstract mixin class _$CreateWalletRequestCopyWith<$Res> implements $CreateWalletRequestCopyWith<$Res> {
  factory _$CreateWalletRequestCopyWith(_CreateWalletRequest value, $Res Function(_CreateWalletRequest) _then) = __$CreateWalletRequestCopyWithImpl;
@override @useResult
$Res call({
 String userId, String currency
});




}
/// @nodoc
class __$CreateWalletRequestCopyWithImpl<$Res>
    implements _$CreateWalletRequestCopyWith<$Res> {
  __$CreateWalletRequestCopyWithImpl(this._self, this._then);

  final _CreateWalletRequest _self;
  final $Res Function(_CreateWalletRequest) _then;

/// Create a copy of CreateWalletRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? currency = null,}) {
  return _then(_CreateWalletRequest(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$DepositRequest {

 double get amount; String get description; String? get reference; Map<String, dynamic>? get metadata;
/// Create a copy of DepositRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositRequestCopyWith<DepositRequest> get copyWith => _$DepositRequestCopyWithImpl<DepositRequest>(this as DepositRequest, _$identity);

  /// Serializes this DepositRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositRequest&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.description, description) || other.description == description)&&(identical(other.reference, reference) || other.reference == reference)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,amount,description,reference,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'DepositRequest(amount: $amount, description: $description, reference: $reference, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $DepositRequestCopyWith<$Res>  {
  factory $DepositRequestCopyWith(DepositRequest value, $Res Function(DepositRequest) _then) = _$DepositRequestCopyWithImpl;
@useResult
$Res call({
 double amount, String description, String? reference, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$DepositRequestCopyWithImpl<$Res>
    implements $DepositRequestCopyWith<$Res> {
  _$DepositRequestCopyWithImpl(this._self, this._then);

  final DepositRequest _self;
  final $Res Function(DepositRequest) _then;

/// Create a copy of DepositRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? amount = null,Object? description = null,Object? reference = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [DepositRequest].
extension DepositRequestPatterns on DepositRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DepositRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DepositRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DepositRequest value)  $default,){
final _that = this;
switch (_that) {
case _DepositRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DepositRequest value)?  $default,){
final _that = this;
switch (_that) {
case _DepositRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double amount,  String description,  String? reference,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DepositRequest() when $default != null:
return $default(_that.amount,_that.description,_that.reference,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double amount,  String description,  String? reference,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _DepositRequest():
return $default(_that.amount,_that.description,_that.reference,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double amount,  String description,  String? reference,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _DepositRequest() when $default != null:
return $default(_that.amount,_that.description,_that.reference,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DepositRequest implements DepositRequest {
  const _DepositRequest({required this.amount, required this.description, this.reference, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _DepositRequest.fromJson(Map<String, dynamic> json) => _$DepositRequestFromJson(json);

@override final  double amount;
@override final  String description;
@override final  String? reference;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of DepositRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositRequestCopyWith<_DepositRequest> get copyWith => __$DepositRequestCopyWithImpl<_DepositRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DepositRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositRequest&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.description, description) || other.description == description)&&(identical(other.reference, reference) || other.reference == reference)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,amount,description,reference,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'DepositRequest(amount: $amount, description: $description, reference: $reference, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$DepositRequestCopyWith<$Res> implements $DepositRequestCopyWith<$Res> {
  factory _$DepositRequestCopyWith(_DepositRequest value, $Res Function(_DepositRequest) _then) = __$DepositRequestCopyWithImpl;
@override @useResult
$Res call({
 double amount, String description, String? reference, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$DepositRequestCopyWithImpl<$Res>
    implements _$DepositRequestCopyWith<$Res> {
  __$DepositRequestCopyWithImpl(this._self, this._then);

  final _DepositRequest _self;
  final $Res Function(_DepositRequest) _then;

/// Create a copy of DepositRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? amount = null,Object? description = null,Object? reference = freezed,Object? metadata = freezed,}) {
  return _then(_DepositRequest(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$WithdrawRequest {

 double get amount; String get description; String? get reference; Map<String, dynamic>? get metadata;
/// Create a copy of WithdrawRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawRequestCopyWith<WithdrawRequest> get copyWith => _$WithdrawRequestCopyWithImpl<WithdrawRequest>(this as WithdrawRequest, _$identity);

  /// Serializes this WithdrawRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawRequest&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.description, description) || other.description == description)&&(identical(other.reference, reference) || other.reference == reference)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,amount,description,reference,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'WithdrawRequest(amount: $amount, description: $description, reference: $reference, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $WithdrawRequestCopyWith<$Res>  {
  factory $WithdrawRequestCopyWith(WithdrawRequest value, $Res Function(WithdrawRequest) _then) = _$WithdrawRequestCopyWithImpl;
@useResult
$Res call({
 double amount, String description, String? reference, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$WithdrawRequestCopyWithImpl<$Res>
    implements $WithdrawRequestCopyWith<$Res> {
  _$WithdrawRequestCopyWithImpl(this._self, this._then);

  final WithdrawRequest _self;
  final $Res Function(WithdrawRequest) _then;

/// Create a copy of WithdrawRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? amount = null,Object? description = null,Object? reference = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [WithdrawRequest].
extension WithdrawRequestPatterns on WithdrawRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WithdrawRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WithdrawRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WithdrawRequest value)  $default,){
final _that = this;
switch (_that) {
case _WithdrawRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WithdrawRequest value)?  $default,){
final _that = this;
switch (_that) {
case _WithdrawRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double amount,  String description,  String? reference,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WithdrawRequest() when $default != null:
return $default(_that.amount,_that.description,_that.reference,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double amount,  String description,  String? reference,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _WithdrawRequest():
return $default(_that.amount,_that.description,_that.reference,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double amount,  String description,  String? reference,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _WithdrawRequest() when $default != null:
return $default(_that.amount,_that.description,_that.reference,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WithdrawRequest implements WithdrawRequest {
  const _WithdrawRequest({required this.amount, required this.description, this.reference, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _WithdrawRequest.fromJson(Map<String, dynamic> json) => _$WithdrawRequestFromJson(json);

@override final  double amount;
@override final  String description;
@override final  String? reference;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of WithdrawRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawRequestCopyWith<_WithdrawRequest> get copyWith => __$WithdrawRequestCopyWithImpl<_WithdrawRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WithdrawRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawRequest&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.description, description) || other.description == description)&&(identical(other.reference, reference) || other.reference == reference)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,amount,description,reference,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'WithdrawRequest(amount: $amount, description: $description, reference: $reference, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$WithdrawRequestCopyWith<$Res> implements $WithdrawRequestCopyWith<$Res> {
  factory _$WithdrawRequestCopyWith(_WithdrawRequest value, $Res Function(_WithdrawRequest) _then) = __$WithdrawRequestCopyWithImpl;
@override @useResult
$Res call({
 double amount, String description, String? reference, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$WithdrawRequestCopyWithImpl<$Res>
    implements _$WithdrawRequestCopyWith<$Res> {
  __$WithdrawRequestCopyWithImpl(this._self, this._then);

  final _WithdrawRequest _self;
  final $Res Function(_WithdrawRequest) _then;

/// Create a copy of WithdrawRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? amount = null,Object? description = null,Object? reference = freezed,Object? metadata = freezed,}) {
  return _then(_WithdrawRequest(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on

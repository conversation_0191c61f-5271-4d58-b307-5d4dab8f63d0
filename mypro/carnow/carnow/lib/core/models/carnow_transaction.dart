import 'package:freezed_annotation/freezed_annotation.dart';

part 'carnow_transaction.freezed.dart';
part 'carnow_transaction.g.dart';

@freezed
abstract class CarnowTransaction with _$CarnowTransaction {
  const factory CarnowTransaction({
    required String id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'wallet_id') required String walletId,
    @Json<PERSON>ey(name: 'user_id') required String userId,
    required double amount,
    required String type, // 'deposit', 'withdraw', 'transfer', 'fee'
    required String status, // 'pending', 'completed', 'failed', 'cancelled'
    required String description,
    String? reference,
    @Json<PERSON>ey(name: 'from_wallet_id') String? fromWalletId,
    @<PERSON><PERSON><PERSON>ey(name: 'to_wallet_id') String? toWalletId,
    double? fee,
    Map<String, dynamic>? metadata,
    @Json<PERSON>ey(name: 'created_at') required DateTime createdAt,
    @<PERSON>son<PERSON>ey(name: 'updated_at') required DateTime updatedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'processed_at') DateTime? processedAt,
  }) = _CarnowTransaction;

  factory CarnowTransaction.from<PERSON><PERSON>(Map<String, dynamic> json) => 
      _$CarnowTransactionFromJson(json);
}

@freezed
abstract class CarnowFinancialOperation with _$CarnowFinancialOperation {
  const factory CarnowFinancialOperation({
    required String id,
    required String userId,
    required String operationType, // 'payment', 'refund', 'transfer', 'fee'
    required double amount,
    required String currency,
    required String status, // 'pending', 'processing', 'completed', 'failed', 'cancelled'
    required String description,
    String? reference,
    String? orderId,
    String? productId,
    Map<String, dynamic>? metadata,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? processedAt,
    String? failureReason,
  }) = _CarnowFinancialOperation;

  factory CarnowFinancialOperation.fromJson(Map<String, dynamic> json) => 
      _$CarnowFinancialOperationFromJson(json);
}

@freezed
abstract class CreateFinancialOperationRequest with _$CreateFinancialOperationRequest {
  const factory CreateFinancialOperationRequest({
    required String userId,
    required String operationType,
    required double amount,
    @Default('SAR') String currency,
    required String description,
    String? reference,
    String? orderId,
    String? productId,
    Map<String, dynamic>? metadata,
  }) = _CreateFinancialOperationRequest;

  factory CreateFinancialOperationRequest.fromJson(Map<String, dynamic> json) => 
      _$CreateFinancialOperationRequestFromJson(json);
}

@freezed
abstract class ProcessOperationRequest with _$ProcessOperationRequest {
  const factory ProcessOperationRequest({
    required String action, // 'approve', 'reject', 'process'
    String? reason,
    Map<String, dynamic>? metadata,
  }) = _ProcessOperationRequest;

  factory ProcessOperationRequest.fromJson(Map<String, dynamic> json) => 
      _$ProcessOperationRequestFromJson(json);
} 
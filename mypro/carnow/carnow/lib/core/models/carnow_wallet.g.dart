// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'carnow_wallet.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CarnowWallet _$CarnowWalletFromJson(Map<String, dynamic> json) =>
    _CarnowWallet(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      availableBalance: (json['available_balance'] as num?)?.toDouble() ?? 0.0,
      frozenBalance: (json['frozen_balance'] as num?)?.toDouble() ?? 0.0,
      currency: json['currency'] as String? ?? 'LYD',
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      lastTransactionId: json['last_transaction_id'] as String?,
      lastTransactionDate: json['last_transaction_date'] == null
          ? null
          : DateTime.parse(json['last_transaction_date'] as String),
    );

Map<String, dynamic> _$CarnowWalletToJson(_CarnowWallet instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'balance': instance.balance,
      'available_balance': instance.availableBalance,
      'frozen_balance': instance.frozenBalance,
      'currency': instance.currency,
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'last_transaction_id': instance.lastTransactionId,
      'last_transaction_date': instance.lastTransactionDate?.toIso8601String(),
    };

_CreateWalletRequest _$CreateWalletRequestFromJson(Map<String, dynamic> json) =>
    _CreateWalletRequest(
      userId: json['userId'] as String,
      currency: json['currency'] as String? ?? 'LYD',
    );

Map<String, dynamic> _$CreateWalletRequestToJson(
  _CreateWalletRequest instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'currency': instance.currency,
};

_DepositRequest _$DepositRequestFromJson(Map<String, dynamic> json) =>
    _DepositRequest(
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String,
      reference: json['reference'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$DepositRequestToJson(_DepositRequest instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'description': instance.description,
      'reference': instance.reference,
      'metadata': instance.metadata,
    };

_WithdrawRequest _$WithdrawRequestFromJson(Map<String, dynamic> json) =>
    _WithdrawRequest(
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String,
      reference: json['reference'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$WithdrawRequestToJson(_WithdrawRequest instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'description': instance.description,
      'reference': instance.reference,
      'metadata': instance.metadata,
    };

import 'package:freezed_annotation/freezed_annotation.dart';

import 'ticket_message_model.dart';
import 'package:carnow/features/account/models/user_model.dart';

part 'ticket_model.freezed.dart';
part 'ticket_model.g.dart';

// ignore_for_file: invalid_annotation_target

enum TicketStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('open')
  open,
  @JsonValue('resolved')
  resolved,
  @JsonValue('closed')
  closed,
}

enum TicketPriority {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

enum TicketCategory {
  @JsonValue('general')
  general,
  @JsonValue('account')
  account,
  @JsonValue('orders')
  orders,
  @JsonValue('payments')
  payments,
  @JsonValue('technical')
  technical,
  @JsonValue('seller')
  seller,
  @JsonValue('returns')
  returns,
}

@freezed
abstract class TicketModel with _$TicketModel {
  const factory TicketModel({
    int? id,
    @JsonKey(name: 'user_id') int? userId,
    UserModel? user,
    String? subject,
    String? description,
    @JsonKey(name: 'ticket_status')
    @Default(TicketStatus.pending)
    TicketStatus status,
    @JsonKey(name: 'priority')
    @Default(TicketPriority.medium)
    TicketPriority priority,
    @JsonKey(name: 'category')
    @Default(TicketCategory.general)
    TicketCategory category,
    @JsonKey(name: 'assigned_to') int? assignedTo,
    UserModel? assignedAgent,
    @JsonKey(name: 'resolved_at') DateTime? resolvedAt,
    @JsonKey(name: 'created_at') DateTime? createdAt,
    @JsonKey(name: 'updated_at') DateTime? updatedAt,
    @JsonKey(name: 'is_deleted', defaultValue: false)
    @Default(false)
    bool isDeleted,
    @Default([]) List<TicketMessageModel> messages,
    // Computed fields
    @JsonKey(name: 'last_message_at') DateTime? lastMessageAt,
    @JsonKey(name: 'last_message_text') String? lastMessageText,
    @JsonKey(name: 'unread_count') @Default(0) int unreadCount,
  }) = _TicketModel;
  const TicketModel._();

  factory TicketModel.fromJson(Map<String, dynamic> json) =>
      _$TicketModelFromJson(json);

  /// Get the ticket status display name in Arabic
  String get statusDisplayName {
    switch (status) {
      case TicketStatus.pending:
        return 'في الانتظار';
      case TicketStatus.open:
        return 'مفتوح';
      case TicketStatus.resolved:
        return 'تم حله';
      case TicketStatus.closed:
        return 'مغلق';
    }
  }

  /// Get the ticket priority display name in Arabic
  String get priorityDisplayName {
    switch (priority) {
      case TicketPriority.low:
        return 'منخفض';
      case TicketPriority.medium:
        return 'متوسط';
      case TicketPriority.high:
        return 'عالي';
      case TicketPriority.urgent:
        return 'عاجل';
    }
  }

  /// Get the ticket category display name in Arabic
  String get categoryDisplayName {
    switch (category) {
      case TicketCategory.general:
        return 'عام';
      case TicketCategory.account:
        return 'الحساب';
      case TicketCategory.orders:
        return 'الطلبات';
      case TicketCategory.payments:
        return 'المدفوعات';
      case TicketCategory.technical:
        return 'تقني';
      case TicketCategory.seller:
        return 'البائع';
      case TicketCategory.returns:
        return 'المرتجعات';
    }
  }

  /// Check if ticket is active (not resolved or closed)
  bool get isActive =>
      status != TicketStatus.resolved && status != TicketStatus.closed;

  /// Check if ticket has unread messages
  bool get hasUnreadMessages => unreadCount > 0;
}

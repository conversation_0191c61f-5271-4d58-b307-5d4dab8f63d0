// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'carnow_transaction.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CarnowTransaction {

 String get id;@JsonKey(name: 'wallet_id') String get walletId;@JsonKey(name: 'user_id') String get userId; double get amount; String get type;// 'deposit', 'withdraw', 'transfer', 'fee'
 String get status;// 'pending', 'completed', 'failed', 'cancelled'
 String get description; String? get reference;@JsonKey(name: 'from_wallet_id') String? get fromWalletId;@JsonKey(name: 'to_wallet_id') String? get toWalletId; double? get fee; Map<String, dynamic>? get metadata;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime get updatedAt;@JsonKey(name: 'processed_at') DateTime? get processedAt;
/// Create a copy of CarnowTransaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CarnowTransactionCopyWith<CarnowTransaction> get copyWith => _$CarnowTransactionCopyWithImpl<CarnowTransaction>(this as CarnowTransaction, _$identity);

  /// Serializes this CarnowTransaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CarnowTransaction&&(identical(other.id, id) || other.id == id)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.description, description) || other.description == description)&&(identical(other.reference, reference) || other.reference == reference)&&(identical(other.fromWalletId, fromWalletId) || other.fromWalletId == fromWalletId)&&(identical(other.toWalletId, toWalletId) || other.toWalletId == toWalletId)&&(identical(other.fee, fee) || other.fee == fee)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,walletId,userId,amount,type,status,description,reference,fromWalletId,toWalletId,fee,const DeepCollectionEquality().hash(metadata),createdAt,updatedAt,processedAt);

@override
String toString() {
  return 'CarnowTransaction(id: $id, walletId: $walletId, userId: $userId, amount: $amount, type: $type, status: $status, description: $description, reference: $reference, fromWalletId: $fromWalletId, toWalletId: $toWalletId, fee: $fee, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt, processedAt: $processedAt)';
}


}

/// @nodoc
abstract mixin class $CarnowTransactionCopyWith<$Res>  {
  factory $CarnowTransactionCopyWith(CarnowTransaction value, $Res Function(CarnowTransaction) _then) = _$CarnowTransactionCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'user_id') String userId, double amount, String type, String status, String description, String? reference,@JsonKey(name: 'from_wallet_id') String? fromWalletId,@JsonKey(name: 'to_wallet_id') String? toWalletId, double? fee, Map<String, dynamic>? metadata,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'processed_at') DateTime? processedAt
});




}
/// @nodoc
class _$CarnowTransactionCopyWithImpl<$Res>
    implements $CarnowTransactionCopyWith<$Res> {
  _$CarnowTransactionCopyWithImpl(this._self, this._then);

  final CarnowTransaction _self;
  final $Res Function(CarnowTransaction) _then;

/// Create a copy of CarnowTransaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? walletId = null,Object? userId = null,Object? amount = null,Object? type = null,Object? status = null,Object? description = null,Object? reference = freezed,Object? fromWalletId = freezed,Object? toWalletId = freezed,Object? fee = freezed,Object? metadata = freezed,Object? createdAt = null,Object? updatedAt = null,Object? processedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,fromWalletId: freezed == fromWalletId ? _self.fromWalletId : fromWalletId // ignore: cast_nullable_to_non_nullable
as String?,toWalletId: freezed == toWalletId ? _self.toWalletId : toWalletId // ignore: cast_nullable_to_non_nullable
as String?,fee: freezed == fee ? _self.fee : fee // ignore: cast_nullable_to_non_nullable
as double?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [CarnowTransaction].
extension CarnowTransactionPatterns on CarnowTransaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CarnowTransaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CarnowTransaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CarnowTransaction value)  $default,){
final _that = this;
switch (_that) {
case _CarnowTransaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CarnowTransaction value)?  $default,){
final _that = this;
switch (_that) {
case _CarnowTransaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId,  double amount,  String type,  String status,  String description,  String? reference, @JsonKey(name: 'from_wallet_id')  String? fromWalletId, @JsonKey(name: 'to_wallet_id')  String? toWalletId,  double? fee,  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'processed_at')  DateTime? processedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CarnowTransaction() when $default != null:
return $default(_that.id,_that.walletId,_that.userId,_that.amount,_that.type,_that.status,_that.description,_that.reference,_that.fromWalletId,_that.toWalletId,_that.fee,_that.metadata,_that.createdAt,_that.updatedAt,_that.processedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId,  double amount,  String type,  String status,  String description,  String? reference, @JsonKey(name: 'from_wallet_id')  String? fromWalletId, @JsonKey(name: 'to_wallet_id')  String? toWalletId,  double? fee,  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'processed_at')  DateTime? processedAt)  $default,) {final _that = this;
switch (_that) {
case _CarnowTransaction():
return $default(_that.id,_that.walletId,_that.userId,_that.amount,_that.type,_that.status,_that.description,_that.reference,_that.fromWalletId,_that.toWalletId,_that.fee,_that.metadata,_that.createdAt,_that.updatedAt,_that.processedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId,  double amount,  String type,  String status,  String description,  String? reference, @JsonKey(name: 'from_wallet_id')  String? fromWalletId, @JsonKey(name: 'to_wallet_id')  String? toWalletId,  double? fee,  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'processed_at')  DateTime? processedAt)?  $default,) {final _that = this;
switch (_that) {
case _CarnowTransaction() when $default != null:
return $default(_that.id,_that.walletId,_that.userId,_that.amount,_that.type,_that.status,_that.description,_that.reference,_that.fromWalletId,_that.toWalletId,_that.fee,_that.metadata,_that.createdAt,_that.updatedAt,_that.processedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CarnowTransaction implements CarnowTransaction {
  const _CarnowTransaction({required this.id, @JsonKey(name: 'wallet_id') required this.walletId, @JsonKey(name: 'user_id') required this.userId, required this.amount, required this.type, required this.status, required this.description, this.reference, @JsonKey(name: 'from_wallet_id') this.fromWalletId, @JsonKey(name: 'to_wallet_id') this.toWalletId, this.fee, final  Map<String, dynamic>? metadata, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt, @JsonKey(name: 'processed_at') this.processedAt}): _metadata = metadata;
  factory _CarnowTransaction.fromJson(Map<String, dynamic> json) => _$CarnowTransactionFromJson(json);

@override final  String id;
@override@JsonKey(name: 'wallet_id') final  String walletId;
@override@JsonKey(name: 'user_id') final  String userId;
@override final  double amount;
@override final  String type;
// 'deposit', 'withdraw', 'transfer', 'fee'
@override final  String status;
// 'pending', 'completed', 'failed', 'cancelled'
@override final  String description;
@override final  String? reference;
@override@JsonKey(name: 'from_wallet_id') final  String? fromWalletId;
@override@JsonKey(name: 'to_wallet_id') final  String? toWalletId;
@override final  double? fee;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;
@override@JsonKey(name: 'processed_at') final  DateTime? processedAt;

/// Create a copy of CarnowTransaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CarnowTransactionCopyWith<_CarnowTransaction> get copyWith => __$CarnowTransactionCopyWithImpl<_CarnowTransaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CarnowTransactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CarnowTransaction&&(identical(other.id, id) || other.id == id)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.description, description) || other.description == description)&&(identical(other.reference, reference) || other.reference == reference)&&(identical(other.fromWalletId, fromWalletId) || other.fromWalletId == fromWalletId)&&(identical(other.toWalletId, toWalletId) || other.toWalletId == toWalletId)&&(identical(other.fee, fee) || other.fee == fee)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,walletId,userId,amount,type,status,description,reference,fromWalletId,toWalletId,fee,const DeepCollectionEquality().hash(_metadata),createdAt,updatedAt,processedAt);

@override
String toString() {
  return 'CarnowTransaction(id: $id, walletId: $walletId, userId: $userId, amount: $amount, type: $type, status: $status, description: $description, reference: $reference, fromWalletId: $fromWalletId, toWalletId: $toWalletId, fee: $fee, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt, processedAt: $processedAt)';
}


}

/// @nodoc
abstract mixin class _$CarnowTransactionCopyWith<$Res> implements $CarnowTransactionCopyWith<$Res> {
  factory _$CarnowTransactionCopyWith(_CarnowTransaction value, $Res Function(_CarnowTransaction) _then) = __$CarnowTransactionCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'user_id') String userId, double amount, String type, String status, String description, String? reference,@JsonKey(name: 'from_wallet_id') String? fromWalletId,@JsonKey(name: 'to_wallet_id') String? toWalletId, double? fee, Map<String, dynamic>? metadata,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'processed_at') DateTime? processedAt
});




}
/// @nodoc
class __$CarnowTransactionCopyWithImpl<$Res>
    implements _$CarnowTransactionCopyWith<$Res> {
  __$CarnowTransactionCopyWithImpl(this._self, this._then);

  final _CarnowTransaction _self;
  final $Res Function(_CarnowTransaction) _then;

/// Create a copy of CarnowTransaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? walletId = null,Object? userId = null,Object? amount = null,Object? type = null,Object? status = null,Object? description = null,Object? reference = freezed,Object? fromWalletId = freezed,Object? toWalletId = freezed,Object? fee = freezed,Object? metadata = freezed,Object? createdAt = null,Object? updatedAt = null,Object? processedAt = freezed,}) {
  return _then(_CarnowTransaction(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,fromWalletId: freezed == fromWalletId ? _self.fromWalletId : fromWalletId // ignore: cast_nullable_to_non_nullable
as String?,toWalletId: freezed == toWalletId ? _self.toWalletId : toWalletId // ignore: cast_nullable_to_non_nullable
as String?,fee: freezed == fee ? _self.fee : fee // ignore: cast_nullable_to_non_nullable
as double?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$CarnowFinancialOperation {

 String get id; String get userId; String get operationType;// 'payment', 'refund', 'transfer', 'fee'
 double get amount; String get currency; String get status;// 'pending', 'processing', 'completed', 'failed', 'cancelled'
 String get description; String? get reference; String? get orderId; String? get productId; Map<String, dynamic>? get metadata; DateTime get createdAt; DateTime get updatedAt; DateTime? get processedAt; String? get failureReason;
/// Create a copy of CarnowFinancialOperation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CarnowFinancialOperationCopyWith<CarnowFinancialOperation> get copyWith => _$CarnowFinancialOperationCopyWithImpl<CarnowFinancialOperation>(this as CarnowFinancialOperation, _$identity);

  /// Serializes this CarnowFinancialOperation to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CarnowFinancialOperation&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.operationType, operationType) || other.operationType == operationType)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.description, description) || other.description == description)&&(identical(other.reference, reference) || other.reference == reference)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.productId, productId) || other.productId == productId)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.failureReason, failureReason) || other.failureReason == failureReason));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,operationType,amount,currency,status,description,reference,orderId,productId,const DeepCollectionEquality().hash(metadata),createdAt,updatedAt,processedAt,failureReason);

@override
String toString() {
  return 'CarnowFinancialOperation(id: $id, userId: $userId, operationType: $operationType, amount: $amount, currency: $currency, status: $status, description: $description, reference: $reference, orderId: $orderId, productId: $productId, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt, processedAt: $processedAt, failureReason: $failureReason)';
}


}

/// @nodoc
abstract mixin class $CarnowFinancialOperationCopyWith<$Res>  {
  factory $CarnowFinancialOperationCopyWith(CarnowFinancialOperation value, $Res Function(CarnowFinancialOperation) _then) = _$CarnowFinancialOperationCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String operationType, double amount, String currency, String status, String description, String? reference, String? orderId, String? productId, Map<String, dynamic>? metadata, DateTime createdAt, DateTime updatedAt, DateTime? processedAt, String? failureReason
});




}
/// @nodoc
class _$CarnowFinancialOperationCopyWithImpl<$Res>
    implements $CarnowFinancialOperationCopyWith<$Res> {
  _$CarnowFinancialOperationCopyWithImpl(this._self, this._then);

  final CarnowFinancialOperation _self;
  final $Res Function(CarnowFinancialOperation) _then;

/// Create a copy of CarnowFinancialOperation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? operationType = null,Object? amount = null,Object? currency = null,Object? status = null,Object? description = null,Object? reference = freezed,Object? orderId = freezed,Object? productId = freezed,Object? metadata = freezed,Object? createdAt = null,Object? updatedAt = null,Object? processedAt = freezed,Object? failureReason = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,operationType: null == operationType ? _self.operationType : operationType // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,failureReason: freezed == failureReason ? _self.failureReason : failureReason // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [CarnowFinancialOperation].
extension CarnowFinancialOperationPatterns on CarnowFinancialOperation {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CarnowFinancialOperation value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CarnowFinancialOperation() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CarnowFinancialOperation value)  $default,){
final _that = this;
switch (_that) {
case _CarnowFinancialOperation():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CarnowFinancialOperation value)?  $default,){
final _that = this;
switch (_that) {
case _CarnowFinancialOperation() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String operationType,  double amount,  String currency,  String status,  String description,  String? reference,  String? orderId,  String? productId,  Map<String, dynamic>? metadata,  DateTime createdAt,  DateTime updatedAt,  DateTime? processedAt,  String? failureReason)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CarnowFinancialOperation() when $default != null:
return $default(_that.id,_that.userId,_that.operationType,_that.amount,_that.currency,_that.status,_that.description,_that.reference,_that.orderId,_that.productId,_that.metadata,_that.createdAt,_that.updatedAt,_that.processedAt,_that.failureReason);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String operationType,  double amount,  String currency,  String status,  String description,  String? reference,  String? orderId,  String? productId,  Map<String, dynamic>? metadata,  DateTime createdAt,  DateTime updatedAt,  DateTime? processedAt,  String? failureReason)  $default,) {final _that = this;
switch (_that) {
case _CarnowFinancialOperation():
return $default(_that.id,_that.userId,_that.operationType,_that.amount,_that.currency,_that.status,_that.description,_that.reference,_that.orderId,_that.productId,_that.metadata,_that.createdAt,_that.updatedAt,_that.processedAt,_that.failureReason);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String operationType,  double amount,  String currency,  String status,  String description,  String? reference,  String? orderId,  String? productId,  Map<String, dynamic>? metadata,  DateTime createdAt,  DateTime updatedAt,  DateTime? processedAt,  String? failureReason)?  $default,) {final _that = this;
switch (_that) {
case _CarnowFinancialOperation() when $default != null:
return $default(_that.id,_that.userId,_that.operationType,_that.amount,_that.currency,_that.status,_that.description,_that.reference,_that.orderId,_that.productId,_that.metadata,_that.createdAt,_that.updatedAt,_that.processedAt,_that.failureReason);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CarnowFinancialOperation implements CarnowFinancialOperation {
  const _CarnowFinancialOperation({required this.id, required this.userId, required this.operationType, required this.amount, required this.currency, required this.status, required this.description, this.reference, this.orderId, this.productId, final  Map<String, dynamic>? metadata, required this.createdAt, required this.updatedAt, this.processedAt, this.failureReason}): _metadata = metadata;
  factory _CarnowFinancialOperation.fromJson(Map<String, dynamic> json) => _$CarnowFinancialOperationFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String operationType;
// 'payment', 'refund', 'transfer', 'fee'
@override final  double amount;
@override final  String currency;
@override final  String status;
// 'pending', 'processing', 'completed', 'failed', 'cancelled'
@override final  String description;
@override final  String? reference;
@override final  String? orderId;
@override final  String? productId;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override final  DateTime? processedAt;
@override final  String? failureReason;

/// Create a copy of CarnowFinancialOperation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CarnowFinancialOperationCopyWith<_CarnowFinancialOperation> get copyWith => __$CarnowFinancialOperationCopyWithImpl<_CarnowFinancialOperation>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CarnowFinancialOperationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CarnowFinancialOperation&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.operationType, operationType) || other.operationType == operationType)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.status, status) || other.status == status)&&(identical(other.description, description) || other.description == description)&&(identical(other.reference, reference) || other.reference == reference)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.productId, productId) || other.productId == productId)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt)&&(identical(other.failureReason, failureReason) || other.failureReason == failureReason));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,operationType,amount,currency,status,description,reference,orderId,productId,const DeepCollectionEquality().hash(_metadata),createdAt,updatedAt,processedAt,failureReason);

@override
String toString() {
  return 'CarnowFinancialOperation(id: $id, userId: $userId, operationType: $operationType, amount: $amount, currency: $currency, status: $status, description: $description, reference: $reference, orderId: $orderId, productId: $productId, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt, processedAt: $processedAt, failureReason: $failureReason)';
}


}

/// @nodoc
abstract mixin class _$CarnowFinancialOperationCopyWith<$Res> implements $CarnowFinancialOperationCopyWith<$Res> {
  factory _$CarnowFinancialOperationCopyWith(_CarnowFinancialOperation value, $Res Function(_CarnowFinancialOperation) _then) = __$CarnowFinancialOperationCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String operationType, double amount, String currency, String status, String description, String? reference, String? orderId, String? productId, Map<String, dynamic>? metadata, DateTime createdAt, DateTime updatedAt, DateTime? processedAt, String? failureReason
});




}
/// @nodoc
class __$CarnowFinancialOperationCopyWithImpl<$Res>
    implements _$CarnowFinancialOperationCopyWith<$Res> {
  __$CarnowFinancialOperationCopyWithImpl(this._self, this._then);

  final _CarnowFinancialOperation _self;
  final $Res Function(_CarnowFinancialOperation) _then;

/// Create a copy of CarnowFinancialOperation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? operationType = null,Object? amount = null,Object? currency = null,Object? status = null,Object? description = null,Object? reference = freezed,Object? orderId = freezed,Object? productId = freezed,Object? metadata = freezed,Object? createdAt = null,Object? updatedAt = null,Object? processedAt = freezed,Object? failureReason = freezed,}) {
  return _then(_CarnowFinancialOperation(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,operationType: null == operationType ? _self.operationType : operationType // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,failureReason: freezed == failureReason ? _self.failureReason : failureReason // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$CreateFinancialOperationRequest {

 String get userId; String get operationType; double get amount; String get currency; String get description; String? get reference; String? get orderId; String? get productId; Map<String, dynamic>? get metadata;
/// Create a copy of CreateFinancialOperationRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateFinancialOperationRequestCopyWith<CreateFinancialOperationRequest> get copyWith => _$CreateFinancialOperationRequestCopyWithImpl<CreateFinancialOperationRequest>(this as CreateFinancialOperationRequest, _$identity);

  /// Serializes this CreateFinancialOperationRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateFinancialOperationRequest&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.operationType, operationType) || other.operationType == operationType)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.description, description) || other.description == description)&&(identical(other.reference, reference) || other.reference == reference)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.productId, productId) || other.productId == productId)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,operationType,amount,currency,description,reference,orderId,productId,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'CreateFinancialOperationRequest(userId: $userId, operationType: $operationType, amount: $amount, currency: $currency, description: $description, reference: $reference, orderId: $orderId, productId: $productId, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $CreateFinancialOperationRequestCopyWith<$Res>  {
  factory $CreateFinancialOperationRequestCopyWith(CreateFinancialOperationRequest value, $Res Function(CreateFinancialOperationRequest) _then) = _$CreateFinancialOperationRequestCopyWithImpl;
@useResult
$Res call({
 String userId, String operationType, double amount, String currency, String description, String? reference, String? orderId, String? productId, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$CreateFinancialOperationRequestCopyWithImpl<$Res>
    implements $CreateFinancialOperationRequestCopyWith<$Res> {
  _$CreateFinancialOperationRequestCopyWithImpl(this._self, this._then);

  final CreateFinancialOperationRequest _self;
  final $Res Function(CreateFinancialOperationRequest) _then;

/// Create a copy of CreateFinancialOperationRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? operationType = null,Object? amount = null,Object? currency = null,Object? description = null,Object? reference = freezed,Object? orderId = freezed,Object? productId = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,operationType: null == operationType ? _self.operationType : operationType // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [CreateFinancialOperationRequest].
extension CreateFinancialOperationRequestPatterns on CreateFinancialOperationRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CreateFinancialOperationRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CreateFinancialOperationRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CreateFinancialOperationRequest value)  $default,){
final _that = this;
switch (_that) {
case _CreateFinancialOperationRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CreateFinancialOperationRequest value)?  $default,){
final _that = this;
switch (_that) {
case _CreateFinancialOperationRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  String operationType,  double amount,  String currency,  String description,  String? reference,  String? orderId,  String? productId,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CreateFinancialOperationRequest() when $default != null:
return $default(_that.userId,_that.operationType,_that.amount,_that.currency,_that.description,_that.reference,_that.orderId,_that.productId,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  String operationType,  double amount,  String currency,  String description,  String? reference,  String? orderId,  String? productId,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _CreateFinancialOperationRequest():
return $default(_that.userId,_that.operationType,_that.amount,_that.currency,_that.description,_that.reference,_that.orderId,_that.productId,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  String operationType,  double amount,  String currency,  String description,  String? reference,  String? orderId,  String? productId,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _CreateFinancialOperationRequest() when $default != null:
return $default(_that.userId,_that.operationType,_that.amount,_that.currency,_that.description,_that.reference,_that.orderId,_that.productId,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CreateFinancialOperationRequest implements CreateFinancialOperationRequest {
  const _CreateFinancialOperationRequest({required this.userId, required this.operationType, required this.amount, this.currency = 'SAR', required this.description, this.reference, this.orderId, this.productId, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _CreateFinancialOperationRequest.fromJson(Map<String, dynamic> json) => _$CreateFinancialOperationRequestFromJson(json);

@override final  String userId;
@override final  String operationType;
@override final  double amount;
@override@JsonKey() final  String currency;
@override final  String description;
@override final  String? reference;
@override final  String? orderId;
@override final  String? productId;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of CreateFinancialOperationRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreateFinancialOperationRequestCopyWith<_CreateFinancialOperationRequest> get copyWith => __$CreateFinancialOperationRequestCopyWithImpl<_CreateFinancialOperationRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CreateFinancialOperationRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CreateFinancialOperationRequest&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.operationType, operationType) || other.operationType == operationType)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.description, description) || other.description == description)&&(identical(other.reference, reference) || other.reference == reference)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.productId, productId) || other.productId == productId)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,operationType,amount,currency,description,reference,orderId,productId,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'CreateFinancialOperationRequest(userId: $userId, operationType: $operationType, amount: $amount, currency: $currency, description: $description, reference: $reference, orderId: $orderId, productId: $productId, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$CreateFinancialOperationRequestCopyWith<$Res> implements $CreateFinancialOperationRequestCopyWith<$Res> {
  factory _$CreateFinancialOperationRequestCopyWith(_CreateFinancialOperationRequest value, $Res Function(_CreateFinancialOperationRequest) _then) = __$CreateFinancialOperationRequestCopyWithImpl;
@override @useResult
$Res call({
 String userId, String operationType, double amount, String currency, String description, String? reference, String? orderId, String? productId, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$CreateFinancialOperationRequestCopyWithImpl<$Res>
    implements _$CreateFinancialOperationRequestCopyWith<$Res> {
  __$CreateFinancialOperationRequestCopyWithImpl(this._self, this._then);

  final _CreateFinancialOperationRequest _self;
  final $Res Function(_CreateFinancialOperationRequest) _then;

/// Create a copy of CreateFinancialOperationRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? operationType = null,Object? amount = null,Object? currency = null,Object? description = null,Object? reference = freezed,Object? orderId = freezed,Object? productId = freezed,Object? metadata = freezed,}) {
  return _then(_CreateFinancialOperationRequest(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,operationType: null == operationType ? _self.operationType : operationType // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reference: freezed == reference ? _self.reference : reference // ignore: cast_nullable_to_non_nullable
as String?,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$ProcessOperationRequest {

 String get action;// 'approve', 'reject', 'process'
 String? get reason; Map<String, dynamic>? get metadata;
/// Create a copy of ProcessOperationRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProcessOperationRequestCopyWith<ProcessOperationRequest> get copyWith => _$ProcessOperationRequestCopyWithImpl<ProcessOperationRequest>(this as ProcessOperationRequest, _$identity);

  /// Serializes this ProcessOperationRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProcessOperationRequest&&(identical(other.action, action) || other.action == action)&&(identical(other.reason, reason) || other.reason == reason)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,action,reason,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'ProcessOperationRequest(action: $action, reason: $reason, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $ProcessOperationRequestCopyWith<$Res>  {
  factory $ProcessOperationRequestCopyWith(ProcessOperationRequest value, $Res Function(ProcessOperationRequest) _then) = _$ProcessOperationRequestCopyWithImpl;
@useResult
$Res call({
 String action, String? reason, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$ProcessOperationRequestCopyWithImpl<$Res>
    implements $ProcessOperationRequestCopyWith<$Res> {
  _$ProcessOperationRequestCopyWithImpl(this._self, this._then);

  final ProcessOperationRequest _self;
  final $Res Function(ProcessOperationRequest) _then;

/// Create a copy of ProcessOperationRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? action = null,Object? reason = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
action: null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as String,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [ProcessOperationRequest].
extension ProcessOperationRequestPatterns on ProcessOperationRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProcessOperationRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProcessOperationRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProcessOperationRequest value)  $default,){
final _that = this;
switch (_that) {
case _ProcessOperationRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProcessOperationRequest value)?  $default,){
final _that = this;
switch (_that) {
case _ProcessOperationRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String action,  String? reason,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProcessOperationRequest() when $default != null:
return $default(_that.action,_that.reason,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String action,  String? reason,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _ProcessOperationRequest():
return $default(_that.action,_that.reason,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String action,  String? reason,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _ProcessOperationRequest() when $default != null:
return $default(_that.action,_that.reason,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProcessOperationRequest implements ProcessOperationRequest {
  const _ProcessOperationRequest({required this.action, this.reason, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _ProcessOperationRequest.fromJson(Map<String, dynamic> json) => _$ProcessOperationRequestFromJson(json);

@override final  String action;
// 'approve', 'reject', 'process'
@override final  String? reason;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of ProcessOperationRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProcessOperationRequestCopyWith<_ProcessOperationRequest> get copyWith => __$ProcessOperationRequestCopyWithImpl<_ProcessOperationRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProcessOperationRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProcessOperationRequest&&(identical(other.action, action) || other.action == action)&&(identical(other.reason, reason) || other.reason == reason)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,action,reason,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'ProcessOperationRequest(action: $action, reason: $reason, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$ProcessOperationRequestCopyWith<$Res> implements $ProcessOperationRequestCopyWith<$Res> {
  factory _$ProcessOperationRequestCopyWith(_ProcessOperationRequest value, $Res Function(_ProcessOperationRequest) _then) = __$ProcessOperationRequestCopyWithImpl;
@override @useResult
$Res call({
 String action, String? reason, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$ProcessOperationRequestCopyWithImpl<$Res>
    implements _$ProcessOperationRequestCopyWith<$Res> {
  __$ProcessOperationRequestCopyWithImpl(this._self, this._then);

  final _ProcessOperationRequest _self;
  final $Res Function(_ProcessOperationRequest) _then;

/// Create a copy of ProcessOperationRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? action = null,Object? reason = freezed,Object? metadata = freezed,}) {
  return _then(_ProcessOperationRequest(
action: null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as String,reason: freezed == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on

import 'package:flutter/material.dart';

/// نظام الخطوط المتخصص لتطبيق CarNow
/// متحسن للتجارة الإلكترونية وقطع غيار السيارات
class CarNowTextStyles {
  CarNowTextStyles._();

  static const String _fontFamily = 'Cairo';

  // ================================
  // أساليب العناوين والشاشات الرئيسية
  // ================================

  /// عناوين الصفحات الرئيسية (مثل: "قطع غيار تويوتا")
  static const TextStyle pageTitle = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 28,
    fontWeight: FontWeight.w700,
    height: 1.2,
    letterSpacing: 0,
  );

  /// عناوين الأقسام (مثل: "المنتجات الأكثر مبيعاً")
  static const TextStyle sectionTitle = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w600,
    height: 1.3,
    letterSpacing: 0,
  );

  /// عناوين الفئات (مثل: "فلاتر الزيت")
  static const TextStyle categoryTitle = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.w600,
    height: 1.3,
    letterSpacing: 0,
  );

  // ================================
  // أساليب المنتجات وقطع الغيار
  // ================================

  /// أسماء المنتجات في البطاقات
  static const TextStyle productName = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w600,
    height: 1.3,
    letterSpacing: 0.1,
  );

  /// أسماء المنتجات في التفاصيل
  static const TextStyle productNameDetail = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 22,
    fontWeight: FontWeight.w700,
    height: 1.3,
    letterSpacing: 0,
  );

  /// وصف المنتج
  static const TextStyle productDescription = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.5,
    letterSpacing: 0.25,
  );

  /// معلومات فنية (رقم القطعة، OEM، المواصفات)
  static const TextStyle technicalInfo = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 13,
    fontWeight: FontWeight.w500,
    height: 1.4,
    letterSpacing: 0.3,
  );

  /// ماركة المنتج
  static const TextStyle brandName = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w600,
    height: 1.3,
    letterSpacing: 0.1,
  );

  // ================================
  // أساليب الأسعار والعملة
  // ================================

  /// السعر الرئيسي (كبير وواضح)
  static const TextStyle priceMain = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 20,
    fontWeight: FontWeight.w700,
    height: 1.2,
    letterSpacing: 0,
  );

  /// السعر في البطاقات
  static const TextStyle priceCard = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w700,
    height: 1.2,
    letterSpacing: 0,
  );

  /// السعر الأصلي (مخطوط عليه)
  static const TextStyle priceOriginal = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.2,
    letterSpacing: 0.1,
    decoration: TextDecoration.lineThrough,
  );

  /// النسبة المئوية للخصم
  static const TextStyle discountPercentage = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w700,
    height: 1.2,
    letterSpacing: 0,
  );

  // ================================
  // أساليب المعلومات والحالات
  // ================================

  /// حالة المخزون (متاح، نفد، كمية قليلة)
  static const TextStyle stockStatus = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 11,
    fontWeight: FontWeight.w600,
    height: 1.2,
    letterSpacing: 0.3,
  );

  /// تقييمات ومراجعات
  static const TextStyle rating = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 13,
    fontWeight: FontWeight.w500,
    height: 1.3,
    letterSpacing: 0.2,
  );

  /// رقم الطلب/الفاتورة
  static const TextStyle orderNumber = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w600,
    height: 1.3,
    letterSpacing: 0.5,
  );

  // ================================
  // أساليب الأزرار والتفاعل
  // ================================

  /// نص الأزرار الرئيسية
  static const TextStyle buttonPrimary = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w600,
    height: 1.2,
    letterSpacing: 0.3,
  );

  /// نص الأزرار الثانوية
  static const TextStyle buttonSecondary = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.2,
    letterSpacing: 0.3,
  );

  /// رابط نصي
  static const TextStyle linkText = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.3,
    letterSpacing: 0.1,
    decoration: TextDecoration.underline,
  );

  // ================================
  // أساليب النماذج والمدخلات
  // ================================

  /// تسميات الحقول
  static const TextStyle fieldLabel = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.3,
    letterSpacing: 0.2,
  );

  /// النص داخل الحقول
  static const TextStyle fieldInput = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 1.4,
    letterSpacing: 0.2,
  );

  /// رسائل الخطأ
  static const TextStyle errorText = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.3,
    letterSpacing: 0.3,
  );

  /// نص المساعدة والتلميحات
  static const TextStyle hintText = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.4,
    letterSpacing: 0.2,
  );

  // ================================
  // أساليب الرسائل والإشعارات
  // ================================

  /// رسائل النجاح
  static const TextStyle successMessage = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.4,
    letterSpacing: 0.2,
  );

  /// رسائل التحذير
  static const TextStyle warningMessage = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.4,
    letterSpacing: 0.2,
  );

  /// الرسائل العامة
  static const TextStyle generalMessage = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.5,
    letterSpacing: 0.25,
  );

  // ================================
  // أساليب خاصة بقطع الغيار
  // ================================

  /// رقم القطعة (Part Number)
  static const TextStyle partNumber = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 13,
    fontWeight: FontWeight.w600,
    height: 1.3,
    letterSpacing: 0.8,
    fontFeatures: [FontFeature.tabularFigures()],
  );

  /// رقم OEM
  static const TextStyle oemNumber = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 13,
    fontWeight: FontWeight.w500,
    height: 1.3,
    letterSpacing: 0.6,
    fontFeatures: [FontFeature.tabularFigures()],
  );

  /// أرقام الشاسيه والموديل
  static const TextStyle vehicleInfo = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.3,
    letterSpacing: 0.3,
  );

  /// المواصفات الفنية
  static const TextStyle specifications = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 13,
    fontWeight: FontWeight.w400,
    height: 1.4,
    letterSpacing: 0.2,
  );

  // ================================
  // Extensions مفيدة
  // ================================
}

/// إضافات مفيدة للنصوص
extension TextStyleExtensions on TextStyle {
  /// تطبيق لون محدد
  TextStyle colored(Color color) => copyWith(color: color);

  /// تطبيق وزن محدد
  TextStyle weighted(FontWeight weight) => copyWith(fontWeight: weight);

  /// تطبيق حجم محدد
  TextStyle sized(double size) => copyWith(fontSize: size);

  /// تطبيق شفافية
  TextStyle withOpacity(double opacity) =>
      copyWith(color: (color ?? Colors.black).withValues(alpha: opacity));

  /// للأرقام والأسعار (استخدام أرقام ثابتة العرض)
  TextStyle get tabular =>
      copyWith(fontFeatures: [const FontFeature.tabularFigures()]);

  /// للنصوص الطويلة (تحسين التباعد)
  TextStyle get readable => copyWith(
    height: (height ?? 1.0) * 1.1,
    letterSpacing: (letterSpacing ?? 0) + 0.1,
  );
}

import 'package:flutter/material.dart';

/// Centralised design-system constants for spacing, radii & colours.
///
/// Use these instead of magic numbers / hard-coded values in UI widgets.
class AppSpacing {
  static const xs = 4.0;
  static const sm = 8.0;
  static const md = 16.0;
  static const lg = 24.0;
  static const xl = 32.0;
}

class AppRadii {
  static const sm = Radius.circular(4);
  static const md = Radius.circular(8);
  static const lg = Radius.circular(16);
}

class AppCorners {
  static const sm = BorderRadius.all(AppRadii.sm);
  static const md = BorderRadius.all(AppRadii.md);
  static const lg = BorderRadius.all(AppRadii.lg);
}
import 'package:flutter/services.dart';
import 'package:logging/logging.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';

import '../models/order_details_model.dart';

final _logger = Logger('InvoicePdfService');

/// خدمة توليد الفاتورة بصيغة PDF
class InvoicePdfService {
  static const String _companyName = 'CarNow';
  static const String _companyEmail = '<EMAIL>';
  static const String _companyPhone = '+218 21 123 4567';
  static const String _companyAddress = 'طرابلس، ليبيا';

  /// توليد فاتورة PDF للطلب
  Future<Uint8List> generateInvoicePdf({
    required OrderDetailsModel order,
    bool includeBranding = true,
  }) async {
    try {
      _logger.info('Generating PDF invoice for order: ${order.orderNumber}');

      final pdf = pw.Document();

      // تحميل خط عربي
      final arabicFont = await _loadArabicFont();
      final boldArabicFont = await _loadBoldArabicFont();

      // إضافة صفحة الفاتورة
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) => _buildInvoicePage(
            order: order,
            arabicFont: arabicFont,
            boldArabicFont: boldArabicFont,
            includeBranding: includeBranding,
          ),
        ),
      );

      final pdfBytes = await pdf.save();
      _logger.info('PDF invoice generated successfully');
      return pdfBytes;
    } catch (e) {
      _logger.severe('Error generating PDF invoice: $e');
      throw Exception('فشل في توليد الفاتورة: $e');
    }
  }

  /// بناء صفحة الفاتورة
  pw.Widget _buildInvoicePage({
    required OrderDetailsModel order,
    required pw.Font arabicFont,
    required pw.Font boldArabicFont,
    required bool includeBranding,
  }) {
    final currencyFormatter = NumberFormat('#,##0.00', 'ar');
    final dateFormatter = DateFormat('dd/MM/yyyy', 'ar');

    return pw.Padding(
      padding: const pw.EdgeInsets.all(24),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // رأس الفاتورة
          if (includeBranding) _buildHeader(boldArabicFont, arabicFont),

          pw.SizedBox(height: 20),

          // معلومات الفاتورة
          _buildInvoiceInfo(
            order: order,
            arabicFont: arabicFont,
            boldArabicFont: boldArabicFont,
            dateFormatter: dateFormatter,
          ),

          pw.SizedBox(height: 20),

          // معلومات العميل
          _buildCustomerInfo(
            order: order,
            arabicFont: arabicFont,
            boldArabicFont: boldArabicFont,
          ),

          pw.SizedBox(height: 30),

          // جدول العناصر
          _buildItemsTable(
            order: order,
            arabicFont: arabicFont,
            boldArabicFont: boldArabicFont,
            currencyFormatter: currencyFormatter,
          ),

          pw.SizedBox(height: 20),

          // ملخص المبالغ
          _buildTotalSummary(
            order: order,
            arabicFont: arabicFont,
            boldArabicFont: boldArabicFont,
            currencyFormatter: currencyFormatter,
          ),

          pw.SizedBox(height: 30),

          // معلومات الدفع
          _buildPaymentInfo(
            order: order,
            arabicFont: arabicFont,
            boldArabicFont: boldArabicFont,
          ),

          pw.Spacer(),

          // تذييل الفاتورة
          _buildFooter(arabicFont),
        ],
      ),
    );
  }

  /// بناء رأس الفاتورة
  pw.Widget _buildHeader(pw.Font boldFont, pw.Font regularFont) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              _companyName,
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 24,
                color: PdfColors.blue800,
              ),
            ),
            pw.SizedBox(height: 4),
            pw.Text(
              _companyEmail,
              style: pw.TextStyle(font: regularFont, fontSize: 12),
            ),
            pw.Text(
              _companyPhone,
              style: pw.TextStyle(font: regularFont, fontSize: 12),
            ),
            pw.Text(
              _companyAddress,
              style: pw.TextStyle(font: regularFont, fontSize: 12),
            ),
          ],
        ),
        pw.Text(
          'فـــاتــورة',
          style: pw.TextStyle(
            font: boldFont,
            fontSize: 28,
            color: PdfColors.blue800,
          ),
        ),
      ],
    );
  }

  /// بناء معلومات الفاتورة
  pw.Widget _buildInvoiceInfo({
    required OrderDetailsModel order,
    required pw.Font arabicFont,
    required pw.Font boldArabicFont,
    required DateFormat dateFormatter,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.RichText(
                text: pw.TextSpan(
                  children: [
                    pw.TextSpan(
                      text: 'رقم الفاتورة: ',
                      style: pw.TextStyle(font: boldArabicFont, fontSize: 12),
                    ),
                    pw.TextSpan(
                      text: order.orderNumber,
                      style: pw.TextStyle(font: arabicFont, fontSize: 12),
                    ),
                  ],
                ),
              ),
              pw.SizedBox(height: 4),
              pw.RichText(
                text: pw.TextSpan(
                  children: [
                    pw.TextSpan(
                      text: 'تاريخ الإصدار: ',
                      style: pw.TextStyle(font: boldArabicFont, fontSize: 12),
                    ),
                    pw.TextSpan(
                      text: dateFormatter.format(order.createdAt),
                      style: pw.TextStyle(font: arabicFont, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Container(
                padding: const pw.EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: pw.BoxDecoration(
                  color: _getStatusColor(order.status),
                  borderRadius: pw.BorderRadius.circular(20),
                ),
                child: pw.Text(
                  _getStatusText(order.status),
                  style: pw.TextStyle(
                    font: boldArabicFont,
                    fontSize: 12,
                    color: PdfColors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء معلومات العميل
  pw.Widget _buildCustomerInfo({
    required OrderDetailsModel order,
    required pw.Font arabicFont,
    required pw.Font boldArabicFont,
  }) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'معلومات العميل',
            style: pw.TextStyle(
              font: boldArabicFont,
              fontSize: 14,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.RichText(
            text: pw.TextSpan(
              children: [
                pw.TextSpan(
                  text: 'الاسم: ',
                  style: pw.TextStyle(font: boldArabicFont, fontSize: 12),
                ),
                pw.TextSpan(
                  text: order.buyerName,
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                ),
              ],
            ),
          ),
          pw.SizedBox(height: 4),
          pw.RichText(
            text: pw.TextSpan(
              children: [
                pw.TextSpan(
                  text: 'البريد الإلكتروني: ',
                  style: pw.TextStyle(font: boldArabicFont, fontSize: 12),
                ),
                pw.TextSpan(
                  text: order.buyerEmail,
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                ),
              ],
            ),
          ),
          pw.SizedBox(height: 4),
          pw.RichText(
            text: pw.TextSpan(
              children: [
                pw.TextSpan(
                  text: 'رقم الهاتف: ',
                  style: pw.TextStyle(font: boldArabicFont, fontSize: 12),
                ),
                pw.TextSpan(
                  text: order.buyerPhone,
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                ),
              ],
            ),
          ),
          pw.SizedBox(height: 4),
          pw.RichText(
            text: pw.TextSpan(
              children: [
                pw.TextSpan(
                  text: 'عنوان التسليم: ',
                  style: pw.TextStyle(font: boldArabicFont, fontSize: 12),
                ),
                pw.TextSpan(
                  text: order.shippingAddress,
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء جدول العناصر
  pw.Widget _buildItemsTable({
    required OrderDetailsModel order,
    required pw.Font arabicFont,
    required pw.Font boldArabicFont,
    required NumberFormat currencyFormatter,
  }) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(3),
        1: const pw.FlexColumnWidth(),
        2: const pw.FlexColumnWidth(1.5),
        3: const pw.FlexColumnWidth(1.5),
      },
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.blue800),
          children: [
            _buildTableCell(
              'اسم المنتج',
              boldArabicFont,
              PdfColors.white,
              isHeader: true,
            ),
            _buildTableCell(
              'الكمية',
              boldArabicFont,
              PdfColors.white,
              isHeader: true,
            ),
            _buildTableCell(
              'سعر الوحدة',
              boldArabicFont,
              PdfColors.white,
              isHeader: true,
            ),
            _buildTableCell(
              'الإجمالي',
              boldArabicFont,
              PdfColors.white,
              isHeader: true,
            ),
          ],
        ),
        // صفوف العناصر
        ...order.items.map(
          (item) => pw.TableRow(
            children: [
              _buildTableCell(item.productName, arabicFont, PdfColors.black),
              _buildTableCell(
                item.quantity.toString(),
                arabicFont,
                PdfColors.black,
                textAlign: pw.TextAlign.center,
              ),
              _buildTableCell(
                '${currencyFormatter.format(item.unitPrice)} ${order.currency}',
                arabicFont,
                PdfColors.black,
                textAlign: pw.TextAlign.center,
              ),
              _buildTableCell(
                '${currencyFormatter.format(item.totalPrice)} ${order.currency}',
                arabicFont,
                PdfColors.black,
                textAlign: pw.TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء خلية الجدول
  pw.Widget _buildTableCell(
    String text,
    pw.Font font,
    PdfColor color, {
    bool isHeader = false,
    pw.TextAlign textAlign = pw.TextAlign.right,
  }) {
    return pw.Container(
      padding: pw.EdgeInsets.all(isHeader ? 8 : 6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: isHeader ? 12 : 10,
          color: color,
        ),
        textAlign: textAlign,
      ),
    );
  }

  /// بناء ملخص المبالغ
  pw.Widget _buildTotalSummary({
    required OrderDetailsModel order,
    required pw.Font arabicFont,
    required pw.Font boldArabicFont,
    required NumberFormat currencyFormatter,
  }) {
    return pw.Container(
      width: 200,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildSummaryRow(
            'المجموع الفرعي:',
            '${currencyFormatter.format(order.subtotal)} ${order.currency}',
            arabicFont,
            boldArabicFont,
          ),
          _buildSummaryRow(
            'الضريبة:',
            '${currencyFormatter.format(order.taxAmount)} ${order.currency}',
            arabicFont,
            boldArabicFont,
          ),
          _buildSummaryRow(
            'تكلفة الشحن:',
            '${currencyFormatter.format(order.shippingCost)} ${order.currency}',
            arabicFont,
            boldArabicFont,
          ),
          pw.Divider(color: PdfColors.grey400),
          _buildSummaryRow(
            'الإجمالي النهائي:',
            '${currencyFormatter.format(order.totalAmount)} ${order.currency}',
            boldArabicFont,
            boldArabicFont,
            isTotal: true,
          ),
        ],
      ),
    );
  }

  /// بناء صف ملخص المبلغ
  pw.Widget _buildSummaryRow(
    String label,
    String value,
    pw.Font labelFont,
    pw.Font valueFont, {
    bool isTotal = false,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              font: labelFont,
              fontSize: isTotal ? 14 : 12,
              color: isTotal ? PdfColors.blue800 : PdfColors.black,
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
              font: valueFont,
              fontSize: isTotal ? 14 : 12,
              color: isTotal ? PdfColors.blue800 : PdfColors.black,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الدفع
  pw.Widget _buildPaymentInfo({
    required OrderDetailsModel order,
    required pw.Font arabicFont,
    required pw.Font boldArabicFont,
  }) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.orange50,
        border: pw.Border.all(color: PdfColors.orange200),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'معلومات الدفع',
            style: pw.TextStyle(
              font: boldArabicFont,
              fontSize: 14,
              color: PdfColors.orange800,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            children: [
              pw.Container(
                width: 16,
                height: 16,
                decoration: pw.BoxDecoration(
                  color: PdfColors.orange800,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Center(
                  child: pw.Text('💰', style: const pw.TextStyle(fontSize: 10)),
                ),
              ),
              pw.SizedBox(width: 8),
              pw.Text(
                'طريقة الدفع: الدفع عند الاستلام (COD)',
                style: pw.TextStyle(
                  font: boldArabicFont,
                  fontSize: 12,
                  color: PdfColors.orange800,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 6),
          pw.Text(
            '• يتم الدفع نقداً عند استلام الطلب',
            style: pw.TextStyle(font: arabicFont, fontSize: 10),
          ),
          pw.Text(
            '• يرجى تحضير المبلغ كاملاً',
            style: pw.TextStyle(font: arabicFont, fontSize: 10),
          ),
          pw.Text(
            '• لا نقبل الشيكات أو البطاقات الائتمانية عند التسليم',
            style: pw.TextStyle(font: arabicFont, fontSize: 10),
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الفاتورة
  pw.Widget _buildFooter(pw.Font arabicFont) {
    return pw.Column(
      children: [
        pw.Divider(color: PdfColors.grey400),
        pw.SizedBox(height: 8),
        pw.Text(
          'شكراً لتسوقكم معنا - CarNow',
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: 12,
            color: PdfColors.grey600,
          ),
          textAlign: pw.TextAlign.center,
        ),
        pw.SizedBox(height: 4),
        pw.Text(
          'للاستفسارات: $_companyEmail | $_companyPhone',
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: 10,
            color: PdfColors.grey600,
          ),
          textAlign: pw.TextAlign.center,
        ),
      ],
    );
  }

  /// تحميل الخط العربي العادي
  Future<pw.Font> _loadArabicFont() async {
    try {
      final fontData = await rootBundle.load('fonts/Cairo/Cairo-300.ttf');
      return pw.Font.ttf(fontData);
    } catch (e) {
      _logger.warning('Failed to load Arabic font, using default: $e');
      return pw.Font.helvetica();
    }
  }

  /// تحميل الخط العربي الثقيل
  Future<pw.Font> _loadBoldArabicFont() async {
    try {
      final fontData = await rootBundle.load('fonts/Cairo/Cairo-500.ttf');
      return pw.Font.ttf(fontData);
    } catch (e) {
      _logger.warning('Failed to load bold Arabic font, using default: $e');
      return pw.Font.helveticaBold();
    }
  }

  /// الحصول على لون حالة الطلب
  PdfColor _getStatusColor(dynamic status) {
    if (status == null) return PdfColors.grey;

    final statusStr = status.toString().split('.').last;
    switch (statusStr) {
      case 'pending':
        return PdfColors.orange600;
      case 'confirmed':
        return PdfColors.blue600;
      case 'processing':
        return PdfColors.indigo600;
      case 'shipped':
        return PdfColors.purple600;
      case 'delivered':
        return PdfColors.green600;
      case 'cancelled':
        return PdfColors.red600;
      case 'refunded':
        return PdfColors.pink600;
      case 'returned':
        return PdfColors.brown600;
      default:
        return PdfColors.grey600;
    }
  }

  /// الحصول على نص حالة الطلب
  String _getStatusText(dynamic status) {
    if (status == null) return 'غير معروف';

    final statusStr = status.toString().split('.').last;
    switch (statusStr) {
      case 'pending':
        return 'قيد الانتظار';
      case 'confirmed':
        return 'مؤكد';
      case 'processing':
        return 'قيد المعالجة';
      case 'shipped':
        return 'تم الشحن';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'ملغي';
      case 'refunded':
        return 'مسترد';
      case 'returned':
        return 'مرتجع';
      default:
        return 'غير معروف';
    }
  }
}

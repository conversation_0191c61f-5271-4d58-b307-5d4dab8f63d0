// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'file_upload_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fileUploadServiceHash() => r'750febe0b92cf1404f660796e0a54ae907c68b5c';

/// File upload service for handling images and documents
///
/// This service provides methods for picking files from the device gallery or
/// camera, and for uploading them to Supabase Storage. It includes validation
/// for file types and sizes.
///
/// Copied from [fileUploadService].
@ProviderFor(fileUploadService)
final fileUploadServiceProvider =
    AutoDisposeProvider<FileUploadService>.internal(
      fileUploadService,
      name: r'fileUploadServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fileUploadServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FileUploadServiceRef = AutoDisposeProviderRef<FileUploadService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

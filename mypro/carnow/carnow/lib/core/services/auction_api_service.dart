import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';
import '../errors/app_error.dart';

part 'auction_api_service.g.dart';

final _logger = Logger('AuctionApiService');

/// Simple Auction API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
AuctionApiService auctionApiService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return AuctionApiService(apiClient);
}

class AuctionApiService {
  const AuctionApiService(this._apiClient);
  
  final SimpleApiClient _apiClient;

  /// Get all auctions
  Future<List<Map<String, dynamic>>> getAuctions() async {
    try {
      _logger.info('Fetching auctions...');
      final response = await _apiClient.get<List<dynamic>>('/auctions');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch auctions: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch auctions',
          code: 'auctions_fetch_failed',
        );
      }
      
      return List<Map<String, dynamic>>.from(response.data!);
    } catch (e) {
      _logger.severe('Error fetching auctions: $e');
      throw AppError.network(
        message: 'Error fetching auctions',
        code: 'auctions_fetch_error',
        originalError: e,
      );
    }
  }

  /// Get auction by ID
  Future<Map<String, dynamic>?> getAuctionById(String id) async {
    try {
      _logger.info('Fetching auction: $id');
      final response = await _apiClient.get<Map<String, dynamic>>('/auctions/$id');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch auction: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch auction',
          code: 'auction_fetch_failed',
        );
      }
      
      return response.data;
    } catch (e) {
      _logger.severe('Error fetching auction: $e');
      throw AppError.network(
        message: 'Error fetching auction',
        code: 'auction_fetch_error',
        originalError: e,
      );
    }
  }

  /// Get user auctions
  Future<List<Map<String, dynamic>>> getUserAuctions(String userId) async {
    try {
      _logger.info('Fetching user auctions: $userId');
      final response = await _apiClient.get<List<dynamic>>('/auctions/user/$userId');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch user auctions: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch user auctions',
          code: 'user_auctions_fetch_failed',
        );
      }
      
      return List<Map<String, dynamic>>.from(response.data!);
    } catch (e) {
      _logger.severe('Error fetching user auctions: $e');
      throw AppError.network(
        message: 'Error fetching user auctions',
        code: 'user_auctions_fetch_error',
        originalError: e,
      );
    }
  }

  /// Get auction bids
  Future<List<Map<String, dynamic>>> getAuctionBids(String auctionId) async {
    try {
      _logger.info('Fetching auction bids: $auctionId');
      final response = await _apiClient.get<List<dynamic>>('/auctions/$auctionId/bids');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch auction bids: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch auction bids',
          code: 'auction_bids_fetch_failed',
        );
      }
      
      return List<Map<String, dynamic>>.from(response.data!);
    } catch (e) {
      _logger.severe('Error fetching auction bids: $e');
      throw AppError.network(
        message: 'Error fetching auction bids',
        code: 'auction_bids_fetch_error',
        originalError: e,
      );
    }
  }

  /// Place a bid
  Future<Map<String, dynamic>> placeBid({
    required String auctionId,
    required String userId,
    required double amount,
  }) async {
    try {
      _logger.info('Placing bid on auction: $auctionId');
      final response = await _apiClient.post<Map<String, dynamic>>('/auctions/$auctionId/bids', data: {
        'user_id': userId,
        'amount': amount,
      });
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to place bid: ${response.error}');
        throw const AppError.network(
          message: 'Failed to place bid',
          code: 'bid_placement_failed',
        );
      }
      
      return response.data!;
    } catch (e) {
      _logger.severe('Error placing bid: $e');
      throw AppError.network(
        message: 'Error placing bid',
        code: 'bid_placement_error',
        originalError: e,
      );
    }
  }

  /// Get auction statistics
  Future<Map<String, dynamic>> getAuctionStats(String auctionId) async {
    try {
      _logger.info('Fetching auction stats: $auctionId');
      final response = await _apiClient.get<Map<String, dynamic>>('/auctions/$auctionId/stats');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch auction stats: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch auction stats',
          code: 'auction_stats_fetch_failed',
        );
      }
      
      return response.data!;
    } catch (e) {
      _logger.severe('Error fetching auction stats: $e');
      throw AppError.network(
        message: 'Error fetching auction stats',
        code: 'auction_stats_fetch_error',
        originalError: e,
      );
    }
  }
} 
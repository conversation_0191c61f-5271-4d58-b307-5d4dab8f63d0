import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/subscription_request.dart';
import '../models/subscription_response.dart';
import '../models/subscription_error.dart';
import '../networking/simple_api_client.dart';
import '../models/api_response.dart';

part 'subscription_service.g.dart';

/// Result type for subscription operations
sealed class SubscriptionResult<T> {
  const SubscriptionResult();

  const factory SubscriptionResult.success(T data) = SubscriptionSuccess<T>;
  const factory SubscriptionResult.failure(SubscriptionError error) =
      SubscriptionFailure<T>;

  R when<R>({
    required R Function(T data) success,
    required R Function(SubscriptionError error) failure,
  }) {
    if (this is SubscriptionSuccess<T>) {
      return success((this as SubscriptionSuccess<T>).data);
    } else if (this is SubscriptionFailure<T>) {
      return failure((this as SubscriptionFailure<T>).error);
    }
    throw StateError('Unknown SubscriptionResult type');
  }
}

class SubscriptionSuccess<T> extends SubscriptionResult<T> {
  const SubscriptionSuccess(this.data);
  final T data;
}

class SubscriptionFailure<T> extends SubscriptionResult<T> {
  const SubscriptionFailure(this.error);
  final SubscriptionError error;
}

/// Abstract interface for subscription service operations
/// Defines the contract for all subscription-related operations
abstract class SubscriptionService {
  /// Submit a new subscription request
  ///
  /// [request] The subscription request data
  /// Returns [SubscriptionResult] containing [SubscriptionResponse] on success or [SubscriptionError] on failure
  Future<SubscriptionResult<SubscriptionResponse>> submitSubscriptionRequest(
    SubscriptionRequest request,
  );

  /// Get all subscriptions for a specific user
  ///
  /// [userId] The user ID to fetch subscriptions for
  /// Returns [SubscriptionResult] containing list of [SubscriptionResponse] on success or [SubscriptionError] on failure
  Future<SubscriptionResult<List<SubscriptionResponse>>> getUserSubscriptions(
    String userId,
  );

  /// Get the status of a specific subscription
  ///
  /// [subscriptionId] The subscription ID to check status for
  /// Returns [SubscriptionResult] containing [SubscriptionResponse] on success or [SubscriptionError] on failure
  Future<SubscriptionResult<SubscriptionResponse>> getSubscriptionStatus(
    String subscriptionId,
  );

  /// Update an existing subscription
  ///
  /// [subscriptionId] The subscription ID to update
  /// [request] The updated subscription data
  /// Returns [SubscriptionResult] containing [SubscriptionResponse] on success or [SubscriptionError] on failure
  Future<SubscriptionResult<SubscriptionResponse>> updateSubscription(
    String subscriptionId,
    SubscriptionRequest request,
  );

  /// Cancel a subscription
  ///
  /// [subscriptionId] The subscription ID to cancel
  /// Returns [SubscriptionResult] containing [SubscriptionResponse] on success or [SubscriptionError] on failure
  Future<SubscriptionResult<SubscriptionResponse>> cancelSubscription(
    String subscriptionId,
  );
}

/// Concrete implementation of SubscriptionService using SimpleApiClient
/// Handles all subscription operations with proper error handling and retry logic
class SubscriptionServiceImpl implements SubscriptionService {
  SubscriptionServiceImpl({required SimpleApiClient apiClient, Logger? logger})
    : _apiClient = apiClient,
      _logger = logger ?? Logger();

  final SimpleApiClient _apiClient;
  final Logger _logger;

  // API endpoints
  static const String _subscriptionsEndpoint = '/api/subscriptions';
  static const String _userSubscriptionsEndpoint = '/api/subscriptions/user';

  // Retry configuration
  static const int _maxRetries = 3;
  static const Duration _baseDelay = Duration(seconds: 1);
  static const double _backoffMultiplier = 2.0;

  @override
  Future<SubscriptionResult<SubscriptionResponse>> submitSubscriptionRequest(
    SubscriptionRequest request,
  ) async {
    _logger.i('📝 Submitting subscription request for user: ${request.userId}');

    // Validate request before sending
    final validationErrors = request.validate();
    if (validationErrors.isNotEmpty) {
      _logger.w('❌ Subscription request validation failed: $validationErrors');
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'بيانات الاشتراك غير صحيحة',
          fieldErrors: validationErrors,
          code: 'VALIDATION_ERROR',
        ),
      );
    }

    return _executeWithRetry(
      operation: () => _submitSubscriptionRequestInternal(request),
      operationName: 'submitSubscriptionRequest',
      context: 'user: ${request.userId}, store: ${request.storeName}',
    );
  }

  @override
  Future<SubscriptionResult<List<SubscriptionResponse>>> getUserSubscriptions(
    String userId,
  ) async {
    _logger.i('📋 Fetching subscriptions for user: $userId');

    if (userId.trim().isEmpty) {
      _logger.w('❌ Invalid user ID provided');
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'معرف المستخدم مطلوب',
          fieldErrors: {'userId': 'معرف المستخدم لا يمكن أن يكون فارغاً'},
          code: 'INVALID_USER_ID',
        ),
      );
    }

    return _executeWithRetry(
      operation: () => _getUserSubscriptionsInternal(userId),
      operationName: 'getUserSubscriptions',
      context: 'user: $userId',
    );
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> getSubscriptionStatus(
    String subscriptionId,
  ) async {
    _logger.i('🔍 Fetching subscription status: $subscriptionId');

    if (subscriptionId.trim().isEmpty) {
      _logger.w('❌ Invalid subscription ID provided');
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'معرف الاشتراك مطلوب',
          fieldErrors: {
            'subscriptionId': 'معرف الاشتراك لا يمكن أن يكون فارغاً',
          },
          code: 'INVALID_SUBSCRIPTION_ID',
        ),
      );
    }

    return _executeWithRetry(
      operation: () => _getSubscriptionStatusInternal(subscriptionId),
      operationName: 'getSubscriptionStatus',
      context: 'subscription: $subscriptionId',
    );
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> updateSubscription(
    String subscriptionId,
    SubscriptionRequest request,
  ) async {
    _logger.i('✏️ Updating subscription: $subscriptionId');

    if (subscriptionId.trim().isEmpty) {
      _logger.w('❌ Invalid subscription ID provided');
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'معرف الاشتراك مطلوب',
          fieldErrors: {
            'subscriptionId': 'معرف الاشتراك لا يمكن أن يكون فارغاً',
          },
          code: 'INVALID_SUBSCRIPTION_ID',
        ),
      );
    }

    // Validate request before sending
    final validationErrors = request.validate();
    if (validationErrors.isNotEmpty) {
      _logger.w('❌ Subscription update validation failed: $validationErrors');
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'بيانات تحديث الاشتراك غير صحيحة',
          fieldErrors: validationErrors,
          code: 'VALIDATION_ERROR',
        ),
      );
    }

    return _executeWithRetry(
      operation: () => _updateSubscriptionInternal(subscriptionId, request),
      operationName: 'updateSubscription',
      context: 'subscription: $subscriptionId, user: ${request.userId}',
    );
  }

  @override
  Future<SubscriptionResult<SubscriptionResponse>> cancelSubscription(
    String subscriptionId,
  ) async {
    _logger.i('❌ Cancelling subscription: $subscriptionId');

    if (subscriptionId.trim().isEmpty) {
      _logger.w('❌ Invalid subscription ID provided');
      return SubscriptionResult.failure(
        SubscriptionError.validationError(
          message: 'معرف الاشتراك مطلوب',
          fieldErrors: {
            'subscriptionId': 'معرف الاشتراك لا يمكن أن يكون فارغاً',
          },
          code: 'INVALID_SUBSCRIPTION_ID',
        ),
      );
    }

    return _executeWithRetry(
      operation: () => _cancelSubscriptionInternal(subscriptionId),
      operationName: 'cancelSubscription',
      context: 'subscription: $subscriptionId',
    );
  }

  /// Internal method to submit subscription request
  Future<SubscriptionResult<SubscriptionResponse>>
  _submitSubscriptionRequestInternal(SubscriptionRequest request) async {
    try {
      _logger.d('🔄 Making API call to submit subscription request');

      final response = await _apiClient.post<Map<String, dynamic>>(
        _subscriptionsEndpoint,
        data: request.toJson(),
      );

      return _handleApiResponse<SubscriptionResponse>(
        response,
        (data) => SubscriptionResponse.fromJson(data),
        'submitSubscriptionRequest',
      );
    } catch (e, stackTrace) {
      _logger.e(
        '💥 Exception in submitSubscriptionRequest',
        error: e,
        stackTrace: stackTrace,
      );
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'submitSubscriptionRequest'),
      );
    }
  }

  /// Internal method to get user subscriptions
  Future<SubscriptionResult<List<SubscriptionResponse>>>
  _getUserSubscriptionsInternal(String userId) async {
    try {
      _logger.d('🔄 Making API call to get user subscriptions');

      final response = await _apiClient.get<List<dynamic>>(
        '$_userSubscriptionsEndpoint/$userId',
      );

      return _handleApiResponse<List<SubscriptionResponse>>(
        response,
        (data) => (data as List<dynamic>)
            .map(
              (item) =>
                  SubscriptionResponse.fromJson(item as Map<String, dynamic>),
            )
            .toList(),
        'getUserSubscriptions',
      );
    } catch (e, stackTrace) {
      _logger.e(
        '💥 Exception in getUserSubscriptions',
        error: e,
        stackTrace: stackTrace,
      );
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'getUserSubscriptions'),
      );
    }
  }

  /// Internal method to get subscription status
  Future<SubscriptionResult<SubscriptionResponse>>
  _getSubscriptionStatusInternal(String subscriptionId) async {
    try {
      _logger.d('🔄 Making API call to get subscription status');

      final response = await _apiClient.get<Map<String, dynamic>>(
        '$_subscriptionsEndpoint/$subscriptionId',
      );

      return _handleApiResponse<SubscriptionResponse>(
        response,
        (data) => SubscriptionResponse.fromJson(data),
        'getSubscriptionStatus',
      );
    } catch (e, stackTrace) {
      _logger.e(
        '💥 Exception in getSubscriptionStatus',
        error: e,
        stackTrace: stackTrace,
      );
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'getSubscriptionStatus'),
      );
    }
  }

  /// Internal method to update subscription
  Future<SubscriptionResult<SubscriptionResponse>> _updateSubscriptionInternal(
    String subscriptionId,
    SubscriptionRequest request,
  ) async {
    try {
      _logger.d('🔄 Making API call to update subscription');

      final response = await _apiClient.put<Map<String, dynamic>>(
        '$_subscriptionsEndpoint/$subscriptionId',
        data: request.toJson(),
      );

      return _handleApiResponse<SubscriptionResponse>(
        response,
        (data) => SubscriptionResponse.fromJson(data),
        'updateSubscription',
      );
    } catch (e, stackTrace) {
      _logger.e(
        '💥 Exception in updateSubscription',
        error: e,
        stackTrace: stackTrace,
      );
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'updateSubscription'),
      );
    }
  }

  /// Internal method to cancel subscription
  Future<SubscriptionResult<SubscriptionResponse>> _cancelSubscriptionInternal(
    String subscriptionId,
  ) async {
    try {
      _logger.d('🔄 Making API call to cancel subscription');

      final response = await _apiClient.delete<Map<String, dynamic>>(
        '$_subscriptionsEndpoint/$subscriptionId',
      );

      return _handleApiResponse<SubscriptionResponse>(
        response,
        (data) => SubscriptionResponse.fromJson(data),
        'cancelSubscription',
      );
    } catch (e, stackTrace) {
      _logger.e(
        '💥 Exception in cancelSubscription',
        error: e,
        stackTrace: stackTrace,
      );
      return SubscriptionResult.failure(
        _createErrorFromException(e, 'cancelSubscription'),
      );
    }
  }

  /// Execute operation with exponential backoff retry logic
  Future<SubscriptionResult<T>> _executeWithRetry<T>({
    required Future<SubscriptionResult<T>> Function() operation,
    required String operationName,
    required String context,
  }) async {
    int attempt = 0;
    Duration delay = _baseDelay;

    while (attempt < _maxRetries) {
      attempt++;

      _logger.d(
        '🔄 Executing $operationName (attempt $attempt/$_maxRetries) - $context',
      );

      try {
        final result = await operation();

        // If successful, return immediately
        if (result is SubscriptionSuccess<T>) {
          _logger.i(
            '✅ $operationName succeeded on attempt $attempt - $context',
          );
          return result;
        }

        // If failed, check if retryable
        final error = (result as SubscriptionFailure<T>).error;
        if (!error.isRetryable || attempt >= _maxRetries) {
          _logger.w(
            '❌ $operationName failed (non-retryable or max retries reached) - $context: ${error.technicalMessage}',
          );
          return result;
        }

        // Log retry attempt
        _logger.w(
          '⚠️ $operationName failed (attempt $attempt/$_maxRetries), retrying in ${delay.inMilliseconds}ms - $context: ${error.technicalMessage}',
        );

        // Wait before retry with exponential backoff
        await Future.delayed(delay);
        delay = Duration(
          milliseconds: (delay.inMilliseconds * _backoffMultiplier).round(),
        );
      } catch (e, stackTrace) {
        _logger.e(
          '💥 Unexpected exception in $operationName (attempt $attempt/$_maxRetries) - $context',
          error: e,
          stackTrace: stackTrace,
        );

        // If this is the last attempt, return the error
        if (attempt >= _maxRetries) {
          return SubscriptionResult.failure(
            _createErrorFromException(e, operationName),
          );
        }

        // Wait before retry
        await Future.delayed(delay);
        delay = Duration(
          milliseconds: (delay.inMilliseconds * _backoffMultiplier).round(),
        );
      }
    }

    // This should never be reached, but just in case
    _logger.e('💥 $operationName exhausted all retries - $context');
    return SubscriptionResult.failure(
      SubscriptionError.unknownError(
        message: 'تم استنفاد جميع محاولات إعادة المحاولة',
        code: 'MAX_RETRIES_EXCEEDED',
        details: {'operation': operationName, 'context': context},
      ),
    );
  }

  /// Handle API response and convert to SubscriptionResult
  SubscriptionResult<T> _handleApiResponse<T>(
    ApiResponse<dynamic> response,
    T Function(dynamic data) dataMapper,
    String operationName,
  ) {
    if (response.isSuccess && response.data != null) {
      try {
        final mappedData = dataMapper(response.data);
        _logger.d('✅ $operationName API response processed successfully');
        return SubscriptionResult.success(mappedData);
      } catch (e, stackTrace) {
        _logger.e(
          '💥 Failed to map API response data in $operationName',
          error: e,
          stackTrace: stackTrace,
        );
        return SubscriptionResult.failure(
          SubscriptionError.serverError(
            message: 'فشل في معالجة استجابة الخادم',
            code: 'RESPONSE_MAPPING_ERROR',
            details: {'originalError': e.toString()},
          ),
        );
      }
    } else if (response.isFailure && response.error != null) {
      _logger.w('❌ $operationName API returned error: ${response.error}');
      return SubscriptionResult.failure(
        _createErrorFromApiResponse(response, operationName),
      );
    } else {
      _logger.w('❌ $operationName API returned unexpected response state');
      return SubscriptionResult.failure(
        SubscriptionError.serverError(
          message: 'استجابة غير متوقعة من الخادم',
          code: 'UNEXPECTED_RESPONSE',
          details: {
            'isSuccess': response.isSuccess,
            'isFailure': response.isFailure,
            'isLoading': response.isLoading,
            'hasData': response.data != null,
            'hasError': response.error != null,
          },
        ),
      );
    }
  }

  /// Create SubscriptionError from API response
  SubscriptionError _createErrorFromApiResponse(
    ApiResponse<dynamic> response,
    String operationName,
  ) {
    final errorMessage = response.error ?? 'خطأ غير معروف من الخادم';

    // Try to parse error message for specific error types
    if (errorMessage.toLowerCase().contains('database') ||
        errorMessage.toLowerCase().contains('db')) {
      return SubscriptionError.databaseError(
        message: 'خطأ في قاعدة البيانات',
        code: 'DATABASE_ERROR',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    if (errorMessage.toLowerCase().contains('network') ||
        errorMessage.toLowerCase().contains('connection') ||
        errorMessage.toLowerCase().contains('timeout')) {
      return SubscriptionError.networkError(
        message: 'خطأ في الاتصال بالخادم',
        code: 'NETWORK_ERROR',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    if (errorMessage.toLowerCase().contains('unauthorized') ||
        errorMessage.toLowerCase().contains('authentication') ||
        errorMessage.toLowerCase().contains('auth')) {
      return SubscriptionError.authenticationError(
        message: 'خطأ في المصادقة',
        code: 'AUTH_ERROR',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    if (errorMessage.toLowerCase().contains('validation') ||
        errorMessage.toLowerCase().contains('invalid')) {
      return SubscriptionError.validationError(
        message: 'بيانات غير صحيحة',
        fieldErrors: {'general': errorMessage},
        code: 'VALIDATION_ERROR',
      );
    }

    // Default to server error
    return SubscriptionError.serverError(
      message: 'خطأ في الخادم',
      code: 'SERVER_ERROR',
      details: {'originalError': errorMessage, 'operation': operationName},
    );
  }

  /// Create SubscriptionError from exception
  SubscriptionError _createErrorFromException(
    dynamic exception,
    String operationName,
  ) {
    final errorMessage = exception.toString();

    // Network-related exceptions
    if (errorMessage.contains('SocketException') ||
        errorMessage.contains('NetworkException') ||
        errorMessage.contains('HttpException') ||
        errorMessage.contains('TimeoutException') ||
        errorMessage.contains('Network socket exception')) {
      return SubscriptionError.networkError(
        message: 'خطأ في الاتصال بالشبكة',
        code: 'NETWORK_EXCEPTION',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    // Format/parsing exceptions
    if (errorMessage.contains('FormatException') ||
        errorMessage.contains('JsonException') ||
        errorMessage.contains('TypeError')) {
      return SubscriptionError.serverError(
        message: 'خطأ في تنسيق البيانات',
        code: 'DATA_FORMAT_ERROR',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    // Authentication exceptions
    if (errorMessage.contains('AuthException') ||
        errorMessage.contains('Unauthorized')) {
      return SubscriptionError.authenticationError(
        message: 'خطأ في المصادقة',
        code: 'AUTH_EXCEPTION',
        details: {'originalError': errorMessage, 'operation': operationName},
      );
    }

    // Default to unknown error
    return SubscriptionError.unknownError(
      message: 'حدث خطأ غير متوقع',
      code: 'UNKNOWN_EXCEPTION',
      details: {'originalError': errorMessage, 'operation': operationName},
    );
  }
}

/// Provider for SubscriptionService
@riverpod
SubscriptionService subscriptionService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return SubscriptionServiceImpl(apiClient: apiClient);
}

/// Provider for SubscriptionService with custom logger
@riverpod
SubscriptionService subscriptionServiceWithLogger(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  final logger = Logger();
  return SubscriptionServiceImpl(apiClient: apiClient, logger: logger);
}

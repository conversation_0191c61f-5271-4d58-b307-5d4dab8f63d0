import 'dart:async';
import 'dart:convert';

import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import 'package:logging/logging.dart';

/// Enhanced WebSocket Service following Forever Plan architecture
/// 
/// Handles real-time communication with the backend
/// Features automatic reconnection, heartbeat, and message handling
class WebSocketService {
  static WebSocketService? _instance;
  static WebSocketService get instance => _instance ??= WebSocketService._();

  WebSocketService._();

  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  
  bool _isConnected = false;
  bool _isConnecting = false;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 5);
  static const Duration _heartbeatInterval = Duration(seconds: 30);

  final _messageController = StreamController<WebSocketMessage>.broadcast();
  final _connectionController = StreamController<bool>.broadcast();
  final _logger = Logger('WebSocketService');

  /// Stream of incoming WebSocket messages
  Stream<WebSocketMessage> get messageStream => _messageController.stream;

  /// Stream of connection status changes
  Stream<bool> get connectionStream => _connectionController.stream;

  /// Current connection status
  bool get isConnected => _isConnected;

  /// Connect to WebSocket server
  Future<void> connect({String? token}) async {
    if (_isConnecting || _isConnected) {
      return;
    }

    _isConnecting = true;
    _logger.info('Connecting to WebSocket server...');

    try {
      final wsUrl = _buildWebSocketUrl();
      final headers = <String, String>{};
      
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      _channel = IOWebSocketChannel.connect(
        wsUrl,
        headers: headers,
        pingInterval: _heartbeatInterval,
      );

      await _channel!.ready;

      _isConnected = true;
      _isConnecting = false;
      _reconnectAttempts = 0;
      
      _connectionController.add(true);
      _logger.info('WebSocket connected successfully');

      // Start listening to messages
      _startListening();
      
      // Start heartbeat
      _startHeartbeat();

    } catch (e) {
      _isConnecting = false;
      _isConnected = false;
      _connectionController.add(false);
      
      _logger.severe('WebSocket connection failed', e);
      
      if (_shouldReconnect && _reconnectAttempts < _maxReconnectAttempts) {
        _scheduleReconnect();
      }
    }
  }

  /// Disconnect from WebSocket server
  void disconnect() {
    _logger.info('Disconnecting from WebSocket server');
    
    _shouldReconnect = false;
    _isConnected = false;
    _isConnecting = false;
    
    _heartbeatTimer?.cancel();
    _reconnectTimer?.cancel();
    _subscription?.cancel();
    
    _channel?.sink.close();
    _channel = null;
    
    _connectionController.add(false);
  }

  /// Send message to WebSocket server
  void sendMessage(Map<String, dynamic> message) {
    if (!_isConnected || _channel == null) {
      _logger.warning('Cannot send message: WebSocket not connected');
      return;
    }

    try {
      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);
      _logger.fine('Message sent: $jsonMessage');
    } catch (e) {
      _logger.severe('Failed to send WebSocket message', e);
    }
  }

  /// Send ping message
  void sendPing() {
    sendMessage({
      'type': 'ping',
      'event': 'heartbeat',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Subscribe to specific event type
  Stream<WebSocketMessage> subscribeToEvent(String eventType) {
    return messageStream.where((message) => message.type == eventType);
  }

  /// Subscribe to cart updates
  Stream<WebSocketMessage> subscribeToCartUpdates() {
    return subscribeToEvent('cart');
  }

  /// Subscribe to order updates
  Stream<WebSocketMessage> subscribeToOrderUpdates() {
    return subscribeToEvent('order');
  }

  /// Subscribe to inventory updates
  Stream<WebSocketMessage> subscribeToInventoryUpdates() {
    return subscribeToEvent('inventory');
  }

  /// Build WebSocket URL
  String _buildWebSocketUrl() {
    // Use production backend URL for WebSocket
    const baseUrl = 'https://backend-go-8klm.onrender.com';
    final wsUrl = baseUrl.replaceFirst('https', 'wss');
    return '$wsUrl/api/v1/ws';
  }

  /// Start listening to WebSocket messages
  void _startListening() {
    _subscription = _channel!.stream.listen(
      (data) {
        try {
          final jsonData = json.decode(data as String);
          final message = WebSocketMessage.fromJson(jsonData);
          
          _logger.fine('Message received: ${message.type}/${message.event}');
          
          // Handle system messages
          if (message.type == 'system') {
            _handleSystemMessage(message);
          } else {
            _messageController.add(message);
          }
          
        } catch (e) {
          _logger.severe('Failed to parse WebSocket message', e);
        }
      },
      onError: (error) {
        _logger.severe('WebSocket stream error', error);
        _handleConnectionError();
      },
      onDone: () {
        _logger.info('WebSocket stream closed');
        _handleConnectionClosed();
      },
    );
  }

  /// Handle system messages
  void _handleSystemMessage(WebSocketMessage message) {
    switch (message.event) {
      case 'connected':
        _logger.info('WebSocket connection confirmed by server');
        break;
      case 'pong':
        _logger.fine('Pong received from server');
        break;
      default:
        _logger.fine('System message: ${message.event}');
    }
  }

  /// Start heartbeat timer
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      if (_isConnected) {
        sendPing();
      } else {
        timer.cancel();
      }
    });
  }

  /// Handle connection error
  void _handleConnectionError() {
    _isConnected = false;
    _connectionController.add(false);
    
    if (_shouldReconnect && _reconnectAttempts < _maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  /// Handle connection closed
  void _handleConnectionClosed() {
    _isConnected = false;
    _connectionController.add(false);
    
    if (_shouldReconnect && _reconnectAttempts < _maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    _reconnectAttempts++;
    _logger.info('Scheduling reconnect attempt $_reconnectAttempts/$_maxReconnectAttempts');
    
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(_reconnectDelay, () {
      if (_shouldReconnect) {
        connect();
      }
    });
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _messageController.close();
    _connectionController.close();
  }
}

/// WebSocket message model
class WebSocketMessage {
  final String type;
  final String event;
  final Map<String, dynamic> data;
  final String? userId;
  final DateTime timestamp;

  WebSocketMessage({
    required this.type,
    required this.event,
    required this.data,
    this.userId,
    required this.timestamp,
  });

  factory WebSocketMessage.fromJson(Map<String, dynamic> json) {
    return WebSocketMessage(
      type: json['type'] ?? '',
      event: json['event'] ?? '',
      data: json['data'] ?? {},
      userId: json['user_id'],
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'event': event,
      'data': data,
      'user_id': userId,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'WebSocketMessage(type: $type, event: $event, userId: $userId)';
  }
}

import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiResponse {
  final int statusCode;
  final Map<String, dynamic> data;
  final String? error;

  ApiResponse({
    required this.statusCode,
    required this.data,
    this.error,
  });

  bool get isSuccess => statusCode >= 200 && statusCode < 300;
}

class ApiClient {
  static const String baseUrl = 'https://backend-go-8klm.onrender.com';
  
  // Headers for all requests
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // GET request
  Future<ApiResponse> get(String endpoint) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
      );

      final data = json.decode(response.body);
      return ApiResponse(
        statusCode: response.statusCode,
        data: data,
      );
    } catch (e) {
      return ApiResponse(
        statusCode: 500,
        data: {},
        error: e.toString(),
      );
    }
  }

  // POST request
  Future<ApiResponse> post(String endpoint, Map<String, dynamic> data) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
        body: json.encode(data),
      );

      final responseData = json.decode(response.body);
      return ApiResponse(
        statusCode: response.statusCode,
        data: responseData,
      );
    } catch (e) {
      return ApiResponse(
        statusCode: 500,
        data: {},
        error: e.toString(),
      );
    }
  }

  // PUT request
  Future<ApiResponse> put(String endpoint, Map<String, dynamic> data) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
        body: json.encode(data),
      );

      final responseData = json.decode(response.body);
      return ApiResponse(
        statusCode: response.statusCode,
        data: responseData,
      );
    } catch (e) {
      return ApiResponse(
        statusCode: 500,
        data: {},
        error: e.toString(),
      );
    }
  }

  // DELETE request
  Future<ApiResponse> delete(String endpoint) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
      );

      final data = json.decode(response.body);
      return ApiResponse(
        statusCode: response.statusCode,
        data: data,
      );
    } catch (e) {
      return ApiResponse(
        statusCode: 500,
        data: {},
        error: e.toString(),
      );
    }
  }

  // Search products with filters
  Future<ApiResponse> searchProducts({
    String? query,
    String? mainCategoryId,
    Map<String, String>? filters,
    int? page,
    int? limit,
  }) async {
    try {
      final queryParams = <String, String>{};
      
      if (query != null && query.isNotEmpty) {
        queryParams['q'] = query;
      }
      
      if (mainCategoryId != null) {
        queryParams['main_category_id'] = mainCategoryId;
      }
      
      if (filters != null) {
        filters.forEach((key, value) {
          queryParams['filter[$key]'] = value;
        });
      }
      
      if (page != null) {
        queryParams['page'] = page.toString();
      }
      
      if (limit != null) {
        queryParams['limit'] = limit.toString();
      }

      final uri = Uri.parse('$baseUrl/api/v1/products/search').replace(queryParameters: queryParams);
      
      final response = await http.get(uri, headers: _headers);
      final data = json.decode(response.body);
      
      return ApiResponse(
        statusCode: response.statusCode,
        data: data,
      );
    } catch (e) {
      return ApiResponse(
        statusCode: 500,
        data: {},
        error: e.toString(),
      );
    }
  }
} 
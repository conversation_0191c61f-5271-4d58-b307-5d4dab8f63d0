import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../config/backend_config.dart';
import '../models/carnow_user.dart';
import '../models/carnow_wallet.dart';
import '../models/carnow_transaction.dart';
import '../errors/app_error.dart';
import '../../../core/auth/unified_auth_provider.dart';

/// Simple CarNow API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
class CarnowApiService {
  CarnowApiService(this._ref) : _dio = Dio() {
    _setupInterceptors();
  }

  final Ref _ref;
  final Dio _dio;
  static const Duration _timeout = Duration(seconds: 30);

  /// Setup basic interceptors for logging and auth
  void _setupInterceptors() {
    _dio.options.baseUrl = CarnowBackendConfig.baseUrlSync;
    _dio.options.connectTimeout = _timeout;
    _dio.options.receiveTimeout = _timeout;
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add auth interceptor
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // FIXED: Use convenience providers for token (Forever Plan compliant)
          try {
            final isAuthenticated = _ref.read(isAuthenticatedProvider);
            final accessToken = _ref.read(currentAccessTokenProvider);
            if (isAuthenticated && accessToken != null) {
              options.headers['Authorization'] = 'Bearer $accessToken';
            }
          } catch (e) {
            // Continue without token if auth system fails
          }
          handler.next(options);
        },
        onError: (error, handler) {
          final appError = _handleDioError(error);
          handler.reject(
            DioException(requestOptions: error.requestOptions, error: appError),
          );
        },
      ),
    );
  }

  /// Handle Dio errors and convert to AppError
  AppError _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return AppError.network(message: 'انتهت مهلة الاتصال');
      case DioExceptionType.connectionError:
        return AppError.network(message: 'خطأ في الاتصال بالشبكة');
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        switch (statusCode) {
          case 401:
            return AppError.authentication(message: 'غير مصرح لك بالوصول');
          case 403:
            return AppError.permission(message: 'ليس لديك صلاحية');
          case 404:
            return AppError.notFound(message: 'المورد غير موجود');
          case 500:
            return AppError.network(message: 'خطأ في الخادم');
          default:
            return AppError.network(message: 'خطأ في الطلب');
        }
      default:
        return AppError.unexpected(message: 'حدث خطأ غير متوقع');
    }
  }

  // ==================== Health Checks ====================

  /// Check backend health
  Future<bool> checkHealth() async {
    try {
      await _dio.get('/health');
      return true;
    } catch (_) {
      return false;
    }
  }

  /// Check backend readiness
  Future<bool> checkReady() async {
    try {
      await _dio.get('/ready');
      return true;
    } catch (_) {
      return false;
    }
  }

  // ==================== User Management ====================

  /// Get user by email
  Future<CarnowUser> getUserByEmail(String email) async {
    final response = await _dio.get('/api/v1/users/email/$email');
    return CarnowUser.fromJson(response.data['data'] ?? response.data);
  }

  /// Create new user
  Future<CarnowUser> createUser(CreateCarnowUserRequest request) async {
    final response = await _dio.post('/api/v1/users', data: request.toJson());
    return CarnowUser.fromJson(response.data['data'] ?? response.data);
  }

  /// Update user
  Future<CarnowUser> updateUser(
    String userId,
    UpdateCarnowUserRequest request,
  ) async {
    final response = await _dio.put(
      '/api/v1/users/$userId',
      data: request.toJson(),
    );
    return CarnowUser.fromJson(response.data['data'] ?? response.data);
  }

  /// Get admin users
  Future<List<CarnowUser>> getAdminUsers() async {
    final response = await _dio.get('/api/v1/admin/users');
    final List<dynamic> users = response.data['data'] ?? response.data;
    return users.map((json) => CarnowUser.fromJson(json)).toList();
  }

  // ==================== Wallet Management ====================

  /// Get wallet by user ID
  Future<CarnowWallet> getWalletByUserId(String userId) async {
    final response = await _dio.get('/api/v1/wallets/$userId');
    return CarnowWallet.fromJson(response.data['data'] ?? response.data);
  }

  /// Create wallet
  Future<CarnowWallet> createWallet(CreateWalletRequest request) async {
    final response = await _dio.post('/api/v1/wallets', data: request.toJson());
    return CarnowWallet.fromJson(response.data['data'] ?? response.data);
  }

  /// Deposit to wallet
  Future<CarnowTransaction> depositToWallet(
    String walletId,
    DepositRequest request,
  ) async {
    final response = await _dio.post(
      '/api/v1/wallets/$walletId/deposit',
      data: request.toJson(),
    );
    return CarnowTransaction.fromJson(response.data['data'] ?? response.data);
  }

  /// Withdraw from wallet
  Future<CarnowTransaction> withdrawFromWallet(
    String walletId,
    WithdrawRequest request,
  ) async {
    final response = await _dio.post(
      '/api/v1/wallets/$walletId/withdraw',
      data: request.toJson(),
    );
    return CarnowTransaction.fromJson(response.data['data'] ?? response.data);
  }

  /// Get wallet transactions
  Future<List<CarnowTransaction>> getWalletTransactions(String walletId) async {
    final response = await _dio.get('/api/v1/wallets/$walletId/transactions');
    final List<dynamic> transactions = response.data['data'] ?? response.data;
    return transactions
        .map((json) => CarnowTransaction.fromJson(json))
        .toList();
  }

  // ==================== Financial Operations ====================

  /// Create financial operation
  Future<CarnowFinancialOperation> createFinancialOperation(
    CreateFinancialOperationRequest request,
  ) async {
    final response = await _dio.post(
      '/api/v1/financial-operations',
      data: request.toJson(),
    );
    return CarnowFinancialOperation.fromJson(
      response.data['data'] ?? response.data,
    );
  }

  /// Process financial operation
  Future<CarnowFinancialOperation> processFinancialOperation(
    String operationId,
    ProcessOperationRequest request,
  ) async {
    final response = await _dio.post(
      '/api/v1/financial-operations/$operationId/process',
      data: request.toJson(),
    );
    return CarnowFinancialOperation.fromJson(
      response.data['data'] ?? response.data,
    );
  }

  /// Get user financial operations
  Future<List<CarnowFinancialOperation>> getUserFinancialOperations(
    String userId,
  ) async {
    final response = await _dio.get(
      '/api/v1/users/$userId/financial-operations',
    );
    final List<dynamic> operations = response.data['data'] ?? response.data;
    return operations
        .map((json) => CarnowFinancialOperation.fromJson(json))
        .toList();
  }

  // ==================== Admin Operations ====================

  /// Get admin stats
  Future<Map<String, dynamic>> getAdminStats() async {
    final response = await _dio.get('/api/v1/admin/stats');
    return response.data['data'] ?? response.data;
  }

  /// Static initialize method for compatibility
  static Future<void> initialize() async {
    // Simple initialization - just ensure Dio is ready
  }

  /// Static dispose method for compatibility with BackendResilienceInitializer
  static void disposeService() {
    // Static dispose method - handles cleanup of shared resources if any
    // Individual instances are disposed through instance dispose() method
  }

  /// Dispose resources
  void dispose() {
    _dio.close();
  }
}

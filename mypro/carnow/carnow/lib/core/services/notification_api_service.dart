import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';
import '../errors/app_error.dart';

part 'notification_api_service.g.dart';

final _logger = Logger('NotificationApiService');

/// Simple Notification API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
NotificationApiService notificationApiService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return NotificationApiService(apiClient);
}

class NotificationApiService {
  const NotificationApiService(this._apiClient);
  
  final SimpleApiClient _apiClient;

  /// Get user notifications with pagination
  Future<List<Map<String, dynamic>>> getUserNotifications({
    int page = 1,
    int limit = 20,
    bool unreadOnly = false,
  }) async {
    try {
      _logger.info('Fetching user notifications...');
      final response = await _apiClient.get<Map<String, dynamic>>('/notifications', queryParameters: {
        'page': page.toString(),
        'limit': limit.toString(),
        'unread_only': unreadOnly.toString(),
      });

      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch notifications: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch notifications',
          code: 'notification_fetch_failed',
        );
      }

      final notifications = response.data!['notifications'] as List<dynamic>?;
      return List<Map<String, dynamic>>.from(notifications ?? []);
    } catch (e) {
      _logger.severe('Error fetching notifications: $e');
      throw AppError.network(
        message: 'Error fetching notifications',
        code: 'notification_fetch_error',
        originalError: e,
      );
    }
  }

  /// Get unread notifications count
  Future<int> getUnreadCount() async {
    try {
      _logger.info('Fetching unread notifications count...');
      final response = await _apiClient.get<Map<String, dynamic>>('/notifications/unread-count');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch unread count: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch unread count',
          code: 'unread_count_fetch_failed',
        );
      }
      
      return response.data!['count'] ?? 0;
    } catch (e) {
      _logger.severe('Error fetching unread count: $e');
      throw AppError.network(
        message: 'Error fetching unread count',
        code: 'unread_count_fetch_error',
        originalError: e,
      );
    }
  }

  /// Send a notification
  Future<void> sendNotification({
    required String userId,
    required String title,
    required String message,
    String? type,
    String? relatedEntityId,
    String? relatedEntityType,
  }) async {
    try {
      _logger.info('Sending notification to user: $userId');
      final response = await _apiClient.post<Map<String, dynamic>>('/notifications', data: {
        'user_id': userId,
        'title': title,
        'message': message,
        'type': type,
        'related_entity_id': relatedEntityId,
        'related_entity_type': relatedEntityType,
      });
      
      if (!response.isSuccess) {
        _logger.warning('Failed to send notification: ${response.error}');
        throw const AppError.network(
          message: 'Failed to send notification',
          code: 'notification_send_failed',
        );
      }
    } catch (e) {
      _logger.severe('Error sending notification: $e');
      throw AppError.network(
        message: 'Error sending notification',
        code: 'notification_send_error',
        originalError: e,
      );
    }
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      _logger.info('Marking notification as read: $notificationId');
      final response = await _apiClient.put<Map<String, dynamic>>('/notifications/$notificationId/read');
      
      if (!response.isSuccess) {
        _logger.warning('Failed to mark notification as read: ${response.error}');
        throw const AppError.network(
          message: 'Failed to mark notification as read',
          code: 'mark_read_failed',
        );
      }
    } catch (e) {
      _logger.severe('Error marking notification as read: $e');
      throw AppError.network(
        message: 'Error marking notification as read',
        code: 'mark_read_error',
        originalError: e,
      );
    }
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    try {
      _logger.info('Clearing all notifications...');
      final response = await _apiClient.delete<Map<String, dynamic>>('/notifications');
      
      if (!response.isSuccess) {
        _logger.warning('Failed to clear notifications: ${response.error}');
        throw const AppError.network(
          message: 'Failed to clear notifications',
          code: 'clear_notifications_failed',
        );
      }
    } catch (e) {
      _logger.severe('Error clearing notifications: $e');
      throw AppError.network(
        message: 'Error clearing notifications',
        code: 'clear_notifications_error',
        originalError: e,
      );
    }
  }

  /// Get unread notification count
  Future<int> getUnreadNotificationCount() async {
    try {
      _logger.info('Fetching unread notification count...');
      final response = await _apiClient.get<Map<String, dynamic>>('/notifications/unread-count');

      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch unread count: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch unread count',
          code: 'unread_count_fetch_failed',
        );
      }

      return response.data!['unread_count'] as int? ?? 0;
    } catch (e) {
      _logger.severe('Error fetching unread count: $e');
      throw AppError.network(
        message: 'Error fetching unread count',
        code: 'unread_count_fetch_error',
        originalError: e,
      );
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      _logger.info('Marking all notifications as read...');
      final response = await _apiClient.patch<Map<String, dynamic>>('/notifications/read-all');

      if (!response.isSuccess) {
        _logger.warning('Failed to mark all as read: ${response.error}');
        throw const AppError.network(
          message: 'Failed to mark all notifications as read',
          code: 'mark_all_read_failed',
        );
      }
    } catch (e) {
      _logger.severe('Error marking all as read: $e');
      throw AppError.network(
        message: 'Error marking all notifications as read',
        code: 'mark_all_read_error',
        originalError: e,
      );
    }
  }
}
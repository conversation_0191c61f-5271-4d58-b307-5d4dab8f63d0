import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

final _logger = Logger();

/// خدمة الـ Cache للتطبيق مع دعم TTL
class CacheService {
  CacheService._();
  static CacheService? _instance;
  static SharedPreferences? _prefs;

  static const String _keyPrefix = 'carnow_cache_';
  static const String _expiryPrefix = 'carnow_expiry_';
  static const Duration _defaultTtl = Duration(minutes: 30);

  /// الحصول على instance واحد من الخدمة
  static Future<CacheService> getInstance() async {
    if (_instance == null) {
      _instance = CacheService._();
      await _instance!._init();
    }
    return _instance!;
  }

  /// تهيئة الخدمة
  Future<void> _init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _logger.i('CacheService initialized successfully');

      // تنظيف الـ Cache المنتهي الصلاحية عند البدء
      await _cleanExpiredCache();
    } catch (e) {
      _logger.e('Failed to initialize CacheService: $e');
      rethrow;
    }
  }

  /// حفظ بيانات في الـ Cache
  Future<bool> setString({
    required String key,
    required String value,
    Duration? ttl,
  }) async {
    try {
      final cacheKey = _keyPrefix + key;
      final expiryKey = _expiryPrefix + key;
      final expiry = DateTime.now().add(ttl ?? _defaultTtl);

      final result = await Future.wait([
        _prefs!.setString(cacheKey, value),
        _prefs!.setString(expiryKey, expiry.toIso8601String()),
      ]);

      final success = result.every((r) => r == true);

      if (success) {
        _logger.i('Cached data for key: $key (expires: ${expiry.toLocal()})');
      } else {
        _logger.i('Failed to cache data for key: $key');
      }

      return success;
    } catch (e) {
      _logger.e('Error caching data for key: $key - $e');
      return false;
    }
  }

  /// استرجاع بيانات من الـ Cache
  Future<String?> getString(String key) async {
    try {
      final cacheKey = _keyPrefix + key;
      final expiryKey = _expiryPrefix + key;

      // فحص انتهاء الصلاحية
      final expiryString = _prefs!.getString(expiryKey);
      if (expiryString == null) {
        return null;
      }

      final expiry = DateTime.parse(expiryString);
      if (DateTime.now().isAfter(expiry)) {
        await _removeKey(key);
        return null;
      }

      // استرجاع البيانات
      final value = _prefs!.getString(cacheKey);

      return value;
    } catch (e) {
      _logger.e('Error retrieving cached data for key: $key - $e');
      return null;
    }
  }

  /// حفظ Object كـ JSON
  Future<bool> setObject({
    required String key,
    required Map<String, dynamic> object,
    Duration? ttl,
  }) async {
    try {
      final jsonString = jsonEncode(object);
      return await setString(key: key, value: jsonString, ttl: ttl);
    } catch (e) {
      _logger.e('Error caching object for key: $key - $e');
      return false;
    }
  }

  /// استرجاع Object من JSON
  Future<Map<String, dynamic>?> getObject(String key) async {
    try {
      final jsonString = await getString(key);
      if (jsonString == null) return null;

      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      _logger.e('Error retrieving cached object for key: $key - $e');
      return null;
    }
  }

  /// حفظ قائمة Objects
  Future<bool> setList({
    required String key,
    required List<Map<String, dynamic>> list,
    Duration? ttl,
  }) async {
    try {
      final jsonString = jsonEncode(list);
      return await setString(key: key, value: jsonString, ttl: ttl);
    } catch (e) {
      _logger.e('Error caching list for key: $key - $e');
      return false;
    }
  }

  /// استرجاع قائمة Objects
  Future<List<Map<String, dynamic>>?> getList(String key) async {
    try {
      final jsonString = await getString(key);
      if (jsonString == null) return null;

      final decoded = jsonDecode(jsonString);
      if (decoded is List) {
        return decoded.cast<Map<String, dynamic>>();
      }

      _logger.i('Cached data for key $key is not a List');
      return null;
    } catch (e) {
      _logger.e('Error retrieving cached list for key: $key - $e');
      return null;
    }
  }

  /// فحص وجود المفتاح في الـ Cache
  Future<bool> hasKey(String key) async {
    try {
      final value = await getString(key);
      return value != null;
    } catch (e) {
      _logger.e('Error checking key existence: $key - $e');
      return false;
    }
  }

  /// حذف مفتاح من الـ Cache
  Future<bool> remove(String key) async {
    try {
      return await _removeKey(key);
    } catch (e) {
      _logger.e('Error removing cached data for key: $key - $e');
      return false;
    }
  }

  /// حذف جميع بيانات الـ Cache
  Future<bool> clear() async {
    try {
      final keys = _prefs!.getKeys();
      final cacheKeys = keys.where(
        (key) => key.startsWith(_keyPrefix) || key.startsWith(_expiryPrefix),
      );

      for (final key in cacheKeys) {
        await _prefs!.remove(key);
      }

      _logger.i('Cache cleared successfully');
      return true;
    } catch (e) {
      _logger.e('Error clearing cache: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات الـ Cache
  Future<CacheStats> getStats() async {
    try {
      final keys = _prefs!.getKeys();
      final cacheKeys = keys.where((key) => key.startsWith(_keyPrefix));
      final expiryKeys = keys.where((key) => key.startsWith(_expiryPrefix));

      var totalSize = 0;
      var expiredCount = 0;
      final now = DateTime.now();

      for (final key in cacheKeys) {
        final value = _prefs!.getString(key);
        if (value != null) {
          totalSize += value.length;
        }
      }

      for (final expiryKey in expiryKeys) {
        final expiryString = _prefs!.getString(expiryKey);
        if (expiryString != null) {
          try {
            final expiry = DateTime.parse(expiryString);
            if (now.isAfter(expiry)) {
              expiredCount++;
            }
          } catch (e) {
            expiredCount++;
          }
        }
      }

      return CacheStats(
        totalItems: cacheKeys.length,
        totalSizeBytes: totalSize,
        expiredItems: expiredCount,
      );
    } catch (e) {
      _logger.e('Error getting cache stats: $e');
      return CacheStats(totalItems: 0, totalSizeBytes: 0, expiredItems: 0);
    }
  }

  /// تنظيف الـ Cache المنتهي الصلاحية
  Future<int> cleanExpiredCache() async => _cleanExpiredCache();

  /// حذف مفتاح محدد (داخلي)
  Future<bool> _removeKey(String key) async {
    final cacheKey = _keyPrefix + key;
    final expiryKey = _expiryPrefix + key;

    final results = await Future.wait([
      _prefs!.remove(cacheKey),
      _prefs!.remove(expiryKey),
    ]);

    final success = results.every((r) => r == true);
    if (success) {
      _logger.i('Removed cached data for key: $key');
    }

    return success;
  }

  /// تنظيف الـ Cache المنتهي الصلاحية (داخلي)
  Future<int> _cleanExpiredCache() async {
    try {
      final keys = _prefs!.getKeys();
      final expiryKeys = keys.where((key) => key.startsWith(_expiryPrefix));
      final now = DateTime.now();
      var cleanedCount = 0;

      for (final expiryKey in expiryKeys) {
        final expiryString = _prefs!.getString(expiryKey);
        if (expiryString != null) {
          try {
            final expiry = DateTime.parse(expiryString);
            if (now.isAfter(expiry)) {
              final originalKey = expiryKey.substring(_expiryPrefix.length);
              await _removeKey(originalKey);
              cleanedCount++;
            }
          } catch (e) {
            // إذا كان التاريخ غير صالح، احذف المفتاح
            final originalKey = expiryKey.substring(_expiryPrefix.length);
            await _removeKey(originalKey);
            cleanedCount++;
          }
        }
      }

      if (cleanedCount > 0) {
        _logger.i('Cleaned $cleanedCount expired cache entries');
      }

      return cleanedCount;
    } catch (e) {
      _logger.e('Error cleaning expired cache: $e');
      return 0;
    }
  }
}

/// إحصائيات الـ Cache
class CacheStats {
  CacheStats({
    required this.totalItems,
    required this.totalSizeBytes,
    required this.expiredItems,
  });
  final int totalItems;
  final int totalSizeBytes;
  final int expiredItems;

  /// الحجم بالـ KB
  double get totalSizeKB => totalSizeBytes / 1024;

  /// الحجم بالـ MB
  double get totalSizeMB => totalSizeKB / 1024;

  /// نسبة العناصر المنتهية الصلاحية
  double get expiredPercentage =>
      totalItems > 0 ? (expiredItems / totalItems) * 100 : 0;

  @override
  String toString() =>
      'CacheStats(items: $totalItems, size: ${totalSizeKB.toStringAsFixed(1)}KB, expired: $expiredItems/${expiredPercentage.toStringAsFixed(1)}%)';
}

/// مفاتيح الـ Cache الشائعة
class CacheKeys {
  static const String products = 'products';
  static const String categories = 'categories';
  static const String userProfile = 'user_profile';
  static const String recentSearches = 'recent_searches';
  static const String favoriteProducts = 'favorite_products';
  static const String cartItems = 'cart_items';
  static const String homePageData = 'home_page_data';
  static const String featuredProducts = 'featured_products';
  static const String userVehicles = 'user_vehicles';
  static const String sellerProfile = 'seller_profile';

  /// بناء مفتاح مع معرف فريد
  static String withId(String base, String id) => '${base}_$id';

  /// بناء مفتاح مع parameters
  static String withParams(String base, Map<String, String> params) {
    final sortedParams = params.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    final paramString = sortedParams
        .map((e) => '${e.key}=${e.value}')
        .join('&');
    return '${base}_$paramString';
  }
}

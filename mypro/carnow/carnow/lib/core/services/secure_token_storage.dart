import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logging/logging.dart';

/// Secure token storage service for JWT tokens
/// Implements production-grade security practices for token management
class SecureTokenStorage {
  static final _logger = Logger('SecureTokenStorage');

  // Secure storage instance with enhanced security options
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'carnow_secure_prefs',
      preferencesKeyPrefix: 'carnow_',
    ),
    iOptions: IOSOptions(
      groupId: 'group.com.carnow.app',
      accountName: 'CarNow',
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
    lOptions: LinuxOptions(),
    wOptions: WindowsOptions(),
    mOptions: MacOsOptions(
      groupId: 'group.com.carnow.app',
      accountName: 'CarNow',
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Storage keys
  static const _accessTokenKey = 'carnow_access_token';
  static const _refreshTokenKey = 'carnow_refresh_token';
  static const _tokenExpiryKey = 'carnow_token_expiry';
  static const _userDataKey = 'carnow_user_data';
  static const _tokenTypeKey = 'carnow_token_type';

  /// Store authentication tokens securely
  static Future<void> storeTokens({
    required String accessToken,
    required String refreshToken,
    required DateTime expiresAt,
    required Map<String, dynamic> userData,
    String tokenType = 'Bearer',
  }) async {
    try {
      _logger.info('🔐 Storing authentication tokens securely');

      // Store tokens with encryption
      await Future.wait([
        _storage.write(key: _accessTokenKey, value: accessToken),
        _storage.write(key: _refreshTokenKey, value: refreshToken),
        _storage.write(
          key: _tokenExpiryKey,
          value: expiresAt.toIso8601String(),
        ),
        _storage.write(key: _userDataKey, value: jsonEncode(userData)),
        _storage.write(key: _tokenTypeKey, value: tokenType),
      ]);

      _logger.info('✅ Tokens stored successfully');
    } catch (e, stackTrace) {
      _logger.severe('❌ Failed to store tokens: $e', e, stackTrace);
      rethrow;
    }
  }

  /// Retrieve access token
  static Future<String?> getAccessToken() async {
    try {
      final token = await _storage.read(key: _accessTokenKey);
      if (token != null) {
        _logger.fine('🔑 Access token retrieved');
      }
      return token;
    } catch (e) {
      _logger.warning('⚠️ Failed to retrieve access token: $e');
      return null;
    }
  }

  /// Retrieve refresh token
  static Future<String?> getRefreshToken() async {
    try {
      final token = await _storage.read(key: _refreshTokenKey);
      if (token != null) {
        _logger.fine('🔄 Refresh token retrieved');
      }
      return token;
    } catch (e) {
      _logger.warning('⚠️ Failed to retrieve refresh token: $e');
      return null;
    }
  }

  /// Get token expiry time
  static Future<DateTime?> getTokenExpiry() async {
    try {
      final expiryString = await _storage.read(key: _tokenExpiryKey);
      if (expiryString != null) {
        return DateTime.parse(expiryString);
      }
      return null;
    } catch (e) {
      _logger.warning('⚠️ Failed to retrieve token expiry: $e');
      return null;
    }
  }

  /// Get stored user data
  static Future<Map<String, dynamic>?> getUserData() async {
    try {
      final userDataString = await _storage.read(key: _userDataKey);
      if (userDataString != null) {
        return jsonDecode(userDataString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      _logger.warning('⚠️ Failed to retrieve user data: $e');
      return null;
    }
  }

  /// Get token type
  static Future<String?> getTokenType() async {
    try {
      return await _storage.read(key: _tokenTypeKey);
    } catch (e) {
      _logger.warning('⚠️ Failed to retrieve token type: $e');
      return 'Bearer'; // Default fallback
    }
  }

  /// Check if access token is expired
  static Future<bool> isTokenExpired() async {
    try {
      final expiry = await getTokenExpiry();
      if (expiry == null) return true;

      // Add 30 second buffer to prevent edge cases
      final now = DateTime.now().add(const Duration(seconds: 30));
      final isExpired = now.isAfter(expiry);

      if (isExpired) {
        _logger.info('⏰ Access token has expired');
      }

      return isExpired;
    } catch (e) {
      _logger.warning('⚠️ Failed to check token expiry: $e');
      return true; // Assume expired on error
    }
  }

  /// Check if we have valid tokens
  static Future<bool> hasValidTokens() async {
    try {
      final accessToken = await getAccessToken();
      final refreshToken = await getRefreshToken();

      if (accessToken == null || refreshToken == null) {
        _logger.info('❌ Missing tokens');
        return false;
      }

      // Check if access token is expired
      final isExpired = await isTokenExpired();
      if (!isExpired) {
        _logger.info('✅ Valid access token available');
        return true;
      }

      // Access token expired, but we have refresh token
      _logger.info('🔄 Access token expired, refresh token available');
      return true; // Can refresh
    } catch (e) {
      _logger.warning('⚠️ Failed to check token validity: $e');
      return false;
    }
  }

  /// Get authorization header value
  static Future<String?> getAuthorizationHeader() async {
    try {
      final accessToken = await getAccessToken();
      final tokenType = await getTokenType();

      if (accessToken != null) {
        return '$tokenType $accessToken';
      }

      return null;
    } catch (e) {
      _logger.warning('⚠️ Failed to get authorization header: $e');
      return null;
    }
  }

  /// Clear all stored tokens and user data
  static Future<void> clearTokens() async {
    try {
      _logger.info('🧹 Clearing all stored tokens');

      await Future.wait([
        _storage.delete(key: _accessTokenKey),
        _storage.delete(key: _refreshTokenKey),
        _storage.delete(key: _tokenExpiryKey),
        _storage.delete(key: _userDataKey),
        _storage.delete(key: _tokenTypeKey),
      ]);

      _logger.info('✅ All tokens cleared successfully');
    } catch (e, stackTrace) {
      _logger.severe('❌ Failed to clear tokens: $e', e, stackTrace);
      rethrow;
    }
  }

  /// Clear all secure storage (nuclear option)
  static Future<void> clearAllSecureStorage() async {
    try {
      _logger.warning('🚨 Clearing ALL secure storage');
      await _storage.deleteAll();
      _logger.info('✅ All secure storage cleared');
    } catch (e, stackTrace) {
      _logger.severe('❌ Failed to clear secure storage: $e', e, stackTrace);
      rethrow;
    }
  }

  /// Get token information for debugging (without exposing actual tokens)
  static Future<Map<String, dynamic>> getTokenInfo() async {
    try {
      final hasAccess = await getAccessToken() != null;
      final hasRefresh = await getRefreshToken() != null;
      final expiry = await getTokenExpiry();
      final userData = await getUserData();
      final tokenType = await getTokenType();
      final isExpired = await isTokenExpired();

      return {
        'has_access_token': hasAccess,
        'has_refresh_token': hasRefresh,
        'token_type': tokenType,
        'expires_at': expiry?.toIso8601String(),
        'is_expired': isExpired,
        'user_id': userData?['id'],
        'user_email': userData?['email'],
        'user_role': userData?['role'],
      };
    } catch (e) {
      _logger.warning('⚠️ Failed to get token info: $e');
      return {'error': e.toString()};
    }
  }
}

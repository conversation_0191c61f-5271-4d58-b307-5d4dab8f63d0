// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auction_api_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$auctionApiServiceHash() => r'61d5e460e1cb180094715203f48cac00e6db05f8';

/// Simple Auction API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [auctionApiService].
@ProviderFor(auctionApiService)
final auctionApiServiceProvider =
    AutoDisposeProvider<AuctionApiService>.internal(
      auctionApiService,
      name: r'auctionApiServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$auctionApiServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuctionApiServiceRef = AutoDisposeProviderRef<AuctionApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

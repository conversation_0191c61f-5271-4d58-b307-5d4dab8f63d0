import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'smart_backend_service.g.dart';

/// حالة السيرفر
enum ServerStatus {
  local,      // السيرفر المحلي
  hosted,     // سيرفر الاستضافة
  offline,    // غير متصل
  checking,   // جاري التحقق
}

/// معلومات السيرفر
class ServerInfo {
  final String url;
  final ServerStatus status;
  final String displayName;
  final DateTime lastChecked;
  final int? responseTime; // بالميلي ثانية

  const ServerInfo({
    required this.url,
    required this.status,
    required this.displayName,
    required this.lastChecked,
    this.responseTime,
  });

  ServerInfo copyWith({
    String? url,
    ServerStatus? status,
    String? displayName,
    DateTime? lastChecked,
    int? responseTime,
  }) {
    return ServerInfo(
      url: url ?? this.url,
      status: status ?? this.status,
      displayName: displayName ?? this.displayName,
      lastChecked: lastChecked ?? this.lastChecked,
      responseTime: responseTime ?? this.responseTime,
    );
  }
}

/// خدمة الكشف الذكي عن السيرفر
class SmartBackendService {
  static SmartBackendService? _instance;
  static SmartBackendService get instance {
    _instance ??= SmartBackendService._internal();
    return _instance!;
  }

  SmartBackendService._internal();

  // URLs السيرفرات
  static const String _localUrl = 'http://localhost:8080';
  static const String _localEmulatorUrl = 'http://********:8080';
  static const String _hostedUrl = 'https://backend-go-8klm.onrender.com';
  
  // معلومات السيرفرات
  final Map<String, ServerInfo> _servers = {
    'local': ServerInfo(
      url: _localUrl,
      status: ServerStatus.checking,
      displayName: 'السيرفر المحلي',
      lastChecked: DateTime.now(),
    ),
    'hosted': ServerInfo(
      url: _hostedUrl,
      status: ServerStatus.checking,
      displayName: 'سيرفر الاستضافة',
      lastChecked: DateTime.now(),
    ),
  };

  ServerInfo? _currentServer;
  final List<Function(ServerInfo)> _listeners = [];
  Timer? _healthCheckTimer;
  bool _isInitialized = false;

  // إعدادات الفحص
  static const Duration _healthCheckInterval = Duration(minutes: 2);
  static const Duration _connectionTimeout = Duration(seconds: 5);
  static const Duration _retryDelay = Duration(seconds: 10);

  /// الحصول على السيرفر الحالي
  ServerInfo? get currentServer => _currentServer;

  /// الحصول على URL السيرفر الحالي
  String get currentUrl => _currentServer?.url ?? _hostedUrl;

  /// الحصول على حالة السيرفر الحالي
  ServerStatus get currentStatus => _currentServer?.status ?? ServerStatus.offline;

  /// هل السيرفر المحلي متاح؟
  bool get isLocalAvailable => _servers['local']?.status == ServerStatus.local;

  /// هل سيرفر الاستضافة متاح؟
  bool get isHostedAvailable => _servers['hosted']?.status == ServerStatus.hosted;

  /// هل تم تهيئة الخدمة؟
  bool get isInitialized => _isInitialized;

  /// إضافة مستمع لتغييرات السيرفر
  void addListener(Function(ServerInfo) listener) {
    _listeners.add(listener);
  }

  /// إزالة مستمع
  void removeListener(Function(ServerInfo) listener) {
    _listeners.remove(listener);
  }

  /// إشعار المستمعين
  void _notifyListeners() {
    if (_currentServer != null) {
      for (final listener in _listeners) {
        listener(_currentServer!);
      }
    }
  }

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🔍 تهيئة خدمة الكشف الذكي عن السيرفر...');
    
    await _checkAllServers();
    _startPeriodicHealthCheck();
    _isInitialized = true;
    
    debugPrint('✅ تم تهيئة خدمة الكشف الذكي بنجاح');
    debugPrint('🎯 السيرفر الحالي: ${_currentServer?.displayName ?? 'غير محدد'}');
  }

  /// فحص جميع السيرفرات وتحديد الأفضل
  Future<void> _checkAllServers() async {
    debugPrint('🔍 فحص جميع السيرفرات...');

    // فحص السيرفر المحلي أولاً
    final localUrl = _getLocalUrl();
    final localResult = await _checkServerHealth(localUrl, 'local');
    
    // فحص سيرفر الاستضافة
    final hostedResult = await _checkServerHealth(_hostedUrl, 'hosted');

    // تحديد السيرفر الأفضل
    _selectBestServer(localResult, hostedResult);
  }

  /// الحصول على URL المحلي المناسب
  String _getLocalUrl() {
    // في Android، استخدم ******** للـ emulator
    // هذا العنوان يشير إلى host machine من داخل Android emulator
    if (Platform.isAndroid && !kIsWeb) {
      debugPrint('🤖 Android detected - using emulator URL: $_localEmulatorUrl');
      return _localEmulatorUrl;
    }
    debugPrint('💻 Using localhost URL: $_localUrl');
    return _localUrl;
  }

  /// فحص صحة سيرفر معين
  Future<ServerInfo?> _checkServerHealth(String url, String serverKey) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      debugPrint('🔍 فحص السيرفر: $url');
      
      final dio = Dio(BaseOptions(
        connectTimeout: _connectionTimeout,
        receiveTimeout: _connectionTimeout,
        sendTimeout: _connectionTimeout,
      ));

      final response = await dio.get('$url/health');
      stopwatch.stop();

      if (response.statusCode == 200) {
        final responseTime = stopwatch.elapsedMilliseconds;
        debugPrint('✅ السيرفر $url متاح (${responseTime}ms)');
        
        final serverInfo = _servers[serverKey]!.copyWith(
          url: url,
          status: serverKey == 'local' ? ServerStatus.local : ServerStatus.hosted,
          lastChecked: DateTime.now(),
          responseTime: responseTime,
        );
        
        _servers[serverKey] = serverInfo;
        return serverInfo;
      }
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ السيرفر $url غير متاح: $e');
    }

    // تحديث حالة السيرفر إلى غير متاح
    final serverInfo = _servers[serverKey]!.copyWith(
      url: url,
      status: ServerStatus.offline,
      lastChecked: DateTime.now(),
      responseTime: null,
    );
    
    _servers[serverKey] = serverInfo;
    return null;
  }

  /// اختيار أفضل سيرفر متاح
  void _selectBestServer(ServerInfo? local, ServerInfo? hosted) {
    ServerInfo? selectedServer;

    // 🌐 PRIORITIZE HOSTED SERVER FOR ANDROID
    // أولوية hosted server على Android لضمان الاتصال الموثوق
    if (Platform.isAndroid && !kIsWeb) {
      if (hosted != null && hosted.status == ServerStatus.hosted) {
        selectedServer = hosted;
        debugPrint('📱 Android: Using reliable hosted server: ${hosted.url}');
      } else if (local != null && local.status == ServerStatus.local) {
        selectedServer = local;
        debugPrint('📱 Android: Fallback to local server: ${local.url}');
      }
    }
    // 💻 PRIORITIZE LOCAL SERVER FOR DESKTOP/WEB
    // أولوية local server على desktop/web للتطوير
    else {
      if (local != null && local.status == ServerStatus.local) {
        selectedServer = local;
        debugPrint('💻 Desktop: Using local server for development: ${local.url}');
      } else if (hosted != null && hosted.status == ServerStatus.hosted) {
        selectedServer = hosted;
        debugPrint('💻 Desktop: Fallback to hosted server: ${hosted.url}');
      }
    }
    
    // إذا لم يكن أي منهما متاحاً، استخدم hosted كقيمة افتراضية
    if (selectedServer == null) {
      selectedServer = _servers['hosted']!.copyWith(
        status: ServerStatus.offline,
        lastChecked: DateTime.now(),
      );
      debugPrint('⚠️ No servers available - defaulting to hosted server');
    }

    // تحديث السيرفر الحالي إذا تغير
    if (_currentServer?.url != selectedServer.url || 
        _currentServer?.status != selectedServer.status) {
      
      final previousServer = _currentServer;
      _currentServer = selectedServer;
      
      _logServerChange(previousServer, selectedServer);
      _notifyListeners();
    }
  }

  /// تسجيل تغيير السيرفر
  void _logServerChange(ServerInfo? previous, ServerInfo current) {
    if (previous == null) {
      debugPrint('🎯 تم تحديد السيرفر الابتدائي: ${current.displayName}');
    } else {
      debugPrint('🔄 تم التبديل من ${previous.displayName} إلى ${current.displayName}');
    }
    
    // عرض معلومات إضافية
    switch (current.status) {
      case ServerStatus.local:
        debugPrint('💻 يتم الاتصال بالسيرفر المحلي - سرعة أفضل');
        break;
      case ServerStatus.hosted:
        debugPrint('☁️ يتم الاتصال بسيرفر الاستضافة - إنترنت مطلوب');
        break;
      case ServerStatus.offline:
        debugPrint('⚠️ لا يوجد سيرفر متاح حالياً');
        break;
      case ServerStatus.checking:
        debugPrint('🔍 جاري التحقق من السيرفرات...');
        break;
    }
  }

  /// بدء الفحص الدوري
  void _startPeriodicHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(_healthCheckInterval, (_) {
      _checkAllServersInBackground();
    });
  }

  /// فحص السيرفرات في الخلفية
  Future<void> _checkAllServersInBackground() async {
    try {
      await _checkAllServers();
    } catch (e) {
      debugPrint('❌ خطأ في الفحص الدوري: $e');
    }
  }

  /// فرض استخدام سيرفر معين
  Future<void> forceServer(String serverKey) async {
    if (!_servers.containsKey(serverKey)) {
      debugPrint('❌ سيرفر غير معروف: $serverKey');
      return;
    }

    final server = _servers[serverKey]!;
    
    // فحص السيرفر المطلوب
    final result = await _checkServerHealth(server.url, serverKey);
    
    if (result != null && result.status != ServerStatus.offline) {
      _currentServer = result;
      _logServerChange(null, result);
      _notifyListeners();
    } else {
      debugPrint('❌ لا يمكن الاتصال بالسيرفر المطلوب: ${server.displayName}');
    }
  }

  /// إعادة فحص فوري
  Future<void> recheckNow() async {
    debugPrint('🔄 إعادة فحص السيرفرات...');
    await _checkAllServers();
  }

  /// الحصول على معلومات جميع السيرفرات
  Map<String, ServerInfo> get allServers => Map.unmodifiable(_servers);

  /// الحصول على رسالة الحالة الحالية
  String getStatusMessage() {
    if (_currentServer == null) {
      return 'جاري التحقق من السيرفرات...';
    }

    switch (_currentServer!.status) {
      case ServerStatus.local:
        final responseTime = _currentServer!.responseTime ?? 0;
        return 'متصل بالسيرفر المحلي (${responseTime}ms) 💻';
      case ServerStatus.hosted:
        final responseTime = _currentServer!.responseTime ?? 0;
        return 'متصل بسيرفر الاستضافة (${responseTime}ms) ☁️';
      case ServerStatus.offline:
        return 'لا يوجد سيرفر متاح حالياً ⚠️';
      case ServerStatus.checking:
        return 'جاري التحقق من السيرفرات... 🔍';
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _healthCheckTimer?.cancel();
    _listeners.clear();
    _isInitialized = false;
  }
}

/// Provider للخدمة الذكية
final smartBackendServiceProvider = Provider<SmartBackendService>((ref) {
  final service = SmartBackendService.instance;
  
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

/// Provider لحالة السيرفر الحالي
@riverpod
class CurrentServer extends _$CurrentServer {
  @override
  ServerInfo? build() {
    return SmartBackendService.instance.currentServer;
  }

  void setServer(ServerInfo? server) {
    state = server;
  }

  void updateServer(ServerInfo server) {
    state = server;
  }

  void clear() {
    state = null;
  }
}

/// Provider لرسالة حالة السيرفر
final serverStatusMessageProvider = Provider<String>((ref) {
  final service = ref.watch(smartBackendServiceProvider);
  return service.getStatusMessage();
});
import 'dart:async';
import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../errors/app_error.dart';
import '../errors/result.dart';

part 'retry_service.g.dart';

final _logger = Logger('RetryService');

/// Retry Service for Phase 3.2.1: Retry and Timeout Logic
///
/// Features:
/// - Exponential backoff retry mechanism
/// - Configurable timeout values
/// - Dead letter queue for failed operations
/// - Graceful service degradation
@riverpod
RetryService retryService(RetryServiceRef ref) {
  return RetryService();
}

class RetryService {
  /// Execute operation with retry logic and exponential backoff
  Future<Result<T>> executeWithRetry<T>(
    Future<T> Function() operation, {
    required String operationName,
    int maxRetries = 3,
    Duration initialDelay = const Duration(milliseconds: 500),
    Duration maxDelay = const Duration(seconds: 30),
    double backoffFactor = 2.0,
    bool enableJitter = true,
    Duration? timeout,
    bool Function(dynamic error)? isRetryable,
    void Function(int attempt, dynamic error, Duration delay)? onRetry,
    Map<String, dynamic>? operationData,
  }) async {
    isRetryable ??= _defaultIsRetryable;

    var attempt = 0;
    var currentDelay = initialDelay;
    dynamic lastError;

    while (attempt < maxRetries) {
      attempt++;

      try {
        _logger.info('Executing $operationName (attempt $attempt/$maxRetries)');

        T result;
        if (timeout != null) {
          result = await operation().timeout(timeout);
        } else {
          result = await operation();
        }

        if (attempt > 1) {
          _logger.info('$operationName succeeded after $attempt attempts');
        }

        return Result.success(result);
      } catch (error, stackTrace) {
        lastError = error;

        _logger.warning(
          '$operationName failed (attempt $attempt/$maxRetries): $error',
        );

        // Check if we should retry
        if (!isRetryable(error) || attempt >= maxRetries) {
          _logger.severe(
            'Operation $operationName failed permanently after $attempt attempts: $error',
          );

          final appError = _convertToAppError(
            error,
            stackTrace: stackTrace,
            operationName: operationName,
            attemptCount: attempt,
            operationData: operationData,
          );

          return Result.failure(appError);
        }

        // Calculate delay for next retry
        final delay = _calculateDelay(
          attempt,
          currentDelay,
          maxDelay,
          backoffFactor,
          enableJitter,
        );

        _logger.info('Retrying $operationName in ${delay.inMilliseconds}ms...');

        // Call retry callback if provided
        if (onRetry != null) {
          onRetry(attempt, error, delay);
        }

        // Wait before retry
        await Future.delayed(delay);

        // Update delay for next iteration
        currentDelay = delay;
      }
    }

    // Should not reach here, but just in case
    final appError = _convertToAppError(
      lastError ?? Exception('Unknown error in retry logic'),
      operationName: operationName,
      attemptCount: attempt,
      operationData: operationData,
    );

    return Result.failure(appError);
  }

  /// Execute operation with timeout only (no retry)
  Future<Result<T>> executeWithTimeout<T>(
    Future<T> Function() operation, {
    required String operationName,
    required Duration timeout,
    Map<String, dynamic>? operationData,
  }) async {
    try {
      _logger.info(
        'Executing $operationName with timeout ${timeout.inSeconds}s',
      );

      final result = await operation().timeout(timeout);
      return Result.success(result);
    } catch (error, stackTrace) {
      _logger.severe('Operation $operationName timed out or failed: $error');

      final appError = _convertToAppError(
        error,
        stackTrace: stackTrace,
        operationName: operationName,
        operationData: operationData,
      );

      return Result.failure(appError);
    }
  }

  /// Execute operation with both retry and timeout
  Future<Result<T>> executeWithRetryAndTimeout<T>(
    Future<T> Function() operation, {
    required String operationName,
    int maxRetries = 3,
    Duration initialDelay = const Duration(milliseconds: 500),
    Duration maxDelay = const Duration(seconds: 30),
    double backoffFactor = 2.0,
    bool enableJitter = true,
    Duration timeout = const Duration(seconds: 30),
    bool Function(dynamic error)? isRetryable,
    void Function(int attempt, dynamic error, Duration delay)? onRetry,
    Map<String, dynamic>? operationData,
  }) async {
    return executeWithRetry<T>(
      operation,
      operationName: operationName,
      maxRetries: maxRetries,
      initialDelay: initialDelay,
      maxDelay: maxDelay,
      backoffFactor: backoffFactor,
      enableJitter: enableJitter,
      timeout: timeout,
      isRetryable: isRetryable,
      onRetry: onRetry,
      operationData: operationData,
    );
  }

  /// Calculate delay with exponential backoff and jitter
  Duration _calculateDelay(
    int attempt,
    Duration currentDelay,
    Duration maxDelay,
    double backoffFactor,
    bool enableJitter,
  ) {
    // Exponential backoff
    var delay = Duration(
      milliseconds: (currentDelay.inMilliseconds * backoffFactor).round(),
    );

    // Apply jitter if enabled (±10% random variation)
    if (enableJitter) {
      final random = Random();
      final jitterFactor = 0.9 + (random.nextDouble() * 0.2); // 0.9 to 1.1
      delay = Duration(
        milliseconds: (delay.inMilliseconds * jitterFactor).round(),
      );
    }

    // Cap at max delay
    if (delay > maxDelay) {
      delay = maxDelay;
    }

    return delay;
  }

  /// Default retry logic - determines if an error is retryable
  bool _defaultIsRetryable(dynamic error) {
    if (error is AppError) {
      return error.canRetry;
    }

    if (error is TimeoutException) {
      return true;
    }

    // Check error message for retryable patterns
    final errorMessage = error.toString().toLowerCase();
    final retryablePatterns = [
      'timeout',
      'connection',
      'network',
      'temporary',
      'unavailable',
      'socket',
      'failed to connect',
      'connection refused',
      'connection reset',
    ];

    return retryablePatterns.any((pattern) => errorMessage.contains(pattern));
  }

  /// Convert any error to AppError
  AppError _convertToAppError(
    dynamic error, {
    StackTrace? stackTrace,
    String? operationName,
    int? attemptCount,
    Map<String, dynamic>? operationData,
  }) {
    if (error is AppError) {
      return error;
    }

    if (error is TimeoutException) {
      return AppError.timeout(
        message: 'Operation timed out: ${operationName ?? 'Unknown'}',
        code: 'OPERATION_TIMEOUT',
        originalError: error,
        data: {
          if (operationData != null) ...operationData,
          if (operationName != null) 'operation': operationName,
          if (attemptCount != null) 'attempts': attemptCount,
          'timeout_duration': error.duration?.inSeconds,
        },
      );
    }

    return AppError.unexpected(
      message: 'Operation failed: ${operationName ?? 'Unknown'}',
      code: 'OPERATION_FAILED',
      originalError: error,
      data: {
        if (operationData != null) ...operationData,
        if (operationName != null) 'operation': operationName,
        if (attemptCount != null) 'attempts': attemptCount,
        if (stackTrace != null) 'stackTrace': stackTrace.toString(),
        'errorType': error.runtimeType.toString(),
        'errorMessage': error.toString(),
      },
    );
  }
}

/// Retry configuration presets
class RetryConfigs {
  /// Quick retry for fast operations
  static const quick = RetryConfig(
    maxRetries: 3,
    initialDelay: Duration(milliseconds: 100),
    maxDelay: Duration(seconds: 2),
    backoffFactor: 1.5,
    timeout: Duration(seconds: 5),
  );

  /// Standard retry for normal operations
  static const standard = RetryConfig(
    maxRetries: 3,
    initialDelay: Duration(milliseconds: 500),
    maxDelay: Duration(seconds: 10),
    backoffFactor: 2.0,
    timeout: Duration(seconds: 30),
  );

  /// Slow retry for heavy operations
  static const slow = RetryConfig(
    maxRetries: 5,
    initialDelay: Duration(seconds: 1),
    maxDelay: Duration(seconds: 60),
    backoffFactor: 2.0,
    timeout: Duration(minutes: 2),
  );

  /// Network retry for API calls
  static const network = RetryConfig(
    maxRetries: 5,
    initialDelay: Duration(milliseconds: 200),
    maxDelay: Duration(seconds: 15),
    backoffFactor: 2.0,
    timeout: Duration(seconds: 30),
  );

  /// Critical retry for important operations
  static const critical = RetryConfig(
    maxRetries: 7,
    initialDelay: Duration(milliseconds: 100),
    maxDelay: Duration(seconds: 30),
    backoffFactor: 1.8,
    timeout: Duration(minutes: 1),
  );
}

/// Retry configuration class
class RetryConfig {
  const RetryConfig({
    required this.maxRetries,
    required this.initialDelay,
    required this.maxDelay,
    required this.backoffFactor,
    required this.timeout,
    this.enableJitter = true,
  });

  final int maxRetries;
  final Duration initialDelay;
  final Duration maxDelay;
  final double backoffFactor;
  final Duration timeout;
  final bool enableJitter;
}

/// Timeout configuration presets
class TimeoutConfigs {
  /// Quick timeout for fast operations
  static const quick = Duration(seconds: 5);

  /// Standard timeout for normal operations
  static const standard = Duration(seconds: 30);

  /// Long timeout for slow operations
  static const long = Duration(minutes: 2);

  /// API timeout for network calls
  static const api = Duration(seconds: 30);

  /// Database timeout for database operations
  static const database = Duration(seconds: 10);

  /// File timeout for file operations
  static const file = Duration(seconds: 60);
}

/// Extension to make retry service easier to use with Ref
extension RetryServiceExtension on Ref {
  /// Execute operation with retry using the retry service
  Future<Result<T>> executeWithRetry<T>(
    Future<T> Function() operation, {
    required String operationName,
    RetryConfig config = RetryConfigs.standard,
    bool Function(dynamic error)? isRetryable,
    void Function(int attempt, dynamic error, Duration delay)? onRetry,
    Map<String, dynamic>? operationData,
  }) {
    final retryService = read(retryServiceProvider);
    return retryService.executeWithRetry<T>(
      operation,
      operationName: operationName,
      maxRetries: config.maxRetries,
      initialDelay: config.initialDelay,
      maxDelay: config.maxDelay,
      backoffFactor: config.backoffFactor,
      enableJitter: config.enableJitter,
      timeout: config.timeout,
      isRetryable: isRetryable,
      onRetry: onRetry,
      operationData: operationData,
    );
  }

  /// Execute operation with timeout only
  Future<Result<T>> executeWithTimeout<T>(
    Future<T> Function() operation, {
    required String operationName,
    Duration timeout = TimeoutConfigs.standard,
    Map<String, dynamic>? operationData,
  }) {
    final retryService = read(retryServiceProvider);
    return retryService.executeWithTimeout<T>(
      operation,
      operationName: operationName,
      timeout: timeout,
      operationData: operationData,
    );
  }
}

/// Retry statistics for monitoring
class RetryStatistics {
  RetryStatistics({
    this.totalOperations = 0,
    this.successfulOperations = 0,
    this.failedOperations = 0,
    this.totalRetries = 0,
    this.averageAttempts = 0.0,
    this.averageDelay = Duration.zero,
    DateTime? lastOperationTime,
  }) : lastOperationTime = lastOperationTime ?? DateTime.now();

  final int totalOperations;
  final int successfulOperations;
  final int failedOperations;
  final int totalRetries;
  final double averageAttempts;
  final Duration averageDelay;
  final DateTime lastOperationTime;

  double get successRate {
    if (totalOperations == 0) return 0.0;
    return successfulOperations / totalOperations;
  }

  double get failureRate {
    if (totalOperations == 0) return 0.0;
    return failedOperations / totalOperations;
  }

  RetryStatistics copyWith({
    int? totalOperations,
    int? successfulOperations,
    int? failedOperations,
    int? totalRetries,
    double? averageAttempts,
    Duration? averageDelay,
    DateTime? lastOperationTime,
  }) {
    return RetryStatistics(
      totalOperations: totalOperations ?? this.totalOperations,
      successfulOperations: successfulOperations ?? this.successfulOperations,
      failedOperations: failedOperations ?? this.failedOperations,
      totalRetries: totalRetries ?? this.totalRetries,
      averageAttempts: averageAttempts ?? this.averageAttempts,
      averageDelay: averageDelay ?? this.averageDelay,
      lastOperationTime: lastOperationTime ?? this.lastOperationTime,
    );
  }
}

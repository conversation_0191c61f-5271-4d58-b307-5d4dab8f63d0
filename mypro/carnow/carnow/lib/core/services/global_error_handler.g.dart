// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'global_error_handler.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$globalErrorHandlerHash() =>
    r'143c9017e685bf44675dee67d69b73082eba4839';

/// Global Error Handler for Phase 3.1.1: Flutter Error Handling
///
/// Features:
/// - Global error handling for all API calls
/// - User-friendly error messages and displays
/// - Retry mechanisms for failed requests
/// - Offline mode and graceful degradation
/// - Integration with dead letter queue for failed operations
///
/// Copied from [globalErrorHandler].
@ProviderFor(globalErrorHandler)
final globalErrorHandlerProvider =
    AutoDisposeProvider<GlobalErrorHandler>.internal(
      globalErrorHandler,
      name: r'globalErrorHandlerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$globalErrorHandlerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GlobalErrorHandlerRef = AutoDisposeProviderRef<GlobalErrorHandler>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'dart:async';

part 'performance_cache_service.g.dart';

/// خدمة التخزين المؤقت للأداء
class PerformanceCacheService {
  PerformanceCacheService();

  final Map<String, _CacheItem> _cache = {};
  final Map<String, Timer> _timers = {};

  /// تخزين البيانات مؤقتاً
  void store<T>(
    String key,
    T data, {
    Duration ttl = const Duration(minutes: 5),
  }) {
    // إزالة التايمر السابق إن وجد
    _timers[key]?.cancel();

    // تخزين البيانات
    _cache[key] = _CacheItem(
      data: data,
      timestamp: DateTime.now(),
      ttl: ttl,
    );

    // إنشاء تايمر جديد لحذف البيانات بعد انتهاء صلاحيتها
    _timers[key] = Timer(ttl, () {
      _cache.remove(key);
      _timers.remove(key);
    });
  }

  /// استرجاع البيانات من التخزين المؤقت
  T? get<T>(String key) {
    final item = _cache[key];
    if (item == null) return null;

    // التحقق من انتهاء الصلاحية
    if (DateTime.now().difference(item.timestamp) > item.ttl) {
      _cache.remove(key);
      _timers[key]?.cancel();
      _timers.remove(key);
      return null;
    }

    return item.data as T?;
  }

  /// التحقق من وجود البيانات في التخزين المؤقت
  bool has(String key) {
    return get(key) != null;
  }

  /// مسح عنصر محدد من التخزين المؤقت
  void remove(String key) {
    _cache.remove(key);
    _timers[key]?.cancel();
    _timers.remove(key);
  }

  /// مسح جميع البيانات من التخزين المؤقت
  void clear() {
    _cache.clear();
    for (final timer in _timers.values) {
      timer.cancel();
    }
    _timers.clear();
  }

  /// مسح البيانات المنتهية الصلاحية
  void cleanExpired() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cache.entries) {
      if (now.difference(entry.value.timestamp) > entry.value.ttl) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      remove(key);
    }
  }

  /// إحصائيات التخزين المؤقت
  Map<String, dynamic> getStats() {
    return {
      'cached_items': _cache.length,
      'active_timers': _timers.length,
      'cache_entries': _cache.keys.toList(),
    };
  }
}

/// عنصر في التخزين المؤقت
class _CacheItem {
  const _CacheItem({
    required this.data,
    required this.timestamp,
    required this.ttl,
  });

  final dynamic data;
  final DateTime timestamp;
  final Duration ttl;
}

/// Provider لخدمة التخزين المؤقت
@riverpod
PerformanceCacheService performanceCacheService(Ref ref) {
  final service = PerformanceCacheService();
  
  // تنظيف التخزين المؤقت عند إنهاء المرجع
  ref.onDispose(service.clear);

  return service;
}

/// مفاتيح التخزين المؤقت
class CacheKeys {
  static const String walletsList = 'wallets_list';
  static const String alertsList = 'alerts_list';
  static const String transactionsList = 'transactions_list';
  static const String dashboardData = 'dashboard_data';
  static const String userProfile = 'user_profile';
  
  static String walletDetails(String walletId) => 'wallet_details_$walletId';
  static String userWallets(String userId) => 'user_wallets_$userId';
  static String transactionHistory(String walletId) => 'transaction_history_$walletId';
  static String searchResults(String query) => 'search_results_${query.hashCode}';
}

/// مدد التخزين المؤقت
class CacheDurations {
  static const Duration short = Duration(minutes: 2);
  static const Duration medium = Duration(minutes: 5);
  static const Duration long = Duration(minutes: 15);
  static const Duration veryLong = Duration(hours: 1);
}
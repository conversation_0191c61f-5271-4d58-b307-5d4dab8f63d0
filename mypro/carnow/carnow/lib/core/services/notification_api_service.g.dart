// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_api_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$notificationApiServiceHash() =>
    r'b759ba4ee581d11cb8582ada089cbb3abe3d4ddf';

/// Simple Notification API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [notificationApiService].
@ProviderFor(notificationApiService)
final notificationApiServiceProvider =
    AutoDisposeProvider<NotificationApiService>.internal(
      notificationApiService,
      name: r'notificationApiServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationApiServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationApiServiceRef =
    AutoDisposeProviderRef<NotificationApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

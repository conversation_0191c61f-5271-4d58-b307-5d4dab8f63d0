import 'dart:async';
import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../errors/app_error.dart';
import '../errors/result.dart';


part 'error_recovery_service.g.dart';

/// خدمة استرداد الأخطاء التلقائية
/// Automatic error recovery service
class ErrorRecoveryService {
  ErrorRecoveryService(this._prefs);

  static final Logger _logger = Logger('ErrorRecoveryService');
  final SharedPreferences _prefs;

  /// تنفيذ عملية مع إعادة المحاولة التلقائية
  /// Execute operation with automatic retry
  Future<Result<T>> executeWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    Duration maxDelay = const Duration(seconds: 30),
    bool Function(dynamic error)? shouldRetry,
    Map<String, dynamic>? metadata,
  }) async {
    var attempt = 0;
    var delay = initialDelay;
    dynamic lastError;

    while (attempt <= maxRetries) {
      try {
        attempt++;
        _logger.info(
          'Executing ${operationName ?? 'operation'} (attempt $attempt/${maxRetries + 1})',
        );

        final result = await operation();

        // تسجيل النجاح إذا كانت هناك محاولات سابقة فاشلة
        if (attempt > 1) {
          _logger.info(
            '${operationName ?? 'operation'} succeeded after $attempt attempts',
          );
          await _recordRecoverySuccess(operationName, attempt, metadata);
        }

        return Result.success(result);
      } catch (error, _) {
        lastError = error;
        _logger.warning(
          '${operationName ?? 'operation'} failed (attempt $attempt/${maxRetries + 1}): $error',
        );

        // تحديد ما إذا كان يجب إعادة المحاولة
        final canRetry =
            attempt <= maxRetries && _shouldRetryError(error, shouldRetry);

        if (!canRetry) {
          _logger.severe(
            '${operationName ?? 'operation'} failed permanently after $attempt attempts',
          );
          await _recordRecoveryFailure(operationName, attempt, error, metadata);

          final appError = error is AppError
              ? error
              : AppError.unexpected(
                  message: error.toString(),
                  originalError: error,
                  data: {
                    'attempts': attempt,
                    'operation_name': operationName,
                    if (metadata != null) ...metadata,
                  },
                );

          return Result.failure(appError);
        }

        // انتظار قبل إعادة المحاولة
        if (attempt <= maxRetries) {
          _logger.info('Waiting ${delay.inMilliseconds}ms before retry...');
          await Future.delayed(delay);

          // زيادة التأخير للمحاولة التالية (exponential backoff)
          delay = Duration(
            milliseconds: min(
              (delay.inMilliseconds * backoffMultiplier).round(),
              maxDelay.inMilliseconds,
            ),
          );
        }
      }
    }

    // لا يجب الوصول هنا، لكن للأمان
    return Result.failure(
      AppError.unexpected(
        message: 'Unexpected error in retry logic',
        originalError: lastError,
        data: {'attempts': attempt, 'operation_name': operationName},
      ),
    );
  }

  /// تحديد ما إذا كان يجب إعادة المحاولة للخطأ
  bool _shouldRetryError(dynamic error, bool Function(dynamic)? customCheck) {
    // فحص مخصص أولاً
    if (customCheck != null) {
      return customCheck(error);
    }

    // قواعد افتراضية لإعادة المحاولة
    if (error is AppError) {
      switch (error.type) {
        case AppErrorType.network:
          return true; // أخطاء الشبكة قابلة للإعادة
        case AppErrorType.rateLimited:
          return true; // أخطاء معدل الطلبات قابلة للإعادة
        case AppErrorType.authentication:
          return false; // أخطاء المصادقة غير قابلة للإعادة
        case AppErrorType.permission:
          return false; // أخطاء الصلاحيات غير قابلة للإعادة
        case AppErrorType.validation:
          return false; // أخطاء التحقق غير قابلة للإعادة
        case AppErrorType.notFound:
          return false; // أخطاء عدم الوجود غير قابلة للإعادة
        case AppErrorType.database:
          return true; // أخطاء قاعدة البيانات قابلة للإعادة أحياناً
        case AppErrorType.unexpected:
          return true; // أخطاء غير متوقعة قابلة للإعادة
        case AppErrorType.server:
          return true; // أخطاء الخادم قابلة للإعادة
        case AppErrorType.authorization:
          return false; // أخطاء التفويض غير قابلة للإعادة
        case AppErrorType.conflict:
          return false; // أخطاء التضارب غير قابلة للإعادة
        case AppErrorType.timeout:
          return true; // أخطاء انتهاء المهلة قابلة للإعادة
        case AppErrorType.featureUnavailable:
          return false; // أخطاء عدم توفر الميزة غير قابلة للإعادة
      }
    }

    // فحص نوع الخطأ
    final errorString = error.toString().toLowerCase();

    // أخطاء الشبكة
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('socket')) {
      return true;
    }

    // أخطاء الخادم المؤقتة
    if (errorString.contains('server error') ||
        errorString.contains('503') ||
        errorString.contains('502') ||
        errorString.contains('504')) {
      return true;
    }

    return false; // افتراضياً لا نعيد المحاولة
  }

  /// تسجيل نجاح الاسترداد
  Future<void> _recordRecoverySuccess(
    String? operationName,
    int attempts,
    Map<String, dynamic>? metadata,
  ) async {
    try {
      // TODO: Replace with proper error logging service
      _logger.info(
        'Error recovery success: ${operationName ?? 'unknown'} (attempts: $attempts)',
      );
    } catch (e) {
      _logger.warning('Failed to record recovery success: $e');
    }
  }

  /// تسجيل فشل الاسترداد
  Future<void> _recordRecoveryFailure(
    String? operationName,
    int attempts,
    dynamic error,
    Map<String, dynamic>? metadata,
  ) async {
    try {
      // TODO: Replace with proper error logging service
      _logger.severe(
        'Error recovery failed: ${operationName ?? 'unknown'} (attempts: $attempts) - $error',
      );
    } catch (e) {
      _logger.warning('Failed to record recovery failure: $e');
    }
  }

  /// تنفيذ عملية مع تخزين مؤقت للنتائج
  /// Execute operation with result caching
  Future<Result<T>> executeWithCache<T>(
    Future<T> Function() operation, {
    required String cacheKey,
    Duration cacheDuration = const Duration(minutes: 5),
    String? operationName,
    T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    try {
      // محاولة الحصول على النتيجة من التخزين المؤقت
      final cachedResult = await _getCachedResult<T>(
        cacheKey,
        cacheDuration,
        fromJson,
      );

      if (cachedResult != null) {
        _logger.info('Cache hit for ${operationName ?? cacheKey}');
        return Result.success(cachedResult);
      }

      // تنفيذ العملية
      _logger.info(
        'Cache miss for ${operationName ?? cacheKey}, executing operation',
      );
      final result = await operation();

      // حفظ النتيجة في التخزين المؤقت
      await _cacheResult(cacheKey, result, toJson);

      return Result.success(result);
    } catch (error, stackTrace) {
      _logger.severe(
        'Operation ${operationName ?? cacheKey} failed: $error',
        error,
        stackTrace,
      );

      final appError = error is AppError
          ? error
          : AppError.unexpected(
              message: error.toString(),
              originalError: error,
              data: {'cache_key': cacheKey, 'operation_name': operationName},
            );

      return Result.failure(appError);
    }
  }

  /// الحصول على نتيجة من التخزين المؤقت
  Future<T?> _getCachedResult<T>(
    String key,
    Duration maxAge,
    T Function(Map<String, dynamic>)? fromJson,
  ) async {
    try {
      final timestampKey = '${key}_timestamp';
      final dataKey = '${key}_data';

      final timestamp = _prefs.getInt(timestampKey);
      if (timestamp == null) return null;

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      if (DateTime.now().difference(cacheTime) > maxAge) {
        // انتهت صلاحية التخزين المؤقت
        await _clearCache(key);
        return null;
      }

      final dataString = _prefs.getString(dataKey);
      if (dataString == null) return null;

      // إذا كان لدينا fromJson، نستخدمه لتحويل البيانات
      if (fromJson != null) {
        // هنا نحتاج لتحويل JSON string إلى Map
        // هذا مبسط، في التطبيق الحقيقي نحتاج json.decode
        return null; // مؤقتاً
      }

      // للأنواع البسيطة
      return dataString as T?;
    } catch (e) {
      _logger.warning('Failed to get cached result for $key: $e');
      return null;
    }
  }

  /// حفظ نتيجة في التخزين المؤقت
  Future<void> _cacheResult<T>(
    String key,
    T result,
    Map<String, dynamic> Function(T)? toJson,
  ) async {
    try {
      final timestampKey = '${key}_timestamp';
      final dataKey = '${key}_data';

      await _prefs.setInt(timestampKey, DateTime.now().millisecondsSinceEpoch);

      if (toJson != null) {
        // تحويل إلى JSON string
        // هذا مبسط، في التطبيق الحقيقي نحتاج json.encode
        await _prefs.setString(dataKey, result.toString());
      } else {
        // للأنواع البسيطة
        await _prefs.setString(dataKey, result.toString());
      }
    } catch (e) {
      _logger.warning('Failed to cache result for $key: $e');
    }
  }

  /// مسح التخزين المؤقت
  Future<void> _clearCache(String key) async {
    try {
      await _prefs.remove('${key}_timestamp');
      await _prefs.remove('${key}_data');
    } catch (e) {
      _logger.warning('Failed to clear cache for $key: $e');
    }
  }

  /// مسح جميع التخزين المؤقت
  Future<void> clearAllCache() async {
    try {
      final keys = _prefs
          .getKeys()
          .where((key) => key.endsWith('_timestamp') || key.endsWith('_data'))
          .toList();

      for (final key in keys) {
        await _prefs.remove(key);
      }

      _logger.info('Cleared all cache entries');
    } catch (e) {
      _logger.warning('Failed to clear all cache: $e');
    }
  }
}

/// Provider لخدمة استرداد الأخطاء
@riverpod
Future<ErrorRecoveryService> errorRecoveryService(Ref ref) async {
  final prefs = await SharedPreferences.getInstance();
  return ErrorRecoveryService(prefs);
}

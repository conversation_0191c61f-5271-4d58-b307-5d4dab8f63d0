import 'dart:typed_data';
import 'dart:io';
import 'package:logging/logging.dart';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../config/email_config.dart';

final _logger = Logger('EmailService');

/// خدمة إرسال الإيميلات
class EmailService {
  /// إرسال فاتورة بصيغة PDF عبر الإيميل
  Future<bool> sendInvoiceEmail({
    required String recipientEmail,
    required String recipientName,
    required String orderNumber,
    required Uint8List pdfBytes,
    String? additionalMessage,
    bool isCashOnDelivery = true,
  }) async {
    try {
      _logger.info('Sending invoice email to: $recipientEmail');

      if (!EmailConfig.isConfigured) {
        _logger.warning('Email configuration is incomplete');
        return false;
      }

      // إعداد خادم SMTP
      final smtpServer = SmtpServer(
        EmailConfig.smtpHost,
        port: EmailConfig.smtpPort,
        username: EmailConfig.companyEmail,
        password: EmailConfig.companyPassword,
      );

      // حفظ PDF مؤقتاً لإرفاقه
      final tempDir = await getTemporaryDirectory();
      final pdfFile = File(path.join(tempDir.path, 'invoice_$orderNumber.pdf'));
      await pdfFile.writeAsBytes(pdfBytes);

      // إنشاء محتوى الإيميل
      final message = Message()
        ..from = Address(EmailConfig.companyEmail, EmailConfig.companyName)
        ..recipients.add(Address(recipientEmail, recipientName))
        ..subject = 'فاتورة الطلب #$orderNumber - ${EmailConfig.companyName}'
        ..html = _buildEmailHtml(
          recipientName: recipientName,
          orderNumber: orderNumber,
          additionalMessage: additionalMessage,
          isCashOnDelivery: isCashOnDelivery,
        )
        ..attachments.add(
          FileAttachment(
            pdfFile,
            fileName: 'invoice_$orderNumber.pdf',
            contentType: 'application/pdf',
          ),
        );

      // إرسال الإيميل
      final sendReport = await send(message, smtpServer);

      // حذف الملف المؤقت
      await pdfFile.delete();

      _logger.info('Email sent successfully: ${sendReport.toString()}');
      return true;
    } catch (e) {
      _logger.severe('Failed to send email: $e');
      return false;
    }
  }

  /// إرسال إشعار تأكيد الطلب
  Future<bool> sendOrderConfirmationEmail({
    required String recipientEmail,
    required String recipientName,
    required String orderNumber,
    required double totalAmount,
    required String currency,
    required DateTime estimatedDelivery,
    bool isCashOnDelivery = true,
  }) async {
    try {
      _logger.info('Sending order confirmation to: $recipientEmail');

      if (!EmailConfig.isConfigured) {
        _logger.warning('Email configuration is incomplete');
        return false;
      }

      final smtpServer = SmtpServer(
        EmailConfig.smtpHost,
        port: EmailConfig.smtpPort,
        username: EmailConfig.companyEmail,
        password: EmailConfig.companyPassword,
      );

      final message = Message()
        ..from = Address(EmailConfig.companyEmail, EmailConfig.companyName)
        ..recipients.add(Address(recipientEmail, recipientName))
        ..subject = 'تأكيد الطلب #$orderNumber - ${EmailConfig.companyName}'
        ..html = _buildOrderConfirmationHtml(
          recipientName: recipientName,
          orderNumber: orderNumber,
          totalAmount: totalAmount,
          currency: currency,
          estimatedDelivery: estimatedDelivery,
          isCashOnDelivery: isCashOnDelivery,
        );

      await send(message, smtpServer);
      _logger.info('Order confirmation email sent successfully');
      return true;
    } catch (e) {
      _logger.severe('Failed to send order confirmation: $e');
      return false;
    }
  }

  /// إرسال إشعار تحديث حالة الطلب
  Future<bool> sendOrderStatusUpdateEmail({
    required String recipientEmail,
    required String recipientName,
    required String orderNumber,
    required String newStatus,
    required String statusMessage,
    String? trackingNumber,
  }) async {
    try {
      _logger.info('Sending status update email to: $recipientEmail');

      if (!EmailConfig.isConfigured) {
        _logger.warning('Email configuration is incomplete');
        return false;
      }

      final smtpServer = SmtpServer(
        EmailConfig.smtpHost,
        port: EmailConfig.smtpPort,
        username: EmailConfig.companyEmail,
        password: EmailConfig.companyPassword,
      );

      final message = Message()
        ..from = Address(EmailConfig.companyEmail, EmailConfig.companyName)
        ..recipients.add(Address(recipientEmail, recipientName))
        ..subject =
            'تحديث حالة الطلب #$orderNumber - ${EmailConfig.companyName}'
        ..html = _buildStatusUpdateHtml(
          recipientName: recipientName,
          orderNumber: orderNumber,
          newStatus: newStatus,
          statusMessage: statusMessage,
          trackingNumber: trackingNumber,
        );

      await send(message, smtpServer);
      _logger.info('Status update email sent successfully');
      return true;
    } catch (e) {
      _logger.severe('Failed to send status update: $e');
      return false;
    }
  }

  /// بناء محتوى HTML للفاتورة
  String _buildEmailHtml({
    required String recipientName,
    required String orderNumber,
    String? additionalMessage,
    required bool isCashOnDelivery,
  }) {
    final paymentMethod = isCashOnDelivery
        ? 'الدفع عند الاستلام'
        : 'دفع إلكتروني';

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة الطلب</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #1976d2;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            color: #1976d2;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .content {
            margin-bottom: 30px;
        }
        .greeting {
            font-size: 18px;
            color: #1976d2;
            margin-bottom: 20px;
        }
        .info-box {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #1976d2;
            margin: 20px 0;
        }
        .payment-info {
            background-color: #fff3e0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ff9800;
            margin: 20px 0;
        }
        .payment-title {
            color: #f57c00;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .footer {
            text-align: center;
            border-top: 1px solid #eee;
            padding-top: 20px;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
        .contact-info {
            margin-top: 20px;
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #1976d2;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">CarNow - كارناو</div>
            <div class="subtitle">منصتك المتخصصة لقطع غيار السيارات</div>
        </div>
        
        <div class="content">
            <div class="greeting">
                السلام عليكم $recipientName،
            </div>
            
            <p>نشكركم لاختياركم منصة CarNow لتلبية احتياجاتكم من قطع غيار السيارات.</p>
            
            <div class="info-box">
                <strong>📋 معلومات الفاتورة:</strong><br>
                • رقم الطلب: <strong>$orderNumber</strong><br>
                • تاريخ الإصدار: <strong>${DateTime.now().toLocal().toString().split(' ')[0]}</strong><br>
                • طريقة الدفع: <strong>$paymentMethod</strong>
            </div>
            
            ${isCashOnDelivery ? '''
            <div class="payment-info">
                <div class="payment-title">💰 تعليمات الدفع عند الاستلام</div>
                <p>• يرجى تحضير المبلغ المطلوب نقداً عند استلام الطلب</p>
                <p>• لا نقبل الشيكات أو البطاقات الائتمانية عند التسليم</p>
                <p>• سيتم التواصل معكم قبل التسليم لتأكيد الموعد</p>
            </div>
            ''' : ''}
            
            <p>تجدون مرفقاً مع هذا الإيميل الفاتورة التفصيلية بصيغة PDF.</p>
            
            ${additionalMessage != null ? '<p><strong>ملاحظة إضافية:</strong> $additionalMessage</p>' : ''}
            
            <p>في حالة وجود أي استفسارات، يرجى التواصل معنا عبر الوسائل المذكورة أدناه.</p>
        </div>
        
        <div class="contact-info">
            <strong>معلومات التواصل:</strong><br>
            📧 البريد الإلكتروني: <EMAIL><br>
            📱 الهاتف: +218 21 123 4567<br>
            📍 العنوان: طرابلس، ليبيا
        </div>
        
        <div class="footer">
            شكراً لثقتكم بنا<br>
            فريق CarNow
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// بناء محتوى HTML لتأكيد الطلب
  String _buildOrderConfirmationHtml({
    required String recipientName,
    required String orderNumber,
    required double totalAmount,
    required String currency,
    required DateTime estimatedDelivery,
    required bool isCashOnDelivery,
  }) {
    final deliveryDate = estimatedDelivery.toLocal().toString().split(' ')[0];
    final paymentMethod = isCashOnDelivery
        ? 'الدفع عند الاستلام'
        : 'دفع إلكتروني';

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تأكيد الطلب</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #4caf50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            color: #4caf50;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .success-icon {
            font-size: 48px;
            color: #4caf50;
            margin-bottom: 15px;
        }
        .order-summary {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
            margin: 20px 0;
        }
        .payment-info {
            background-color: #fff3e0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ff9800;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            border-top: 1px solid #eee;
            padding-top: 20px;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon">✅</div>
            <div class="logo">تم تأكيد طلبكم بنجاح</div>
        </div>
        
        <div class="content">
            <p>عزيزي/عزيزتي $recipientName،</p>
            <p>نود إعلامكم بأنه تم استلام وتأكيد طلبكم بنجاح.</p>
            
            <div class="order-summary">
                <strong>📦 ملخص الطلب:</strong><br>
                • رقم الطلب: <strong>$orderNumber</strong><br>
                • المبلغ الإجمالي: <strong>${totalAmount.toStringAsFixed(2)} $currency</strong><br>
                • طريقة الدفع: <strong>$paymentMethod</strong><br>
                • التسليم المتوقع: <strong>$deliveryDate</strong>
            </div>
            
            ${isCashOnDelivery ? '''
            <div class="payment-info">
                <strong>💰 تذكير - الدفع عند الاستلام:</strong><br>
                يرجى تحضير مبلغ ${totalAmount.toStringAsFixed(2)} $currency نقداً عند استلام الطلب
            </div>
            ''' : ''}
            
            <p>سنقوم بمعالجة طلبكم والتواصل معكم لتنسيق موعد التسليم.</p>
            <p>شكراً لثقتكم بخدماتنا.</p>
        </div>
        
        <div class="footer">
            فريق CarNow<br>
            <EMAIL> | +218 21 123 4567
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// بناء محتوى HTML لتحديث حالة الطلب
  String _buildStatusUpdateHtml({
    required String recipientName,
    required String orderNumber,
    required String newStatus,
    required String statusMessage,
    String? trackingNumber,
  }) {
    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث حالة الطلب</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2196f3;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .status-update {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
        }
        .tracking-info {
            background-color: #f3e5f5;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>تحديث حالة الطلب</h2>
        </div>
        
        <div class="content">
            <p>عزيزي/عزيزتي $recipientName،</p>
            
            <div class="status-update">
                <strong>📋 رقم الطلب:</strong> $orderNumber<br>
                <strong>🔄 الحالة الجديدة:</strong> $newStatus<br>
                <strong>📝 التفاصيل:</strong> $statusMessage
            </div>
            
            ${trackingNumber != null ? '''
            <div class="tracking-info">
                <strong>📦 رقم التتبع:</strong> $trackingNumber
            </div>
            ''' : ''}
            
            <p>شكراً لاختياركم CarNow.</p>
        </div>
        
        <div class="footer">
            فريق CarNow<br>
            <EMAIL> | +218 21 123 4567
        </div>
    </div>
</body>
</html>
    ''';
  }
}

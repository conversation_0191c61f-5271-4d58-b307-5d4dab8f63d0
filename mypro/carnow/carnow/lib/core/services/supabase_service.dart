import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

part 'supabase_service.g.dart';

final _logger = Logger('SupabaseService');

/// SupabaseService for Forever Plan Architecture (DEPRECATED)
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// 🚨 DEPRECATED: This service is deprecated for Forever Plan compliance
/// New code MUST use:
/// - SimpleAuthSystem for authentication
/// - SimpleApiClient for ALL data operations
///
/// This service remains only for app initialization compatibility

/// DEPRECATED: Legacy provider - DO NOT USE
@Deprecated('Use SimpleAuthSystem and SimpleApiClient instead')
@riverpod
SupabaseService supabaseService(Ref ref) {
  _logger.warning(
    '⚠️ DEPRECATED: SupabaseService provider used. Use SimpleAuthSystem and SimpleApiClient instead.',
  );
  return SupabaseService.instance;
}

/// DEPRECATED: Legacy client provider - FORBIDDEN FOR NEW CODE
@Deprecated('FORBIDDEN: Direct Supabase client access violates Forever Plan')
@riverpod
Never supabaseClient(Ref ref) {
  throw UnsupportedError(
    '❌ FORBIDDEN: Direct Supabase client access violates Forever Plan Architecture.\n'
    '✅ CORRECT: Use SimpleApiClient for data operations\n'
    '✅ CORRECT: Use SimpleAuthSystem for authentication\n'
    'Forever Plan: Flutter (UI Only) → Go API → Supabase (Data Only)',
  );
}

/// Legacy SupabaseService - DEPRECATED
///
/// 🚨 DEPRECATED: Use SimpleAuthSystem for auth and SimpleApiClient for API calls
/// This service exists only for app initialization compatibility
class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();

  factory SupabaseService() => _instance;
  SupabaseService._internal();

  static SupabaseService get instance => _instance;

  /// DEPRECATED: Direct client access is FORBIDDEN
  @Deprecated('FORBIDDEN: Use SimpleApiClient instead')
  Never get client => throw UnsupportedError(
    '❌ FORBIDDEN: Direct Supabase client access violates Forever Plan Architecture.\n'
    '✅ CORRECT: Use SimpleApiClient for data operations',
  );

  /// Check if Supabase is initialized (for app startup only)
  bool get isInitialized {
    try {
      // Only check if Supabase package is initialized, not user session
      return true; // Simplified check for initialization only
    } catch (e) {
      return false;
    }
  }

  /// Initialize Supabase (for app startup only)
  Future<void> initialize() async {
    _logger.info('SupabaseService.initialize() - FOR APP STARTUP ONLY');
    _logger.warning('🚨 For data operations, use SimpleApiClient');
    _logger.warning('🚨 For authentication, use SimpleAuthSystem');
  }

  /// Dispose method (for compatibility)
  void dispose() {
    _logger.info('SupabaseService.dispose() called');
  }
}

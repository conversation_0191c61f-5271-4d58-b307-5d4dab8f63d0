import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';
import '../errors/app_error.dart';

part 'recommendation_api_service.g.dart';

final _logger = Logger('RecommendationApiService');

/// Simple Recommendation API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
RecommendationApiService recommendationApiService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return RecommendationApiService(apiClient);
}

class RecommendationApiService {
  const RecommendationApiService(this._apiClient);
  
  final SimpleApiClient _apiClient;

  /// Get personalized recommendations
  Future<List<Map<String, dynamic>>> getPersonalizedRecommendations() async {
    try {
      _logger.info('Fetching personalized recommendations...');
      final response = await _apiClient.get<List<dynamic>>('/recommendations/personalized');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch personalized recommendations: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch personalized recommendations',
          code: 'personalized_recommendations_fetch_failed',
        );
      }
      
      return List<Map<String, dynamic>>.from(response.data!);
    } catch (e) {
      _logger.severe('Error fetching personalized recommendations: $e');
      throw AppError.network(
        message: 'Error fetching personalized recommendations',
        code: 'personalized_recommendations_fetch_error',
        originalError: e,
      );
    }
  }

  /// Get product recommendations
  Future<List<Map<String, dynamic>>> getProductRecommendations(String productId) async {
    try {
      _logger.info('Fetching product recommendations: $productId');
      final response = await _apiClient.get<List<dynamic>>('/recommendations/product/$productId');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch product recommendations: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch product recommendations',
          code: 'product_recommendations_fetch_failed',
        );
      }
      
      return List<Map<String, dynamic>>.from(response.data!);
    } catch (e) {
      _logger.severe('Error fetching product recommendations: $e');
      throw AppError.network(
        message: 'Error fetching product recommendations',
        code: 'product_recommendations_fetch_error',
        originalError: e,
      );
    }
  }

  /// Get category recommendations
  Future<List<Map<String, dynamic>>> getCategoryRecommendations(String categoryId) async {
    try {
      _logger.info('Fetching category recommendations: $categoryId');
      final response = await _apiClient.get<List<dynamic>>('/recommendations/category/$categoryId');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch category recommendations: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch category recommendations',
          code: 'category_recommendations_fetch_failed',
        );
      }
      
      return List<Map<String, dynamic>>.from(response.data!);
    } catch (e) {
      _logger.severe('Error fetching category recommendations: $e');
      throw AppError.network(
        message: 'Error fetching category recommendations',
        code: 'category_recommendations_fetch_error',
        originalError: e,
      );
    }
  }
} 
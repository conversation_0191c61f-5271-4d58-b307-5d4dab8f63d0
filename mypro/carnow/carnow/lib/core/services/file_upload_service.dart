import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logging/logging.dart';
import 'package:path/path.dart' as path;
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:dio/dio.dart';
import 'package:uuid/uuid.dart';

import '../networking/simple_api_client.dart';

part 'file_upload_service.g.dart';

final _logger = Logger('FileUploadService');

/// File upload service for handling images and documents
///
/// This service provides methods for picking files from the device gallery or
/// camera, and for uploading them to Supabase Storage. It includes validation
/// for file types and sizes.
@riverpod
FileUploadService fileUploadService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return FileUploadService(apiClient);
}

class FileUploadService {
  /// Constructs a [FileUploadService] instance.
  ///
  /// Requires a [SimpleApiClient] API client for uploading files through Go backend.
  FileUploadService(this._apiClient);
  // Maximum file size allowed for uploads (10MB).
  static const int _maxFileSize = 10 * 1024 * 1024; // 10MB
  // Allowed image extensions.
  static const List<String> _allowedImageTypes = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
  ];
  // Allowed document extensions.
  static const List<String> _allowedDocumentTypes = [
    'pdf',
    'doc',
    'docx',
    'txt',
  ];

  final SimpleApiClient _apiClient;
  final ImagePicker _imagePicker = ImagePicker();
  final Uuid _uuid = const Uuid();

  /// Pick image from gallery
  ///
  /// Opens the device's image gallery for the user to select a single image.
  ///
  /// Parameters:
  ///   - [imageQuality]: The quality of the picked image (0-100, default: 85).
  ///   - [maxWidth]: Optional maximum width for the picked image.
  ///   - [maxHeight]: Optional maximum height for the picked image.
  ///
  /// Returns an [XFile] object representing the picked image, or `null` if no
  /// image was picked.
  /// Throws an [Exception] if the selected file is invalid (type or size).
  Future<XFile?> pickImageFromGallery({
    int imageQuality = 85,
    double? maxWidth,
    double? maxHeight,
  }) async {
    try {
      _logger.info('Picking image from gallery');

      final image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (image != null) {
        // Validate the picked file before returning.
        final isValid = await _validateFile(image);
        if (!isValid) {
          _logger.warning('Invalid file selected from gallery: ${image.name}');
          throw Exception(
            'Invalid file format or size. Allowed image types: '
            '${_allowedImageTypes.join(', ')}. Max size: '
            '${_maxFileSize / (1024 * 1024)}MB',
          );
        }
      }

      return image;
    } catch (e) {
      _logger.severe('Error picking image from gallery: $e');
      rethrow;
    }
  }

  /// Pick image from camera
  ///
  /// Opens the device's camera for the user to capture an image.
  ///
  /// Parameters:
  ///   - [imageQuality]: The quality of the captured image (0-100,
  ///     default: 85).
  ///   - [maxWidth]: Optional maximum width for the captured image.
  ///   - [maxHeight]: Optional maximum height for the captured image.
  ///
  /// Returns an [XFile] object representing the captured image, or `null` if
  /// no image was captured.
  /// Throws an [Exception] if the captured file is invalid (type or size).
  Future<XFile?> pickImageFromCamera({
    int imageQuality = 85,
    double? maxWidth,
    double? maxHeight,
  }) async {
    try {
      _logger.info('Picking image from camera');

      final image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (image != null) {
        // Validate the captured file before returning.
        final isValid = await _validateFile(image);
        if (!isValid) {
          _logger.warning('Invalid file captured from camera: ${image.name}');
          throw Exception(
            'Invalid file format or size. Allowed image types: '
            '${_allowedImageTypes.join(', ')}. Max size: '
            '${_maxFileSize / (1024 * 1024)}MB',
          );
        }
      }

      return image;
    } catch (e) {
      _logger.severe('Error picking image from camera: $e');
      rethrow;
    }
  }

  /// Pick multiple images
  ///
  /// Opens the device's image gallery for the user to select multiple images.
  ///
  /// Parameters:
  ///   - [imageQuality]: The quality of the picked images (0-100, default: 85).
  ///   - [maxWidth]: Optional maximum width for the picked images.
  ///   - [maxHeight]: Optional maximum height for the picked images.
  ///   - [maxImages]: The maximum number of images that can be selected
  ///     (default: 5).
  ///
  /// Returns a list of [XFile] objects representing the valid picked images.
  /// Invalid files (type or size) are skipped.
  Future<List<XFile>> pickMultipleImages({
    int imageQuality = 85,
    double? maxWidth,
    double? maxHeight,
    int maxImages = 5, // Default maximum number of images
  }) async {
    try {
      _logger.info('Picking multiple images (max: $maxImages)');

      final images = await _imagePicker.pickMultiImage(
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      // Limit number of images if more than maxImages are selected.
      final limitedImages = images.length > maxImages
          ? images.sublist(0, maxImages)
          : images;

      // Validate each image.
      final validImages = <XFile>[];
      for (final image in limitedImages) {
        final isValid = await _validateFile(image);
        if (isValid) {
          validImages.add(image);
        } else {
          _logger.warning(
            'Skipping invalid image: ${image.name}. Allowed types: '
            '${_allowedImageTypes.join(', ')}, max size: '
            '${_maxFileSize / (1024 * 1024)}MB',
          );
        }
      }

      if (images.length > maxImages) {
        _logger.info(
          'Picked ${images.length} images, but limited to $maxImages. '
          'Valid images: ${validImages.length}',
        );
      } else {
        _logger.info(
          'Picked ${limitedImages.length} images. '
          'Valid images: ${validImages.length}',
        );
      }

      return validImages;
    } catch (e) {
      _logger.severe('Error picking multiple images: $e');
      rethrow;
    }
  }

  /// Pick document file
  ///
  /// Opens the device's file picker for the user to select a single document.
  /// Allowed document types are defined in [_allowedDocumentTypes].
  ///
  /// Returns a [PlatformFile] object representing the picked document, or
  /// `null` if no document was picked.
  /// Throws an [Exception] if the selected file size exceeds the maximum
  /// allowed size.
  Future<PlatformFile?> pickDocument() async {
    try {
      _logger.info('Picking document file');

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: _allowedDocumentTypes,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // Validate file size.
        if (file.size > _maxFileSize) {
          _logger.warning(
            'Document size exceeds limit: ${file.name}, size: ${file.size}',
          );
          throw Exception(
            'File size exceeds maximum allowed size '
            '(${_maxFileSize / (1024 * 1024)}MB). Allowed document types: '
            '${_allowedDocumentTypes.join(', ')}.',
          );
        }
        _logger.info('Document picked: ${file.name}, size: ${file.size}');
        return file;
      }
      _logger.info('No document picked.');
      return null;
    } catch (e) {
      _logger.severe('Error picking document: $e');
      rethrow;
    }
  }

  /// Upload image to Supabase storage
  ///
  /// Uploads the provided [imageFile] to the specified Supabase Storage
  /// [bucket].
  ///
  /// Parameters:
  ///   - [imageFile]: The [XFile] image to upload.
  ///   - [bucket]: The Supabase Storage bucket name (default:
  ///     'product_images').
  ///   - [folder]: Optional folder path within the bucket.
  ///   - [customName]: Optional custom name for the uploaded file (without
  ///     extension).
  ///     If not provided, a UUID will be generated.
  ///
  /// Returns the public URL of the uploaded image.
  Future<String> uploadImage(
    XFile imageFile, {
    String bucket = 'product_images',
    String? folder,
    String? customName,
  }) async {
    try {
      _logger.info(
        'Uploading image ${imageFile.name} to bucket: $bucket'
        '${folder != null ? '/folder' : ''}',
      );

      // Generate unique filename
      final extension = path.extension(imageFile.name).toLowerCase();
      final fileNameWithoutExt = customName ?? _uuid.v4();
      final fileName = '$fileNameWithoutExt$extension';
      final filePath = folder != null ? '$folder/$fileName' : fileName;

      // Read file bytes
      final Uint8List fileBytes;
      if (kIsWeb) {
        fileBytes = await imageFile.readAsBytes();
      } else {
        final file = File(imageFile.path);
        fileBytes = await file.readAsBytes();
      }

      // Validate file size again before upload, just in case.
      if (fileBytes.lengthInBytes > _maxFileSize) {
        _logger.warning(
          'Image ${imageFile.name} size (${fileBytes.lengthInBytes} bytes) '
          'exceeds upload limit of $_maxFileSize bytes.',
        );
        throw Exception(
          'Image size exceeds maximum allowed size (${_maxFileSize / (1024 * 1024)}MB).',
        );
      }

      // Upload to Go backend API
      _logger.fine('Uploading binary data for $filePath to backend');
      final formData = FormData.fromMap({
        'file': MultipartFile.fromBytes(
          fileBytes,
          filename: fileName,
          contentType: DioMediaType.parse(imageFile.mimeType ?? 'image/jpeg'),
        ),
        'bucket': bucket,
        'folder': folder ?? '',
      });

      final response = await _apiClient.uploadFormData<Map<String, dynamic>>('/upload/image', formData);
      
      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to upload image');
      }
      
      final publicUrl = response.data!['url'] as String;

      _logger.info('Image uploaded successfully: $publicUrl');
      return publicUrl;
    } catch (e) {
      _logger.severe('Error uploading image ${imageFile.name}: $e');
      rethrow;
    }
  }

  /// Upload multiple images
  ///
  /// Uploads a list of [imageFiles] to the specified Supabase Storage [bucket].
  ///
  /// Parameters:
  ///   - [imageFiles]: The list of [XFile] images to upload.
  ///   - [bucket]: The Supabase Storage bucket name (default: 'product_images').
  ///   - [folder]: Optional folder path within the bucket.
  ///   - [onProgress]: Optional callback function to track upload progress,
  ///     providing (uploadedCount, totalCount).
  ///
  /// Returns a list of public URLs for the successfully uploaded images.
  /// Images that fail to upload are skipped.
  Future<List<String>> uploadMultipleImages(
    List<XFile> imageFiles, {
    String bucket = 'product_images',
    String? folder,
    void Function(int, int)? onProgress, // Callback for progress updates
  }) async {
    try {
      _logger.info(
        'Uploading ${imageFiles.length} images to bucket: $bucket${folder != null ? '/folder' : ''}',
      );

      final uploadedUrls = <String>[];

      for (var i = 0; i < imageFiles.length; i++) {
        final imageFile = imageFiles[i];

        try {
          // Perform validation again before uploading each image
          final isValid = await _validateFile(imageFile);
          if (!isValid) {
            _logger.warning(
              'Skipping invalid image during multi-upload: ${imageFile.name}',
            );
            onProgress?.call(
              i + 1,
              imageFiles.length,
            ); // Still report progress for skipped file
            continue; // Skip to the next file
          }

          final url = await uploadImage(
            imageFile,
            bucket: bucket,
            folder: folder,
            // Note: customName is not used here to ensure unique names for multiple files by default
          );
          uploadedUrls.add(url);

          // Report progress
          onProgress?.call(i + 1, imageFiles.length);
        } catch (e) {
          _logger.warning('Failed to upload image ${imageFile.name}: $e');
          // Continue with other images, report progress for this failed attempt
          onProgress?.call(i + 1, imageFiles.length);
        }
      }

      _logger.info(
        'Uploaded ${uploadedUrls.length}/${imageFiles.length} images successfully.',
      );
      return uploadedUrls;
    } catch (e) {
      _logger.severe('Error uploading multiple images: $e');
      rethrow;
    }
  }

  /// Upload document file
  ///
  /// Uploads the provided [documentFile] (a [PlatformFile]) to the specified
  /// Supabase Storage [bucket].
  ///
  /// Parameters:
  ///   - [documentFile]: The [PlatformFile] document to upload.
  ///   - [bucket]: The Supabase Storage bucket name (default: 'documents').
  ///   - [folder]: Optional folder path within the bucket.
  ///   - [customName]: Optional custom name for the uploaded file (without extension).
  ///     If not provided, a UUID will be generated.
  ///
  /// Returns the public URL of the uploaded document.
  Future<String> uploadDocument(
    PlatformFile documentFile, {
    String bucket = 'documents',
    String? folder,
    String? customName,
  }) async {
    try {
      _logger.info(
        'Uploading document ${documentFile.name} to bucket: $bucket${folder != null ? '/folder' : ''}',
      );

      // Generate unique filename
      final extension = path.extension(documentFile.name).toLowerCase();
      final fileName = customName ?? '${_uuid.v4()}$extension';

      // Get file bytes
      final Uint8List fileBytes;
      if (documentFile.bytes != null) {
        fileBytes = documentFile.bytes!;
      } else if (documentFile.path != null) {
        final file = File(documentFile.path!);
        fileBytes = await file.readAsBytes();
      } else {
        throw Exception('No file data available');
      }

      // Upload to Go backend API
      final formData = FormData.fromMap({
        'file': MultipartFile.fromBytes(fileBytes, filename: fileName),
        'bucket': bucket,
        'folder': folder ?? '',
      });

      final response = await _apiClient.uploadFormData<Map<String, dynamic>>(
        '/upload/document',
        formData,
      );
      
      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to upload document');
      }
      
      final publicUrl = response.data!['url'] as String;

      _logger.info('Document uploaded successfully: $publicUrl');
      return publicUrl;
    } catch (e) {
      _logger.severe('Error uploading document: $e');
      rethrow;
    }
  }

  /// Delete file from storage
  Future<void> deleteFile(
    String fileUrl, {
    String bucket = 'product_images',
  }) async {
    try {
      // Extract file path from URL
      final uri = Uri.parse(fileUrl);
      final pathSegments = uri.pathSegments;

      // Find the file path after the bucket name
      final bucketIndex = pathSegments.indexOf(bucket);
      if (bucketIndex == -1 || bucketIndex >= pathSegments.length - 1) {
        throw Exception('Invalid file URL format');
      }

      final filePath = pathSegments.sublist(bucketIndex + 1).join('/');

      // Delete from Go backend API
      await _apiClient.delete(
        '/upload/file',
        queryParameters: {'bucket': bucket, 'filePath': filePath},
      );

      _logger.info('File deleted successfully: $filePath');
    } catch (e) {
      _logger.severe('Error deleting file: $e');
      rethrow;
    }
  }

  /// Validate file format and size
  Future<bool> _validateFile(XFile file) async {
    try {
      // Check file size
      final fileSize = await file.length();
      if (fileSize > _maxFileSize) {
        _logger.warning('File size too large: ${fileSize / (1024 * 1024)}MB');
        return false;
      }

      // Check file extension
      final extension = path.extension(file.name).toLowerCase().substring(1);
      if (!_allowedImageTypes.contains(extension)) {
        _logger.warning('Invalid file type: $extension');
        return false;
      }

      return true;
    } catch (e) {
      _logger.severe('Error validating file: $e');
      return false;
    }
  }

  /// Get file size in human readable format
  static String getFileSizeString(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// Check if file type is supported
  static bool isImageTypeSupported(String extension) =>
      _allowedImageTypes.contains(extension.toLowerCase());

  /// Check if document type is supported
  static bool isDocumentTypeSupported(String extension) =>
      _allowedDocumentTypes.contains(extension.toLowerCase());

  /// Get maximum file size
  static int get maxFileSize => _maxFileSize;

  /// Get allowed image types
  static List<String> get allowedImageTypes => _allowedImageTypes;

  /// Get allowed document types
  static List<String> get allowedDocumentTypes => _allowedDocumentTypes;
}

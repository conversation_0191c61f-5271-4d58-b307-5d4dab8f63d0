import 'package:dio/dio.dart';
import 'package:logging/logging.dart';

import '../config/backend_config.dart';
import 'network_connectivity_service.dart';

final _logger = Logger('BackendHealthCheck');

/// Backend health check service
class BackendHealthCheck {
  static final BackendHealthCheck _instance = BackendHealthCheck._internal();
  factory BackendHealthCheck() => _instance;
  BackendHealthCheck._internal();

  final Dio _dio = Dio(BaseOptions(
    baseUrl: BackendConfig.baseUrl,
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
  ));

  /// Check if backend is healthy
  Future<bool> isHealthy() async {
    try {
      _logger.info('Checking backend health...');
      
      final response = await _dio.get('/health');
      
      if (response.statusCode == 200) {
        _logger.info('Backend is healthy');
        NetworkConnectivityService().updateBackendStatus(true);
        return true;
      } else {
        _logger.warning('Backend health check failed: ${response.statusCode}');
        NetworkConnectivityService().updateBackendStatus(false);
        return false;
      }
    } catch (e) {
      _logger.severe('Backend health check error: $e');
      NetworkConnectivityService().updateBackendStatus(false);
      return false;
    }
  }

  /// Get backend status with detailed information
  Future<BackendStatus> getStatus() async {
    try {
      final startTime = DateTime.now();
      final response = await _dio.get('/health');
      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime);

      return BackendStatus(
        isHealthy: response.statusCode == 200,
        responseTime: responseTime,
        statusCode: response.statusCode,
        lastChecked: DateTime.now(),
        error: null,
      );
    } catch (e) {
      return BackendStatus(
        isHealthy: false,
        responseTime: null,
        statusCode: null,
        lastChecked: DateTime.now(),
        error: e.toString(),
      );
    }
  }
}

/// Backend status information
class BackendStatus {
  final bool isHealthy;
  final Duration? responseTime;
  final int? statusCode;
  final DateTime lastChecked;
  final String? error;

  const BackendStatus({
    required this.isHealthy,
    this.responseTime,
    this.statusCode,
    required this.lastChecked,
    this.error,
  });

  @override
  String toString() {
    return 'BackendStatus(isHealthy: $isHealthy, responseTime: $responseTime, statusCode: $statusCode, error: $error)';
  }
} 
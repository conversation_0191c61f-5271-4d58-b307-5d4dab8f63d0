import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import 'dart:math' show pow;

/// AccessibilityService provides comprehensive accessibility features
/// for the CarNow authentication system
class AccessibilityService {
  static const AccessibilityService _instance = AccessibilityService._internal();
  factory AccessibilityService() => _instance;
  const AccessibilityService._internal();

  /// Announces a message to screen readers
  static void announce(String message, {TextDirection? textDirection}) {
    SemanticsService.announce(
      message,
      textDirection ?? TextDirection.ltr,
    );
  }

  /// Provides haptic feedback for authentication events
  static void provideHapticFeedback(AuthHapticType type) {
    switch (type) {
      case AuthHapticType.success:
        HapticFeedback.lightImpact();
        break;
      case AuthHapticType.error:
        HapticFeedback.heavyImpact();
        break;
      case AuthHapticType.warning:
        HapticFeedback.mediumImpact();
        break;
      case AuthHapticType.selection:
        HapticFeedback.selectionClick();
        break;
      case AuthHapticType.lightImpact:
        HapticFeedback.lightImpact();
        break;
    }
  }

  /// Creates semantic labels for authentication components
  static String createSemanticLabel({
    required String baseLabel,
    String? hint,
    String? value,
    bool isRequired = false,
    bool hasError = false,
    String? errorMessage,
  }) {
    final buffer = StringBuffer(baseLabel);

    if (isRequired) {
      buffer.write(', مطلوب'); // Required in Arabic
    }

    if (value != null && value.isNotEmpty) {
      buffer.write(', القيمة الحالية: $value'); // Current value in Arabic
    }

    if (hint != null) {
      buffer.write(', $hint');
    }

    if (hasError && errorMessage != null) {
      buffer.write(', خطأ: $errorMessage'); // Error in Arabic
    }

    return buffer.toString();
  }

  /// Creates accessible button semantics
  static Semantics createAccessibleButton({
    required Widget child,
    required String label,
    required VoidCallback? onTap,
    String? hint,
    bool enabled = true,
    bool isLoading = false,
  }) {
    return Semantics(
      button: true,
      enabled: enabled && !isLoading,
      label: isLoading ? '$label، جاري التحميل' : label, // Loading in Arabic
      hint: hint,
      onTap: enabled && !isLoading ? onTap : null,
      child: child,
    );
  }

  /// Creates accessible text field semantics
  static Semantics createAccessibleTextField({
    required Widget child,
    required String label,
    String? hint,
    String? value,
    bool isRequired = false,
    bool hasError = false,
    String? errorMessage,
    bool isPassword = false,
  }) {
    return Semantics(
      textField: true,
      label: createSemanticLabel(
        baseLabel: label,
        hint: hint,
        value: isPassword ? null : value,
        isRequired: isRequired,
        hasError: hasError,
        errorMessage: errorMessage,
      ),
      obscured: isPassword,
      child: child,
    );
  }

  /// Creates accessible form semantics
  static Semantics createAccessibleForm({
    required Widget child,
    required String formName,
    int? currentStep,
    int? totalSteps,
  }) {
    String label = formName;
    if (currentStep != null && totalSteps != null) {
      label += '، الخطوة $currentStep من $totalSteps'; // Step X of Y in Arabic
    }

    return Semantics(
      container: true,
      label: label,
      child: child,
    );
  }

  /// Announces authentication status changes
  static void announceAuthStatus(AuthStatusType status, {String? customMessage}) {
    String message;
    
    switch (status) {
      case AuthStatusType.signingIn:
        message = customMessage ?? 'جاري تسجيل الدخول'; // Signing in
        break;
      case AuthStatusType.signInSuccess:
        message = customMessage ?? 'تم تسجيل الدخول بنجاح'; // Sign in successful
        provideHapticFeedback(AuthHapticType.success);
        break;
      case AuthStatusType.signInError:
        message = customMessage ?? 'فشل في تسجيل الدخول'; // Sign in failed
        provideHapticFeedback(AuthHapticType.error);
        break;
      case AuthStatusType.signingUp:
        message = customMessage ?? 'جاري إنشاء الحساب'; // Creating account
        break;
      case AuthStatusType.signUpSuccess:
        message = customMessage ?? 'تم إنشاء الحساب بنجاح'; // Account created successfully
        provideHapticFeedback(AuthHapticType.success);
        break;
      case AuthStatusType.signUpError:
        message = customMessage ?? 'فشل في إنشاء الحساب'; // Account creation failed
        provideHapticFeedback(AuthHapticType.error);
        break;
      case AuthStatusType.validationError:
        message = customMessage ?? 'يرجى تصحيح الأخطاء في النموذج'; // Please correct form errors
        provideHapticFeedback(AuthHapticType.warning);
        break;
      case AuthStatusType.networkError:
        message = customMessage ?? 'خطأ في الاتصال بالشبكة'; // Network connection error
        provideHapticFeedback(AuthHapticType.error);
        break;
    }

    announce(message);
  }

  /// Creates high contrast theme data
  static ThemeData createHighContrastTheme(ThemeData baseTheme) {
    return baseTheme.copyWith(
      // High contrast colors
      colorScheme: baseTheme.colorScheme.copyWith(
        primary: const Color(0xFF000000),
        onPrimary: const Color(0xFFFFFFFF),
        secondary: const Color(0xFF000000),
        onSecondary: const Color(0xFFFFFFFF),
        error: const Color(0xFFD32F2F),
        onError: const Color(0xFFFFFFFF),
        surface: const Color(0xFFFFFFFF),
        onSurface: const Color(0xFF000000),
      ),
      // High contrast input decoration
      inputDecorationTheme: baseTheme.inputDecorationTheme.copyWith(
        border: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.black, width: 2.0),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.black, width: 2.0),
        ),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.black, width: 3.0),
        ),
        errorBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Color(0xFFD32F2F), width: 2.0),
        ),
      ),
      // High contrast elevated button
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
          side: const BorderSide(color: Colors.black, width: 2.0),
        ),
      ),
    );
  }

  /// Validates color contrast ratio
  static bool hasGoodContrast(Color foreground, Color background) {
    final double contrastRatio = _calculateContrastRatio(foreground, background);
    return contrastRatio >= 4.5; // WCAG AA standard
  }

  /// Calculates contrast ratio between two colors
  static double _calculateContrastRatio(Color color1, Color color2) {
    final double luminance1 = _calculateLuminance(color1);
    final double luminance2 = _calculateLuminance(color2);
    
    final double lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final double darker = luminance1 > luminance2 ? luminance2 : luminance1;
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculates relative luminance of a color
  static double _calculateLuminance(Color color) {
    final double r = _gammaCorrect(((color.r * 255.0).round() & 0xff) / 255.0);
    final double g = _gammaCorrect(((color.g * 255.0).round() & 0xff) / 255.0);
    final double b = _gammaCorrect(((color.b * 255.0).round() & 0xff) / 255.0);
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Applies gamma correction
  static double _gammaCorrect(double value) {
    return value <= 0.03928 ? value / 12.92 : pow((value + 0.055) / 1.055, 2.4).toDouble();
  }

  /// Creates focus management helper
  static FocusTraversalGroup createFocusTraversalGroup({
    required Widget child,
    FocusTraversalPolicy? policy,
  }) {
    return FocusTraversalGroup(
      policy: policy ?? OrderedTraversalPolicy(),
      child: child,
    );
  }

  /// Creates keyboard shortcuts for authentication
  static Map<ShortcutActivator, Intent> getAuthKeyboardShortcuts() {
    return {
      const SingleActivator(LogicalKeyboardKey.enter): const ActivateIntent(),
      const SingleActivator(LogicalKeyboardKey.escape): const DismissIntent(),
      const SingleActivator(LogicalKeyboardKey.tab): const NextFocusIntent(),
      const SingleActivator(LogicalKeyboardKey.tab, shift: true): const PreviousFocusIntent(),
    };
  }
}

/// Types of haptic feedback for authentication
enum AuthHapticType {
  success,
  error,
  warning,
  selection,
  lightImpact,
}

/// Types of authentication status announcements
enum AuthStatusType {
  signingIn,
  signInSuccess,
  signInError,
  signingUp,
  signUpSuccess,
  signUpError,
  validationError,
  networkError,
}

/// Custom focus traversal policy for authentication forms
class AuthFocusTraversalPolicy extends OrderedTraversalPolicy {
  @override
  bool inDirection(FocusNode node, TraversalDirection direction) {
    switch (direction) {
      case TraversalDirection.up:
      case TraversalDirection.down:
        return super.inDirection(node, direction);
      case TraversalDirection.left:
      case TraversalDirection.right:
        // Handle RTL layout for Arabic
        return super.inDirection(node, direction);
    }
  }
}

/// Extension to add accessibility helpers to widgets
extension AccessibilityWidgetExtensions on Widget {
  /// Wraps widget with accessibility semantics
  Widget withAccessibility({
    String? label,
    String? hint,
    bool? button,
    bool? textField,
    bool? enabled,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: button,
      textField: textField,
      enabled: enabled,
      onTap: onTap,
      child: this,
    );
  }

  /// Wraps widget with high contrast support
  Widget withHighContrast(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    if (mediaQuery.highContrast) {
      return Theme(
        data: AccessibilityService.createHighContrastTheme(Theme.of(context)),
        child: this,
      );
    }
    return this;
  }

  /// Wraps widget with focus management
  Widget withFocusManagement({FocusTraversalPolicy? policy}) {
    return AccessibilityService.createFocusTraversalGroup(
      policy: policy,
      child: this,
    );
  }
}

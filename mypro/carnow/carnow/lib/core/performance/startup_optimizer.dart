import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:riverpod/riverpod.dart';
import '../utils/unified_logger.dart';

/// نظام تحسين بدء التطبيق - إصدار محسن
class StartupOptimizer {
  static bool _isInitialized = false;
  static bool _isOptimizing = false;
  static final List<String> _preloadedAssets = [];
  static final Map<String, dynamic> _cachedData = {};
  static final Stopwatch _initStopwatch = Stopwatch();

  /// تحسين بدء التطبيق - أسرع وأكثر كفاءة
  static Future<void> optimizeStartup() async {
    if (_isInitialized || _isOptimizing) return;

    _isOptimizing = true;
    _initStopwatch.start();
    UnifiedLogger.info(
      'Starting optimized app initialization...',
      tag: 'StartupOptimizer',
    );

    try {
      // تشغيل التحسينات الأساسية فقط
      await Future.wait([
        _preloadCriticalAssetsOnly(),
        _prepareEssentialCache(),
        _optimizeFlutterSettings(),
      ]);

      // تأجيل الخدمات غير الأساسية
      _scheduleNonCriticalOptimizations();

      _isInitialized = true;
      _isOptimizing = false;
      _initStopwatch.stop();

      UnifiedLogger.info(
        'Essential app optimization completed in ${_initStopwatch.elapsedMilliseconds}ms',
        tag: 'StartupOptimizer',
      );
    } catch (e, stackTrace) {
      _isOptimizing = false;
      UnifiedLogger.error(
        'Failed to optimize startup',
        tag: 'StartupOptimizer',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// تحميل الأصول الحيوية فقط
  static Future<void> _preloadCriticalAssetsOnly() async {
    const criticalAssets = [
      'assets/images/logo.png', // الشعار فقط
    ];

    for (final asset in criticalAssets) {
      try {
        await rootBundle.load(asset);
        _preloadedAssets.add(asset);
        UnifiedLogger.debug(
          'Preloaded critical asset: $asset',
          tag: 'StartupOptimizer',
        );
      } catch (e) {
        UnifiedLogger.warning(
          'Failed to preload critical asset: $asset',
          tag: 'StartupOptimizer',
        );
      }
    }
  }

  /// تحضير الذاكرة المؤقتة الأساسية فقط
  static Future<void> _prepareEssentialCache() async {
    try {
      // تحضير البيانات الأساسية فقط
      _cachedData['app_start_time'] = DateTime.now().toIso8601String();
      _cachedData['is_optimized'] = true;

      UnifiedLogger.debug('Essential cache prepared', tag: 'StartupOptimizer');
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to prepare essential cache',
        tag: 'StartupOptimizer',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// تحسين إعدادات Flutter للأداء
  static Future<void> _optimizeFlutterSettings() async {
    try {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // تحسين الذاكرة المؤقتة للصور - إعدادات متحفظة
        final imageCache = PaintingBinding.instance.imageCache;
        imageCache.maximumSize = 50; // تقليل من الافتراضي 1000
        imageCache.maximumSizeBytes = 25 << 20; // 25MB بدلاً من 100MB

        UnifiedLogger.debug(
          'Flutter image cache optimized',
          tag: 'StartupOptimizer',
        );
      });

      UnifiedLogger.debug('Flutter settings optimized', tag: 'StartupOptimizer');
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to optimize Flutter settings',
        tag: 'StartupOptimizer',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// جدولة التحسينات غير الأساسية لوقت لاحق
  static void _scheduleNonCriticalOptimizations() {
    // تأجيل العمليات غير الأساسية لـ 5 ثوانٍ بعد بدء التطبيق
    Future.delayed(const Duration(seconds: 5), _runNonCriticalOptimizations);
  }

  /// تشغيل التحسينات غير الأساسية
  static Future<void> _runNonCriticalOptimizations() async {
    try {
      UnifiedLogger.info(
        'Starting non-critical optimizations...',
        tag: 'StartupOptimizer',
      );

      await Future.wait([
        _loadSecondaryAssets(),
        _expandCache(),
        _cleanupMemory(),
      ]);

      UnifiedLogger.info(
        'Non-critical optimizations completed',
        tag: 'StartupOptimizer',
      );
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed non-critical optimizations',
        tag: 'StartupOptimizer',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// تحميل الأصول الثانوية
  static Future<void> _loadSecondaryAssets() async {
    const secondaryAssets = [
      'assets/images/placeholder.png',
      'assets/icons/google_logo.svg',
    ];

    for (int i = 0; i < secondaryAssets.length; i++) {
      final asset = secondaryAssets[i];

      // تأخير بين كل أصل
      await Future.delayed(Duration(milliseconds: 500 * (i + 1)));

      try {
        await rootBundle.load(asset);
        _preloadedAssets.add(asset);
        UnifiedLogger.debug(
          'Loaded secondary asset: $asset',
          tag: 'StartupOptimizer',
        );
      } catch (e) {
        UnifiedLogger.warning(
          'Failed to load secondary asset: $asset',
          tag: 'StartupOptimizer',
        );
      }
    }
  }

  /// توسيع الذاكرة المؤقتة
  static Future<void> _expandCache() async {
    try {
      _cachedData['optimization_completed'] = DateTime.now().toIso8601String();
      _cachedData['total_preloaded_assets'] = _preloadedAssets.length;
      _cachedData['initialization_time_ms'] =
          _initStopwatch.elapsedMilliseconds;

      UnifiedLogger.debug(
        'Cache expanded with additional data',
        tag: 'StartupOptimizer',
      );
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to expand cache',
        tag: 'StartupOptimizer',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// تنظيف الذاكرة
  static Future<void> _cleanupMemory() async {
    try {
      // تنظيف الذاكرة المؤقتة للصور إذا كانت ممتلئة
      final imageCache = PaintingBinding.instance.imageCache;
      if (imageCache.currentSize > imageCache.maximumSize * 0.8) {
        imageCache.clearLiveImages();
        UnifiedLogger.debug('Memory cleanup performed', tag: 'StartupOptimizer');
      }
    } catch (e, stackTrace) {
      UnifiedLogger.error(
        'Failed to cleanup memory',
        tag: 'StartupOptimizer',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// التحقق من حالة التحسين
  static bool get isOptimized => _isInitialized;

  /// التحقق من حالة التحسين الجاري
  static bool get isOptimizing => _isOptimizing;

  /// إحصائيات التحسين المحسنة
  static Map<String, dynamic> get optimizationStats => {
    'isInitialized': _isInitialized,
    'isOptimizing': _isOptimizing,
    'initializationTimeMs': _initStopwatch.elapsedMilliseconds,
    'preloadedAssets': _preloadedAssets.length,
    'cachedDataEntries': _cachedData.length,
    'memoryUsage': _getMemoryUsage(),
    'preloadedAssetsList': _preloadedAssets,
  };

  /// الحصول على استخدام الذاكرة
  static Map<String, dynamic> _getMemoryUsage() {
    final imageCache = PaintingBinding.instance.imageCache;
    return {
      'imageCacheSize': imageCache.currentSize,
      'imageCacheBytes': imageCache.currentSizeBytes,
      'imageCacheUtilization': imageCache.currentSize / imageCache.maximumSize,
    };
  }

  /// إعادة تعيين التحسين (للاختبار)
  static void reset() {
    _isInitialized = false;
    _isOptimizing = false;
    _preloadedAssets.clear();
    _cachedData.clear();
    _initStopwatch.reset();
    UnifiedLogger.debug('Startup optimization reset', tag: 'StartupOptimizer');
  }

  /// تقرير مفصل عن حالة التحسين
  static void printOptimizationReport() {
    if (!kDebugMode) return;

    final stats = optimizationStats;
    UnifiedLogger.info('\n${"=" * 50}', tag: 'StartupOptimizer');
    UnifiedLogger.info('📊 STARTUP OPTIMIZATION REPORT', tag: 'StartupOptimizer');
    UnifiedLogger.info('=' * 50, tag: 'StartupOptimizer');
    UnifiedLogger.info(
      '✅ Initialized: ${stats['isInitialized']}',
      tag: 'StartupOptimizer',
    );
    UnifiedLogger.info(
      '⏱️  Init Time: ${stats['initializationTimeMs']}ms',
      tag: 'StartupOptimizer',
    );
    UnifiedLogger.info(
      '📁 Assets Loaded: ${stats['preloadedAssets']}',
      tag: 'StartupOptimizer',
    );
    UnifiedLogger.info(
      '💾 Cache Entries: ${stats['cachedDataEntries']}',
      tag: 'StartupOptimizer',
    );
    UnifiedLogger.info(
      '🧠 Memory Usage: ${stats['memoryUsage']}',
      tag: 'StartupOptimizer',
    );
    UnifiedLogger.info('=' * 50, tag: 'StartupOptimizer');
  }
}

/// Provider للتحسين المحسن
final startupOptimizerProvider = Provider<StartupOptimizer>((ref) {
  return StartupOptimizer();
});

/// Widget محسّن للبدء السريع - إصدار محسن
class OptimizedAppInitializer extends StatefulWidget {
  const OptimizedAppInitializer({
    super.key,
    required this.child,
    this.loadingWidget,
    this.timeoutDuration = const Duration(seconds: 10),
  });

  final Widget child;
  final Widget? loadingWidget;
  final Duration timeoutDuration;

  @override
  State<OptimizedAppInitializer> createState() =>
      _OptimizedAppInitializerState();
}

class _OptimizedAppInitializerState extends State<OptimizedAppInitializer> {
  bool _isInitialized = false;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // تشغيل التحسين مع timeout
      await Future.any([
        StartupOptimizer.optimizeStartup(),
        Future.delayed(widget.timeoutDuration, () {
          throw const TimeoutException('Startup optimization timed out');
        }),
      ]);

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      UnifiedLogger.error(
        'App initialization failed: $e',
        tag: 'OptimizedAppInitializer',
      );
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return _buildErrorWidget();
    }

    if (!_isInitialized) {
      return widget.loadingWidget ?? _buildDefaultLoadingWidget();
    }

    return widget.child;
  }

  Widget _buildErrorWidget() {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 64),
              const SizedBox(height: 16),
              const Text('Failed to initialize app'),
              if (_errorMessage != null) ...[
                const SizedBox(height: 8),
                Text(
                  _errorMessage!,
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: 16),
              FilledButton(
                onPressed: () {
                  setState(() {
                    _hasError = false;
                    _isInitialized = false;
                  });
                  _initializeApp();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultLoadingWidget() {
    return MaterialApp(
      home: Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/images/logo.png',
                width: 120,
                height: 120,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(
                    Icons.directions_car,
                    size: 120,
                    color: Color(0xFFFF6B35),
                  );
                },
              ),
              const SizedBox(height: 24),
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6B35)),
              ),
              const SizedBox(height: 16),
              const Text(
                'CarNow',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFFF6B35),
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'تحميل التطبيق...',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// استثناء انتهاء المهلة الزمنية
class TimeoutException implements Exception {
  const TimeoutException(this.message);
  final String message;

  @override
  String toString() => 'TimeoutException: $message';
}

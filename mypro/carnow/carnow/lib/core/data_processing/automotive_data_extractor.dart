import 'package:csv/csv.dart';

class AutomotiveDataExtractor {
  // Arabic translations for body styles (adapted for Libyan market)
  static const Map<String, String> bodyStyleTranslations = {
    'Convertible/Cabriolet': 'كابريوليه / سيارة مكشوفة',
    'Coupe': 'كوبيه',
    'Sedan/Saloon': 'سيدان',
    'Hatchback/Liftback/Notchback': 'هاتشباك',
    'Sport Utility Vehicle (SUV)/Multi-Purpose Vehicle (MPV)':
        'سيارة رياضية متعددة الاستخدامات',
    'Crossover Utility Vehicle (CUV)': 'كروس أوفر',
    'Pickup': 'بيك أب',
    'Van': 'فان',
    'Minivan': 'مينى فان',
    'Wagon': 'عربة / واجن',
    'Truck': 'شاحنة',
    'Bus': 'حافلة',
    'Bus - School Bus': 'حافلة مدرسية',
    'Motorcycle - Cruiser': 'دراجة نارية - كروزر',
    'Motorcycle - Sport': 'دراجة نارية رياضية',
    'Motorcycle - Standard': 'دراجة نارية عادية',
    'Motorcycle - Touring / Sport Touring': 'دراجة نارية سياحية',
    'Motorcycle - Scooter': 'سكوتر',
    'Off-road Vehicle - All Terrain Vehicle (ATV) (Motorcycle-style)':
        'مركبة لجميع التضاريس',
    'Ambulance': 'سيارة إسعاف',
    'Fire Apparatus': 'مركبة إطفاء',
    'Limousine': 'ليموزين',
    'Roadster': 'رودستر',
    'Sport Utility Truck (SUT)': 'شاحنة رياضية متعددة الاستخدامات',
    'Cargo Van': 'فان شحن',
    'Step Van / Walk-in Van': 'فان خطوة / فان مشي',
    'Motorhome': 'منزل متنقل',
    'Trailer': 'مقطورة',
    'Truck-Tractor': 'جرار شاحنة',
  };

  // Arabic translations for transmission types
  static const Map<String, String> transmissionTranslations = {
    'Manual/Standard': 'يدوي / عادي',
    'Automatic': 'أوتوماتيك',
    'Continuously Variable Transmission (CVT)': 'ناقل الحركة المتغير باستمرار',
    'Automated Manual Transmission (AMT)': 'ناقل الحركة اليدوي الآلي',
    'Dual-Clutch Transmission (DCT)': 'ناقل الحركة مزدوج القابض',
    'Electronic Continuously Variable (e-CVT)':
        'ناقل الحركة المتغير إلكترونياً',
    'Motorcycle - Chain Drive': 'دراجة نارية - نقل بالسلسلة',
    'Motorcycle - Shaft Drive': 'دراجة نارية - نقل بالعمود',
    'Motorcycle - CVT Belt Drive': 'دراجة نارية - نقل بالحزام المتغير',
    'Motorcycle - Chain Drive Off-Road':
        'دراجة نارية - نقل بالسلسلة للطرق الوعرة',
    'Motorcycle - Shaft Drive Off-Road':
        'دراجة نارية - نقل بالعمود للطرق الوعرة',
  };

  // Arabic translations for brake systems
  static const Map<String, String> brakeSystemTranslations = {
    'Hydraulic': 'هيدروليكي',
    'Air': 'هوائي',
    'Electric': 'كهربائي',
    'Mechanical': 'ميكانيكي',
    'Air and Hydraulic': 'هوائي وهيدروليكي',
  };

  // Arabic translations for valvetrain designs
  static const Map<String, String> valvetrainTranslations = {
    'SOHC': 'كامة علوية واحدة',
    'DOHC': 'كامتين علويتين',
    'OHV': 'صمامات علوية',
    'CVA': 'تشغيل الصمام المتغير',
  };

  // Arabic translations for trailer types
  static const Map<String, String> trailerTypeTranslations = {
    'Ball Hitch': 'ربط كروي',
    'Fifth Wheel': 'العجلة الخامسة',
    'Gooseneck': 'رقبة الأوزة',
    'Kingpin Hitch': 'ربط الملك',
    'Pintle Hitch': 'ربط الدبوس',
    'Semi-Trailer': 'نصف مقطورة',
    'Full Trailer': 'مقطورة كاملة',
    'Pole Trailer': 'مقطورة عمود',
    'Logging Trailer': 'مقطورة قطع الأشجار',
    'Boat Trailer': 'مقطورة قوارب',
  };

  // Libyan market preferences and adaptations
  static const Map<String, bool> libyanMarketPopularity = {
    // Very popular in Libya
    'Sedan/Saloon': true,
    'Pickup': true,
    'Sport Utility Vehicle (SUV)/Multi-Purpose Vehicle (MPV)': true,
    'Hatchback/Liftback/Notchback': true,
    'Van': true,
    'Truck': true,
    'Manual/Standard': true,
    'Automatic': true,

    // Moderately popular
    'Coupe': false,
    'Crossover Utility Vehicle (CUV)': false,
    'Minivan': false,
    'Wagon': false,

    // Limited popularity
    'Convertible/Cabriolet': false,
    'Roadster': false,
    'Limousine': false,
  };

  // Common Libyan vehicle categories
  static const List<String> libyanVehicleCategories = [
    'سيارات شخصية', // Personal cars
    'سيارات تجارية', // Commercial vehicles
    'شاحنات', // Trucks
    'حافلات', // Buses
    'دراجات نارية', // Motorcycles
    'مركبات خاصة', // Special vehicles
  ];

  static Future<List<Map<String, dynamic>>> extractBodyStyles() async {
    const csvContent = '''
Id;Name
1;Convertible/Cabriolet
3;Coupe
13;Sedan/Saloon
5;Hatchback/Liftback/Notchback
7;Sport Utility Vehicle (SUV)/Multi-Purpose Vehicle (MPV)
8;Crossover Utility Vehicle (CUV)
60;Pickup
9;Van
2;Minivan
15;Wagon
11;Truck
16;Bus
73;Bus - School Bus
82;Motorcycle - Cruiser
80;Motorcycle - Sport
6;Motorcycle - Standard
81;Motorcycle - Touring / Sport Touring
12;Motorcycle - Scooter
69;Off-road Vehicle - All Terrain Vehicle (ATV) (Motorcycle-style)
128;Ambulance
130;Fire Apparatus
117;Limousine
10;Roadster
119;Sport Utility Truck (SUT)
95;Cargo Van
111;Step Van / Walk-in Van
108;Motorhome
61;Trailer
66;Truck-Tractor
''';

    final rows = const CsvToListConverter(
      fieldDelimiter: ';',
      eol: '\n',
    ).convert(csvContent);

    final bodyStyles = <Map<String, dynamic>>[];

    for (var i = 1; i < rows.length; i++) {
      if (rows[i].isNotEmpty && rows[i].length >= 2) {
        final id = int.tryParse(rows[i][0].toString()) ?? 0;
        final name = rows[i][1].toString().trim();

        if (name.isNotEmpty) {
          final arabicName = bodyStyleTranslations[name] ?? name;
          final isPopular = libyanMarketPopularity[name] ?? false;

          // Determine category based on vehicle type
          final category = _determineVehicleCategory(name);

          bodyStyles.add({
            'id': id,
            'name': arabicName,
            'name_english': name,
            'category': category,
            'description': _generateDescription(name, arabicName),
            'is_popular_in_libya': isPopular,
            'icon_name': _getIconName(name),
            'sort_order': isPopular ? i : i + 1000,
          });
        }
      }
    }

    return bodyStyles;
  }

  static Future<List<Map<String, dynamic>>> extractTransmissionTypes() async {
    const csvContent = '''
Id;Name
3;Manual/Standard
2;Automatic
7;Continuously Variable Transmission (CVT)
8;Automated Manual Transmission (AMT)
14;Dual-Clutch Transmission (DCT)
4;Electronic Continuously Variable (e-CVT)
10;Motorcycle - Chain Drive
9;Motorcycle - Shaft Drive
12;Motorcycle - CVT Belt Drive
13;Motorcycle - Chain Drive Off-Road
11;Motorcycle - Shaft Drive Off-Road
''';

    final rows = const CsvToListConverter(
      fieldDelimiter: ';',
      eol: '\n',
    ).convert(csvContent);

    final transmissionTypes = <Map<String, dynamic>>[];

    for (var i = 1; i < rows.length; i++) {
      if (rows[i].isNotEmpty && rows[i].length >= 2) {
        final id = int.tryParse(rows[i][0].toString()) ?? 0;
        final name = rows[i][1].toString().trim();

        if (name.isNotEmpty) {
          final arabicName = transmissionTranslations[name] ?? name;
          final isPopular = libyanMarketPopularity[name] ?? false;

          final category = name.startsWith('Motorcycle')
              ? 'دراجة نارية'
              : 'سيارة';

          transmissionTypes.add({
            'id': id,
            'name': arabicName,
            'name_english': name,
            'category': category,
            'description': _generateTransmissionDescription(name, arabicName),
            'is_popular_in_libya': isPopular,
            'sort_order': isPopular ? i : i + 1000,
          });
        }
      }
    }

    return transmissionTypes;
  }

  static Future<List<Map<String, dynamic>>> extractBrakeSystems() async {
    const csvContent = '''
Id;Name
1;Hydraulic
2;Air
3;Electric
4;Mechanical
5;Air and Hydraulic
''';

    final rows = const CsvToListConverter(
      fieldDelimiter: ';',
      eol: '\n',
    ).convert(csvContent);

    final brakeSystems = <Map<String, dynamic>>[];

    for (var i = 1; i < rows.length; i++) {
      if (rows[i].isNotEmpty && rows[i].length >= 2) {
        final id = int.tryParse(rows[i][0].toString()) ?? 0;
        final name = rows[i][1].toString().trim();

        if (name.isNotEmpty) {
          final arabicName = brakeSystemTranslations[name] ?? name;

          brakeSystems.add({
            'id': id,
            'name': arabicName,
            'name_english': name,
            'description': _generateBrakeDescription(name, arabicName),
            'sort_order': i,
          });
        }
      }
    }

    return brakeSystems;
  }

  static Future<List<Map<String, dynamic>>> extractValvetrainDesigns() async {
    const csvContent = '''
Id;Name
1;SOHC
2;DOHC
3;OHV
4;CVA
''';

    final rows = const CsvToListConverter(
      fieldDelimiter: ';',
      eol: '\n',
    ).convert(csvContent);

    final valvetrainDesigns = <Map<String, dynamic>>[];

    for (var i = 1; i < rows.length; i++) {
      if (rows[i].isNotEmpty && rows[i].length >= 2) {
        final id = int.tryParse(rows[i][0].toString()) ?? 0;
        final name = rows[i][1].toString().trim();

        if (name.isNotEmpty) {
          final arabicName = valvetrainTranslations[name] ?? name;

          valvetrainDesigns.add({
            'id': id,
            'name': arabicName,
            'name_english': name,
            'description': _generateValvetrainDescription(name, arabicName),
            'sort_order': i,
          });
        }
      }
    }

    return valvetrainDesigns;
  }

  static Future<List<Map<String, dynamic>>> extractTrailerTypes() async {
    const csvContent = '''
Id;Name
1;Ball Hitch
2;Fifth Wheel
3;Gooseneck
4;Kingpin Hitch
5;Pintle Hitch
6;Semi-Trailer
7;Full Trailer
8;Pole Trailer
9;Logging Trailer
10;Boat Trailer
''';

    final rows = const CsvToListConverter(
      fieldDelimiter: ';',
      eol: '\n',
    ).convert(csvContent);

    final trailerTypes = <Map<String, dynamic>>[];

    for (var i = 1; i < rows.length; i++) {
      if (rows[i].isNotEmpty && rows[i].length >= 2) {
        final id = int.tryParse(rows[i][0].toString()) ?? 0;
        final name = rows[i][1].toString().trim();

        if (name.isNotEmpty) {
          final arabicName = trailerTypeTranslations[name] ?? name;

          trailerTypes.add({
            'id': id,
            'name': arabicName,
            'name_english': name,
            'description': _generateTrailerDescription(name, arabicName),
            'sort_order': i,
          });
        }
      }
    }

    return trailerTypes;
  }

  // Helper methods for categorization and descriptions
  static String _determineVehicleCategory(String bodyStyle) {
    if (bodyStyle.contains('Motorcycle') || bodyStyle.contains('Scooter')) {
      return 'دراجات نارية';
    } else if (bodyStyle.contains('Truck') || bodyStyle.contains('Pickup')) {
      return 'شاحنات';
    } else if (bodyStyle.contains('Bus')) {
      return 'حافلات';
    } else if (bodyStyle.contains('Van') || bodyStyle == 'Minivan') {
      return 'سيارات تجارية';
    } else if (bodyStyle.contains('Ambulance') || bodyStyle.contains('Fire')) {
      return 'مركبات خاصة';
    } else {
      return 'سيارات شخصية';
    }
  }

  static String _generateDescription(String englishName, String arabicName) {
    final descriptions = <String, String>{
      'Sedan/Saloon':
          'سيارة بأربعة أبواب وصندوق منفصل، مناسبة للاستخدام اليومي والعائلي',
      'Sport Utility Vehicle (SUV)/Multi-Purpose Vehicle (MPV)':
          'سيارة رياضية متعددة الاستخدامات، مناسبة للطرق الوعرة '
          'والاستخدام العائلي',
      'Pickup': 'شاحنة صغيرة بمنطقة شحن مفتوحة، مناسبة للعمل والاستخدام الشخصي',
      'Hatchback/Liftback/Notchback':
          'سيارة بباب خلفي يفتح لأعلى، مناسبة للمدينة والاستخدام اليومي',
      'Van': 'مركبة واسعة للنقل، مناسبة للأعمال التجارية ونقل البضائع',
      'Coupe': 'سيارة رياضية ببابين، تتميز بالتصميم الأنيق والأداء المميز',
      'Convertible/Cabriolet':
          'سيارة مكشوفة بسقف قابل للطي، مناسبة للطقس الجميل',
      'Crossover Utility Vehicle (CUV)':
          'مركبة متعددة الاستخدامات تجمع بين مزايا السيارة والـ SUV',
      'Minivan': 'مركبة عائلية واسعة بعدة مقاعد، مناسبة للعائلات الكبيرة',
      'Wagon': 'سيارة بمساحة تخزين كبيرة، تجمع بين الراحة والعملية',
      'Truck': 'شاحنة كبيرة للنقل الثقيل والأعمال التجارية',
      'Bus': 'حافلة لنقل الركاب، مناسبة للنقل العام والسياحة',
      'Bus - School Bus': 'حافلة مدرسية مخصصة لنقل الطلاب بأمان',
      'Motorcycle - Cruiser': 'دراجة نارية مريحة للرحلات الطويلة',
      'Motorcycle - Sport': 'دراجة نارية رياضية عالية الأداء',
      'Motorcycle - Standard': 'دراجة نارية عادية متعددة الاستخدامات',
      'Motorcycle - Touring / Sport Touring':
          'دراجة نارية سياحية للرحلات الطويلة',
      'Motorcycle - Scooter': 'سكوتر صغير ومناسب للمدينة',
      'Off-road Vehicle - All Terrain Vehicle (ATV) (Motorcycle-style)':
          'مركبة لجميع التضاريس، مناسبة للمغامرات والطرق الوعرة',
      'Ambulance': 'سيارة إسعاف مجهزة للطوارئ الطبية',
      'Fire Apparatus': 'مركبة إطفاء مجهزة لمكافحة الحرائق',
      'Limousine': 'سيارة فاخرة طويلة للمناسبات الخاصة',
      'Roadster': 'سيارة رياضية مكشوفة بمقعدين',
      'Sport Utility Truck (SUT)': 'شاحنة رياضية متعددة الاستخدامات',
      'Cargo Van': 'فان شحن مخصص لنقل البضائع',
      'Step Van / Walk-in Van': 'فان بباب جانبي للدخول السهل',
      'Motorhome': 'منزل متنقل للرحلات والتخييم',
      'Trailer': 'مقطورة للنقل والشحن',
      'Truck-Tractor': 'جرار شاحنة لسحب المقطورات الكبيرة',
    };

    return descriptions[englishName] ?? 'نوع من أنواع المركبات المختلفة';
  }

  static String _generateTransmissionDescription(
    String englishName,
    String arabicName,
  ) {
    final descriptions = <String, String>{
      'Manual/Standard':
          'ناقل الحركة اليدوي التقليدي، يتطلب تدخل السائق لتغيير السرعات',
      'Automatic':
          'ناقل الحركة الأوتوماتيكي، يغير السرعات تلقائياً حسب السرعة والحمل',
      'Continuously Variable Transmission (CVT)':
          'ناقل الحركة المتغير باستمرار، يوفر تسارع سلس وكفاءة في '
          'استهلاك الوقود',
      'Automated Manual Transmission (AMT)':
          'ناقل الحركة اليدوي الآلي، يجمع بين مزايا اليدوي والأوتوماتيكي',
      'Dual-Clutch Transmission (DCT)':
          'ناقل الحركة مزدوج القابض، يوفر تغيير سرعات سريع وسلس',
      'Electronic Continuously Variable (e-CVT)':
          'ناقل الحركة المتغير إلكترونياً، يستخدم في السيارات الهجينة',
      'Motorcycle - Chain Drive':
          'نقل الحركة بالسلسلة للدراجات النارية، نظام قوي وموثوق',
      'Motorcycle - Shaft Drive':
          'نقل الحركة بالعمود للدراجات النارية، نظام هادئ وقليل الصيانة',
      'Motorcycle - CVT Belt Drive':
          'نقل الحركة بالحزام المتغير، نظام أوتوماتيكي للدراجات النارية',
      'Motorcycle - Chain Drive Off-Road':
          'نقل الحركة بالسلسلة للطرق الوعرة، مقاوم للظروف الصعبة',
      'Motorcycle - Shaft Drive Off-Road':
          'نقل الحركة بالعمود للطرق الوعرة، نظام محمي ومتين',
    };

    return descriptions[englishName] ?? 'نوع من أنواع ناقل الحركة';
  }

  static String _generateBrakeDescription(
    String englishName,
    String arabicName,
  ) {
    final descriptions = <String, String>{
      'Hydraulic':
          'نظام فرامل يعتمد على السوائل لنقل القوة، الأكثر شيوعاً في السيارات',
      'Air': 'نظام فرامل هوائي، يستخدم عادة في الشاحنات الكبيرة والحافلات',
      'Electric': 'نظام فرامل كهربائي، يستخدم في السيارات الكهربائية والهجينة',
      'Mechanical': 'نظام فرامل ميكانيكي، يعتمد على الكابلات والروافع',
      'Air and Hydraulic':
          'نظام فرامل مختلط يجمع بين الهوائي والهيدروليكي للمركبات الثقيلة',
    };

    return descriptions[englishName] ?? 'نوع من أنواع أنظمة الفرامل';
  }

  static String _generateValvetrainDescription(
    String englishName,
    String arabicName,
  ) {
    final descriptions = <String, String>{
      'SOHC': 'كامة علوية واحدة، تصميم بسيط وموثوق واقتصادي في الصيانة',
      'DOHC': 'كامتين علويتين، تصميم متقدم لأداء أفضل وكفاءة عالية',
      'OHV': 'صمامات علوية، تصميم تقليدي وقوي مناسب للمحركات الكبيرة',
      'CVA': 'تشغيل الصمام المتغير، تقنية متقدمة لتحسين الأداء والاقتصاد',
    };

    return descriptions[englishName] ?? 'نوع من أنواع تصاميم نظام الصمامات';
  }

  static String _generateTrailerDescription(
    String englishName,
    String arabicName,
  ) {
    final descriptions = <String, String>{
      'Ball Hitch': 'نظام ربط كروي، الأكثر شيوعاً للمقطورات الصغيرة والمتوسطة',
      'Fifth Wheel': 'نظام العجلة الخامسة، يستخدم للمقطورات الكبيرة والثقيلة',
      'Gooseneck': 'نظام رقبة الأوزة، قوي ومناسب للأحمال الثقيلة جداً',
      'Kingpin Hitch': 'نظام ربط الملك، يستخدم في المقطورات التجارية الكبيرة',
      'Pintle Hitch': 'نظام ربط الدبوس، قوي ومتين للاستخدام العسكري والصناعي',
      'Semi-Trailer': 'نصف مقطورة، تعتمد على الجرار لحمل الوزن الأمامي',
      'Full Trailer': 'مقطورة كاملة، لها محاور أمامية وخلفية مستقلة',
      'Pole Trailer': 'مقطورة عمود، مخصصة لنقل الأحمال الطويلة كالأخشاب',
      'Logging Trailer': 'مقطورة قطع الأشجار، مصممة خصيصاً لنقل جذوع الأشجار',
      'Boat Trailer': 'مقطورة قوارب، مصممة لنقل وإنزال القوارب في الماء',
    };

    return descriptions[englishName] ?? 'نوع من أنواع أنظمة ربط المقطورات';
  }

  static String _getIconName(String bodyStyle) {
    final icons = <String, String>{
      'Sedan/Saloon': 'car',
      'Sport Utility Vehicle': 'suv',
      'SUV': 'suv',
      'Pickup': 'pickup',
      'Truck': 'truck',
      'Van': 'van',
      'Bus': 'bus',
      'Motorcycle': 'motorcycle',
      'Coupe': 'sports_car',
      'Hatchback': 'car',
      'Convertible': 'convertible',
      'Crossover': 'suv',
      'Minivan': 'van',
      'Wagon': 'wagon',
      'Ambulance': 'ambulance',
      'Fire': 'fire_truck',
      'Limousine': 'limousine',
      'Roadster': 'sports_car',
      'Trailer': 'trailer',
      'Motorhome': 'rv',
      'Scooter': 'scooter',
    };

    for (final key in icons.keys) {
      if (bodyStyle.contains(key)) {
        return icons[key]!;
      }
    }

    return 'vehicle';
  }

  // Generate SQL insert statements for database migration
  static Future<String> generateSQLInserts() async {
    final bodyStyles = await extractBodyStyles();
    final transmissionTypes = await extractTransmissionTypes();
    final brakeSystems = await extractBrakeSystems();
    final valvetrainDesigns = await extractValvetrainDesigns();
    final trailerTypes = await extractTrailerTypes();

    final sql = StringBuffer()
      ..writeln('-- Body Styles')
      ..writeln(
        'INSERT INTO vehicle_body_styles (id, name, name_english, category, '
        'description, is_popular_in_libya, icon_name) VALUES',
      );

    for (var i = 0; i < bodyStyles.length; i++) {
      final style = bodyStyles[i];
      sql.write(
        "(${style['id']}, '${_escapeSql(style['name'].toString())}', "
        "'${_escapeSql(style['name_english'].toString())}', "
        "'${_escapeSql(style['category'].toString())}', "
        "'${_escapeSql(style['description'].toString())}', "
        "${style['is_popular_in_libya']}, '${style['icon_name']}')",
      );
      if (i < bodyStyles.length - 1) {
        sql.write(',');
      }
      sql.writeln();
    }

    sql
      ..writeln(
        'ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, '
        'name_english = EXCLUDED.name_english, '
        'category = EXCLUDED.category, '
        'description = EXCLUDED.description, '
        'is_popular_in_libya = EXCLUDED.is_popular_in_libya, '
        'icon_name = EXCLUDED.icon_name;',
      )
      ..writeln()
      ..writeln('-- Transmission Types')
      ..writeln(
        'INSERT INTO vehicle_transmission_types (id, name, name_english, '
        'category, description) VALUES',
      );
    for (var i = 0; i < transmissionTypes.length; i++) {
      final trans = transmissionTypes[i];
      sql.write(
        "(${trans['id']}, '${_escapeSql(trans['name'].toString())}', "
        "'${_escapeSql(trans['name_english'].toString())}', "
        "'${_escapeSql(trans['category'].toString())}', "
        "'${_escapeSql(trans['description'].toString())}')",
      );
      if (i < transmissionTypes.length - 1) {
        sql.write(',');
      }
      sql.writeln();
    }
    sql
      ..writeln(
        'ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, '
        'name_english = EXCLUDED.name_english, '
        'category = EXCLUDED.category, '
        'description = EXCLUDED.description;',
      )
      ..writeln()
      ..writeln('-- Brake Systems')
      ..writeln(
        'INSERT INTO vehicle_brake_systems (id, name, name_english, '
        'description) VALUES',
      );
    for (var i = 0; i < brakeSystems.length; i++) {
      final brake = brakeSystems[i];
      sql.write(
        "(${brake['id']}, '${_escapeSql(brake['name'].toString())}', "
        "'${_escapeSql(brake['name_english'].toString())}', "
        "'${_escapeSql(brake['description'].toString())}')",
      );
      if (i < brakeSystems.length - 1) {
        sql.write(',');
      }
      sql.writeln();
    }
    sql
      ..writeln(
        'ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, '
        'name_english = EXCLUDED.name_english, '
        'description = EXCLUDED.description;',
      )
      ..writeln()
      ..writeln('-- Valvetrain Designs')
      ..writeln(
        'INSERT INTO vehicle_valvetrain_designs (id, name, name_english, '
        'description) VALUES',
      );

    for (var i = 0; i < valvetrainDesigns.length; i++) {
      final valvetrain = valvetrainDesigns[i];
      sql.write(
        "(${valvetrain['id']}, '${_escapeSql(valvetrain['name'].toString())}', "
        "'${_escapeSql(valvetrain['name_english'].toString())}', "
        "'${_escapeSql(valvetrain['description'].toString())}')",
      );
      if (i < valvetrainDesigns.length - 1) {
        sql.write(',');
      }
      sql.writeln();
    }

    sql
      ..writeln(
        'ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, '
        'name_english = EXCLUDED.name_english, '
        'description = EXCLUDED.description;',
      )
      ..writeln()
      ..writeln('-- Trailer Types')
      ..writeln(
        'INSERT INTO vehicle_trailer_types (id, name, name_english, '
        'description) VALUES',
      );

    for (var i = 0; i < trailerTypes.length; i++) {
      final trailer = trailerTypes[i];
      sql.write(
        "(${trailer['id']}, '${_escapeSql(trailer['name'].toString())}', "
        "'${_escapeSql(trailer['name_english'].toString())}', "
        "'${_escapeSql(trailer['description'].toString())}')",
      );
      if (i < trailerTypes.length - 1) {
        sql.write(',');
      }
      sql.writeln();
    }

    sql.writeln(
      'ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, '
      'name_english = EXCLUDED.name_english, '
      'description = EXCLUDED.description;',
    );

    return sql.toString();
  }

  static String _escapeSql(String value) => value.replaceAll("'", "''");
}

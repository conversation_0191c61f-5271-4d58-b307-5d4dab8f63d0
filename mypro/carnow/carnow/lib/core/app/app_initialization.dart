import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:dio/dio.dart';

import '../auth/unified_auth_provider.dart';
import '../auth/auth_models.dart';

final _logger = Logger();

/// App Initialization - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// Robust initialization with proper error handling and timeout management
/// Uses UnifiedAuthProvider for authentication without race conditions
class AppInitialization {
  static ProviderContainer? _container;
  
  /// Initialize the app with robust error handling and timeout management
  static Future<ProviderContainer> initialize() async {
    try {
      _logger.i('🚀 Starting CarNow app initialization...');
      
      // No Supabase initialization - Forever Plan Compliance
      // Flutter UI Only → Go API → Supabase Data
      
      // Create provider container first
      _container = ProviderContainer();
      
      // Initialize authentication system - removed timeout to prevent freezing
      await _initializeAuthProvider(_container!);
      
      // Wake up backend services asynchronously (don't block app startup)
      _wakeUpBackendAsync();
      
      _logger.i('✅ CarNow app initialization completed successfully');
      return _container!;
    } catch (e, stack) {
      _logger.e('❌ App initialization failed', error: e, stackTrace: stack);
      
      // Graceful degradation - create container anyway for offline functionality
      _container ??= ProviderContainer();
      _logger.w('⚠️ Continuing with offline functionality');
      return _container!;
    }
  }

  /// Simple backend wake-up without creating complex services
  static void _wakeUpBackendAsync() {
    Future.microtask(() async {
      try {
        final dio = Dio();
        dio.options.connectTimeout = const Duration(seconds: 5);
        dio.options.receiveTimeout = const Duration(seconds: 5);
        
        _logger.i('🌐 Simple backend ping...');
        
        // Simple ping only - no complex health service
        await dio.get('https://backend-go-8klm.onrender.com/health');
        _logger.i('✅ Backend ping completed');
      } catch (e) {
        _logger.w('⚠️ Backend ping failed (normal if sleeping): $e');
      }
    });
  }
  
  /// Initialize auth provider using proper Riverpod pattern
  static Future<void> _initializeAuthProvider(ProviderContainer container) async {
    try {
      // Initialize the provider by reading it once - this triggers the build() method
      // The UnifiedAuthProvider.build() method now handles synchronous initialization
      final authState = container.read(unifiedAuthProviderProvider);
      
      _logger.i('✅ Auth provider initialized with state: ${authState.runtimeType}');
      
      // Log the initialization phase for debugging
      if (authState is AuthStateInitial) {
        _logger.d('Auth provider in initial state - async initialization will continue in background');
      } else if (authState is AuthStateAuthenticated) {
        _logger.i('User session restored successfully');
      } else if (authState is AuthStateUnauthenticated) {
        _logger.i('No existing session found - user needs to sign in');
      } else if (authState is AuthStateError) {
        _logger.w('Auth provider initialized with error state');
      }
      
    } catch (e) {
      _logger.e('Failed to initialize auth provider: $e');
      rethrow;
    }
  }
  
  /// Cleanup resources
  static Future<void> cleanup() async {
    try {
      _logger.i('🧹 Cleaning up app resources...');
      
      _container?.dispose();
      _container = null;
      
      _logger.i('✅ App cleanup completed');
    } catch (e) {
      _logger.w('⚠️ App cleanup warning: $e');
    }
  }
  
  /// Get current container (for testing)
  static ProviderContainer? get container => _container;
}

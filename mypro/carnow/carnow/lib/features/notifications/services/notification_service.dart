import 'package:flutter/foundation.dart' show debugPrint;
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/services/notification_api_service.dart';
import '../models/notification_model.dart';

part 'notification_service.g.dart';

/// Provider for user notifications - Forever Plan Architecture
/// ✅ Uses NotificationApiService instead of direct Supabase calls
@riverpod
Future<List<NotificationModel>> userNotifications(Ref ref) async {
  final apiService = ref.watch(notificationApiServiceProvider);

  try {
    final notificationsData = await apiService.getUserNotifications();
    return notificationsData
        .map((data) => NotificationModel.fromJson(data))
        .toList();
  } catch (e) {
    debugPrint('Error fetching notifications: $e');
    return [];
  }
}

/// Provider for unread notification count
@riverpod
Future<int> unreadNotificationCount(Ref ref) async {
  final apiService = ref.watch(notificationApiServiceProvider);

  try {
    return await apiService.getUnreadNotificationCount();
  } catch (e) {
    debugPrint('Error fetching unread count: $e');
    return 0;
  }
}



/// Legacy NotificationService - DEPRECATED
/// ❌ This class violates Forever Plan Architecture with direct Supabase calls
/// ✅ Use userNotificationsProvider and notificationApiServiceProvider instead
@Deprecated('Use userNotificationsProvider and notificationApiServiceProvider instead')
class NotificationService {
  const NotificationService();

  /// Get all notifications for the current user
  /// ❌ DEPRECATED: Use userNotificationsProvider instead
  @Deprecated('Use userNotificationsProvider instead')
  Future<List<NotificationModel>> getNotifications() async {
    debugPrint('⚠️ DEPRECATED: Use userNotificationsProvider instead');
    return [];
  }

  /// Mark notification as read
  /// ❌ DEPRECATED: Use NotificationApiService instead
  @Deprecated('Use NotificationApiService instead')
  Future<void> markAsRead(String notificationId) async {
    debugPrint('⚠️ DEPRECATED: Use NotificationApiService instead');
  }

  /// Mark all notifications as read
  /// ❌ DEPRECATED: Use NotificationApiService instead
  @Deprecated('Use NotificationApiService instead')
  Future<void> markAllAsRead() async {
    debugPrint('⚠️ DEPRECATED: Use NotificationApiService instead');
  }

  /// Delete notification (soft delete)
  /// ❌ DEPRECATED: Use NotificationApiService instead
  @Deprecated('Use NotificationApiService instead')
  Future<void> deleteNotification(String notificationId) async {
    debugPrint('⚠️ DEPRECATED: Use NotificationApiService instead');
  }

  /// Get unread notification count
  /// ❌ DEPRECATED: Use unreadNotificationCountProvider instead
  @Deprecated('Use unreadNotificationCountProvider instead')
  Future<int> getUnreadCount() async {
    debugPrint('⚠️ DEPRECATED: Use unreadNotificationCountProvider instead');
    return 0;
  }

  /// Send a notification to a user
  /// ❌ DEPRECATED: Use NotificationApiService instead
  @Deprecated('Use NotificationApiService instead')
  Future<void> sendNotification({
    required String userId,
    required String title,
    required String message,
    String? type,
    String? relatedEntityId,
    String? relatedEntityType,
  }) async {
    debugPrint('⚠️ DEPRECATED: Use NotificationApiService instead');
  }

  /// Clear all notifications for the current user
  /// ❌ DEPRECATED: Use NotificationApiService instead
  @Deprecated('Use NotificationApiService instead')
  Future<void> clearAllNotifications() async {
    debugPrint('⚠️ DEPRECATED: Use NotificationApiService instead');
  }
}

/// DEPRECATED: Provider for NotificationService
/// ✅ Use userNotificationsProvider and notificationApiServiceProvider instead
@Deprecated('Use userNotificationsProvider and notificationApiServiceProvider instead')
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return const NotificationService();
});

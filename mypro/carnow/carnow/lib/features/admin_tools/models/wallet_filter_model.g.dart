// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_filter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WalletFilterModel _$WalletFilterModelFromJson(Map<String, dynamic> json) =>
    _WalletFilterModel(
      status: json['status'] as String?,
      isVerified: json['isVerified'] as bool?,
      isFlagged: json['isFlagged'] as bool?,
      minBalance: (json['minBalance'] as num?)?.toDouble(),
      maxBalance: (json['maxBalance'] as num?)?.toDouble(),
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
    );

Map<String, dynamic> _$WalletFilterModelToJson(_WalletFilterModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'isVerified': instance.isVerified,
      'isFlagged': instance.isFlagged,
      'minBalance': instance.minBalance,
      'maxBalance': instance.maxBalance,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
    };

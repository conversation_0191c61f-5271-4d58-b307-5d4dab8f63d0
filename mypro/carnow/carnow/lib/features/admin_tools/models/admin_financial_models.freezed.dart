// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admin_financial_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WalletAdjustmentRequest {

@JsonKey(name: 'wallet_id') String get walletId;@JsonKey(name: 'adjustment_amount') String get adjustmentAmount;@JsonKey(name: 'adjustment_type') String get adjustmentType; String get reason;@JsonKey(name: 'internal_notes') String? get internalNotes;@JsonKey(name: 'related_transaction_id') String? get relatedTransactionId;@JsonKey(name: 'admin_user_id') String get adminUserId;
/// Create a copy of WalletAdjustmentRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletAdjustmentRequestCopyWith<WalletAdjustmentRequest> get copyWith => _$WalletAdjustmentRequestCopyWithImpl<WalletAdjustmentRequest>(this as WalletAdjustmentRequest, _$identity);

  /// Serializes this WalletAdjustmentRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletAdjustmentRequest&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.adjustmentAmount, adjustmentAmount) || other.adjustmentAmount == adjustmentAmount)&&(identical(other.adjustmentType, adjustmentType) || other.adjustmentType == adjustmentType)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.internalNotes, internalNotes) || other.internalNotes == internalNotes)&&(identical(other.relatedTransactionId, relatedTransactionId) || other.relatedTransactionId == relatedTransactionId)&&(identical(other.adminUserId, adminUserId) || other.adminUserId == adminUserId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,walletId,adjustmentAmount,adjustmentType,reason,internalNotes,relatedTransactionId,adminUserId);

@override
String toString() {
  return 'WalletAdjustmentRequest(walletId: $walletId, adjustmentAmount: $adjustmentAmount, adjustmentType: $adjustmentType, reason: $reason, internalNotes: $internalNotes, relatedTransactionId: $relatedTransactionId, adminUserId: $adminUserId)';
}


}

/// @nodoc
abstract mixin class $WalletAdjustmentRequestCopyWith<$Res>  {
  factory $WalletAdjustmentRequestCopyWith(WalletAdjustmentRequest value, $Res Function(WalletAdjustmentRequest) _then) = _$WalletAdjustmentRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'adjustment_amount') String adjustmentAmount,@JsonKey(name: 'adjustment_type') String adjustmentType, String reason,@JsonKey(name: 'internal_notes') String? internalNotes,@JsonKey(name: 'related_transaction_id') String? relatedTransactionId,@JsonKey(name: 'admin_user_id') String adminUserId
});




}
/// @nodoc
class _$WalletAdjustmentRequestCopyWithImpl<$Res>
    implements $WalletAdjustmentRequestCopyWith<$Res> {
  _$WalletAdjustmentRequestCopyWithImpl(this._self, this._then);

  final WalletAdjustmentRequest _self;
  final $Res Function(WalletAdjustmentRequest) _then;

/// Create a copy of WalletAdjustmentRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? walletId = null,Object? adjustmentAmount = null,Object? adjustmentType = null,Object? reason = null,Object? internalNotes = freezed,Object? relatedTransactionId = freezed,Object? adminUserId = null,}) {
  return _then(_self.copyWith(
walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,adjustmentAmount: null == adjustmentAmount ? _self.adjustmentAmount : adjustmentAmount // ignore: cast_nullable_to_non_nullable
as String,adjustmentType: null == adjustmentType ? _self.adjustmentType : adjustmentType // ignore: cast_nullable_to_non_nullable
as String,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,internalNotes: freezed == internalNotes ? _self.internalNotes : internalNotes // ignore: cast_nullable_to_non_nullable
as String?,relatedTransactionId: freezed == relatedTransactionId ? _self.relatedTransactionId : relatedTransactionId // ignore: cast_nullable_to_non_nullable
as String?,adminUserId: null == adminUserId ? _self.adminUserId : adminUserId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletAdjustmentRequest].
extension WalletAdjustmentRequestPatterns on WalletAdjustmentRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletAdjustmentRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletAdjustmentRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletAdjustmentRequest value)  $default,){
final _that = this;
switch (_that) {
case _WalletAdjustmentRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletAdjustmentRequest value)?  $default,){
final _that = this;
switch (_that) {
case _WalletAdjustmentRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'adjustment_amount')  String adjustmentAmount, @JsonKey(name: 'adjustment_type')  String adjustmentType,  String reason, @JsonKey(name: 'internal_notes')  String? internalNotes, @JsonKey(name: 'related_transaction_id')  String? relatedTransactionId, @JsonKey(name: 'admin_user_id')  String adminUserId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletAdjustmentRequest() when $default != null:
return $default(_that.walletId,_that.adjustmentAmount,_that.adjustmentType,_that.reason,_that.internalNotes,_that.relatedTransactionId,_that.adminUserId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'adjustment_amount')  String adjustmentAmount, @JsonKey(name: 'adjustment_type')  String adjustmentType,  String reason, @JsonKey(name: 'internal_notes')  String? internalNotes, @JsonKey(name: 'related_transaction_id')  String? relatedTransactionId, @JsonKey(name: 'admin_user_id')  String adminUserId)  $default,) {final _that = this;
switch (_that) {
case _WalletAdjustmentRequest():
return $default(_that.walletId,_that.adjustmentAmount,_that.adjustmentType,_that.reason,_that.internalNotes,_that.relatedTransactionId,_that.adminUserId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'adjustment_amount')  String adjustmentAmount, @JsonKey(name: 'adjustment_type')  String adjustmentType,  String reason, @JsonKey(name: 'internal_notes')  String? internalNotes, @JsonKey(name: 'related_transaction_id')  String? relatedTransactionId, @JsonKey(name: 'admin_user_id')  String adminUserId)?  $default,) {final _that = this;
switch (_that) {
case _WalletAdjustmentRequest() when $default != null:
return $default(_that.walletId,_that.adjustmentAmount,_that.adjustmentType,_that.reason,_that.internalNotes,_that.relatedTransactionId,_that.adminUserId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletAdjustmentRequest implements WalletAdjustmentRequest {
  const _WalletAdjustmentRequest({@JsonKey(name: 'wallet_id') required this.walletId, @JsonKey(name: 'adjustment_amount') required this.adjustmentAmount, @JsonKey(name: 'adjustment_type') required this.adjustmentType, required this.reason, @JsonKey(name: 'internal_notes') this.internalNotes, @JsonKey(name: 'related_transaction_id') this.relatedTransactionId, @JsonKey(name: 'admin_user_id') required this.adminUserId});
  factory _WalletAdjustmentRequest.fromJson(Map<String, dynamic> json) => _$WalletAdjustmentRequestFromJson(json);

@override@JsonKey(name: 'wallet_id') final  String walletId;
@override@JsonKey(name: 'adjustment_amount') final  String adjustmentAmount;
@override@JsonKey(name: 'adjustment_type') final  String adjustmentType;
@override final  String reason;
@override@JsonKey(name: 'internal_notes') final  String? internalNotes;
@override@JsonKey(name: 'related_transaction_id') final  String? relatedTransactionId;
@override@JsonKey(name: 'admin_user_id') final  String adminUserId;

/// Create a copy of WalletAdjustmentRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletAdjustmentRequestCopyWith<_WalletAdjustmentRequest> get copyWith => __$WalletAdjustmentRequestCopyWithImpl<_WalletAdjustmentRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletAdjustmentRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletAdjustmentRequest&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.adjustmentAmount, adjustmentAmount) || other.adjustmentAmount == adjustmentAmount)&&(identical(other.adjustmentType, adjustmentType) || other.adjustmentType == adjustmentType)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.internalNotes, internalNotes) || other.internalNotes == internalNotes)&&(identical(other.relatedTransactionId, relatedTransactionId) || other.relatedTransactionId == relatedTransactionId)&&(identical(other.adminUserId, adminUserId) || other.adminUserId == adminUserId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,walletId,adjustmentAmount,adjustmentType,reason,internalNotes,relatedTransactionId,adminUserId);

@override
String toString() {
  return 'WalletAdjustmentRequest(walletId: $walletId, adjustmentAmount: $adjustmentAmount, adjustmentType: $adjustmentType, reason: $reason, internalNotes: $internalNotes, relatedTransactionId: $relatedTransactionId, adminUserId: $adminUserId)';
}


}

/// @nodoc
abstract mixin class _$WalletAdjustmentRequestCopyWith<$Res> implements $WalletAdjustmentRequestCopyWith<$Res> {
  factory _$WalletAdjustmentRequestCopyWith(_WalletAdjustmentRequest value, $Res Function(_WalletAdjustmentRequest) _then) = __$WalletAdjustmentRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'adjustment_amount') String adjustmentAmount,@JsonKey(name: 'adjustment_type') String adjustmentType, String reason,@JsonKey(name: 'internal_notes') String? internalNotes,@JsonKey(name: 'related_transaction_id') String? relatedTransactionId,@JsonKey(name: 'admin_user_id') String adminUserId
});




}
/// @nodoc
class __$WalletAdjustmentRequestCopyWithImpl<$Res>
    implements _$WalletAdjustmentRequestCopyWith<$Res> {
  __$WalletAdjustmentRequestCopyWithImpl(this._self, this._then);

  final _WalletAdjustmentRequest _self;
  final $Res Function(_WalletAdjustmentRequest) _then;

/// Create a copy of WalletAdjustmentRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? walletId = null,Object? adjustmentAmount = null,Object? adjustmentType = null,Object? reason = null,Object? internalNotes = freezed,Object? relatedTransactionId = freezed,Object? adminUserId = null,}) {
  return _then(_WalletAdjustmentRequest(
walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,adjustmentAmount: null == adjustmentAmount ? _self.adjustmentAmount : adjustmentAmount // ignore: cast_nullable_to_non_nullable
as String,adjustmentType: null == adjustmentType ? _self.adjustmentType : adjustmentType // ignore: cast_nullable_to_non_nullable
as String,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,internalNotes: freezed == internalNotes ? _self.internalNotes : internalNotes // ignore: cast_nullable_to_non_nullable
as String?,relatedTransactionId: freezed == relatedTransactionId ? _self.relatedTransactionId : relatedTransactionId // ignore: cast_nullable_to_non_nullable
as String?,adminUserId: null == adminUserId ? _self.adminUserId : adminUserId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$WalletAdjustmentResponse {

 String get id;@JsonKey(name: 'wallet_id') String get walletId;@JsonKey(name: 'user_id') String get userId;@JsonKey(name: 'adjustment_amount') String get adjustmentAmount;@JsonKey(name: 'adjustment_type') String get adjustmentType;@JsonKey(name: 'previous_balance') String get previousBalance;@JsonKey(name: 'new_balance') String get newBalance;@JsonKey(name: 'admin_user_id') String get adminUserId; String get reason;@JsonKey(name: 'internal_notes') String? get internalNotes;@JsonKey(name: 'requires_approval') bool get requiresApproval;@JsonKey(name: 'is_approved') bool get isApproved;@JsonKey(name: 'approved_by') String? get approvedBy;@JsonKey(name: 'approved_at') DateTime? get approvedAt;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'processed_at') DateTime? get processedAt;
/// Create a copy of WalletAdjustmentResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletAdjustmentResponseCopyWith<WalletAdjustmentResponse> get copyWith => _$WalletAdjustmentResponseCopyWithImpl<WalletAdjustmentResponse>(this as WalletAdjustmentResponse, _$identity);

  /// Serializes this WalletAdjustmentResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletAdjustmentResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.adjustmentAmount, adjustmentAmount) || other.adjustmentAmount == adjustmentAmount)&&(identical(other.adjustmentType, adjustmentType) || other.adjustmentType == adjustmentType)&&(identical(other.previousBalance, previousBalance) || other.previousBalance == previousBalance)&&(identical(other.newBalance, newBalance) || other.newBalance == newBalance)&&(identical(other.adminUserId, adminUserId) || other.adminUserId == adminUserId)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.internalNotes, internalNotes) || other.internalNotes == internalNotes)&&(identical(other.requiresApproval, requiresApproval) || other.requiresApproval == requiresApproval)&&(identical(other.isApproved, isApproved) || other.isApproved == isApproved)&&(identical(other.approvedBy, approvedBy) || other.approvedBy == approvedBy)&&(identical(other.approvedAt, approvedAt) || other.approvedAt == approvedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,walletId,userId,adjustmentAmount,adjustmentType,previousBalance,newBalance,adminUserId,reason,internalNotes,requiresApproval,isApproved,approvedBy,approvedAt,createdAt,processedAt);

@override
String toString() {
  return 'WalletAdjustmentResponse(id: $id, walletId: $walletId, userId: $userId, adjustmentAmount: $adjustmentAmount, adjustmentType: $adjustmentType, previousBalance: $previousBalance, newBalance: $newBalance, adminUserId: $adminUserId, reason: $reason, internalNotes: $internalNotes, requiresApproval: $requiresApproval, isApproved: $isApproved, approvedBy: $approvedBy, approvedAt: $approvedAt, createdAt: $createdAt, processedAt: $processedAt)';
}


}

/// @nodoc
abstract mixin class $WalletAdjustmentResponseCopyWith<$Res>  {
  factory $WalletAdjustmentResponseCopyWith(WalletAdjustmentResponse value, $Res Function(WalletAdjustmentResponse) _then) = _$WalletAdjustmentResponseCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'user_id') String userId,@JsonKey(name: 'adjustment_amount') String adjustmentAmount,@JsonKey(name: 'adjustment_type') String adjustmentType,@JsonKey(name: 'previous_balance') String previousBalance,@JsonKey(name: 'new_balance') String newBalance,@JsonKey(name: 'admin_user_id') String adminUserId, String reason,@JsonKey(name: 'internal_notes') String? internalNotes,@JsonKey(name: 'requires_approval') bool requiresApproval,@JsonKey(name: 'is_approved') bool isApproved,@JsonKey(name: 'approved_by') String? approvedBy,@JsonKey(name: 'approved_at') DateTime? approvedAt,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'processed_at') DateTime? processedAt
});




}
/// @nodoc
class _$WalletAdjustmentResponseCopyWithImpl<$Res>
    implements $WalletAdjustmentResponseCopyWith<$Res> {
  _$WalletAdjustmentResponseCopyWithImpl(this._self, this._then);

  final WalletAdjustmentResponse _self;
  final $Res Function(WalletAdjustmentResponse) _then;

/// Create a copy of WalletAdjustmentResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? walletId = null,Object? userId = null,Object? adjustmentAmount = null,Object? adjustmentType = null,Object? previousBalance = null,Object? newBalance = null,Object? adminUserId = null,Object? reason = null,Object? internalNotes = freezed,Object? requiresApproval = null,Object? isApproved = null,Object? approvedBy = freezed,Object? approvedAt = freezed,Object? createdAt = null,Object? processedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,adjustmentAmount: null == adjustmentAmount ? _self.adjustmentAmount : adjustmentAmount // ignore: cast_nullable_to_non_nullable
as String,adjustmentType: null == adjustmentType ? _self.adjustmentType : adjustmentType // ignore: cast_nullable_to_non_nullable
as String,previousBalance: null == previousBalance ? _self.previousBalance : previousBalance // ignore: cast_nullable_to_non_nullable
as String,newBalance: null == newBalance ? _self.newBalance : newBalance // ignore: cast_nullable_to_non_nullable
as String,adminUserId: null == adminUserId ? _self.adminUserId : adminUserId // ignore: cast_nullable_to_non_nullable
as String,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,internalNotes: freezed == internalNotes ? _self.internalNotes : internalNotes // ignore: cast_nullable_to_non_nullable
as String?,requiresApproval: null == requiresApproval ? _self.requiresApproval : requiresApproval // ignore: cast_nullable_to_non_nullable
as bool,isApproved: null == isApproved ? _self.isApproved : isApproved // ignore: cast_nullable_to_non_nullable
as bool,approvedBy: freezed == approvedBy ? _self.approvedBy : approvedBy // ignore: cast_nullable_to_non_nullable
as String?,approvedAt: freezed == approvedAt ? _self.approvedAt : approvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletAdjustmentResponse].
extension WalletAdjustmentResponsePatterns on WalletAdjustmentResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletAdjustmentResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletAdjustmentResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletAdjustmentResponse value)  $default,){
final _that = this;
switch (_that) {
case _WalletAdjustmentResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletAdjustmentResponse value)?  $default,){
final _that = this;
switch (_that) {
case _WalletAdjustmentResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId, @JsonKey(name: 'adjustment_amount')  String adjustmentAmount, @JsonKey(name: 'adjustment_type')  String adjustmentType, @JsonKey(name: 'previous_balance')  String previousBalance, @JsonKey(name: 'new_balance')  String newBalance, @JsonKey(name: 'admin_user_id')  String adminUserId,  String reason, @JsonKey(name: 'internal_notes')  String? internalNotes, @JsonKey(name: 'requires_approval')  bool requiresApproval, @JsonKey(name: 'is_approved')  bool isApproved, @JsonKey(name: 'approved_by')  String? approvedBy, @JsonKey(name: 'approved_at')  DateTime? approvedAt, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'processed_at')  DateTime? processedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletAdjustmentResponse() when $default != null:
return $default(_that.id,_that.walletId,_that.userId,_that.adjustmentAmount,_that.adjustmentType,_that.previousBalance,_that.newBalance,_that.adminUserId,_that.reason,_that.internalNotes,_that.requiresApproval,_that.isApproved,_that.approvedBy,_that.approvedAt,_that.createdAt,_that.processedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId, @JsonKey(name: 'adjustment_amount')  String adjustmentAmount, @JsonKey(name: 'adjustment_type')  String adjustmentType, @JsonKey(name: 'previous_balance')  String previousBalance, @JsonKey(name: 'new_balance')  String newBalance, @JsonKey(name: 'admin_user_id')  String adminUserId,  String reason, @JsonKey(name: 'internal_notes')  String? internalNotes, @JsonKey(name: 'requires_approval')  bool requiresApproval, @JsonKey(name: 'is_approved')  bool isApproved, @JsonKey(name: 'approved_by')  String? approvedBy, @JsonKey(name: 'approved_at')  DateTime? approvedAt, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'processed_at')  DateTime? processedAt)  $default,) {final _that = this;
switch (_that) {
case _WalletAdjustmentResponse():
return $default(_that.id,_that.walletId,_that.userId,_that.adjustmentAmount,_that.adjustmentType,_that.previousBalance,_that.newBalance,_that.adminUserId,_that.reason,_that.internalNotes,_that.requiresApproval,_that.isApproved,_that.approvedBy,_that.approvedAt,_that.createdAt,_that.processedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId, @JsonKey(name: 'adjustment_amount')  String adjustmentAmount, @JsonKey(name: 'adjustment_type')  String adjustmentType, @JsonKey(name: 'previous_balance')  String previousBalance, @JsonKey(name: 'new_balance')  String newBalance, @JsonKey(name: 'admin_user_id')  String adminUserId,  String reason, @JsonKey(name: 'internal_notes')  String? internalNotes, @JsonKey(name: 'requires_approval')  bool requiresApproval, @JsonKey(name: 'is_approved')  bool isApproved, @JsonKey(name: 'approved_by')  String? approvedBy, @JsonKey(name: 'approved_at')  DateTime? approvedAt, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'processed_at')  DateTime? processedAt)?  $default,) {final _that = this;
switch (_that) {
case _WalletAdjustmentResponse() when $default != null:
return $default(_that.id,_that.walletId,_that.userId,_that.adjustmentAmount,_that.adjustmentType,_that.previousBalance,_that.newBalance,_that.adminUserId,_that.reason,_that.internalNotes,_that.requiresApproval,_that.isApproved,_that.approvedBy,_that.approvedAt,_that.createdAt,_that.processedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletAdjustmentResponse implements WalletAdjustmentResponse {
  const _WalletAdjustmentResponse({required this.id, @JsonKey(name: 'wallet_id') required this.walletId, @JsonKey(name: 'user_id') required this.userId, @JsonKey(name: 'adjustment_amount') required this.adjustmentAmount, @JsonKey(name: 'adjustment_type') required this.adjustmentType, @JsonKey(name: 'previous_balance') required this.previousBalance, @JsonKey(name: 'new_balance') required this.newBalance, @JsonKey(name: 'admin_user_id') required this.adminUserId, required this.reason, @JsonKey(name: 'internal_notes') this.internalNotes, @JsonKey(name: 'requires_approval') required this.requiresApproval, @JsonKey(name: 'is_approved') required this.isApproved, @JsonKey(name: 'approved_by') this.approvedBy, @JsonKey(name: 'approved_at') this.approvedAt, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'processed_at') this.processedAt});
  factory _WalletAdjustmentResponse.fromJson(Map<String, dynamic> json) => _$WalletAdjustmentResponseFromJson(json);

@override final  String id;
@override@JsonKey(name: 'wallet_id') final  String walletId;
@override@JsonKey(name: 'user_id') final  String userId;
@override@JsonKey(name: 'adjustment_amount') final  String adjustmentAmount;
@override@JsonKey(name: 'adjustment_type') final  String adjustmentType;
@override@JsonKey(name: 'previous_balance') final  String previousBalance;
@override@JsonKey(name: 'new_balance') final  String newBalance;
@override@JsonKey(name: 'admin_user_id') final  String adminUserId;
@override final  String reason;
@override@JsonKey(name: 'internal_notes') final  String? internalNotes;
@override@JsonKey(name: 'requires_approval') final  bool requiresApproval;
@override@JsonKey(name: 'is_approved') final  bool isApproved;
@override@JsonKey(name: 'approved_by') final  String? approvedBy;
@override@JsonKey(name: 'approved_at') final  DateTime? approvedAt;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'processed_at') final  DateTime? processedAt;

/// Create a copy of WalletAdjustmentResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletAdjustmentResponseCopyWith<_WalletAdjustmentResponse> get copyWith => __$WalletAdjustmentResponseCopyWithImpl<_WalletAdjustmentResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletAdjustmentResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletAdjustmentResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.adjustmentAmount, adjustmentAmount) || other.adjustmentAmount == adjustmentAmount)&&(identical(other.adjustmentType, adjustmentType) || other.adjustmentType == adjustmentType)&&(identical(other.previousBalance, previousBalance) || other.previousBalance == previousBalance)&&(identical(other.newBalance, newBalance) || other.newBalance == newBalance)&&(identical(other.adminUserId, adminUserId) || other.adminUserId == adminUserId)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.internalNotes, internalNotes) || other.internalNotes == internalNotes)&&(identical(other.requiresApproval, requiresApproval) || other.requiresApproval == requiresApproval)&&(identical(other.isApproved, isApproved) || other.isApproved == isApproved)&&(identical(other.approvedBy, approvedBy) || other.approvedBy == approvedBy)&&(identical(other.approvedAt, approvedAt) || other.approvedAt == approvedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.processedAt, processedAt) || other.processedAt == processedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,walletId,userId,adjustmentAmount,adjustmentType,previousBalance,newBalance,adminUserId,reason,internalNotes,requiresApproval,isApproved,approvedBy,approvedAt,createdAt,processedAt);

@override
String toString() {
  return 'WalletAdjustmentResponse(id: $id, walletId: $walletId, userId: $userId, adjustmentAmount: $adjustmentAmount, adjustmentType: $adjustmentType, previousBalance: $previousBalance, newBalance: $newBalance, adminUserId: $adminUserId, reason: $reason, internalNotes: $internalNotes, requiresApproval: $requiresApproval, isApproved: $isApproved, approvedBy: $approvedBy, approvedAt: $approvedAt, createdAt: $createdAt, processedAt: $processedAt)';
}


}

/// @nodoc
abstract mixin class _$WalletAdjustmentResponseCopyWith<$Res> implements $WalletAdjustmentResponseCopyWith<$Res> {
  factory _$WalletAdjustmentResponseCopyWith(_WalletAdjustmentResponse value, $Res Function(_WalletAdjustmentResponse) _then) = __$WalletAdjustmentResponseCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'user_id') String userId,@JsonKey(name: 'adjustment_amount') String adjustmentAmount,@JsonKey(name: 'adjustment_type') String adjustmentType,@JsonKey(name: 'previous_balance') String previousBalance,@JsonKey(name: 'new_balance') String newBalance,@JsonKey(name: 'admin_user_id') String adminUserId, String reason,@JsonKey(name: 'internal_notes') String? internalNotes,@JsonKey(name: 'requires_approval') bool requiresApproval,@JsonKey(name: 'is_approved') bool isApproved,@JsonKey(name: 'approved_by') String? approvedBy,@JsonKey(name: 'approved_at') DateTime? approvedAt,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'processed_at') DateTime? processedAt
});




}
/// @nodoc
class __$WalletAdjustmentResponseCopyWithImpl<$Res>
    implements _$WalletAdjustmentResponseCopyWith<$Res> {
  __$WalletAdjustmentResponseCopyWithImpl(this._self, this._then);

  final _WalletAdjustmentResponse _self;
  final $Res Function(_WalletAdjustmentResponse) _then;

/// Create a copy of WalletAdjustmentResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? walletId = null,Object? userId = null,Object? adjustmentAmount = null,Object? adjustmentType = null,Object? previousBalance = null,Object? newBalance = null,Object? adminUserId = null,Object? reason = null,Object? internalNotes = freezed,Object? requiresApproval = null,Object? isApproved = null,Object? approvedBy = freezed,Object? approvedAt = freezed,Object? createdAt = null,Object? processedAt = freezed,}) {
  return _then(_WalletAdjustmentResponse(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,adjustmentAmount: null == adjustmentAmount ? _self.adjustmentAmount : adjustmentAmount // ignore: cast_nullable_to_non_nullable
as String,adjustmentType: null == adjustmentType ? _self.adjustmentType : adjustmentType // ignore: cast_nullable_to_non_nullable
as String,previousBalance: null == previousBalance ? _self.previousBalance : previousBalance // ignore: cast_nullable_to_non_nullable
as String,newBalance: null == newBalance ? _self.newBalance : newBalance // ignore: cast_nullable_to_non_nullable
as String,adminUserId: null == adminUserId ? _self.adminUserId : adminUserId // ignore: cast_nullable_to_non_nullable
as String,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,internalNotes: freezed == internalNotes ? _self.internalNotes : internalNotes // ignore: cast_nullable_to_non_nullable
as String?,requiresApproval: null == requiresApproval ? _self.requiresApproval : requiresApproval // ignore: cast_nullable_to_non_nullable
as bool,isApproved: null == isApproved ? _self.isApproved : isApproved // ignore: cast_nullable_to_non_nullable
as bool,approvedBy: freezed == approvedBy ? _self.approvedBy : approvedBy // ignore: cast_nullable_to_non_nullable
as String?,approvedAt: freezed == approvedAt ? _self.approvedAt : approvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,processedAt: freezed == processedAt ? _self.processedAt : processedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$WalletOverview {

@JsonKey(name: 'wallet_id') String get walletId;@JsonKey(name: 'user_id') String get userId;@JsonKey(name: 'user_email') String get userEmail; String get balance; String get currency;@JsonKey(name: 'is_active') bool get isActive;@JsonKey(name: 'is_locked') bool get isLocked;@JsonKey(name: 'verification_level') int get verificationLevel;@JsonKey(name: 'verification_status') String get verificationStatus;@JsonKey(name: 'daily_limit') String get dailyLimit;@JsonKey(name: 'monthly_limit') String get monthlyLimit;@JsonKey(name: 'can_deposit') bool get canDeposit;@JsonKey(name: 'can_withdraw') bool get canWithdraw;@JsonKey(name: 'can_transfer') bool get canTransfer;@JsonKey(name: 'total_transactions') int get totalTransactions;@JsonKey(name: 'total_deposits') String get totalDeposits;@JsonKey(name: 'total_withdrawals') String get totalWithdrawals;@JsonKey(name: 'last_activity') DateTime? get lastActivity;@JsonKey(name: 'suspicious_activity_count') int get suspiciousActivityCount;@JsonKey(name: 'is_flagged') bool get isFlagged;@JsonKey(name: 'created_at') DateTime get createdAt;
/// Create a copy of WalletOverview
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletOverviewCopyWith<WalletOverview> get copyWith => _$WalletOverviewCopyWithImpl<WalletOverview>(this as WalletOverview, _$identity);

  /// Serializes this WalletOverview to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletOverview&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userEmail, userEmail) || other.userEmail == userEmail)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isLocked, isLocked) || other.isLocked == isLocked)&&(identical(other.verificationLevel, verificationLevel) || other.verificationLevel == verificationLevel)&&(identical(other.verificationStatus, verificationStatus) || other.verificationStatus == verificationStatus)&&(identical(other.dailyLimit, dailyLimit) || other.dailyLimit == dailyLimit)&&(identical(other.monthlyLimit, monthlyLimit) || other.monthlyLimit == monthlyLimit)&&(identical(other.canDeposit, canDeposit) || other.canDeposit == canDeposit)&&(identical(other.canWithdraw, canWithdraw) || other.canWithdraw == canWithdraw)&&(identical(other.canTransfer, canTransfer) || other.canTransfer == canTransfer)&&(identical(other.totalTransactions, totalTransactions) || other.totalTransactions == totalTransactions)&&(identical(other.totalDeposits, totalDeposits) || other.totalDeposits == totalDeposits)&&(identical(other.totalWithdrawals, totalWithdrawals) || other.totalWithdrawals == totalWithdrawals)&&(identical(other.lastActivity, lastActivity) || other.lastActivity == lastActivity)&&(identical(other.suspiciousActivityCount, suspiciousActivityCount) || other.suspiciousActivityCount == suspiciousActivityCount)&&(identical(other.isFlagged, isFlagged) || other.isFlagged == isFlagged)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,walletId,userId,userEmail,balance,currency,isActive,isLocked,verificationLevel,verificationStatus,dailyLimit,monthlyLimit,canDeposit,canWithdraw,canTransfer,totalTransactions,totalDeposits,totalWithdrawals,lastActivity,suspiciousActivityCount,isFlagged,createdAt]);

@override
String toString() {
  return 'WalletOverview(walletId: $walletId, userId: $userId, userEmail: $userEmail, balance: $balance, currency: $currency, isActive: $isActive, isLocked: $isLocked, verificationLevel: $verificationLevel, verificationStatus: $verificationStatus, dailyLimit: $dailyLimit, monthlyLimit: $monthlyLimit, canDeposit: $canDeposit, canWithdraw: $canWithdraw, canTransfer: $canTransfer, totalTransactions: $totalTransactions, totalDeposits: $totalDeposits, totalWithdrawals: $totalWithdrawals, lastActivity: $lastActivity, suspiciousActivityCount: $suspiciousActivityCount, isFlagged: $isFlagged, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $WalletOverviewCopyWith<$Res>  {
  factory $WalletOverviewCopyWith(WalletOverview value, $Res Function(WalletOverview) _then) = _$WalletOverviewCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'user_id') String userId,@JsonKey(name: 'user_email') String userEmail, String balance, String currency,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'is_locked') bool isLocked,@JsonKey(name: 'verification_level') int verificationLevel,@JsonKey(name: 'verification_status') String verificationStatus,@JsonKey(name: 'daily_limit') String dailyLimit,@JsonKey(name: 'monthly_limit') String monthlyLimit,@JsonKey(name: 'can_deposit') bool canDeposit,@JsonKey(name: 'can_withdraw') bool canWithdraw,@JsonKey(name: 'can_transfer') bool canTransfer,@JsonKey(name: 'total_transactions') int totalTransactions,@JsonKey(name: 'total_deposits') String totalDeposits,@JsonKey(name: 'total_withdrawals') String totalWithdrawals,@JsonKey(name: 'last_activity') DateTime? lastActivity,@JsonKey(name: 'suspicious_activity_count') int suspiciousActivityCount,@JsonKey(name: 'is_flagged') bool isFlagged,@JsonKey(name: 'created_at') DateTime createdAt
});




}
/// @nodoc
class _$WalletOverviewCopyWithImpl<$Res>
    implements $WalletOverviewCopyWith<$Res> {
  _$WalletOverviewCopyWithImpl(this._self, this._then);

  final WalletOverview _self;
  final $Res Function(WalletOverview) _then;

/// Create a copy of WalletOverview
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? walletId = null,Object? userId = null,Object? userEmail = null,Object? balance = null,Object? currency = null,Object? isActive = null,Object? isLocked = null,Object? verificationLevel = null,Object? verificationStatus = null,Object? dailyLimit = null,Object? monthlyLimit = null,Object? canDeposit = null,Object? canWithdraw = null,Object? canTransfer = null,Object? totalTransactions = null,Object? totalDeposits = null,Object? totalWithdrawals = null,Object? lastActivity = freezed,Object? suspiciousActivityCount = null,Object? isFlagged = null,Object? createdAt = null,}) {
  return _then(_self.copyWith(
walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userEmail: null == userEmail ? _self.userEmail : userEmail // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isLocked: null == isLocked ? _self.isLocked : isLocked // ignore: cast_nullable_to_non_nullable
as bool,verificationLevel: null == verificationLevel ? _self.verificationLevel : verificationLevel // ignore: cast_nullable_to_non_nullable
as int,verificationStatus: null == verificationStatus ? _self.verificationStatus : verificationStatus // ignore: cast_nullable_to_non_nullable
as String,dailyLimit: null == dailyLimit ? _self.dailyLimit : dailyLimit // ignore: cast_nullable_to_non_nullable
as String,monthlyLimit: null == monthlyLimit ? _self.monthlyLimit : monthlyLimit // ignore: cast_nullable_to_non_nullable
as String,canDeposit: null == canDeposit ? _self.canDeposit : canDeposit // ignore: cast_nullable_to_non_nullable
as bool,canWithdraw: null == canWithdraw ? _self.canWithdraw : canWithdraw // ignore: cast_nullable_to_non_nullable
as bool,canTransfer: null == canTransfer ? _self.canTransfer : canTransfer // ignore: cast_nullable_to_non_nullable
as bool,totalTransactions: null == totalTransactions ? _self.totalTransactions : totalTransactions // ignore: cast_nullable_to_non_nullable
as int,totalDeposits: null == totalDeposits ? _self.totalDeposits : totalDeposits // ignore: cast_nullable_to_non_nullable
as String,totalWithdrawals: null == totalWithdrawals ? _self.totalWithdrawals : totalWithdrawals // ignore: cast_nullable_to_non_nullable
as String,lastActivity: freezed == lastActivity ? _self.lastActivity : lastActivity // ignore: cast_nullable_to_non_nullable
as DateTime?,suspiciousActivityCount: null == suspiciousActivityCount ? _self.suspiciousActivityCount : suspiciousActivityCount // ignore: cast_nullable_to_non_nullable
as int,isFlagged: null == isFlagged ? _self.isFlagged : isFlagged // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletOverview].
extension WalletOverviewPatterns on WalletOverview {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletOverview value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletOverview() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletOverview value)  $default,){
final _that = this;
switch (_that) {
case _WalletOverview():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletOverview value)?  $default,){
final _that = this;
switch (_that) {
case _WalletOverview() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId, @JsonKey(name: 'user_email')  String userEmail,  String balance,  String currency, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'is_locked')  bool isLocked, @JsonKey(name: 'verification_level')  int verificationLevel, @JsonKey(name: 'verification_status')  String verificationStatus, @JsonKey(name: 'daily_limit')  String dailyLimit, @JsonKey(name: 'monthly_limit')  String monthlyLimit, @JsonKey(name: 'can_deposit')  bool canDeposit, @JsonKey(name: 'can_withdraw')  bool canWithdraw, @JsonKey(name: 'can_transfer')  bool canTransfer, @JsonKey(name: 'total_transactions')  int totalTransactions, @JsonKey(name: 'total_deposits')  String totalDeposits, @JsonKey(name: 'total_withdrawals')  String totalWithdrawals, @JsonKey(name: 'last_activity')  DateTime? lastActivity, @JsonKey(name: 'suspicious_activity_count')  int suspiciousActivityCount, @JsonKey(name: 'is_flagged')  bool isFlagged, @JsonKey(name: 'created_at')  DateTime createdAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletOverview() when $default != null:
return $default(_that.walletId,_that.userId,_that.userEmail,_that.balance,_that.currency,_that.isActive,_that.isLocked,_that.verificationLevel,_that.verificationStatus,_that.dailyLimit,_that.monthlyLimit,_that.canDeposit,_that.canWithdraw,_that.canTransfer,_that.totalTransactions,_that.totalDeposits,_that.totalWithdrawals,_that.lastActivity,_that.suspiciousActivityCount,_that.isFlagged,_that.createdAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId, @JsonKey(name: 'user_email')  String userEmail,  String balance,  String currency, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'is_locked')  bool isLocked, @JsonKey(name: 'verification_level')  int verificationLevel, @JsonKey(name: 'verification_status')  String verificationStatus, @JsonKey(name: 'daily_limit')  String dailyLimit, @JsonKey(name: 'monthly_limit')  String monthlyLimit, @JsonKey(name: 'can_deposit')  bool canDeposit, @JsonKey(name: 'can_withdraw')  bool canWithdraw, @JsonKey(name: 'can_transfer')  bool canTransfer, @JsonKey(name: 'total_transactions')  int totalTransactions, @JsonKey(name: 'total_deposits')  String totalDeposits, @JsonKey(name: 'total_withdrawals')  String totalWithdrawals, @JsonKey(name: 'last_activity')  DateTime? lastActivity, @JsonKey(name: 'suspicious_activity_count')  int suspiciousActivityCount, @JsonKey(name: 'is_flagged')  bool isFlagged, @JsonKey(name: 'created_at')  DateTime createdAt)  $default,) {final _that = this;
switch (_that) {
case _WalletOverview():
return $default(_that.walletId,_that.userId,_that.userEmail,_that.balance,_that.currency,_that.isActive,_that.isLocked,_that.verificationLevel,_that.verificationStatus,_that.dailyLimit,_that.monthlyLimit,_that.canDeposit,_that.canWithdraw,_that.canTransfer,_that.totalTransactions,_that.totalDeposits,_that.totalWithdrawals,_that.lastActivity,_that.suspiciousActivityCount,_that.isFlagged,_that.createdAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_id')  String userId, @JsonKey(name: 'user_email')  String userEmail,  String balance,  String currency, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'is_locked')  bool isLocked, @JsonKey(name: 'verification_level')  int verificationLevel, @JsonKey(name: 'verification_status')  String verificationStatus, @JsonKey(name: 'daily_limit')  String dailyLimit, @JsonKey(name: 'monthly_limit')  String monthlyLimit, @JsonKey(name: 'can_deposit')  bool canDeposit, @JsonKey(name: 'can_withdraw')  bool canWithdraw, @JsonKey(name: 'can_transfer')  bool canTransfer, @JsonKey(name: 'total_transactions')  int totalTransactions, @JsonKey(name: 'total_deposits')  String totalDeposits, @JsonKey(name: 'total_withdrawals')  String totalWithdrawals, @JsonKey(name: 'last_activity')  DateTime? lastActivity, @JsonKey(name: 'suspicious_activity_count')  int suspiciousActivityCount, @JsonKey(name: 'is_flagged')  bool isFlagged, @JsonKey(name: 'created_at')  DateTime createdAt)?  $default,) {final _that = this;
switch (_that) {
case _WalletOverview() when $default != null:
return $default(_that.walletId,_that.userId,_that.userEmail,_that.balance,_that.currency,_that.isActive,_that.isLocked,_that.verificationLevel,_that.verificationStatus,_that.dailyLimit,_that.monthlyLimit,_that.canDeposit,_that.canWithdraw,_that.canTransfer,_that.totalTransactions,_that.totalDeposits,_that.totalWithdrawals,_that.lastActivity,_that.suspiciousActivityCount,_that.isFlagged,_that.createdAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletOverview implements WalletOverview {
  const _WalletOverview({@JsonKey(name: 'wallet_id') required this.walletId, @JsonKey(name: 'user_id') required this.userId, @JsonKey(name: 'user_email') required this.userEmail, required this.balance, required this.currency, @JsonKey(name: 'is_active') required this.isActive, @JsonKey(name: 'is_locked') required this.isLocked, @JsonKey(name: 'verification_level') required this.verificationLevel, @JsonKey(name: 'verification_status') required this.verificationStatus, @JsonKey(name: 'daily_limit') required this.dailyLimit, @JsonKey(name: 'monthly_limit') required this.monthlyLimit, @JsonKey(name: 'can_deposit') required this.canDeposit, @JsonKey(name: 'can_withdraw') required this.canWithdraw, @JsonKey(name: 'can_transfer') required this.canTransfer, @JsonKey(name: 'total_transactions') required this.totalTransactions, @JsonKey(name: 'total_deposits') required this.totalDeposits, @JsonKey(name: 'total_withdrawals') required this.totalWithdrawals, @JsonKey(name: 'last_activity') this.lastActivity, @JsonKey(name: 'suspicious_activity_count') required this.suspiciousActivityCount, @JsonKey(name: 'is_flagged') required this.isFlagged, @JsonKey(name: 'created_at') required this.createdAt});
  factory _WalletOverview.fromJson(Map<String, dynamic> json) => _$WalletOverviewFromJson(json);

@override@JsonKey(name: 'wallet_id') final  String walletId;
@override@JsonKey(name: 'user_id') final  String userId;
@override@JsonKey(name: 'user_email') final  String userEmail;
@override final  String balance;
@override final  String currency;
@override@JsonKey(name: 'is_active') final  bool isActive;
@override@JsonKey(name: 'is_locked') final  bool isLocked;
@override@JsonKey(name: 'verification_level') final  int verificationLevel;
@override@JsonKey(name: 'verification_status') final  String verificationStatus;
@override@JsonKey(name: 'daily_limit') final  String dailyLimit;
@override@JsonKey(name: 'monthly_limit') final  String monthlyLimit;
@override@JsonKey(name: 'can_deposit') final  bool canDeposit;
@override@JsonKey(name: 'can_withdraw') final  bool canWithdraw;
@override@JsonKey(name: 'can_transfer') final  bool canTransfer;
@override@JsonKey(name: 'total_transactions') final  int totalTransactions;
@override@JsonKey(name: 'total_deposits') final  String totalDeposits;
@override@JsonKey(name: 'total_withdrawals') final  String totalWithdrawals;
@override@JsonKey(name: 'last_activity') final  DateTime? lastActivity;
@override@JsonKey(name: 'suspicious_activity_count') final  int suspiciousActivityCount;
@override@JsonKey(name: 'is_flagged') final  bool isFlagged;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;

/// Create a copy of WalletOverview
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletOverviewCopyWith<_WalletOverview> get copyWith => __$WalletOverviewCopyWithImpl<_WalletOverview>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletOverviewToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletOverview&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userEmail, userEmail) || other.userEmail == userEmail)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isLocked, isLocked) || other.isLocked == isLocked)&&(identical(other.verificationLevel, verificationLevel) || other.verificationLevel == verificationLevel)&&(identical(other.verificationStatus, verificationStatus) || other.verificationStatus == verificationStatus)&&(identical(other.dailyLimit, dailyLimit) || other.dailyLimit == dailyLimit)&&(identical(other.monthlyLimit, monthlyLimit) || other.monthlyLimit == monthlyLimit)&&(identical(other.canDeposit, canDeposit) || other.canDeposit == canDeposit)&&(identical(other.canWithdraw, canWithdraw) || other.canWithdraw == canWithdraw)&&(identical(other.canTransfer, canTransfer) || other.canTransfer == canTransfer)&&(identical(other.totalTransactions, totalTransactions) || other.totalTransactions == totalTransactions)&&(identical(other.totalDeposits, totalDeposits) || other.totalDeposits == totalDeposits)&&(identical(other.totalWithdrawals, totalWithdrawals) || other.totalWithdrawals == totalWithdrawals)&&(identical(other.lastActivity, lastActivity) || other.lastActivity == lastActivity)&&(identical(other.suspiciousActivityCount, suspiciousActivityCount) || other.suspiciousActivityCount == suspiciousActivityCount)&&(identical(other.isFlagged, isFlagged) || other.isFlagged == isFlagged)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,walletId,userId,userEmail,balance,currency,isActive,isLocked,verificationLevel,verificationStatus,dailyLimit,monthlyLimit,canDeposit,canWithdraw,canTransfer,totalTransactions,totalDeposits,totalWithdrawals,lastActivity,suspiciousActivityCount,isFlagged,createdAt]);

@override
String toString() {
  return 'WalletOverview(walletId: $walletId, userId: $userId, userEmail: $userEmail, balance: $balance, currency: $currency, isActive: $isActive, isLocked: $isLocked, verificationLevel: $verificationLevel, verificationStatus: $verificationStatus, dailyLimit: $dailyLimit, monthlyLimit: $monthlyLimit, canDeposit: $canDeposit, canWithdraw: $canWithdraw, canTransfer: $canTransfer, totalTransactions: $totalTransactions, totalDeposits: $totalDeposits, totalWithdrawals: $totalWithdrawals, lastActivity: $lastActivity, suspiciousActivityCount: $suspiciousActivityCount, isFlagged: $isFlagged, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$WalletOverviewCopyWith<$Res> implements $WalletOverviewCopyWith<$Res> {
  factory _$WalletOverviewCopyWith(_WalletOverview value, $Res Function(_WalletOverview) _then) = __$WalletOverviewCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'user_id') String userId,@JsonKey(name: 'user_email') String userEmail, String balance, String currency,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'is_locked') bool isLocked,@JsonKey(name: 'verification_level') int verificationLevel,@JsonKey(name: 'verification_status') String verificationStatus,@JsonKey(name: 'daily_limit') String dailyLimit,@JsonKey(name: 'monthly_limit') String monthlyLimit,@JsonKey(name: 'can_deposit') bool canDeposit,@JsonKey(name: 'can_withdraw') bool canWithdraw,@JsonKey(name: 'can_transfer') bool canTransfer,@JsonKey(name: 'total_transactions') int totalTransactions,@JsonKey(name: 'total_deposits') String totalDeposits,@JsonKey(name: 'total_withdrawals') String totalWithdrawals,@JsonKey(name: 'last_activity') DateTime? lastActivity,@JsonKey(name: 'suspicious_activity_count') int suspiciousActivityCount,@JsonKey(name: 'is_flagged') bool isFlagged,@JsonKey(name: 'created_at') DateTime createdAt
});




}
/// @nodoc
class __$WalletOverviewCopyWithImpl<$Res>
    implements _$WalletOverviewCopyWith<$Res> {
  __$WalletOverviewCopyWithImpl(this._self, this._then);

  final _WalletOverview _self;
  final $Res Function(_WalletOverview) _then;

/// Create a copy of WalletOverview
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? walletId = null,Object? userId = null,Object? userEmail = null,Object? balance = null,Object? currency = null,Object? isActive = null,Object? isLocked = null,Object? verificationLevel = null,Object? verificationStatus = null,Object? dailyLimit = null,Object? monthlyLimit = null,Object? canDeposit = null,Object? canWithdraw = null,Object? canTransfer = null,Object? totalTransactions = null,Object? totalDeposits = null,Object? totalWithdrawals = null,Object? lastActivity = freezed,Object? suspiciousActivityCount = null,Object? isFlagged = null,Object? createdAt = null,}) {
  return _then(_WalletOverview(
walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userEmail: null == userEmail ? _self.userEmail : userEmail // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isLocked: null == isLocked ? _self.isLocked : isLocked // ignore: cast_nullable_to_non_nullable
as bool,verificationLevel: null == verificationLevel ? _self.verificationLevel : verificationLevel // ignore: cast_nullable_to_non_nullable
as int,verificationStatus: null == verificationStatus ? _self.verificationStatus : verificationStatus // ignore: cast_nullable_to_non_nullable
as String,dailyLimit: null == dailyLimit ? _self.dailyLimit : dailyLimit // ignore: cast_nullable_to_non_nullable
as String,monthlyLimit: null == monthlyLimit ? _self.monthlyLimit : monthlyLimit // ignore: cast_nullable_to_non_nullable
as String,canDeposit: null == canDeposit ? _self.canDeposit : canDeposit // ignore: cast_nullable_to_non_nullable
as bool,canWithdraw: null == canWithdraw ? _self.canWithdraw : canWithdraw // ignore: cast_nullable_to_non_nullable
as bool,canTransfer: null == canTransfer ? _self.canTransfer : canTransfer // ignore: cast_nullable_to_non_nullable
as bool,totalTransactions: null == totalTransactions ? _self.totalTransactions : totalTransactions // ignore: cast_nullable_to_non_nullable
as int,totalDeposits: null == totalDeposits ? _self.totalDeposits : totalDeposits // ignore: cast_nullable_to_non_nullable
as String,totalWithdrawals: null == totalWithdrawals ? _self.totalWithdrawals : totalWithdrawals // ignore: cast_nullable_to_non_nullable
as String,lastActivity: freezed == lastActivity ? _self.lastActivity : lastActivity // ignore: cast_nullable_to_non_nullable
as DateTime?,suspiciousActivityCount: null == suspiciousActivityCount ? _self.suspiciousActivityCount : suspiciousActivityCount // ignore: cast_nullable_to_non_nullable
as int,isFlagged: null == isFlagged ? _self.isFlagged : isFlagged // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$CreateAlertRequest {

@JsonKey(name: 'alert_type') String get alertType; AlertSeverity get severity; String get title; String get description;@JsonKey(name: 'user_id') String? get userId;@JsonKey(name: 'wallet_id') String? get walletId;@JsonKey(name: 'transaction_id') String? get transactionId;@JsonKey(name: 'alert_data') Map<String, dynamic>? get alertData;@JsonKey(name: 'threshold_value') String? get thresholdValue;@JsonKey(name: 'actual_value') String? get actualValue;
/// Create a copy of CreateAlertRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateAlertRequestCopyWith<CreateAlertRequest> get copyWith => _$CreateAlertRequestCopyWithImpl<CreateAlertRequest>(this as CreateAlertRequest, _$identity);

  /// Serializes this CreateAlertRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateAlertRequest&&(identical(other.alertType, alertType) || other.alertType == alertType)&&(identical(other.severity, severity) || other.severity == severity)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&const DeepCollectionEquality().equals(other.alertData, alertData)&&(identical(other.thresholdValue, thresholdValue) || other.thresholdValue == thresholdValue)&&(identical(other.actualValue, actualValue) || other.actualValue == actualValue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,alertType,severity,title,description,userId,walletId,transactionId,const DeepCollectionEquality().hash(alertData),thresholdValue,actualValue);

@override
String toString() {
  return 'CreateAlertRequest(alertType: $alertType, severity: $severity, title: $title, description: $description, userId: $userId, walletId: $walletId, transactionId: $transactionId, alertData: $alertData, thresholdValue: $thresholdValue, actualValue: $actualValue)';
}


}

/// @nodoc
abstract mixin class $CreateAlertRequestCopyWith<$Res>  {
  factory $CreateAlertRequestCopyWith(CreateAlertRequest value, $Res Function(CreateAlertRequest) _then) = _$CreateAlertRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'alert_type') String alertType, AlertSeverity severity, String title, String description,@JsonKey(name: 'user_id') String? userId,@JsonKey(name: 'wallet_id') String? walletId,@JsonKey(name: 'transaction_id') String? transactionId,@JsonKey(name: 'alert_data') Map<String, dynamic>? alertData,@JsonKey(name: 'threshold_value') String? thresholdValue,@JsonKey(name: 'actual_value') String? actualValue
});




}
/// @nodoc
class _$CreateAlertRequestCopyWithImpl<$Res>
    implements $CreateAlertRequestCopyWith<$Res> {
  _$CreateAlertRequestCopyWithImpl(this._self, this._then);

  final CreateAlertRequest _self;
  final $Res Function(CreateAlertRequest) _then;

/// Create a copy of CreateAlertRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? alertType = null,Object? severity = null,Object? title = null,Object? description = null,Object? userId = freezed,Object? walletId = freezed,Object? transactionId = freezed,Object? alertData = freezed,Object? thresholdValue = freezed,Object? actualValue = freezed,}) {
  return _then(_self.copyWith(
alertType: null == alertType ? _self.alertType : alertType // ignore: cast_nullable_to_non_nullable
as String,severity: null == severity ? _self.severity : severity // ignore: cast_nullable_to_non_nullable
as AlertSeverity,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,walletId: freezed == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,alertData: freezed == alertData ? _self.alertData : alertData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,thresholdValue: freezed == thresholdValue ? _self.thresholdValue : thresholdValue // ignore: cast_nullable_to_non_nullable
as String?,actualValue: freezed == actualValue ? _self.actualValue : actualValue // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [CreateAlertRequest].
extension CreateAlertRequestPatterns on CreateAlertRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CreateAlertRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CreateAlertRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CreateAlertRequest value)  $default,){
final _that = this;
switch (_that) {
case _CreateAlertRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CreateAlertRequest value)?  $default,){
final _that = this;
switch (_that) {
case _CreateAlertRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'alert_type')  String alertType,  AlertSeverity severity,  String title,  String description, @JsonKey(name: 'user_id')  String? userId, @JsonKey(name: 'wallet_id')  String? walletId, @JsonKey(name: 'transaction_id')  String? transactionId, @JsonKey(name: 'alert_data')  Map<String, dynamic>? alertData, @JsonKey(name: 'threshold_value')  String? thresholdValue, @JsonKey(name: 'actual_value')  String? actualValue)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CreateAlertRequest() when $default != null:
return $default(_that.alertType,_that.severity,_that.title,_that.description,_that.userId,_that.walletId,_that.transactionId,_that.alertData,_that.thresholdValue,_that.actualValue);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'alert_type')  String alertType,  AlertSeverity severity,  String title,  String description, @JsonKey(name: 'user_id')  String? userId, @JsonKey(name: 'wallet_id')  String? walletId, @JsonKey(name: 'transaction_id')  String? transactionId, @JsonKey(name: 'alert_data')  Map<String, dynamic>? alertData, @JsonKey(name: 'threshold_value')  String? thresholdValue, @JsonKey(name: 'actual_value')  String? actualValue)  $default,) {final _that = this;
switch (_that) {
case _CreateAlertRequest():
return $default(_that.alertType,_that.severity,_that.title,_that.description,_that.userId,_that.walletId,_that.transactionId,_that.alertData,_that.thresholdValue,_that.actualValue);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'alert_type')  String alertType,  AlertSeverity severity,  String title,  String description, @JsonKey(name: 'user_id')  String? userId, @JsonKey(name: 'wallet_id')  String? walletId, @JsonKey(name: 'transaction_id')  String? transactionId, @JsonKey(name: 'alert_data')  Map<String, dynamic>? alertData, @JsonKey(name: 'threshold_value')  String? thresholdValue, @JsonKey(name: 'actual_value')  String? actualValue)?  $default,) {final _that = this;
switch (_that) {
case _CreateAlertRequest() when $default != null:
return $default(_that.alertType,_that.severity,_that.title,_that.description,_that.userId,_that.walletId,_that.transactionId,_that.alertData,_that.thresholdValue,_that.actualValue);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CreateAlertRequest implements CreateAlertRequest {
  const _CreateAlertRequest({@JsonKey(name: 'alert_type') required this.alertType, required this.severity, required this.title, required this.description, @JsonKey(name: 'user_id') this.userId, @JsonKey(name: 'wallet_id') this.walletId, @JsonKey(name: 'transaction_id') this.transactionId, @JsonKey(name: 'alert_data') final  Map<String, dynamic>? alertData, @JsonKey(name: 'threshold_value') this.thresholdValue, @JsonKey(name: 'actual_value') this.actualValue}): _alertData = alertData;
  factory _CreateAlertRequest.fromJson(Map<String, dynamic> json) => _$CreateAlertRequestFromJson(json);

@override@JsonKey(name: 'alert_type') final  String alertType;
@override final  AlertSeverity severity;
@override final  String title;
@override final  String description;
@override@JsonKey(name: 'user_id') final  String? userId;
@override@JsonKey(name: 'wallet_id') final  String? walletId;
@override@JsonKey(name: 'transaction_id') final  String? transactionId;
 final  Map<String, dynamic>? _alertData;
@override@JsonKey(name: 'alert_data') Map<String, dynamic>? get alertData {
  final value = _alertData;
  if (value == null) return null;
  if (_alertData is EqualUnmodifiableMapView) return _alertData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(name: 'threshold_value') final  String? thresholdValue;
@override@JsonKey(name: 'actual_value') final  String? actualValue;

/// Create a copy of CreateAlertRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreateAlertRequestCopyWith<_CreateAlertRequest> get copyWith => __$CreateAlertRequestCopyWithImpl<_CreateAlertRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CreateAlertRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CreateAlertRequest&&(identical(other.alertType, alertType) || other.alertType == alertType)&&(identical(other.severity, severity) || other.severity == severity)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&const DeepCollectionEquality().equals(other._alertData, _alertData)&&(identical(other.thresholdValue, thresholdValue) || other.thresholdValue == thresholdValue)&&(identical(other.actualValue, actualValue) || other.actualValue == actualValue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,alertType,severity,title,description,userId,walletId,transactionId,const DeepCollectionEquality().hash(_alertData),thresholdValue,actualValue);

@override
String toString() {
  return 'CreateAlertRequest(alertType: $alertType, severity: $severity, title: $title, description: $description, userId: $userId, walletId: $walletId, transactionId: $transactionId, alertData: $alertData, thresholdValue: $thresholdValue, actualValue: $actualValue)';
}


}

/// @nodoc
abstract mixin class _$CreateAlertRequestCopyWith<$Res> implements $CreateAlertRequestCopyWith<$Res> {
  factory _$CreateAlertRequestCopyWith(_CreateAlertRequest value, $Res Function(_CreateAlertRequest) _then) = __$CreateAlertRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'alert_type') String alertType, AlertSeverity severity, String title, String description,@JsonKey(name: 'user_id') String? userId,@JsonKey(name: 'wallet_id') String? walletId,@JsonKey(name: 'transaction_id') String? transactionId,@JsonKey(name: 'alert_data') Map<String, dynamic>? alertData,@JsonKey(name: 'threshold_value') String? thresholdValue,@JsonKey(name: 'actual_value') String? actualValue
});




}
/// @nodoc
class __$CreateAlertRequestCopyWithImpl<$Res>
    implements _$CreateAlertRequestCopyWith<$Res> {
  __$CreateAlertRequestCopyWithImpl(this._self, this._then);

  final _CreateAlertRequest _self;
  final $Res Function(_CreateAlertRequest) _then;

/// Create a copy of CreateAlertRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? alertType = null,Object? severity = null,Object? title = null,Object? description = null,Object? userId = freezed,Object? walletId = freezed,Object? transactionId = freezed,Object? alertData = freezed,Object? thresholdValue = freezed,Object? actualValue = freezed,}) {
  return _then(_CreateAlertRequest(
alertType: null == alertType ? _self.alertType : alertType // ignore: cast_nullable_to_non_nullable
as String,severity: null == severity ? _self.severity : severity // ignore: cast_nullable_to_non_nullable
as AlertSeverity,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,walletId: freezed == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,alertData: freezed == alertData ? _self._alertData : alertData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,thresholdValue: freezed == thresholdValue ? _self.thresholdValue : thresholdValue // ignore: cast_nullable_to_non_nullable
as String?,actualValue: freezed == actualValue ? _self.actualValue : actualValue // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$FinancialAlert {

 String get id;@JsonKey(name: 'alert_type') String get alertType; AlertSeverity get severity; String get title; String get description;@JsonKey(name: 'user_id') String? get userId;@JsonKey(name: 'wallet_id') String? get walletId;@JsonKey(name: 'transaction_id') String? get transactionId;@JsonKey(name: 'alert_data') Map<String, dynamic>? get alertData;@JsonKey(name: 'threshold_value') String? get thresholdValue;@JsonKey(name: 'actual_value') String? get actualValue;@JsonKey(name: 'is_resolved') bool get isResolved;@JsonKey(name: 'resolved_by') String? get resolvedBy;@JsonKey(name: 'resolution_notes') String? get resolutionNotes;@JsonKey(name: 'resolved_at') DateTime? get resolvedAt;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime get updatedAt;
/// Create a copy of FinancialAlert
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FinancialAlertCopyWith<FinancialAlert> get copyWith => _$FinancialAlertCopyWithImpl<FinancialAlert>(this as FinancialAlert, _$identity);

  /// Serializes this FinancialAlert to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FinancialAlert&&(identical(other.id, id) || other.id == id)&&(identical(other.alertType, alertType) || other.alertType == alertType)&&(identical(other.severity, severity) || other.severity == severity)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&const DeepCollectionEquality().equals(other.alertData, alertData)&&(identical(other.thresholdValue, thresholdValue) || other.thresholdValue == thresholdValue)&&(identical(other.actualValue, actualValue) || other.actualValue == actualValue)&&(identical(other.isResolved, isResolved) || other.isResolved == isResolved)&&(identical(other.resolvedBy, resolvedBy) || other.resolvedBy == resolvedBy)&&(identical(other.resolutionNotes, resolutionNotes) || other.resolutionNotes == resolutionNotes)&&(identical(other.resolvedAt, resolvedAt) || other.resolvedAt == resolvedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,alertType,severity,title,description,userId,walletId,transactionId,const DeepCollectionEquality().hash(alertData),thresholdValue,actualValue,isResolved,resolvedBy,resolutionNotes,resolvedAt,createdAt,updatedAt);

@override
String toString() {
  return 'FinancialAlert(id: $id, alertType: $alertType, severity: $severity, title: $title, description: $description, userId: $userId, walletId: $walletId, transactionId: $transactionId, alertData: $alertData, thresholdValue: $thresholdValue, actualValue: $actualValue, isResolved: $isResolved, resolvedBy: $resolvedBy, resolutionNotes: $resolutionNotes, resolvedAt: $resolvedAt, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $FinancialAlertCopyWith<$Res>  {
  factory $FinancialAlertCopyWith(FinancialAlert value, $Res Function(FinancialAlert) _then) = _$FinancialAlertCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'alert_type') String alertType, AlertSeverity severity, String title, String description,@JsonKey(name: 'user_id') String? userId,@JsonKey(name: 'wallet_id') String? walletId,@JsonKey(name: 'transaction_id') String? transactionId,@JsonKey(name: 'alert_data') Map<String, dynamic>? alertData,@JsonKey(name: 'threshold_value') String? thresholdValue,@JsonKey(name: 'actual_value') String? actualValue,@JsonKey(name: 'is_resolved') bool isResolved,@JsonKey(name: 'resolved_by') String? resolvedBy,@JsonKey(name: 'resolution_notes') String? resolutionNotes,@JsonKey(name: 'resolved_at') DateTime? resolvedAt,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt
});




}
/// @nodoc
class _$FinancialAlertCopyWithImpl<$Res>
    implements $FinancialAlertCopyWith<$Res> {
  _$FinancialAlertCopyWithImpl(this._self, this._then);

  final FinancialAlert _self;
  final $Res Function(FinancialAlert) _then;

/// Create a copy of FinancialAlert
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? alertType = null,Object? severity = null,Object? title = null,Object? description = null,Object? userId = freezed,Object? walletId = freezed,Object? transactionId = freezed,Object? alertData = freezed,Object? thresholdValue = freezed,Object? actualValue = freezed,Object? isResolved = null,Object? resolvedBy = freezed,Object? resolutionNotes = freezed,Object? resolvedAt = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,alertType: null == alertType ? _self.alertType : alertType // ignore: cast_nullable_to_non_nullable
as String,severity: null == severity ? _self.severity : severity // ignore: cast_nullable_to_non_nullable
as AlertSeverity,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,walletId: freezed == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,alertData: freezed == alertData ? _self.alertData : alertData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,thresholdValue: freezed == thresholdValue ? _self.thresholdValue : thresholdValue // ignore: cast_nullable_to_non_nullable
as String?,actualValue: freezed == actualValue ? _self.actualValue : actualValue // ignore: cast_nullable_to_non_nullable
as String?,isResolved: null == isResolved ? _self.isResolved : isResolved // ignore: cast_nullable_to_non_nullable
as bool,resolvedBy: freezed == resolvedBy ? _self.resolvedBy : resolvedBy // ignore: cast_nullable_to_non_nullable
as String?,resolutionNotes: freezed == resolutionNotes ? _self.resolutionNotes : resolutionNotes // ignore: cast_nullable_to_non_nullable
as String?,resolvedAt: freezed == resolvedAt ? _self.resolvedAt : resolvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [FinancialAlert].
extension FinancialAlertPatterns on FinancialAlert {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FinancialAlert value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FinancialAlert() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FinancialAlert value)  $default,){
final _that = this;
switch (_that) {
case _FinancialAlert():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FinancialAlert value)?  $default,){
final _that = this;
switch (_that) {
case _FinancialAlert() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'alert_type')  String alertType,  AlertSeverity severity,  String title,  String description, @JsonKey(name: 'user_id')  String? userId, @JsonKey(name: 'wallet_id')  String? walletId, @JsonKey(name: 'transaction_id')  String? transactionId, @JsonKey(name: 'alert_data')  Map<String, dynamic>? alertData, @JsonKey(name: 'threshold_value')  String? thresholdValue, @JsonKey(name: 'actual_value')  String? actualValue, @JsonKey(name: 'is_resolved')  bool isResolved, @JsonKey(name: 'resolved_by')  String? resolvedBy, @JsonKey(name: 'resolution_notes')  String? resolutionNotes, @JsonKey(name: 'resolved_at')  DateTime? resolvedAt, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FinancialAlert() when $default != null:
return $default(_that.id,_that.alertType,_that.severity,_that.title,_that.description,_that.userId,_that.walletId,_that.transactionId,_that.alertData,_that.thresholdValue,_that.actualValue,_that.isResolved,_that.resolvedBy,_that.resolutionNotes,_that.resolvedAt,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'alert_type')  String alertType,  AlertSeverity severity,  String title,  String description, @JsonKey(name: 'user_id')  String? userId, @JsonKey(name: 'wallet_id')  String? walletId, @JsonKey(name: 'transaction_id')  String? transactionId, @JsonKey(name: 'alert_data')  Map<String, dynamic>? alertData, @JsonKey(name: 'threshold_value')  String? thresholdValue, @JsonKey(name: 'actual_value')  String? actualValue, @JsonKey(name: 'is_resolved')  bool isResolved, @JsonKey(name: 'resolved_by')  String? resolvedBy, @JsonKey(name: 'resolution_notes')  String? resolutionNotes, @JsonKey(name: 'resolved_at')  DateTime? resolvedAt, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _FinancialAlert():
return $default(_that.id,_that.alertType,_that.severity,_that.title,_that.description,_that.userId,_that.walletId,_that.transactionId,_that.alertData,_that.thresholdValue,_that.actualValue,_that.isResolved,_that.resolvedBy,_that.resolutionNotes,_that.resolvedAt,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'alert_type')  String alertType,  AlertSeverity severity,  String title,  String description, @JsonKey(name: 'user_id')  String? userId, @JsonKey(name: 'wallet_id')  String? walletId, @JsonKey(name: 'transaction_id')  String? transactionId, @JsonKey(name: 'alert_data')  Map<String, dynamic>? alertData, @JsonKey(name: 'threshold_value')  String? thresholdValue, @JsonKey(name: 'actual_value')  String? actualValue, @JsonKey(name: 'is_resolved')  bool isResolved, @JsonKey(name: 'resolved_by')  String? resolvedBy, @JsonKey(name: 'resolution_notes')  String? resolutionNotes, @JsonKey(name: 'resolved_at')  DateTime? resolvedAt, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _FinancialAlert() when $default != null:
return $default(_that.id,_that.alertType,_that.severity,_that.title,_that.description,_that.userId,_that.walletId,_that.transactionId,_that.alertData,_that.thresholdValue,_that.actualValue,_that.isResolved,_that.resolvedBy,_that.resolutionNotes,_that.resolvedAt,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FinancialAlert implements FinancialAlert {
  const _FinancialAlert({required this.id, @JsonKey(name: 'alert_type') required this.alertType, required this.severity, required this.title, required this.description, @JsonKey(name: 'user_id') this.userId, @JsonKey(name: 'wallet_id') this.walletId, @JsonKey(name: 'transaction_id') this.transactionId, @JsonKey(name: 'alert_data') final  Map<String, dynamic>? alertData, @JsonKey(name: 'threshold_value') this.thresholdValue, @JsonKey(name: 'actual_value') this.actualValue, @JsonKey(name: 'is_resolved') required this.isResolved, @JsonKey(name: 'resolved_by') this.resolvedBy, @JsonKey(name: 'resolution_notes') this.resolutionNotes, @JsonKey(name: 'resolved_at') this.resolvedAt, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt}): _alertData = alertData;
  factory _FinancialAlert.fromJson(Map<String, dynamic> json) => _$FinancialAlertFromJson(json);

@override final  String id;
@override@JsonKey(name: 'alert_type') final  String alertType;
@override final  AlertSeverity severity;
@override final  String title;
@override final  String description;
@override@JsonKey(name: 'user_id') final  String? userId;
@override@JsonKey(name: 'wallet_id') final  String? walletId;
@override@JsonKey(name: 'transaction_id') final  String? transactionId;
 final  Map<String, dynamic>? _alertData;
@override@JsonKey(name: 'alert_data') Map<String, dynamic>? get alertData {
  final value = _alertData;
  if (value == null) return null;
  if (_alertData is EqualUnmodifiableMapView) return _alertData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(name: 'threshold_value') final  String? thresholdValue;
@override@JsonKey(name: 'actual_value') final  String? actualValue;
@override@JsonKey(name: 'is_resolved') final  bool isResolved;
@override@JsonKey(name: 'resolved_by') final  String? resolvedBy;
@override@JsonKey(name: 'resolution_notes') final  String? resolutionNotes;
@override@JsonKey(name: 'resolved_at') final  DateTime? resolvedAt;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;

/// Create a copy of FinancialAlert
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FinancialAlertCopyWith<_FinancialAlert> get copyWith => __$FinancialAlertCopyWithImpl<_FinancialAlert>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FinancialAlertToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FinancialAlert&&(identical(other.id, id) || other.id == id)&&(identical(other.alertType, alertType) || other.alertType == alertType)&&(identical(other.severity, severity) || other.severity == severity)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&const DeepCollectionEquality().equals(other._alertData, _alertData)&&(identical(other.thresholdValue, thresholdValue) || other.thresholdValue == thresholdValue)&&(identical(other.actualValue, actualValue) || other.actualValue == actualValue)&&(identical(other.isResolved, isResolved) || other.isResolved == isResolved)&&(identical(other.resolvedBy, resolvedBy) || other.resolvedBy == resolvedBy)&&(identical(other.resolutionNotes, resolutionNotes) || other.resolutionNotes == resolutionNotes)&&(identical(other.resolvedAt, resolvedAt) || other.resolvedAt == resolvedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,alertType,severity,title,description,userId,walletId,transactionId,const DeepCollectionEquality().hash(_alertData),thresholdValue,actualValue,isResolved,resolvedBy,resolutionNotes,resolvedAt,createdAt,updatedAt);

@override
String toString() {
  return 'FinancialAlert(id: $id, alertType: $alertType, severity: $severity, title: $title, description: $description, userId: $userId, walletId: $walletId, transactionId: $transactionId, alertData: $alertData, thresholdValue: $thresholdValue, actualValue: $actualValue, isResolved: $isResolved, resolvedBy: $resolvedBy, resolutionNotes: $resolutionNotes, resolvedAt: $resolvedAt, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$FinancialAlertCopyWith<$Res> implements $FinancialAlertCopyWith<$Res> {
  factory _$FinancialAlertCopyWith(_FinancialAlert value, $Res Function(_FinancialAlert) _then) = __$FinancialAlertCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'alert_type') String alertType, AlertSeverity severity, String title, String description,@JsonKey(name: 'user_id') String? userId,@JsonKey(name: 'wallet_id') String? walletId,@JsonKey(name: 'transaction_id') String? transactionId,@JsonKey(name: 'alert_data') Map<String, dynamic>? alertData,@JsonKey(name: 'threshold_value') String? thresholdValue,@JsonKey(name: 'actual_value') String? actualValue,@JsonKey(name: 'is_resolved') bool isResolved,@JsonKey(name: 'resolved_by') String? resolvedBy,@JsonKey(name: 'resolution_notes') String? resolutionNotes,@JsonKey(name: 'resolved_at') DateTime? resolvedAt,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt
});




}
/// @nodoc
class __$FinancialAlertCopyWithImpl<$Res>
    implements _$FinancialAlertCopyWith<$Res> {
  __$FinancialAlertCopyWithImpl(this._self, this._then);

  final _FinancialAlert _self;
  final $Res Function(_FinancialAlert) _then;

/// Create a copy of FinancialAlert
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? alertType = null,Object? severity = null,Object? title = null,Object? description = null,Object? userId = freezed,Object? walletId = freezed,Object? transactionId = freezed,Object? alertData = freezed,Object? thresholdValue = freezed,Object? actualValue = freezed,Object? isResolved = null,Object? resolvedBy = freezed,Object? resolutionNotes = freezed,Object? resolvedAt = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_FinancialAlert(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,alertType: null == alertType ? _self.alertType : alertType // ignore: cast_nullable_to_non_nullable
as String,severity: null == severity ? _self.severity : severity // ignore: cast_nullable_to_non_nullable
as AlertSeverity,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,walletId: freezed == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,alertData: freezed == alertData ? _self._alertData : alertData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,thresholdValue: freezed == thresholdValue ? _self.thresholdValue : thresholdValue // ignore: cast_nullable_to_non_nullable
as String?,actualValue: freezed == actualValue ? _self.actualValue : actualValue // ignore: cast_nullable_to_non_nullable
as String?,isResolved: null == isResolved ? _self.isResolved : isResolved // ignore: cast_nullable_to_non_nullable
as bool,resolvedBy: freezed == resolvedBy ? _self.resolvedBy : resolvedBy // ignore: cast_nullable_to_non_nullable
as String?,resolutionNotes: freezed == resolutionNotes ? _self.resolutionNotes : resolutionNotes // ignore: cast_nullable_to_non_nullable
as String?,resolvedAt: freezed == resolvedAt ? _self.resolvedAt : resolvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$ResolveAlertRequest {

@JsonKey(name: 'resolved_by') String get resolvedBy;@JsonKey(name: 'resolution_notes') String get resolutionNotes;
/// Create a copy of ResolveAlertRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ResolveAlertRequestCopyWith<ResolveAlertRequest> get copyWith => _$ResolveAlertRequestCopyWithImpl<ResolveAlertRequest>(this as ResolveAlertRequest, _$identity);

  /// Serializes this ResolveAlertRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ResolveAlertRequest&&(identical(other.resolvedBy, resolvedBy) || other.resolvedBy == resolvedBy)&&(identical(other.resolutionNotes, resolutionNotes) || other.resolutionNotes == resolutionNotes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,resolvedBy,resolutionNotes);

@override
String toString() {
  return 'ResolveAlertRequest(resolvedBy: $resolvedBy, resolutionNotes: $resolutionNotes)';
}


}

/// @nodoc
abstract mixin class $ResolveAlertRequestCopyWith<$Res>  {
  factory $ResolveAlertRequestCopyWith(ResolveAlertRequest value, $Res Function(ResolveAlertRequest) _then) = _$ResolveAlertRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'resolved_by') String resolvedBy,@JsonKey(name: 'resolution_notes') String resolutionNotes
});




}
/// @nodoc
class _$ResolveAlertRequestCopyWithImpl<$Res>
    implements $ResolveAlertRequestCopyWith<$Res> {
  _$ResolveAlertRequestCopyWithImpl(this._self, this._then);

  final ResolveAlertRequest _self;
  final $Res Function(ResolveAlertRequest) _then;

/// Create a copy of ResolveAlertRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? resolvedBy = null,Object? resolutionNotes = null,}) {
  return _then(_self.copyWith(
resolvedBy: null == resolvedBy ? _self.resolvedBy : resolvedBy // ignore: cast_nullable_to_non_nullable
as String,resolutionNotes: null == resolutionNotes ? _self.resolutionNotes : resolutionNotes // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ResolveAlertRequest].
extension ResolveAlertRequestPatterns on ResolveAlertRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ResolveAlertRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ResolveAlertRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ResolveAlertRequest value)  $default,){
final _that = this;
switch (_that) {
case _ResolveAlertRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ResolveAlertRequest value)?  $default,){
final _that = this;
switch (_that) {
case _ResolveAlertRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'resolved_by')  String resolvedBy, @JsonKey(name: 'resolution_notes')  String resolutionNotes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ResolveAlertRequest() when $default != null:
return $default(_that.resolvedBy,_that.resolutionNotes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'resolved_by')  String resolvedBy, @JsonKey(name: 'resolution_notes')  String resolutionNotes)  $default,) {final _that = this;
switch (_that) {
case _ResolveAlertRequest():
return $default(_that.resolvedBy,_that.resolutionNotes);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'resolved_by')  String resolvedBy, @JsonKey(name: 'resolution_notes')  String resolutionNotes)?  $default,) {final _that = this;
switch (_that) {
case _ResolveAlertRequest() when $default != null:
return $default(_that.resolvedBy,_that.resolutionNotes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ResolveAlertRequest implements ResolveAlertRequest {
  const _ResolveAlertRequest({@JsonKey(name: 'resolved_by') required this.resolvedBy, @JsonKey(name: 'resolution_notes') required this.resolutionNotes});
  factory _ResolveAlertRequest.fromJson(Map<String, dynamic> json) => _$ResolveAlertRequestFromJson(json);

@override@JsonKey(name: 'resolved_by') final  String resolvedBy;
@override@JsonKey(name: 'resolution_notes') final  String resolutionNotes;

/// Create a copy of ResolveAlertRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ResolveAlertRequestCopyWith<_ResolveAlertRequest> get copyWith => __$ResolveAlertRequestCopyWithImpl<_ResolveAlertRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ResolveAlertRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ResolveAlertRequest&&(identical(other.resolvedBy, resolvedBy) || other.resolvedBy == resolvedBy)&&(identical(other.resolutionNotes, resolutionNotes) || other.resolutionNotes == resolutionNotes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,resolvedBy,resolutionNotes);

@override
String toString() {
  return 'ResolveAlertRequest(resolvedBy: $resolvedBy, resolutionNotes: $resolutionNotes)';
}


}

/// @nodoc
abstract mixin class _$ResolveAlertRequestCopyWith<$Res> implements $ResolveAlertRequestCopyWith<$Res> {
  factory _$ResolveAlertRequestCopyWith(_ResolveAlertRequest value, $Res Function(_ResolveAlertRequest) _then) = __$ResolveAlertRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'resolved_by') String resolvedBy,@JsonKey(name: 'resolution_notes') String resolutionNotes
});




}
/// @nodoc
class __$ResolveAlertRequestCopyWithImpl<$Res>
    implements _$ResolveAlertRequestCopyWith<$Res> {
  __$ResolveAlertRequestCopyWithImpl(this._self, this._then);

  final _ResolveAlertRequest _self;
  final $Res Function(_ResolveAlertRequest) _then;

/// Create a copy of ResolveAlertRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? resolvedBy = null,Object? resolutionNotes = null,}) {
  return _then(_ResolveAlertRequest(
resolvedBy: null == resolvedBy ? _self.resolvedBy : resolvedBy // ignore: cast_nullable_to_non_nullable
as String,resolutionNotes: null == resolutionNotes ? _self.resolutionNotes : resolutionNotes // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$FinancialSummary {

@JsonKey(name: 'total_wallets') int get totalWallets;@JsonKey(name: 'active_wallets') int get activeWallets;@JsonKey(name: 'total_platform_balance') String get totalPlatformBalance;@JsonKey(name: 'total_transactions_today') int get totalTransactionsToday;@JsonKey(name: 'total_volume_today') String get totalVolumeToday;@JsonKey(name: 'new_users_today') int get newUsersToday;@JsonKey(name: 'verified_users') int get verifiedUsers;@JsonKey(name: 'pending_verifications') int get pendingVerifications;@JsonKey(name: 'pending_transactions') int get pendingTransactions;@JsonKey(name: 'failed_transactions_today') int get failedTransactionsToday;@JsonKey(name: 'refund_requests') int get refundRequests;@JsonKey(name: 'active_alerts') int get activeAlerts;@JsonKey(name: 'critical_alerts') int get criticalAlerts;@JsonKey(name: 'resolved_alerts_today') int get resolvedAlertsToday;@JsonKey(name: 'platform_earnings_today') String get platformEarningsToday;@JsonKey(name: 'platform_earnings_month') String get platformEarningsMonth;@JsonKey(name: 'average_commission_rate') String get averageCommissionRate;
/// Create a copy of FinancialSummary
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FinancialSummaryCopyWith<FinancialSummary> get copyWith => _$FinancialSummaryCopyWithImpl<FinancialSummary>(this as FinancialSummary, _$identity);

  /// Serializes this FinancialSummary to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FinancialSummary&&(identical(other.totalWallets, totalWallets) || other.totalWallets == totalWallets)&&(identical(other.activeWallets, activeWallets) || other.activeWallets == activeWallets)&&(identical(other.totalPlatformBalance, totalPlatformBalance) || other.totalPlatformBalance == totalPlatformBalance)&&(identical(other.totalTransactionsToday, totalTransactionsToday) || other.totalTransactionsToday == totalTransactionsToday)&&(identical(other.totalVolumeToday, totalVolumeToday) || other.totalVolumeToday == totalVolumeToday)&&(identical(other.newUsersToday, newUsersToday) || other.newUsersToday == newUsersToday)&&(identical(other.verifiedUsers, verifiedUsers) || other.verifiedUsers == verifiedUsers)&&(identical(other.pendingVerifications, pendingVerifications) || other.pendingVerifications == pendingVerifications)&&(identical(other.pendingTransactions, pendingTransactions) || other.pendingTransactions == pendingTransactions)&&(identical(other.failedTransactionsToday, failedTransactionsToday) || other.failedTransactionsToday == failedTransactionsToday)&&(identical(other.refundRequests, refundRequests) || other.refundRequests == refundRequests)&&(identical(other.activeAlerts, activeAlerts) || other.activeAlerts == activeAlerts)&&(identical(other.criticalAlerts, criticalAlerts) || other.criticalAlerts == criticalAlerts)&&(identical(other.resolvedAlertsToday, resolvedAlertsToday) || other.resolvedAlertsToday == resolvedAlertsToday)&&(identical(other.platformEarningsToday, platformEarningsToday) || other.platformEarningsToday == platformEarningsToday)&&(identical(other.platformEarningsMonth, platformEarningsMonth) || other.platformEarningsMonth == platformEarningsMonth)&&(identical(other.averageCommissionRate, averageCommissionRate) || other.averageCommissionRate == averageCommissionRate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalWallets,activeWallets,totalPlatformBalance,totalTransactionsToday,totalVolumeToday,newUsersToday,verifiedUsers,pendingVerifications,pendingTransactions,failedTransactionsToday,refundRequests,activeAlerts,criticalAlerts,resolvedAlertsToday,platformEarningsToday,platformEarningsMonth,averageCommissionRate);

@override
String toString() {
  return 'FinancialSummary(totalWallets: $totalWallets, activeWallets: $activeWallets, totalPlatformBalance: $totalPlatformBalance, totalTransactionsToday: $totalTransactionsToday, totalVolumeToday: $totalVolumeToday, newUsersToday: $newUsersToday, verifiedUsers: $verifiedUsers, pendingVerifications: $pendingVerifications, pendingTransactions: $pendingTransactions, failedTransactionsToday: $failedTransactionsToday, refundRequests: $refundRequests, activeAlerts: $activeAlerts, criticalAlerts: $criticalAlerts, resolvedAlertsToday: $resolvedAlertsToday, platformEarningsToday: $platformEarningsToday, platformEarningsMonth: $platformEarningsMonth, averageCommissionRate: $averageCommissionRate)';
}


}

/// @nodoc
abstract mixin class $FinancialSummaryCopyWith<$Res>  {
  factory $FinancialSummaryCopyWith(FinancialSummary value, $Res Function(FinancialSummary) _then) = _$FinancialSummaryCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'total_wallets') int totalWallets,@JsonKey(name: 'active_wallets') int activeWallets,@JsonKey(name: 'total_platform_balance') String totalPlatformBalance,@JsonKey(name: 'total_transactions_today') int totalTransactionsToday,@JsonKey(name: 'total_volume_today') String totalVolumeToday,@JsonKey(name: 'new_users_today') int newUsersToday,@JsonKey(name: 'verified_users') int verifiedUsers,@JsonKey(name: 'pending_verifications') int pendingVerifications,@JsonKey(name: 'pending_transactions') int pendingTransactions,@JsonKey(name: 'failed_transactions_today') int failedTransactionsToday,@JsonKey(name: 'refund_requests') int refundRequests,@JsonKey(name: 'active_alerts') int activeAlerts,@JsonKey(name: 'critical_alerts') int criticalAlerts,@JsonKey(name: 'resolved_alerts_today') int resolvedAlertsToday,@JsonKey(name: 'platform_earnings_today') String platformEarningsToday,@JsonKey(name: 'platform_earnings_month') String platformEarningsMonth,@JsonKey(name: 'average_commission_rate') String averageCommissionRate
});




}
/// @nodoc
class _$FinancialSummaryCopyWithImpl<$Res>
    implements $FinancialSummaryCopyWith<$Res> {
  _$FinancialSummaryCopyWithImpl(this._self, this._then);

  final FinancialSummary _self;
  final $Res Function(FinancialSummary) _then;

/// Create a copy of FinancialSummary
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalWallets = null,Object? activeWallets = null,Object? totalPlatformBalance = null,Object? totalTransactionsToday = null,Object? totalVolumeToday = null,Object? newUsersToday = null,Object? verifiedUsers = null,Object? pendingVerifications = null,Object? pendingTransactions = null,Object? failedTransactionsToday = null,Object? refundRequests = null,Object? activeAlerts = null,Object? criticalAlerts = null,Object? resolvedAlertsToday = null,Object? platformEarningsToday = null,Object? platformEarningsMonth = null,Object? averageCommissionRate = null,}) {
  return _then(_self.copyWith(
totalWallets: null == totalWallets ? _self.totalWallets : totalWallets // ignore: cast_nullable_to_non_nullable
as int,activeWallets: null == activeWallets ? _self.activeWallets : activeWallets // ignore: cast_nullable_to_non_nullable
as int,totalPlatformBalance: null == totalPlatformBalance ? _self.totalPlatformBalance : totalPlatformBalance // ignore: cast_nullable_to_non_nullable
as String,totalTransactionsToday: null == totalTransactionsToday ? _self.totalTransactionsToday : totalTransactionsToday // ignore: cast_nullable_to_non_nullable
as int,totalVolumeToday: null == totalVolumeToday ? _self.totalVolumeToday : totalVolumeToday // ignore: cast_nullable_to_non_nullable
as String,newUsersToday: null == newUsersToday ? _self.newUsersToday : newUsersToday // ignore: cast_nullable_to_non_nullable
as int,verifiedUsers: null == verifiedUsers ? _self.verifiedUsers : verifiedUsers // ignore: cast_nullable_to_non_nullable
as int,pendingVerifications: null == pendingVerifications ? _self.pendingVerifications : pendingVerifications // ignore: cast_nullable_to_non_nullable
as int,pendingTransactions: null == pendingTransactions ? _self.pendingTransactions : pendingTransactions // ignore: cast_nullable_to_non_nullable
as int,failedTransactionsToday: null == failedTransactionsToday ? _self.failedTransactionsToday : failedTransactionsToday // ignore: cast_nullable_to_non_nullable
as int,refundRequests: null == refundRequests ? _self.refundRequests : refundRequests // ignore: cast_nullable_to_non_nullable
as int,activeAlerts: null == activeAlerts ? _self.activeAlerts : activeAlerts // ignore: cast_nullable_to_non_nullable
as int,criticalAlerts: null == criticalAlerts ? _self.criticalAlerts : criticalAlerts // ignore: cast_nullable_to_non_nullable
as int,resolvedAlertsToday: null == resolvedAlertsToday ? _self.resolvedAlertsToday : resolvedAlertsToday // ignore: cast_nullable_to_non_nullable
as int,platformEarningsToday: null == platformEarningsToday ? _self.platformEarningsToday : platformEarningsToday // ignore: cast_nullable_to_non_nullable
as String,platformEarningsMonth: null == platformEarningsMonth ? _self.platformEarningsMonth : platformEarningsMonth // ignore: cast_nullable_to_non_nullable
as String,averageCommissionRate: null == averageCommissionRate ? _self.averageCommissionRate : averageCommissionRate // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [FinancialSummary].
extension FinancialSummaryPatterns on FinancialSummary {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FinancialSummary value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FinancialSummary() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FinancialSummary value)  $default,){
final _that = this;
switch (_that) {
case _FinancialSummary():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FinancialSummary value)?  $default,){
final _that = this;
switch (_that) {
case _FinancialSummary() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'total_wallets')  int totalWallets, @JsonKey(name: 'active_wallets')  int activeWallets, @JsonKey(name: 'total_platform_balance')  String totalPlatformBalance, @JsonKey(name: 'total_transactions_today')  int totalTransactionsToday, @JsonKey(name: 'total_volume_today')  String totalVolumeToday, @JsonKey(name: 'new_users_today')  int newUsersToday, @JsonKey(name: 'verified_users')  int verifiedUsers, @JsonKey(name: 'pending_verifications')  int pendingVerifications, @JsonKey(name: 'pending_transactions')  int pendingTransactions, @JsonKey(name: 'failed_transactions_today')  int failedTransactionsToday, @JsonKey(name: 'refund_requests')  int refundRequests, @JsonKey(name: 'active_alerts')  int activeAlerts, @JsonKey(name: 'critical_alerts')  int criticalAlerts, @JsonKey(name: 'resolved_alerts_today')  int resolvedAlertsToday, @JsonKey(name: 'platform_earnings_today')  String platformEarningsToday, @JsonKey(name: 'platform_earnings_month')  String platformEarningsMonth, @JsonKey(name: 'average_commission_rate')  String averageCommissionRate)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FinancialSummary() when $default != null:
return $default(_that.totalWallets,_that.activeWallets,_that.totalPlatformBalance,_that.totalTransactionsToday,_that.totalVolumeToday,_that.newUsersToday,_that.verifiedUsers,_that.pendingVerifications,_that.pendingTransactions,_that.failedTransactionsToday,_that.refundRequests,_that.activeAlerts,_that.criticalAlerts,_that.resolvedAlertsToday,_that.platformEarningsToday,_that.platformEarningsMonth,_that.averageCommissionRate);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'total_wallets')  int totalWallets, @JsonKey(name: 'active_wallets')  int activeWallets, @JsonKey(name: 'total_platform_balance')  String totalPlatformBalance, @JsonKey(name: 'total_transactions_today')  int totalTransactionsToday, @JsonKey(name: 'total_volume_today')  String totalVolumeToday, @JsonKey(name: 'new_users_today')  int newUsersToday, @JsonKey(name: 'verified_users')  int verifiedUsers, @JsonKey(name: 'pending_verifications')  int pendingVerifications, @JsonKey(name: 'pending_transactions')  int pendingTransactions, @JsonKey(name: 'failed_transactions_today')  int failedTransactionsToday, @JsonKey(name: 'refund_requests')  int refundRequests, @JsonKey(name: 'active_alerts')  int activeAlerts, @JsonKey(name: 'critical_alerts')  int criticalAlerts, @JsonKey(name: 'resolved_alerts_today')  int resolvedAlertsToday, @JsonKey(name: 'platform_earnings_today')  String platformEarningsToday, @JsonKey(name: 'platform_earnings_month')  String platformEarningsMonth, @JsonKey(name: 'average_commission_rate')  String averageCommissionRate)  $default,) {final _that = this;
switch (_that) {
case _FinancialSummary():
return $default(_that.totalWallets,_that.activeWallets,_that.totalPlatformBalance,_that.totalTransactionsToday,_that.totalVolumeToday,_that.newUsersToday,_that.verifiedUsers,_that.pendingVerifications,_that.pendingTransactions,_that.failedTransactionsToday,_that.refundRequests,_that.activeAlerts,_that.criticalAlerts,_that.resolvedAlertsToday,_that.platformEarningsToday,_that.platformEarningsMonth,_that.averageCommissionRate);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'total_wallets')  int totalWallets, @JsonKey(name: 'active_wallets')  int activeWallets, @JsonKey(name: 'total_platform_balance')  String totalPlatformBalance, @JsonKey(name: 'total_transactions_today')  int totalTransactionsToday, @JsonKey(name: 'total_volume_today')  String totalVolumeToday, @JsonKey(name: 'new_users_today')  int newUsersToday, @JsonKey(name: 'verified_users')  int verifiedUsers, @JsonKey(name: 'pending_verifications')  int pendingVerifications, @JsonKey(name: 'pending_transactions')  int pendingTransactions, @JsonKey(name: 'failed_transactions_today')  int failedTransactionsToday, @JsonKey(name: 'refund_requests')  int refundRequests, @JsonKey(name: 'active_alerts')  int activeAlerts, @JsonKey(name: 'critical_alerts')  int criticalAlerts, @JsonKey(name: 'resolved_alerts_today')  int resolvedAlertsToday, @JsonKey(name: 'platform_earnings_today')  String platformEarningsToday, @JsonKey(name: 'platform_earnings_month')  String platformEarningsMonth, @JsonKey(name: 'average_commission_rate')  String averageCommissionRate)?  $default,) {final _that = this;
switch (_that) {
case _FinancialSummary() when $default != null:
return $default(_that.totalWallets,_that.activeWallets,_that.totalPlatformBalance,_that.totalTransactionsToday,_that.totalVolumeToday,_that.newUsersToday,_that.verifiedUsers,_that.pendingVerifications,_that.pendingTransactions,_that.failedTransactionsToday,_that.refundRequests,_that.activeAlerts,_that.criticalAlerts,_that.resolvedAlertsToday,_that.platformEarningsToday,_that.platformEarningsMonth,_that.averageCommissionRate);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FinancialSummary implements FinancialSummary {
  const _FinancialSummary({@JsonKey(name: 'total_wallets') required this.totalWallets, @JsonKey(name: 'active_wallets') required this.activeWallets, @JsonKey(name: 'total_platform_balance') required this.totalPlatformBalance, @JsonKey(name: 'total_transactions_today') required this.totalTransactionsToday, @JsonKey(name: 'total_volume_today') required this.totalVolumeToday, @JsonKey(name: 'new_users_today') required this.newUsersToday, @JsonKey(name: 'verified_users') required this.verifiedUsers, @JsonKey(name: 'pending_verifications') required this.pendingVerifications, @JsonKey(name: 'pending_transactions') required this.pendingTransactions, @JsonKey(name: 'failed_transactions_today') required this.failedTransactionsToday, @JsonKey(name: 'refund_requests') required this.refundRequests, @JsonKey(name: 'active_alerts') required this.activeAlerts, @JsonKey(name: 'critical_alerts') required this.criticalAlerts, @JsonKey(name: 'resolved_alerts_today') required this.resolvedAlertsToday, @JsonKey(name: 'platform_earnings_today') required this.platformEarningsToday, @JsonKey(name: 'platform_earnings_month') required this.platformEarningsMonth, @JsonKey(name: 'average_commission_rate') required this.averageCommissionRate});
  factory _FinancialSummary.fromJson(Map<String, dynamic> json) => _$FinancialSummaryFromJson(json);

@override@JsonKey(name: 'total_wallets') final  int totalWallets;
@override@JsonKey(name: 'active_wallets') final  int activeWallets;
@override@JsonKey(name: 'total_platform_balance') final  String totalPlatformBalance;
@override@JsonKey(name: 'total_transactions_today') final  int totalTransactionsToday;
@override@JsonKey(name: 'total_volume_today') final  String totalVolumeToday;
@override@JsonKey(name: 'new_users_today') final  int newUsersToday;
@override@JsonKey(name: 'verified_users') final  int verifiedUsers;
@override@JsonKey(name: 'pending_verifications') final  int pendingVerifications;
@override@JsonKey(name: 'pending_transactions') final  int pendingTransactions;
@override@JsonKey(name: 'failed_transactions_today') final  int failedTransactionsToday;
@override@JsonKey(name: 'refund_requests') final  int refundRequests;
@override@JsonKey(name: 'active_alerts') final  int activeAlerts;
@override@JsonKey(name: 'critical_alerts') final  int criticalAlerts;
@override@JsonKey(name: 'resolved_alerts_today') final  int resolvedAlertsToday;
@override@JsonKey(name: 'platform_earnings_today') final  String platformEarningsToday;
@override@JsonKey(name: 'platform_earnings_month') final  String platformEarningsMonth;
@override@JsonKey(name: 'average_commission_rate') final  String averageCommissionRate;

/// Create a copy of FinancialSummary
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FinancialSummaryCopyWith<_FinancialSummary> get copyWith => __$FinancialSummaryCopyWithImpl<_FinancialSummary>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FinancialSummaryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FinancialSummary&&(identical(other.totalWallets, totalWallets) || other.totalWallets == totalWallets)&&(identical(other.activeWallets, activeWallets) || other.activeWallets == activeWallets)&&(identical(other.totalPlatformBalance, totalPlatformBalance) || other.totalPlatformBalance == totalPlatformBalance)&&(identical(other.totalTransactionsToday, totalTransactionsToday) || other.totalTransactionsToday == totalTransactionsToday)&&(identical(other.totalVolumeToday, totalVolumeToday) || other.totalVolumeToday == totalVolumeToday)&&(identical(other.newUsersToday, newUsersToday) || other.newUsersToday == newUsersToday)&&(identical(other.verifiedUsers, verifiedUsers) || other.verifiedUsers == verifiedUsers)&&(identical(other.pendingVerifications, pendingVerifications) || other.pendingVerifications == pendingVerifications)&&(identical(other.pendingTransactions, pendingTransactions) || other.pendingTransactions == pendingTransactions)&&(identical(other.failedTransactionsToday, failedTransactionsToday) || other.failedTransactionsToday == failedTransactionsToday)&&(identical(other.refundRequests, refundRequests) || other.refundRequests == refundRequests)&&(identical(other.activeAlerts, activeAlerts) || other.activeAlerts == activeAlerts)&&(identical(other.criticalAlerts, criticalAlerts) || other.criticalAlerts == criticalAlerts)&&(identical(other.resolvedAlertsToday, resolvedAlertsToday) || other.resolvedAlertsToday == resolvedAlertsToday)&&(identical(other.platformEarningsToday, platformEarningsToday) || other.platformEarningsToday == platformEarningsToday)&&(identical(other.platformEarningsMonth, platformEarningsMonth) || other.platformEarningsMonth == platformEarningsMonth)&&(identical(other.averageCommissionRate, averageCommissionRate) || other.averageCommissionRate == averageCommissionRate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalWallets,activeWallets,totalPlatformBalance,totalTransactionsToday,totalVolumeToday,newUsersToday,verifiedUsers,pendingVerifications,pendingTransactions,failedTransactionsToday,refundRequests,activeAlerts,criticalAlerts,resolvedAlertsToday,platformEarningsToday,platformEarningsMonth,averageCommissionRate);

@override
String toString() {
  return 'FinancialSummary(totalWallets: $totalWallets, activeWallets: $activeWallets, totalPlatformBalance: $totalPlatformBalance, totalTransactionsToday: $totalTransactionsToday, totalVolumeToday: $totalVolumeToday, newUsersToday: $newUsersToday, verifiedUsers: $verifiedUsers, pendingVerifications: $pendingVerifications, pendingTransactions: $pendingTransactions, failedTransactionsToday: $failedTransactionsToday, refundRequests: $refundRequests, activeAlerts: $activeAlerts, criticalAlerts: $criticalAlerts, resolvedAlertsToday: $resolvedAlertsToday, platformEarningsToday: $platformEarningsToday, platformEarningsMonth: $platformEarningsMonth, averageCommissionRate: $averageCommissionRate)';
}


}

/// @nodoc
abstract mixin class _$FinancialSummaryCopyWith<$Res> implements $FinancialSummaryCopyWith<$Res> {
  factory _$FinancialSummaryCopyWith(_FinancialSummary value, $Res Function(_FinancialSummary) _then) = __$FinancialSummaryCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'total_wallets') int totalWallets,@JsonKey(name: 'active_wallets') int activeWallets,@JsonKey(name: 'total_platform_balance') String totalPlatformBalance,@JsonKey(name: 'total_transactions_today') int totalTransactionsToday,@JsonKey(name: 'total_volume_today') String totalVolumeToday,@JsonKey(name: 'new_users_today') int newUsersToday,@JsonKey(name: 'verified_users') int verifiedUsers,@JsonKey(name: 'pending_verifications') int pendingVerifications,@JsonKey(name: 'pending_transactions') int pendingTransactions,@JsonKey(name: 'failed_transactions_today') int failedTransactionsToday,@JsonKey(name: 'refund_requests') int refundRequests,@JsonKey(name: 'active_alerts') int activeAlerts,@JsonKey(name: 'critical_alerts') int criticalAlerts,@JsonKey(name: 'resolved_alerts_today') int resolvedAlertsToday,@JsonKey(name: 'platform_earnings_today') String platformEarningsToday,@JsonKey(name: 'platform_earnings_month') String platformEarningsMonth,@JsonKey(name: 'average_commission_rate') String averageCommissionRate
});




}
/// @nodoc
class __$FinancialSummaryCopyWithImpl<$Res>
    implements _$FinancialSummaryCopyWith<$Res> {
  __$FinancialSummaryCopyWithImpl(this._self, this._then);

  final _FinancialSummary _self;
  final $Res Function(_FinancialSummary) _then;

/// Create a copy of FinancialSummary
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalWallets = null,Object? activeWallets = null,Object? totalPlatformBalance = null,Object? totalTransactionsToday = null,Object? totalVolumeToday = null,Object? newUsersToday = null,Object? verifiedUsers = null,Object? pendingVerifications = null,Object? pendingTransactions = null,Object? failedTransactionsToday = null,Object? refundRequests = null,Object? activeAlerts = null,Object? criticalAlerts = null,Object? resolvedAlertsToday = null,Object? platformEarningsToday = null,Object? platformEarningsMonth = null,Object? averageCommissionRate = null,}) {
  return _then(_FinancialSummary(
totalWallets: null == totalWallets ? _self.totalWallets : totalWallets // ignore: cast_nullable_to_non_nullable
as int,activeWallets: null == activeWallets ? _self.activeWallets : activeWallets // ignore: cast_nullable_to_non_nullable
as int,totalPlatformBalance: null == totalPlatformBalance ? _self.totalPlatformBalance : totalPlatformBalance // ignore: cast_nullable_to_non_nullable
as String,totalTransactionsToday: null == totalTransactionsToday ? _self.totalTransactionsToday : totalTransactionsToday // ignore: cast_nullable_to_non_nullable
as int,totalVolumeToday: null == totalVolumeToday ? _self.totalVolumeToday : totalVolumeToday // ignore: cast_nullable_to_non_nullable
as String,newUsersToday: null == newUsersToday ? _self.newUsersToday : newUsersToday // ignore: cast_nullable_to_non_nullable
as int,verifiedUsers: null == verifiedUsers ? _self.verifiedUsers : verifiedUsers // ignore: cast_nullable_to_non_nullable
as int,pendingVerifications: null == pendingVerifications ? _self.pendingVerifications : pendingVerifications // ignore: cast_nullable_to_non_nullable
as int,pendingTransactions: null == pendingTransactions ? _self.pendingTransactions : pendingTransactions // ignore: cast_nullable_to_non_nullable
as int,failedTransactionsToday: null == failedTransactionsToday ? _self.failedTransactionsToday : failedTransactionsToday // ignore: cast_nullable_to_non_nullable
as int,refundRequests: null == refundRequests ? _self.refundRequests : refundRequests // ignore: cast_nullable_to_non_nullable
as int,activeAlerts: null == activeAlerts ? _self.activeAlerts : activeAlerts // ignore: cast_nullable_to_non_nullable
as int,criticalAlerts: null == criticalAlerts ? _self.criticalAlerts : criticalAlerts // ignore: cast_nullable_to_non_nullable
as int,resolvedAlertsToday: null == resolvedAlertsToday ? _self.resolvedAlertsToday : resolvedAlertsToday // ignore: cast_nullable_to_non_nullable
as int,platformEarningsToday: null == platformEarningsToday ? _self.platformEarningsToday : platformEarningsToday // ignore: cast_nullable_to_non_nullable
as String,platformEarningsMonth: null == platformEarningsMonth ? _self.platformEarningsMonth : platformEarningsMonth // ignore: cast_nullable_to_non_nullable
as String,averageCommissionRate: null == averageCommissionRate ? _self.averageCommissionRate : averageCommissionRate // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$TransactionAnalysis {

@JsonKey(name: 'transaction_id') String get transactionId; String get amount; String get type; String get status;@JsonKey(name: 'from_user_id') String? get fromUserId;@JsonKey(name: 'to_user_id') String? get toUserId;@JsonKey(name: 'risk_score') double get riskScore;@JsonKey(name: 'risk_factors') List<String> get riskFactors;@JsonKey(name: 'is_suspicious') bool get isSuspicious;@JsonKey(name: 'related_transactions') List<String> get relatedTransactions;@JsonKey(name: 'time_since_account_creation') int get timeSinceAccountCreation;@JsonKey(name: 'user_transaction_count') int get userTransactionCount;@JsonKey(name: 'created_at') DateTime get createdAt;
/// Create a copy of TransactionAnalysis
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransactionAnalysisCopyWith<TransactionAnalysis> get copyWith => _$TransactionAnalysisCopyWithImpl<TransactionAnalysis>(this as TransactionAnalysis, _$identity);

  /// Serializes this TransactionAnalysis to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransactionAnalysis&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.fromUserId, fromUserId) || other.fromUserId == fromUserId)&&(identical(other.toUserId, toUserId) || other.toUserId == toUserId)&&(identical(other.riskScore, riskScore) || other.riskScore == riskScore)&&const DeepCollectionEquality().equals(other.riskFactors, riskFactors)&&(identical(other.isSuspicious, isSuspicious) || other.isSuspicious == isSuspicious)&&const DeepCollectionEquality().equals(other.relatedTransactions, relatedTransactions)&&(identical(other.timeSinceAccountCreation, timeSinceAccountCreation) || other.timeSinceAccountCreation == timeSinceAccountCreation)&&(identical(other.userTransactionCount, userTransactionCount) || other.userTransactionCount == userTransactionCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,transactionId,amount,type,status,fromUserId,toUserId,riskScore,const DeepCollectionEquality().hash(riskFactors),isSuspicious,const DeepCollectionEquality().hash(relatedTransactions),timeSinceAccountCreation,userTransactionCount,createdAt);

@override
String toString() {
  return 'TransactionAnalysis(transactionId: $transactionId, amount: $amount, type: $type, status: $status, fromUserId: $fromUserId, toUserId: $toUserId, riskScore: $riskScore, riskFactors: $riskFactors, isSuspicious: $isSuspicious, relatedTransactions: $relatedTransactions, timeSinceAccountCreation: $timeSinceAccountCreation, userTransactionCount: $userTransactionCount, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $TransactionAnalysisCopyWith<$Res>  {
  factory $TransactionAnalysisCopyWith(TransactionAnalysis value, $Res Function(TransactionAnalysis) _then) = _$TransactionAnalysisCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'transaction_id') String transactionId, String amount, String type, String status,@JsonKey(name: 'from_user_id') String? fromUserId,@JsonKey(name: 'to_user_id') String? toUserId,@JsonKey(name: 'risk_score') double riskScore,@JsonKey(name: 'risk_factors') List<String> riskFactors,@JsonKey(name: 'is_suspicious') bool isSuspicious,@JsonKey(name: 'related_transactions') List<String> relatedTransactions,@JsonKey(name: 'time_since_account_creation') int timeSinceAccountCreation,@JsonKey(name: 'user_transaction_count') int userTransactionCount,@JsonKey(name: 'created_at') DateTime createdAt
});




}
/// @nodoc
class _$TransactionAnalysisCopyWithImpl<$Res>
    implements $TransactionAnalysisCopyWith<$Res> {
  _$TransactionAnalysisCopyWithImpl(this._self, this._then);

  final TransactionAnalysis _self;
  final $Res Function(TransactionAnalysis) _then;

/// Create a copy of TransactionAnalysis
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? transactionId = null,Object? amount = null,Object? type = null,Object? status = null,Object? fromUserId = freezed,Object? toUserId = freezed,Object? riskScore = null,Object? riskFactors = null,Object? isSuspicious = null,Object? relatedTransactions = null,Object? timeSinceAccountCreation = null,Object? userTransactionCount = null,Object? createdAt = null,}) {
  return _then(_self.copyWith(
transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,fromUserId: freezed == fromUserId ? _self.fromUserId : fromUserId // ignore: cast_nullable_to_non_nullable
as String?,toUserId: freezed == toUserId ? _self.toUserId : toUserId // ignore: cast_nullable_to_non_nullable
as String?,riskScore: null == riskScore ? _self.riskScore : riskScore // ignore: cast_nullable_to_non_nullable
as double,riskFactors: null == riskFactors ? _self.riskFactors : riskFactors // ignore: cast_nullable_to_non_nullable
as List<String>,isSuspicious: null == isSuspicious ? _self.isSuspicious : isSuspicious // ignore: cast_nullable_to_non_nullable
as bool,relatedTransactions: null == relatedTransactions ? _self.relatedTransactions : relatedTransactions // ignore: cast_nullable_to_non_nullable
as List<String>,timeSinceAccountCreation: null == timeSinceAccountCreation ? _self.timeSinceAccountCreation : timeSinceAccountCreation // ignore: cast_nullable_to_non_nullable
as int,userTransactionCount: null == userTransactionCount ? _self.userTransactionCount : userTransactionCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [TransactionAnalysis].
extension TransactionAnalysisPatterns on TransactionAnalysis {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TransactionAnalysis value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TransactionAnalysis() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TransactionAnalysis value)  $default,){
final _that = this;
switch (_that) {
case _TransactionAnalysis():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TransactionAnalysis value)?  $default,){
final _that = this;
switch (_that) {
case _TransactionAnalysis() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'transaction_id')  String transactionId,  String amount,  String type,  String status, @JsonKey(name: 'from_user_id')  String? fromUserId, @JsonKey(name: 'to_user_id')  String? toUserId, @JsonKey(name: 'risk_score')  double riskScore, @JsonKey(name: 'risk_factors')  List<String> riskFactors, @JsonKey(name: 'is_suspicious')  bool isSuspicious, @JsonKey(name: 'related_transactions')  List<String> relatedTransactions, @JsonKey(name: 'time_since_account_creation')  int timeSinceAccountCreation, @JsonKey(name: 'user_transaction_count')  int userTransactionCount, @JsonKey(name: 'created_at')  DateTime createdAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TransactionAnalysis() when $default != null:
return $default(_that.transactionId,_that.amount,_that.type,_that.status,_that.fromUserId,_that.toUserId,_that.riskScore,_that.riskFactors,_that.isSuspicious,_that.relatedTransactions,_that.timeSinceAccountCreation,_that.userTransactionCount,_that.createdAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'transaction_id')  String transactionId,  String amount,  String type,  String status, @JsonKey(name: 'from_user_id')  String? fromUserId, @JsonKey(name: 'to_user_id')  String? toUserId, @JsonKey(name: 'risk_score')  double riskScore, @JsonKey(name: 'risk_factors')  List<String> riskFactors, @JsonKey(name: 'is_suspicious')  bool isSuspicious, @JsonKey(name: 'related_transactions')  List<String> relatedTransactions, @JsonKey(name: 'time_since_account_creation')  int timeSinceAccountCreation, @JsonKey(name: 'user_transaction_count')  int userTransactionCount, @JsonKey(name: 'created_at')  DateTime createdAt)  $default,) {final _that = this;
switch (_that) {
case _TransactionAnalysis():
return $default(_that.transactionId,_that.amount,_that.type,_that.status,_that.fromUserId,_that.toUserId,_that.riskScore,_that.riskFactors,_that.isSuspicious,_that.relatedTransactions,_that.timeSinceAccountCreation,_that.userTransactionCount,_that.createdAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'transaction_id')  String transactionId,  String amount,  String type,  String status, @JsonKey(name: 'from_user_id')  String? fromUserId, @JsonKey(name: 'to_user_id')  String? toUserId, @JsonKey(name: 'risk_score')  double riskScore, @JsonKey(name: 'risk_factors')  List<String> riskFactors, @JsonKey(name: 'is_suspicious')  bool isSuspicious, @JsonKey(name: 'related_transactions')  List<String> relatedTransactions, @JsonKey(name: 'time_since_account_creation')  int timeSinceAccountCreation, @JsonKey(name: 'user_transaction_count')  int userTransactionCount, @JsonKey(name: 'created_at')  DateTime createdAt)?  $default,) {final _that = this;
switch (_that) {
case _TransactionAnalysis() when $default != null:
return $default(_that.transactionId,_that.amount,_that.type,_that.status,_that.fromUserId,_that.toUserId,_that.riskScore,_that.riskFactors,_that.isSuspicious,_that.relatedTransactions,_that.timeSinceAccountCreation,_that.userTransactionCount,_that.createdAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TransactionAnalysis implements TransactionAnalysis {
  const _TransactionAnalysis({@JsonKey(name: 'transaction_id') required this.transactionId, required this.amount, required this.type, required this.status, @JsonKey(name: 'from_user_id') this.fromUserId, @JsonKey(name: 'to_user_id') this.toUserId, @JsonKey(name: 'risk_score') required this.riskScore, @JsonKey(name: 'risk_factors') required final  List<String> riskFactors, @JsonKey(name: 'is_suspicious') required this.isSuspicious, @JsonKey(name: 'related_transactions') required final  List<String> relatedTransactions, @JsonKey(name: 'time_since_account_creation') required this.timeSinceAccountCreation, @JsonKey(name: 'user_transaction_count') required this.userTransactionCount, @JsonKey(name: 'created_at') required this.createdAt}): _riskFactors = riskFactors,_relatedTransactions = relatedTransactions;
  factory _TransactionAnalysis.fromJson(Map<String, dynamic> json) => _$TransactionAnalysisFromJson(json);

@override@JsonKey(name: 'transaction_id') final  String transactionId;
@override final  String amount;
@override final  String type;
@override final  String status;
@override@JsonKey(name: 'from_user_id') final  String? fromUserId;
@override@JsonKey(name: 'to_user_id') final  String? toUserId;
@override@JsonKey(name: 'risk_score') final  double riskScore;
 final  List<String> _riskFactors;
@override@JsonKey(name: 'risk_factors') List<String> get riskFactors {
  if (_riskFactors is EqualUnmodifiableListView) return _riskFactors;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_riskFactors);
}

@override@JsonKey(name: 'is_suspicious') final  bool isSuspicious;
 final  List<String> _relatedTransactions;
@override@JsonKey(name: 'related_transactions') List<String> get relatedTransactions {
  if (_relatedTransactions is EqualUnmodifiableListView) return _relatedTransactions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_relatedTransactions);
}

@override@JsonKey(name: 'time_since_account_creation') final  int timeSinceAccountCreation;
@override@JsonKey(name: 'user_transaction_count') final  int userTransactionCount;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;

/// Create a copy of TransactionAnalysis
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TransactionAnalysisCopyWith<_TransactionAnalysis> get copyWith => __$TransactionAnalysisCopyWithImpl<_TransactionAnalysis>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TransactionAnalysisToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TransactionAnalysis&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.fromUserId, fromUserId) || other.fromUserId == fromUserId)&&(identical(other.toUserId, toUserId) || other.toUserId == toUserId)&&(identical(other.riskScore, riskScore) || other.riskScore == riskScore)&&const DeepCollectionEquality().equals(other._riskFactors, _riskFactors)&&(identical(other.isSuspicious, isSuspicious) || other.isSuspicious == isSuspicious)&&const DeepCollectionEquality().equals(other._relatedTransactions, _relatedTransactions)&&(identical(other.timeSinceAccountCreation, timeSinceAccountCreation) || other.timeSinceAccountCreation == timeSinceAccountCreation)&&(identical(other.userTransactionCount, userTransactionCount) || other.userTransactionCount == userTransactionCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,transactionId,amount,type,status,fromUserId,toUserId,riskScore,const DeepCollectionEquality().hash(_riskFactors),isSuspicious,const DeepCollectionEquality().hash(_relatedTransactions),timeSinceAccountCreation,userTransactionCount,createdAt);

@override
String toString() {
  return 'TransactionAnalysis(transactionId: $transactionId, amount: $amount, type: $type, status: $status, fromUserId: $fromUserId, toUserId: $toUserId, riskScore: $riskScore, riskFactors: $riskFactors, isSuspicious: $isSuspicious, relatedTransactions: $relatedTransactions, timeSinceAccountCreation: $timeSinceAccountCreation, userTransactionCount: $userTransactionCount, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$TransactionAnalysisCopyWith<$Res> implements $TransactionAnalysisCopyWith<$Res> {
  factory _$TransactionAnalysisCopyWith(_TransactionAnalysis value, $Res Function(_TransactionAnalysis) _then) = __$TransactionAnalysisCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'transaction_id') String transactionId, String amount, String type, String status,@JsonKey(name: 'from_user_id') String? fromUserId,@JsonKey(name: 'to_user_id') String? toUserId,@JsonKey(name: 'risk_score') double riskScore,@JsonKey(name: 'risk_factors') List<String> riskFactors,@JsonKey(name: 'is_suspicious') bool isSuspicious,@JsonKey(name: 'related_transactions') List<String> relatedTransactions,@JsonKey(name: 'time_since_account_creation') int timeSinceAccountCreation,@JsonKey(name: 'user_transaction_count') int userTransactionCount,@JsonKey(name: 'created_at') DateTime createdAt
});




}
/// @nodoc
class __$TransactionAnalysisCopyWithImpl<$Res>
    implements _$TransactionAnalysisCopyWith<$Res> {
  __$TransactionAnalysisCopyWithImpl(this._self, this._then);

  final _TransactionAnalysis _self;
  final $Res Function(_TransactionAnalysis) _then;

/// Create a copy of TransactionAnalysis
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? transactionId = null,Object? amount = null,Object? type = null,Object? status = null,Object? fromUserId = freezed,Object? toUserId = freezed,Object? riskScore = null,Object? riskFactors = null,Object? isSuspicious = null,Object? relatedTransactions = null,Object? timeSinceAccountCreation = null,Object? userTransactionCount = null,Object? createdAt = null,}) {
  return _then(_TransactionAnalysis(
transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,fromUserId: freezed == fromUserId ? _self.fromUserId : fromUserId // ignore: cast_nullable_to_non_nullable
as String?,toUserId: freezed == toUserId ? _self.toUserId : toUserId // ignore: cast_nullable_to_non_nullable
as String?,riskScore: null == riskScore ? _self.riskScore : riskScore // ignore: cast_nullable_to_non_nullable
as double,riskFactors: null == riskFactors ? _self._riskFactors : riskFactors // ignore: cast_nullable_to_non_nullable
as List<String>,isSuspicious: null == isSuspicious ? _self.isSuspicious : isSuspicious // ignore: cast_nullable_to_non_nullable
as bool,relatedTransactions: null == relatedTransactions ? _self._relatedTransactions : relatedTransactions // ignore: cast_nullable_to_non_nullable
as List<String>,timeSinceAccountCreation: null == timeSinceAccountCreation ? _self.timeSinceAccountCreation : timeSinceAccountCreation // ignore: cast_nullable_to_non_nullable
as int,userTransactionCount: null == userTransactionCount ? _self.userTransactionCount : userTransactionCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$AdminActionLog {

 String get id;@JsonKey(name: 'admin_user_id') String get adminUserId;@JsonKey(name: 'admin_email') String get adminEmail;@JsonKey(name: 'action_type') AdminActionType get actionType;@JsonKey(name: 'target_user_id') String? get targetUserId;@JsonKey(name: 'target_wallet_id') String? get targetWalletId;@JsonKey(name: 'target_transaction_id') String? get targetTransactionId; String get description; String get reason;@JsonKey(name: 'previous_value') String? get previousValue;@JsonKey(name: 'new_value') String? get newValue; Map<String, dynamic>? get metadata;@JsonKey(name: 'ip_address') String? get ipAddress;@JsonKey(name: 'user_agent') String? get userAgent;@JsonKey(name: 'created_at') DateTime get createdAt;
/// Create a copy of AdminActionLog
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AdminActionLogCopyWith<AdminActionLog> get copyWith => _$AdminActionLogCopyWithImpl<AdminActionLog>(this as AdminActionLog, _$identity);

  /// Serializes this AdminActionLog to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdminActionLog&&(identical(other.id, id) || other.id == id)&&(identical(other.adminUserId, adminUserId) || other.adminUserId == adminUserId)&&(identical(other.adminEmail, adminEmail) || other.adminEmail == adminEmail)&&(identical(other.actionType, actionType) || other.actionType == actionType)&&(identical(other.targetUserId, targetUserId) || other.targetUserId == targetUserId)&&(identical(other.targetWalletId, targetWalletId) || other.targetWalletId == targetWalletId)&&(identical(other.targetTransactionId, targetTransactionId) || other.targetTransactionId == targetTransactionId)&&(identical(other.description, description) || other.description == description)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.previousValue, previousValue) || other.previousValue == previousValue)&&(identical(other.newValue, newValue) || other.newValue == newValue)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.ipAddress, ipAddress) || other.ipAddress == ipAddress)&&(identical(other.userAgent, userAgent) || other.userAgent == userAgent)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,adminUserId,adminEmail,actionType,targetUserId,targetWalletId,targetTransactionId,description,reason,previousValue,newValue,const DeepCollectionEquality().hash(metadata),ipAddress,userAgent,createdAt);

@override
String toString() {
  return 'AdminActionLog(id: $id, adminUserId: $adminUserId, adminEmail: $adminEmail, actionType: $actionType, targetUserId: $targetUserId, targetWalletId: $targetWalletId, targetTransactionId: $targetTransactionId, description: $description, reason: $reason, previousValue: $previousValue, newValue: $newValue, metadata: $metadata, ipAddress: $ipAddress, userAgent: $userAgent, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $AdminActionLogCopyWith<$Res>  {
  factory $AdminActionLogCopyWith(AdminActionLog value, $Res Function(AdminActionLog) _then) = _$AdminActionLogCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'admin_user_id') String adminUserId,@JsonKey(name: 'admin_email') String adminEmail,@JsonKey(name: 'action_type') AdminActionType actionType,@JsonKey(name: 'target_user_id') String? targetUserId,@JsonKey(name: 'target_wallet_id') String? targetWalletId,@JsonKey(name: 'target_transaction_id') String? targetTransactionId, String description, String reason,@JsonKey(name: 'previous_value') String? previousValue,@JsonKey(name: 'new_value') String? newValue, Map<String, dynamic>? metadata,@JsonKey(name: 'ip_address') String? ipAddress,@JsonKey(name: 'user_agent') String? userAgent,@JsonKey(name: 'created_at') DateTime createdAt
});




}
/// @nodoc
class _$AdminActionLogCopyWithImpl<$Res>
    implements $AdminActionLogCopyWith<$Res> {
  _$AdminActionLogCopyWithImpl(this._self, this._then);

  final AdminActionLog _self;
  final $Res Function(AdminActionLog) _then;

/// Create a copy of AdminActionLog
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? adminUserId = null,Object? adminEmail = null,Object? actionType = null,Object? targetUserId = freezed,Object? targetWalletId = freezed,Object? targetTransactionId = freezed,Object? description = null,Object? reason = null,Object? previousValue = freezed,Object? newValue = freezed,Object? metadata = freezed,Object? ipAddress = freezed,Object? userAgent = freezed,Object? createdAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,adminUserId: null == adminUserId ? _self.adminUserId : adminUserId // ignore: cast_nullable_to_non_nullable
as String,adminEmail: null == adminEmail ? _self.adminEmail : adminEmail // ignore: cast_nullable_to_non_nullable
as String,actionType: null == actionType ? _self.actionType : actionType // ignore: cast_nullable_to_non_nullable
as AdminActionType,targetUserId: freezed == targetUserId ? _self.targetUserId : targetUserId // ignore: cast_nullable_to_non_nullable
as String?,targetWalletId: freezed == targetWalletId ? _self.targetWalletId : targetWalletId // ignore: cast_nullable_to_non_nullable
as String?,targetTransactionId: freezed == targetTransactionId ? _self.targetTransactionId : targetTransactionId // ignore: cast_nullable_to_non_nullable
as String?,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,previousValue: freezed == previousValue ? _self.previousValue : previousValue // ignore: cast_nullable_to_non_nullable
as String?,newValue: freezed == newValue ? _self.newValue : newValue // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,ipAddress: freezed == ipAddress ? _self.ipAddress : ipAddress // ignore: cast_nullable_to_non_nullable
as String?,userAgent: freezed == userAgent ? _self.userAgent : userAgent // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [AdminActionLog].
extension AdminActionLogPatterns on AdminActionLog {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AdminActionLog value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AdminActionLog() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AdminActionLog value)  $default,){
final _that = this;
switch (_that) {
case _AdminActionLog():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AdminActionLog value)?  $default,){
final _that = this;
switch (_that) {
case _AdminActionLog() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'admin_user_id')  String adminUserId, @JsonKey(name: 'admin_email')  String adminEmail, @JsonKey(name: 'action_type')  AdminActionType actionType, @JsonKey(name: 'target_user_id')  String? targetUserId, @JsonKey(name: 'target_wallet_id')  String? targetWalletId, @JsonKey(name: 'target_transaction_id')  String? targetTransactionId,  String description,  String reason, @JsonKey(name: 'previous_value')  String? previousValue, @JsonKey(name: 'new_value')  String? newValue,  Map<String, dynamic>? metadata, @JsonKey(name: 'ip_address')  String? ipAddress, @JsonKey(name: 'user_agent')  String? userAgent, @JsonKey(name: 'created_at')  DateTime createdAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AdminActionLog() when $default != null:
return $default(_that.id,_that.adminUserId,_that.adminEmail,_that.actionType,_that.targetUserId,_that.targetWalletId,_that.targetTransactionId,_that.description,_that.reason,_that.previousValue,_that.newValue,_that.metadata,_that.ipAddress,_that.userAgent,_that.createdAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'admin_user_id')  String adminUserId, @JsonKey(name: 'admin_email')  String adminEmail, @JsonKey(name: 'action_type')  AdminActionType actionType, @JsonKey(name: 'target_user_id')  String? targetUserId, @JsonKey(name: 'target_wallet_id')  String? targetWalletId, @JsonKey(name: 'target_transaction_id')  String? targetTransactionId,  String description,  String reason, @JsonKey(name: 'previous_value')  String? previousValue, @JsonKey(name: 'new_value')  String? newValue,  Map<String, dynamic>? metadata, @JsonKey(name: 'ip_address')  String? ipAddress, @JsonKey(name: 'user_agent')  String? userAgent, @JsonKey(name: 'created_at')  DateTime createdAt)  $default,) {final _that = this;
switch (_that) {
case _AdminActionLog():
return $default(_that.id,_that.adminUserId,_that.adminEmail,_that.actionType,_that.targetUserId,_that.targetWalletId,_that.targetTransactionId,_that.description,_that.reason,_that.previousValue,_that.newValue,_that.metadata,_that.ipAddress,_that.userAgent,_that.createdAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'admin_user_id')  String adminUserId, @JsonKey(name: 'admin_email')  String adminEmail, @JsonKey(name: 'action_type')  AdminActionType actionType, @JsonKey(name: 'target_user_id')  String? targetUserId, @JsonKey(name: 'target_wallet_id')  String? targetWalletId, @JsonKey(name: 'target_transaction_id')  String? targetTransactionId,  String description,  String reason, @JsonKey(name: 'previous_value')  String? previousValue, @JsonKey(name: 'new_value')  String? newValue,  Map<String, dynamic>? metadata, @JsonKey(name: 'ip_address')  String? ipAddress, @JsonKey(name: 'user_agent')  String? userAgent, @JsonKey(name: 'created_at')  DateTime createdAt)?  $default,) {final _that = this;
switch (_that) {
case _AdminActionLog() when $default != null:
return $default(_that.id,_that.adminUserId,_that.adminEmail,_that.actionType,_that.targetUserId,_that.targetWalletId,_that.targetTransactionId,_that.description,_that.reason,_that.previousValue,_that.newValue,_that.metadata,_that.ipAddress,_that.userAgent,_that.createdAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AdminActionLog implements AdminActionLog {
  const _AdminActionLog({required this.id, @JsonKey(name: 'admin_user_id') required this.adminUserId, @JsonKey(name: 'admin_email') required this.adminEmail, @JsonKey(name: 'action_type') required this.actionType, @JsonKey(name: 'target_user_id') this.targetUserId, @JsonKey(name: 'target_wallet_id') this.targetWalletId, @JsonKey(name: 'target_transaction_id') this.targetTransactionId, required this.description, required this.reason, @JsonKey(name: 'previous_value') this.previousValue, @JsonKey(name: 'new_value') this.newValue, final  Map<String, dynamic>? metadata, @JsonKey(name: 'ip_address') this.ipAddress, @JsonKey(name: 'user_agent') this.userAgent, @JsonKey(name: 'created_at') required this.createdAt}): _metadata = metadata;
  factory _AdminActionLog.fromJson(Map<String, dynamic> json) => _$AdminActionLogFromJson(json);

@override final  String id;
@override@JsonKey(name: 'admin_user_id') final  String adminUserId;
@override@JsonKey(name: 'admin_email') final  String adminEmail;
@override@JsonKey(name: 'action_type') final  AdminActionType actionType;
@override@JsonKey(name: 'target_user_id') final  String? targetUserId;
@override@JsonKey(name: 'target_wallet_id') final  String? targetWalletId;
@override@JsonKey(name: 'target_transaction_id') final  String? targetTransactionId;
@override final  String description;
@override final  String reason;
@override@JsonKey(name: 'previous_value') final  String? previousValue;
@override@JsonKey(name: 'new_value') final  String? newValue;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(name: 'ip_address') final  String? ipAddress;
@override@JsonKey(name: 'user_agent') final  String? userAgent;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;

/// Create a copy of AdminActionLog
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AdminActionLogCopyWith<_AdminActionLog> get copyWith => __$AdminActionLogCopyWithImpl<_AdminActionLog>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AdminActionLogToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AdminActionLog&&(identical(other.id, id) || other.id == id)&&(identical(other.adminUserId, adminUserId) || other.adminUserId == adminUserId)&&(identical(other.adminEmail, adminEmail) || other.adminEmail == adminEmail)&&(identical(other.actionType, actionType) || other.actionType == actionType)&&(identical(other.targetUserId, targetUserId) || other.targetUserId == targetUserId)&&(identical(other.targetWalletId, targetWalletId) || other.targetWalletId == targetWalletId)&&(identical(other.targetTransactionId, targetTransactionId) || other.targetTransactionId == targetTransactionId)&&(identical(other.description, description) || other.description == description)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.previousValue, previousValue) || other.previousValue == previousValue)&&(identical(other.newValue, newValue) || other.newValue == newValue)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.ipAddress, ipAddress) || other.ipAddress == ipAddress)&&(identical(other.userAgent, userAgent) || other.userAgent == userAgent)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,adminUserId,adminEmail,actionType,targetUserId,targetWalletId,targetTransactionId,description,reason,previousValue,newValue,const DeepCollectionEquality().hash(_metadata),ipAddress,userAgent,createdAt);

@override
String toString() {
  return 'AdminActionLog(id: $id, adminUserId: $adminUserId, adminEmail: $adminEmail, actionType: $actionType, targetUserId: $targetUserId, targetWalletId: $targetWalletId, targetTransactionId: $targetTransactionId, description: $description, reason: $reason, previousValue: $previousValue, newValue: $newValue, metadata: $metadata, ipAddress: $ipAddress, userAgent: $userAgent, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$AdminActionLogCopyWith<$Res> implements $AdminActionLogCopyWith<$Res> {
  factory _$AdminActionLogCopyWith(_AdminActionLog value, $Res Function(_AdminActionLog) _then) = __$AdminActionLogCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'admin_user_id') String adminUserId,@JsonKey(name: 'admin_email') String adminEmail,@JsonKey(name: 'action_type') AdminActionType actionType,@JsonKey(name: 'target_user_id') String? targetUserId,@JsonKey(name: 'target_wallet_id') String? targetWalletId,@JsonKey(name: 'target_transaction_id') String? targetTransactionId, String description, String reason,@JsonKey(name: 'previous_value') String? previousValue,@JsonKey(name: 'new_value') String? newValue, Map<String, dynamic>? metadata,@JsonKey(name: 'ip_address') String? ipAddress,@JsonKey(name: 'user_agent') String? userAgent,@JsonKey(name: 'created_at') DateTime createdAt
});




}
/// @nodoc
class __$AdminActionLogCopyWithImpl<$Res>
    implements _$AdminActionLogCopyWith<$Res> {
  __$AdminActionLogCopyWithImpl(this._self, this._then);

  final _AdminActionLog _self;
  final $Res Function(_AdminActionLog) _then;

/// Create a copy of AdminActionLog
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? adminUserId = null,Object? adminEmail = null,Object? actionType = null,Object? targetUserId = freezed,Object? targetWalletId = freezed,Object? targetTransactionId = freezed,Object? description = null,Object? reason = null,Object? previousValue = freezed,Object? newValue = freezed,Object? metadata = freezed,Object? ipAddress = freezed,Object? userAgent = freezed,Object? createdAt = null,}) {
  return _then(_AdminActionLog(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,adminUserId: null == adminUserId ? _self.adminUserId : adminUserId // ignore: cast_nullable_to_non_nullable
as String,adminEmail: null == adminEmail ? _self.adminEmail : adminEmail // ignore: cast_nullable_to_non_nullable
as String,actionType: null == actionType ? _self.actionType : actionType // ignore: cast_nullable_to_non_nullable
as AdminActionType,targetUserId: freezed == targetUserId ? _self.targetUserId : targetUserId // ignore: cast_nullable_to_non_nullable
as String?,targetWalletId: freezed == targetWalletId ? _self.targetWalletId : targetWalletId // ignore: cast_nullable_to_non_nullable
as String?,targetTransactionId: freezed == targetTransactionId ? _self.targetTransactionId : targetTransactionId // ignore: cast_nullable_to_non_nullable
as String?,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,previousValue: freezed == previousValue ? _self.previousValue : previousValue // ignore: cast_nullable_to_non_nullable
as String?,newValue: freezed == newValue ? _self.newValue : newValue // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,ipAddress: freezed == ipAddress ? _self.ipAddress : ipAddress // ignore: cast_nullable_to_non_nullable
as String?,userAgent: freezed == userAgent ? _self.userAgent : userAgent // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$AdminDashboard {

@JsonKey(name: 'financial_summary') FinancialSummary get financialSummary;@JsonKey(name: 'recent_alerts') List<FinancialAlert> get recentAlerts;@JsonKey(name: 'recent_transactions') List<TransactionAnalysis> get recentTransactions;@JsonKey(name: 'recent_admin_actions') List<AdminActionLog> get recentAdminActions;@JsonKey(name: 'pending_approvals') int get pendingApprovals;@JsonKey(name: 'system_health') Map<String, dynamic> get systemHealth;
/// Create a copy of AdminDashboard
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AdminDashboardCopyWith<AdminDashboard> get copyWith => _$AdminDashboardCopyWithImpl<AdminDashboard>(this as AdminDashboard, _$identity);

  /// Serializes this AdminDashboard to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdminDashboard&&(identical(other.financialSummary, financialSummary) || other.financialSummary == financialSummary)&&const DeepCollectionEquality().equals(other.recentAlerts, recentAlerts)&&const DeepCollectionEquality().equals(other.recentTransactions, recentTransactions)&&const DeepCollectionEquality().equals(other.recentAdminActions, recentAdminActions)&&(identical(other.pendingApprovals, pendingApprovals) || other.pendingApprovals == pendingApprovals)&&const DeepCollectionEquality().equals(other.systemHealth, systemHealth));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,financialSummary,const DeepCollectionEquality().hash(recentAlerts),const DeepCollectionEquality().hash(recentTransactions),const DeepCollectionEquality().hash(recentAdminActions),pendingApprovals,const DeepCollectionEquality().hash(systemHealth));

@override
String toString() {
  return 'AdminDashboard(financialSummary: $financialSummary, recentAlerts: $recentAlerts, recentTransactions: $recentTransactions, recentAdminActions: $recentAdminActions, pendingApprovals: $pendingApprovals, systemHealth: $systemHealth)';
}


}

/// @nodoc
abstract mixin class $AdminDashboardCopyWith<$Res>  {
  factory $AdminDashboardCopyWith(AdminDashboard value, $Res Function(AdminDashboard) _then) = _$AdminDashboardCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'financial_summary') FinancialSummary financialSummary,@JsonKey(name: 'recent_alerts') List<FinancialAlert> recentAlerts,@JsonKey(name: 'recent_transactions') List<TransactionAnalysis> recentTransactions,@JsonKey(name: 'recent_admin_actions') List<AdminActionLog> recentAdminActions,@JsonKey(name: 'pending_approvals') int pendingApprovals,@JsonKey(name: 'system_health') Map<String, dynamic> systemHealth
});


$FinancialSummaryCopyWith<$Res> get financialSummary;

}
/// @nodoc
class _$AdminDashboardCopyWithImpl<$Res>
    implements $AdminDashboardCopyWith<$Res> {
  _$AdminDashboardCopyWithImpl(this._self, this._then);

  final AdminDashboard _self;
  final $Res Function(AdminDashboard) _then;

/// Create a copy of AdminDashboard
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? financialSummary = null,Object? recentAlerts = null,Object? recentTransactions = null,Object? recentAdminActions = null,Object? pendingApprovals = null,Object? systemHealth = null,}) {
  return _then(_self.copyWith(
financialSummary: null == financialSummary ? _self.financialSummary : financialSummary // ignore: cast_nullable_to_non_nullable
as FinancialSummary,recentAlerts: null == recentAlerts ? _self.recentAlerts : recentAlerts // ignore: cast_nullable_to_non_nullable
as List<FinancialAlert>,recentTransactions: null == recentTransactions ? _self.recentTransactions : recentTransactions // ignore: cast_nullable_to_non_nullable
as List<TransactionAnalysis>,recentAdminActions: null == recentAdminActions ? _self.recentAdminActions : recentAdminActions // ignore: cast_nullable_to_non_nullable
as List<AdminActionLog>,pendingApprovals: null == pendingApprovals ? _self.pendingApprovals : pendingApprovals // ignore: cast_nullable_to_non_nullable
as int,systemHealth: null == systemHealth ? _self.systemHealth : systemHealth // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}
/// Create a copy of AdminDashboard
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FinancialSummaryCopyWith<$Res> get financialSummary {
  
  return $FinancialSummaryCopyWith<$Res>(_self.financialSummary, (value) {
    return _then(_self.copyWith(financialSummary: value));
  });
}
}


/// Adds pattern-matching-related methods to [AdminDashboard].
extension AdminDashboardPatterns on AdminDashboard {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AdminDashboard value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AdminDashboard() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AdminDashboard value)  $default,){
final _that = this;
switch (_that) {
case _AdminDashboard():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AdminDashboard value)?  $default,){
final _that = this;
switch (_that) {
case _AdminDashboard() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'financial_summary')  FinancialSummary financialSummary, @JsonKey(name: 'recent_alerts')  List<FinancialAlert> recentAlerts, @JsonKey(name: 'recent_transactions')  List<TransactionAnalysis> recentTransactions, @JsonKey(name: 'recent_admin_actions')  List<AdminActionLog> recentAdminActions, @JsonKey(name: 'pending_approvals')  int pendingApprovals, @JsonKey(name: 'system_health')  Map<String, dynamic> systemHealth)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AdminDashboard() when $default != null:
return $default(_that.financialSummary,_that.recentAlerts,_that.recentTransactions,_that.recentAdminActions,_that.pendingApprovals,_that.systemHealth);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'financial_summary')  FinancialSummary financialSummary, @JsonKey(name: 'recent_alerts')  List<FinancialAlert> recentAlerts, @JsonKey(name: 'recent_transactions')  List<TransactionAnalysis> recentTransactions, @JsonKey(name: 'recent_admin_actions')  List<AdminActionLog> recentAdminActions, @JsonKey(name: 'pending_approvals')  int pendingApprovals, @JsonKey(name: 'system_health')  Map<String, dynamic> systemHealth)  $default,) {final _that = this;
switch (_that) {
case _AdminDashboard():
return $default(_that.financialSummary,_that.recentAlerts,_that.recentTransactions,_that.recentAdminActions,_that.pendingApprovals,_that.systemHealth);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'financial_summary')  FinancialSummary financialSummary, @JsonKey(name: 'recent_alerts')  List<FinancialAlert> recentAlerts, @JsonKey(name: 'recent_transactions')  List<TransactionAnalysis> recentTransactions, @JsonKey(name: 'recent_admin_actions')  List<AdminActionLog> recentAdminActions, @JsonKey(name: 'pending_approvals')  int pendingApprovals, @JsonKey(name: 'system_health')  Map<String, dynamic> systemHealth)?  $default,) {final _that = this;
switch (_that) {
case _AdminDashboard() when $default != null:
return $default(_that.financialSummary,_that.recentAlerts,_that.recentTransactions,_that.recentAdminActions,_that.pendingApprovals,_that.systemHealth);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AdminDashboard implements AdminDashboard {
  const _AdminDashboard({@JsonKey(name: 'financial_summary') required this.financialSummary, @JsonKey(name: 'recent_alerts') required final  List<FinancialAlert> recentAlerts, @JsonKey(name: 'recent_transactions') required final  List<TransactionAnalysis> recentTransactions, @JsonKey(name: 'recent_admin_actions') required final  List<AdminActionLog> recentAdminActions, @JsonKey(name: 'pending_approvals') required this.pendingApprovals, @JsonKey(name: 'system_health') required final  Map<String, dynamic> systemHealth}): _recentAlerts = recentAlerts,_recentTransactions = recentTransactions,_recentAdminActions = recentAdminActions,_systemHealth = systemHealth;
  factory _AdminDashboard.fromJson(Map<String, dynamic> json) => _$AdminDashboardFromJson(json);

@override@JsonKey(name: 'financial_summary') final  FinancialSummary financialSummary;
 final  List<FinancialAlert> _recentAlerts;
@override@JsonKey(name: 'recent_alerts') List<FinancialAlert> get recentAlerts {
  if (_recentAlerts is EqualUnmodifiableListView) return _recentAlerts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_recentAlerts);
}

 final  List<TransactionAnalysis> _recentTransactions;
@override@JsonKey(name: 'recent_transactions') List<TransactionAnalysis> get recentTransactions {
  if (_recentTransactions is EqualUnmodifiableListView) return _recentTransactions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_recentTransactions);
}

 final  List<AdminActionLog> _recentAdminActions;
@override@JsonKey(name: 'recent_admin_actions') List<AdminActionLog> get recentAdminActions {
  if (_recentAdminActions is EqualUnmodifiableListView) return _recentAdminActions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_recentAdminActions);
}

@override@JsonKey(name: 'pending_approvals') final  int pendingApprovals;
 final  Map<String, dynamic> _systemHealth;
@override@JsonKey(name: 'system_health') Map<String, dynamic> get systemHealth {
  if (_systemHealth is EqualUnmodifiableMapView) return _systemHealth;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_systemHealth);
}


/// Create a copy of AdminDashboard
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AdminDashboardCopyWith<_AdminDashboard> get copyWith => __$AdminDashboardCopyWithImpl<_AdminDashboard>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AdminDashboardToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AdminDashboard&&(identical(other.financialSummary, financialSummary) || other.financialSummary == financialSummary)&&const DeepCollectionEquality().equals(other._recentAlerts, _recentAlerts)&&const DeepCollectionEquality().equals(other._recentTransactions, _recentTransactions)&&const DeepCollectionEquality().equals(other._recentAdminActions, _recentAdminActions)&&(identical(other.pendingApprovals, pendingApprovals) || other.pendingApprovals == pendingApprovals)&&const DeepCollectionEquality().equals(other._systemHealth, _systemHealth));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,financialSummary,const DeepCollectionEquality().hash(_recentAlerts),const DeepCollectionEquality().hash(_recentTransactions),const DeepCollectionEquality().hash(_recentAdminActions),pendingApprovals,const DeepCollectionEquality().hash(_systemHealth));

@override
String toString() {
  return 'AdminDashboard(financialSummary: $financialSummary, recentAlerts: $recentAlerts, recentTransactions: $recentTransactions, recentAdminActions: $recentAdminActions, pendingApprovals: $pendingApprovals, systemHealth: $systemHealth)';
}


}

/// @nodoc
abstract mixin class _$AdminDashboardCopyWith<$Res> implements $AdminDashboardCopyWith<$Res> {
  factory _$AdminDashboardCopyWith(_AdminDashboard value, $Res Function(_AdminDashboard) _then) = __$AdminDashboardCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'financial_summary') FinancialSummary financialSummary,@JsonKey(name: 'recent_alerts') List<FinancialAlert> recentAlerts,@JsonKey(name: 'recent_transactions') List<TransactionAnalysis> recentTransactions,@JsonKey(name: 'recent_admin_actions') List<AdminActionLog> recentAdminActions,@JsonKey(name: 'pending_approvals') int pendingApprovals,@JsonKey(name: 'system_health') Map<String, dynamic> systemHealth
});


@override $FinancialSummaryCopyWith<$Res> get financialSummary;

}
/// @nodoc
class __$AdminDashboardCopyWithImpl<$Res>
    implements _$AdminDashboardCopyWith<$Res> {
  __$AdminDashboardCopyWithImpl(this._self, this._then);

  final _AdminDashboard _self;
  final $Res Function(_AdminDashboard) _then;

/// Create a copy of AdminDashboard
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? financialSummary = null,Object? recentAlerts = null,Object? recentTransactions = null,Object? recentAdminActions = null,Object? pendingApprovals = null,Object? systemHealth = null,}) {
  return _then(_AdminDashboard(
financialSummary: null == financialSummary ? _self.financialSummary : financialSummary // ignore: cast_nullable_to_non_nullable
as FinancialSummary,recentAlerts: null == recentAlerts ? _self._recentAlerts : recentAlerts // ignore: cast_nullable_to_non_nullable
as List<FinancialAlert>,recentTransactions: null == recentTransactions ? _self._recentTransactions : recentTransactions // ignore: cast_nullable_to_non_nullable
as List<TransactionAnalysis>,recentAdminActions: null == recentAdminActions ? _self._recentAdminActions : recentAdminActions // ignore: cast_nullable_to_non_nullable
as List<AdminActionLog>,pendingApprovals: null == pendingApprovals ? _self.pendingApprovals : pendingApprovals // ignore: cast_nullable_to_non_nullable
as int,systemHealth: null == systemHealth ? _self._systemHealth : systemHealth // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

/// Create a copy of AdminDashboard
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FinancialSummaryCopyWith<$Res> get financialSummary {
  
  return $FinancialSummaryCopyWith<$Res>(_self.financialSummary, (value) {
    return _then(_self.copyWith(financialSummary: value));
  });
}
}


/// @nodoc
mixin _$GenerateReportRequest {

@JsonKey(name: 'report_type') ReportType get reportType; String get title; String? get description;@JsonKey(name: 'period_start') DateTime get periodStart;@JsonKey(name: 'period_end') DateTime get periodEnd;@JsonKey(name: 'generated_by') String get generatedBy; Map<String, dynamic>? get filters;
/// Create a copy of GenerateReportRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GenerateReportRequestCopyWith<GenerateReportRequest> get copyWith => _$GenerateReportRequestCopyWithImpl<GenerateReportRequest>(this as GenerateReportRequest, _$identity);

  /// Serializes this GenerateReportRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GenerateReportRequest&&(identical(other.reportType, reportType) || other.reportType == reportType)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.periodStart, periodStart) || other.periodStart == periodStart)&&(identical(other.periodEnd, periodEnd) || other.periodEnd == periodEnd)&&(identical(other.generatedBy, generatedBy) || other.generatedBy == generatedBy)&&const DeepCollectionEquality().equals(other.filters, filters));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,reportType,title,description,periodStart,periodEnd,generatedBy,const DeepCollectionEquality().hash(filters));

@override
String toString() {
  return 'GenerateReportRequest(reportType: $reportType, title: $title, description: $description, periodStart: $periodStart, periodEnd: $periodEnd, generatedBy: $generatedBy, filters: $filters)';
}


}

/// @nodoc
abstract mixin class $GenerateReportRequestCopyWith<$Res>  {
  factory $GenerateReportRequestCopyWith(GenerateReportRequest value, $Res Function(GenerateReportRequest) _then) = _$GenerateReportRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'report_type') ReportType reportType, String title, String? description,@JsonKey(name: 'period_start') DateTime periodStart,@JsonKey(name: 'period_end') DateTime periodEnd,@JsonKey(name: 'generated_by') String generatedBy, Map<String, dynamic>? filters
});




}
/// @nodoc
class _$GenerateReportRequestCopyWithImpl<$Res>
    implements $GenerateReportRequestCopyWith<$Res> {
  _$GenerateReportRequestCopyWithImpl(this._self, this._then);

  final GenerateReportRequest _self;
  final $Res Function(GenerateReportRequest) _then;

/// Create a copy of GenerateReportRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? reportType = null,Object? title = null,Object? description = freezed,Object? periodStart = null,Object? periodEnd = null,Object? generatedBy = null,Object? filters = freezed,}) {
  return _then(_self.copyWith(
reportType: null == reportType ? _self.reportType : reportType // ignore: cast_nullable_to_non_nullable
as ReportType,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,periodStart: null == periodStart ? _self.periodStart : periodStart // ignore: cast_nullable_to_non_nullable
as DateTime,periodEnd: null == periodEnd ? _self.periodEnd : periodEnd // ignore: cast_nullable_to_non_nullable
as DateTime,generatedBy: null == generatedBy ? _self.generatedBy : generatedBy // ignore: cast_nullable_to_non_nullable
as String,filters: freezed == filters ? _self.filters : filters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [GenerateReportRequest].
extension GenerateReportRequestPatterns on GenerateReportRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _GenerateReportRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _GenerateReportRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _GenerateReportRequest value)  $default,){
final _that = this;
switch (_that) {
case _GenerateReportRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _GenerateReportRequest value)?  $default,){
final _that = this;
switch (_that) {
case _GenerateReportRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'report_type')  ReportType reportType,  String title,  String? description, @JsonKey(name: 'period_start')  DateTime periodStart, @JsonKey(name: 'period_end')  DateTime periodEnd, @JsonKey(name: 'generated_by')  String generatedBy,  Map<String, dynamic>? filters)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _GenerateReportRequest() when $default != null:
return $default(_that.reportType,_that.title,_that.description,_that.periodStart,_that.periodEnd,_that.generatedBy,_that.filters);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'report_type')  ReportType reportType,  String title,  String? description, @JsonKey(name: 'period_start')  DateTime periodStart, @JsonKey(name: 'period_end')  DateTime periodEnd, @JsonKey(name: 'generated_by')  String generatedBy,  Map<String, dynamic>? filters)  $default,) {final _that = this;
switch (_that) {
case _GenerateReportRequest():
return $default(_that.reportType,_that.title,_that.description,_that.periodStart,_that.periodEnd,_that.generatedBy,_that.filters);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'report_type')  ReportType reportType,  String title,  String? description, @JsonKey(name: 'period_start')  DateTime periodStart, @JsonKey(name: 'period_end')  DateTime periodEnd, @JsonKey(name: 'generated_by')  String generatedBy,  Map<String, dynamic>? filters)?  $default,) {final _that = this;
switch (_that) {
case _GenerateReportRequest() when $default != null:
return $default(_that.reportType,_that.title,_that.description,_that.periodStart,_that.periodEnd,_that.generatedBy,_that.filters);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _GenerateReportRequest implements GenerateReportRequest {
  const _GenerateReportRequest({@JsonKey(name: 'report_type') required this.reportType, required this.title, this.description, @JsonKey(name: 'period_start') required this.periodStart, @JsonKey(name: 'period_end') required this.periodEnd, @JsonKey(name: 'generated_by') required this.generatedBy, final  Map<String, dynamic>? filters}): _filters = filters;
  factory _GenerateReportRequest.fromJson(Map<String, dynamic> json) => _$GenerateReportRequestFromJson(json);

@override@JsonKey(name: 'report_type') final  ReportType reportType;
@override final  String title;
@override final  String? description;
@override@JsonKey(name: 'period_start') final  DateTime periodStart;
@override@JsonKey(name: 'period_end') final  DateTime periodEnd;
@override@JsonKey(name: 'generated_by') final  String generatedBy;
 final  Map<String, dynamic>? _filters;
@override Map<String, dynamic>? get filters {
  final value = _filters;
  if (value == null) return null;
  if (_filters is EqualUnmodifiableMapView) return _filters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of GenerateReportRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GenerateReportRequestCopyWith<_GenerateReportRequest> get copyWith => __$GenerateReportRequestCopyWithImpl<_GenerateReportRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GenerateReportRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GenerateReportRequest&&(identical(other.reportType, reportType) || other.reportType == reportType)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.periodStart, periodStart) || other.periodStart == periodStart)&&(identical(other.periodEnd, periodEnd) || other.periodEnd == periodEnd)&&(identical(other.generatedBy, generatedBy) || other.generatedBy == generatedBy)&&const DeepCollectionEquality().equals(other._filters, _filters));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,reportType,title,description,periodStart,periodEnd,generatedBy,const DeepCollectionEquality().hash(_filters));

@override
String toString() {
  return 'GenerateReportRequest(reportType: $reportType, title: $title, description: $description, periodStart: $periodStart, periodEnd: $periodEnd, generatedBy: $generatedBy, filters: $filters)';
}


}

/// @nodoc
abstract mixin class _$GenerateReportRequestCopyWith<$Res> implements $GenerateReportRequestCopyWith<$Res> {
  factory _$GenerateReportRequestCopyWith(_GenerateReportRequest value, $Res Function(_GenerateReportRequest) _then) = __$GenerateReportRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'report_type') ReportType reportType, String title, String? description,@JsonKey(name: 'period_start') DateTime periodStart,@JsonKey(name: 'period_end') DateTime periodEnd,@JsonKey(name: 'generated_by') String generatedBy, Map<String, dynamic>? filters
});




}
/// @nodoc
class __$GenerateReportRequestCopyWithImpl<$Res>
    implements _$GenerateReportRequestCopyWith<$Res> {
  __$GenerateReportRequestCopyWithImpl(this._self, this._then);

  final _GenerateReportRequest _self;
  final $Res Function(_GenerateReportRequest) _then;

/// Create a copy of GenerateReportRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? reportType = null,Object? title = null,Object? description = freezed,Object? periodStart = null,Object? periodEnd = null,Object? generatedBy = null,Object? filters = freezed,}) {
  return _then(_GenerateReportRequest(
reportType: null == reportType ? _self.reportType : reportType // ignore: cast_nullable_to_non_nullable
as ReportType,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,periodStart: null == periodStart ? _self.periodStart : periodStart // ignore: cast_nullable_to_non_nullable
as DateTime,periodEnd: null == periodEnd ? _self.periodEnd : periodEnd // ignore: cast_nullable_to_non_nullable
as DateTime,generatedBy: null == generatedBy ? _self.generatedBy : generatedBy // ignore: cast_nullable_to_non_nullable
as String,filters: freezed == filters ? _self._filters : filters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$AdminReport {

 String get id;@JsonKey(name: 'report_type') ReportType get reportType; String get title; String? get description;@JsonKey(name: 'period_start') DateTime get periodStart;@JsonKey(name: 'period_end') DateTime get periodEnd;@JsonKey(name: 'report_data') Map<String, dynamic> get reportData;@JsonKey(name: 'summary_metrics') Map<String, dynamic>? get summaryMetrics;@JsonKey(name: 'generated_by') String get generatedBy;@JsonKey(name: 'generation_time') DateTime get generationTime;@JsonKey(name: 'file_path') String? get filePath;@JsonKey(name: 'file_size') int? get fileSize;@JsonKey(name: 'export_format') String? get exportFormat;@JsonKey(name: 'is_exported') bool get isExported;@JsonKey(name: 'export_count') int get exportCount;@JsonKey(name: 'last_exported_at') DateTime? get lastExportedAt;
/// Create a copy of AdminReport
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AdminReportCopyWith<AdminReport> get copyWith => _$AdminReportCopyWithImpl<AdminReport>(this as AdminReport, _$identity);

  /// Serializes this AdminReport to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdminReport&&(identical(other.id, id) || other.id == id)&&(identical(other.reportType, reportType) || other.reportType == reportType)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.periodStart, periodStart) || other.periodStart == periodStart)&&(identical(other.periodEnd, periodEnd) || other.periodEnd == periodEnd)&&const DeepCollectionEquality().equals(other.reportData, reportData)&&const DeepCollectionEquality().equals(other.summaryMetrics, summaryMetrics)&&(identical(other.generatedBy, generatedBy) || other.generatedBy == generatedBy)&&(identical(other.generationTime, generationTime) || other.generationTime == generationTime)&&(identical(other.filePath, filePath) || other.filePath == filePath)&&(identical(other.fileSize, fileSize) || other.fileSize == fileSize)&&(identical(other.exportFormat, exportFormat) || other.exportFormat == exportFormat)&&(identical(other.isExported, isExported) || other.isExported == isExported)&&(identical(other.exportCount, exportCount) || other.exportCount == exportCount)&&(identical(other.lastExportedAt, lastExportedAt) || other.lastExportedAt == lastExportedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,reportType,title,description,periodStart,periodEnd,const DeepCollectionEquality().hash(reportData),const DeepCollectionEquality().hash(summaryMetrics),generatedBy,generationTime,filePath,fileSize,exportFormat,isExported,exportCount,lastExportedAt);

@override
String toString() {
  return 'AdminReport(id: $id, reportType: $reportType, title: $title, description: $description, periodStart: $periodStart, periodEnd: $periodEnd, reportData: $reportData, summaryMetrics: $summaryMetrics, generatedBy: $generatedBy, generationTime: $generationTime, filePath: $filePath, fileSize: $fileSize, exportFormat: $exportFormat, isExported: $isExported, exportCount: $exportCount, lastExportedAt: $lastExportedAt)';
}


}

/// @nodoc
abstract mixin class $AdminReportCopyWith<$Res>  {
  factory $AdminReportCopyWith(AdminReport value, $Res Function(AdminReport) _then) = _$AdminReportCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'report_type') ReportType reportType, String title, String? description,@JsonKey(name: 'period_start') DateTime periodStart,@JsonKey(name: 'period_end') DateTime periodEnd,@JsonKey(name: 'report_data') Map<String, dynamic> reportData,@JsonKey(name: 'summary_metrics') Map<String, dynamic>? summaryMetrics,@JsonKey(name: 'generated_by') String generatedBy,@JsonKey(name: 'generation_time') DateTime generationTime,@JsonKey(name: 'file_path') String? filePath,@JsonKey(name: 'file_size') int? fileSize,@JsonKey(name: 'export_format') String? exportFormat,@JsonKey(name: 'is_exported') bool isExported,@JsonKey(name: 'export_count') int exportCount,@JsonKey(name: 'last_exported_at') DateTime? lastExportedAt
});




}
/// @nodoc
class _$AdminReportCopyWithImpl<$Res>
    implements $AdminReportCopyWith<$Res> {
  _$AdminReportCopyWithImpl(this._self, this._then);

  final AdminReport _self;
  final $Res Function(AdminReport) _then;

/// Create a copy of AdminReport
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? reportType = null,Object? title = null,Object? description = freezed,Object? periodStart = null,Object? periodEnd = null,Object? reportData = null,Object? summaryMetrics = freezed,Object? generatedBy = null,Object? generationTime = null,Object? filePath = freezed,Object? fileSize = freezed,Object? exportFormat = freezed,Object? isExported = null,Object? exportCount = null,Object? lastExportedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,reportType: null == reportType ? _self.reportType : reportType // ignore: cast_nullable_to_non_nullable
as ReportType,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,periodStart: null == periodStart ? _self.periodStart : periodStart // ignore: cast_nullable_to_non_nullable
as DateTime,periodEnd: null == periodEnd ? _self.periodEnd : periodEnd // ignore: cast_nullable_to_non_nullable
as DateTime,reportData: null == reportData ? _self.reportData : reportData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,summaryMetrics: freezed == summaryMetrics ? _self.summaryMetrics : summaryMetrics // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,generatedBy: null == generatedBy ? _self.generatedBy : generatedBy // ignore: cast_nullable_to_non_nullable
as String,generationTime: null == generationTime ? _self.generationTime : generationTime // ignore: cast_nullable_to_non_nullable
as DateTime,filePath: freezed == filePath ? _self.filePath : filePath // ignore: cast_nullable_to_non_nullable
as String?,fileSize: freezed == fileSize ? _self.fileSize : fileSize // ignore: cast_nullable_to_non_nullable
as int?,exportFormat: freezed == exportFormat ? _self.exportFormat : exportFormat // ignore: cast_nullable_to_non_nullable
as String?,isExported: null == isExported ? _self.isExported : isExported // ignore: cast_nullable_to_non_nullable
as bool,exportCount: null == exportCount ? _self.exportCount : exportCount // ignore: cast_nullable_to_non_nullable
as int,lastExportedAt: freezed == lastExportedAt ? _self.lastExportedAt : lastExportedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [AdminReport].
extension AdminReportPatterns on AdminReport {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AdminReport value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AdminReport() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AdminReport value)  $default,){
final _that = this;
switch (_that) {
case _AdminReport():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AdminReport value)?  $default,){
final _that = this;
switch (_that) {
case _AdminReport() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'report_type')  ReportType reportType,  String title,  String? description, @JsonKey(name: 'period_start')  DateTime periodStart, @JsonKey(name: 'period_end')  DateTime periodEnd, @JsonKey(name: 'report_data')  Map<String, dynamic> reportData, @JsonKey(name: 'summary_metrics')  Map<String, dynamic>? summaryMetrics, @JsonKey(name: 'generated_by')  String generatedBy, @JsonKey(name: 'generation_time')  DateTime generationTime, @JsonKey(name: 'file_path')  String? filePath, @JsonKey(name: 'file_size')  int? fileSize, @JsonKey(name: 'export_format')  String? exportFormat, @JsonKey(name: 'is_exported')  bool isExported, @JsonKey(name: 'export_count')  int exportCount, @JsonKey(name: 'last_exported_at')  DateTime? lastExportedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AdminReport() when $default != null:
return $default(_that.id,_that.reportType,_that.title,_that.description,_that.periodStart,_that.periodEnd,_that.reportData,_that.summaryMetrics,_that.generatedBy,_that.generationTime,_that.filePath,_that.fileSize,_that.exportFormat,_that.isExported,_that.exportCount,_that.lastExportedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'report_type')  ReportType reportType,  String title,  String? description, @JsonKey(name: 'period_start')  DateTime periodStart, @JsonKey(name: 'period_end')  DateTime periodEnd, @JsonKey(name: 'report_data')  Map<String, dynamic> reportData, @JsonKey(name: 'summary_metrics')  Map<String, dynamic>? summaryMetrics, @JsonKey(name: 'generated_by')  String generatedBy, @JsonKey(name: 'generation_time')  DateTime generationTime, @JsonKey(name: 'file_path')  String? filePath, @JsonKey(name: 'file_size')  int? fileSize, @JsonKey(name: 'export_format')  String? exportFormat, @JsonKey(name: 'is_exported')  bool isExported, @JsonKey(name: 'export_count')  int exportCount, @JsonKey(name: 'last_exported_at')  DateTime? lastExportedAt)  $default,) {final _that = this;
switch (_that) {
case _AdminReport():
return $default(_that.id,_that.reportType,_that.title,_that.description,_that.periodStart,_that.periodEnd,_that.reportData,_that.summaryMetrics,_that.generatedBy,_that.generationTime,_that.filePath,_that.fileSize,_that.exportFormat,_that.isExported,_that.exportCount,_that.lastExportedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'report_type')  ReportType reportType,  String title,  String? description, @JsonKey(name: 'period_start')  DateTime periodStart, @JsonKey(name: 'period_end')  DateTime periodEnd, @JsonKey(name: 'report_data')  Map<String, dynamic> reportData, @JsonKey(name: 'summary_metrics')  Map<String, dynamic>? summaryMetrics, @JsonKey(name: 'generated_by')  String generatedBy, @JsonKey(name: 'generation_time')  DateTime generationTime, @JsonKey(name: 'file_path')  String? filePath, @JsonKey(name: 'file_size')  int? fileSize, @JsonKey(name: 'export_format')  String? exportFormat, @JsonKey(name: 'is_exported')  bool isExported, @JsonKey(name: 'export_count')  int exportCount, @JsonKey(name: 'last_exported_at')  DateTime? lastExportedAt)?  $default,) {final _that = this;
switch (_that) {
case _AdminReport() when $default != null:
return $default(_that.id,_that.reportType,_that.title,_that.description,_that.periodStart,_that.periodEnd,_that.reportData,_that.summaryMetrics,_that.generatedBy,_that.generationTime,_that.filePath,_that.fileSize,_that.exportFormat,_that.isExported,_that.exportCount,_that.lastExportedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AdminReport implements AdminReport {
  const _AdminReport({required this.id, @JsonKey(name: 'report_type') required this.reportType, required this.title, this.description, @JsonKey(name: 'period_start') required this.periodStart, @JsonKey(name: 'period_end') required this.periodEnd, @JsonKey(name: 'report_data') required final  Map<String, dynamic> reportData, @JsonKey(name: 'summary_metrics') final  Map<String, dynamic>? summaryMetrics, @JsonKey(name: 'generated_by') required this.generatedBy, @JsonKey(name: 'generation_time') required this.generationTime, @JsonKey(name: 'file_path') this.filePath, @JsonKey(name: 'file_size') this.fileSize, @JsonKey(name: 'export_format') this.exportFormat, @JsonKey(name: 'is_exported') required this.isExported, @JsonKey(name: 'export_count') required this.exportCount, @JsonKey(name: 'last_exported_at') this.lastExportedAt}): _reportData = reportData,_summaryMetrics = summaryMetrics;
  factory _AdminReport.fromJson(Map<String, dynamic> json) => _$AdminReportFromJson(json);

@override final  String id;
@override@JsonKey(name: 'report_type') final  ReportType reportType;
@override final  String title;
@override final  String? description;
@override@JsonKey(name: 'period_start') final  DateTime periodStart;
@override@JsonKey(name: 'period_end') final  DateTime periodEnd;
 final  Map<String, dynamic> _reportData;
@override@JsonKey(name: 'report_data') Map<String, dynamic> get reportData {
  if (_reportData is EqualUnmodifiableMapView) return _reportData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_reportData);
}

 final  Map<String, dynamic>? _summaryMetrics;
@override@JsonKey(name: 'summary_metrics') Map<String, dynamic>? get summaryMetrics {
  final value = _summaryMetrics;
  if (value == null) return null;
  if (_summaryMetrics is EqualUnmodifiableMapView) return _summaryMetrics;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override@JsonKey(name: 'generated_by') final  String generatedBy;
@override@JsonKey(name: 'generation_time') final  DateTime generationTime;
@override@JsonKey(name: 'file_path') final  String? filePath;
@override@JsonKey(name: 'file_size') final  int? fileSize;
@override@JsonKey(name: 'export_format') final  String? exportFormat;
@override@JsonKey(name: 'is_exported') final  bool isExported;
@override@JsonKey(name: 'export_count') final  int exportCount;
@override@JsonKey(name: 'last_exported_at') final  DateTime? lastExportedAt;

/// Create a copy of AdminReport
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AdminReportCopyWith<_AdminReport> get copyWith => __$AdminReportCopyWithImpl<_AdminReport>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AdminReportToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AdminReport&&(identical(other.id, id) || other.id == id)&&(identical(other.reportType, reportType) || other.reportType == reportType)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.periodStart, periodStart) || other.periodStart == periodStart)&&(identical(other.periodEnd, periodEnd) || other.periodEnd == periodEnd)&&const DeepCollectionEquality().equals(other._reportData, _reportData)&&const DeepCollectionEquality().equals(other._summaryMetrics, _summaryMetrics)&&(identical(other.generatedBy, generatedBy) || other.generatedBy == generatedBy)&&(identical(other.generationTime, generationTime) || other.generationTime == generationTime)&&(identical(other.filePath, filePath) || other.filePath == filePath)&&(identical(other.fileSize, fileSize) || other.fileSize == fileSize)&&(identical(other.exportFormat, exportFormat) || other.exportFormat == exportFormat)&&(identical(other.isExported, isExported) || other.isExported == isExported)&&(identical(other.exportCount, exportCount) || other.exportCount == exportCount)&&(identical(other.lastExportedAt, lastExportedAt) || other.lastExportedAt == lastExportedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,reportType,title,description,periodStart,periodEnd,const DeepCollectionEquality().hash(_reportData),const DeepCollectionEquality().hash(_summaryMetrics),generatedBy,generationTime,filePath,fileSize,exportFormat,isExported,exportCount,lastExportedAt);

@override
String toString() {
  return 'AdminReport(id: $id, reportType: $reportType, title: $title, description: $description, periodStart: $periodStart, periodEnd: $periodEnd, reportData: $reportData, summaryMetrics: $summaryMetrics, generatedBy: $generatedBy, generationTime: $generationTime, filePath: $filePath, fileSize: $fileSize, exportFormat: $exportFormat, isExported: $isExported, exportCount: $exportCount, lastExportedAt: $lastExportedAt)';
}


}

/// @nodoc
abstract mixin class _$AdminReportCopyWith<$Res> implements $AdminReportCopyWith<$Res> {
  factory _$AdminReportCopyWith(_AdminReport value, $Res Function(_AdminReport) _then) = __$AdminReportCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'report_type') ReportType reportType, String title, String? description,@JsonKey(name: 'period_start') DateTime periodStart,@JsonKey(name: 'period_end') DateTime periodEnd,@JsonKey(name: 'report_data') Map<String, dynamic> reportData,@JsonKey(name: 'summary_metrics') Map<String, dynamic>? summaryMetrics,@JsonKey(name: 'generated_by') String generatedBy,@JsonKey(name: 'generation_time') DateTime generationTime,@JsonKey(name: 'file_path') String? filePath,@JsonKey(name: 'file_size') int? fileSize,@JsonKey(name: 'export_format') String? exportFormat,@JsonKey(name: 'is_exported') bool isExported,@JsonKey(name: 'export_count') int exportCount,@JsonKey(name: 'last_exported_at') DateTime? lastExportedAt
});




}
/// @nodoc
class __$AdminReportCopyWithImpl<$Res>
    implements _$AdminReportCopyWith<$Res> {
  __$AdminReportCopyWithImpl(this._self, this._then);

  final _AdminReport _self;
  final $Res Function(_AdminReport) _then;

/// Create a copy of AdminReport
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? reportType = null,Object? title = null,Object? description = freezed,Object? periodStart = null,Object? periodEnd = null,Object? reportData = null,Object? summaryMetrics = freezed,Object? generatedBy = null,Object? generationTime = null,Object? filePath = freezed,Object? fileSize = freezed,Object? exportFormat = freezed,Object? isExported = null,Object? exportCount = null,Object? lastExportedAt = freezed,}) {
  return _then(_AdminReport(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,reportType: null == reportType ? _self.reportType : reportType // ignore: cast_nullable_to_non_nullable
as ReportType,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,periodStart: null == periodStart ? _self.periodStart : periodStart // ignore: cast_nullable_to_non_nullable
as DateTime,periodEnd: null == periodEnd ? _self.periodEnd : periodEnd // ignore: cast_nullable_to_non_nullable
as DateTime,reportData: null == reportData ? _self._reportData : reportData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,summaryMetrics: freezed == summaryMetrics ? _self._summaryMetrics : summaryMetrics // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,generatedBy: null == generatedBy ? _self.generatedBy : generatedBy // ignore: cast_nullable_to_non_nullable
as String,generationTime: null == generationTime ? _self.generationTime : generationTime // ignore: cast_nullable_to_non_nullable
as DateTime,filePath: freezed == filePath ? _self.filePath : filePath // ignore: cast_nullable_to_non_nullable
as String?,fileSize: freezed == fileSize ? _self.fileSize : fileSize // ignore: cast_nullable_to_non_nullable
as int?,exportFormat: freezed == exportFormat ? _self.exportFormat : exportFormat // ignore: cast_nullable_to_non_nullable
as String?,isExported: null == isExported ? _self.isExported : isExported // ignore: cast_nullable_to_non_nullable
as bool,exportCount: null == exportCount ? _self.exportCount : exportCount // ignore: cast_nullable_to_non_nullable
as int,lastExportedAt: freezed == lastExportedAt ? _self.lastExportedAt : lastExportedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$WalletListItem {

@JsonKey(name: 'wallet_id') String get walletId;@JsonKey(name: 'user_email') String get userEmail; String get balance;@JsonKey(name: 'is_active') bool get isActive;@JsonKey(name: 'is_locked') bool get isLocked;@JsonKey(name: 'created_at') DateTime get createdAt;
/// Create a copy of WalletListItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletListItemCopyWith<WalletListItem> get copyWith => _$WalletListItemCopyWithImpl<WalletListItem>(this as WalletListItem, _$identity);

  /// Serializes this WalletListItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletListItem&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.userEmail, userEmail) || other.userEmail == userEmail)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isLocked, isLocked) || other.isLocked == isLocked)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,walletId,userEmail,balance,isActive,isLocked,createdAt);

@override
String toString() {
  return 'WalletListItem(walletId: $walletId, userEmail: $userEmail, balance: $balance, isActive: $isActive, isLocked: $isLocked, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class $WalletListItemCopyWith<$Res>  {
  factory $WalletListItemCopyWith(WalletListItem value, $Res Function(WalletListItem) _then) = _$WalletListItemCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'user_email') String userEmail, String balance,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'is_locked') bool isLocked,@JsonKey(name: 'created_at') DateTime createdAt
});




}
/// @nodoc
class _$WalletListItemCopyWithImpl<$Res>
    implements $WalletListItemCopyWith<$Res> {
  _$WalletListItemCopyWithImpl(this._self, this._then);

  final WalletListItem _self;
  final $Res Function(WalletListItem) _then;

/// Create a copy of WalletListItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? walletId = null,Object? userEmail = null,Object? balance = null,Object? isActive = null,Object? isLocked = null,Object? createdAt = null,}) {
  return _then(_self.copyWith(
walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,userEmail: null == userEmail ? _self.userEmail : userEmail // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isLocked: null == isLocked ? _self.isLocked : isLocked // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletListItem].
extension WalletListItemPatterns on WalletListItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletListItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletListItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletListItem value)  $default,){
final _that = this;
switch (_that) {
case _WalletListItem():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletListItem value)?  $default,){
final _that = this;
switch (_that) {
case _WalletListItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_email')  String userEmail,  String balance, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'is_locked')  bool isLocked, @JsonKey(name: 'created_at')  DateTime createdAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletListItem() when $default != null:
return $default(_that.walletId,_that.userEmail,_that.balance,_that.isActive,_that.isLocked,_that.createdAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_email')  String userEmail,  String balance, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'is_locked')  bool isLocked, @JsonKey(name: 'created_at')  DateTime createdAt)  $default,) {final _that = this;
switch (_that) {
case _WalletListItem():
return $default(_that.walletId,_that.userEmail,_that.balance,_that.isActive,_that.isLocked,_that.createdAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'wallet_id')  String walletId, @JsonKey(name: 'user_email')  String userEmail,  String balance, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'is_locked')  bool isLocked, @JsonKey(name: 'created_at')  DateTime createdAt)?  $default,) {final _that = this;
switch (_that) {
case _WalletListItem() when $default != null:
return $default(_that.walletId,_that.userEmail,_that.balance,_that.isActive,_that.isLocked,_that.createdAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletListItem implements WalletListItem {
  const _WalletListItem({@JsonKey(name: 'wallet_id') required this.walletId, @JsonKey(name: 'user_email') required this.userEmail, required this.balance, @JsonKey(name: 'is_active') required this.isActive, @JsonKey(name: 'is_locked') required this.isLocked, @JsonKey(name: 'created_at') required this.createdAt});
  factory _WalletListItem.fromJson(Map<String, dynamic> json) => _$WalletListItemFromJson(json);

@override@JsonKey(name: 'wallet_id') final  String walletId;
@override@JsonKey(name: 'user_email') final  String userEmail;
@override final  String balance;
@override@JsonKey(name: 'is_active') final  bool isActive;
@override@JsonKey(name: 'is_locked') final  bool isLocked;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;

/// Create a copy of WalletListItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletListItemCopyWith<_WalletListItem> get copyWith => __$WalletListItemCopyWithImpl<_WalletListItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletListItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletListItem&&(identical(other.walletId, walletId) || other.walletId == walletId)&&(identical(other.userEmail, userEmail) || other.userEmail == userEmail)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isLocked, isLocked) || other.isLocked == isLocked)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,walletId,userEmail,balance,isActive,isLocked,createdAt);

@override
String toString() {
  return 'WalletListItem(walletId: $walletId, userEmail: $userEmail, balance: $balance, isActive: $isActive, isLocked: $isLocked, createdAt: $createdAt)';
}


}

/// @nodoc
abstract mixin class _$WalletListItemCopyWith<$Res> implements $WalletListItemCopyWith<$Res> {
  factory _$WalletListItemCopyWith(_WalletListItem value, $Res Function(_WalletListItem) _then) = __$WalletListItemCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'wallet_id') String walletId,@JsonKey(name: 'user_email') String userEmail, String balance,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'is_locked') bool isLocked,@JsonKey(name: 'created_at') DateTime createdAt
});




}
/// @nodoc
class __$WalletListItemCopyWithImpl<$Res>
    implements _$WalletListItemCopyWith<$Res> {
  __$WalletListItemCopyWithImpl(this._self, this._then);

  final _WalletListItem _self;
  final $Res Function(_WalletListItem) _then;

/// Create a copy of WalletListItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? walletId = null,Object? userEmail = null,Object? balance = null,Object? isActive = null,Object? isLocked = null,Object? createdAt = null,}) {
  return _then(_WalletListItem(
walletId: null == walletId ? _self.walletId : walletId // ignore: cast_nullable_to_non_nullable
as String,userEmail: null == userEmail ? _self.userEmail : userEmail // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _self.balance : balance // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isLocked: null == isLocked ? _self.isLocked : isLocked // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$WalletFilters {

 String? get status; String? get verificationLevel; bool? get isFlagged; String? get minBalance; String? get maxBalance; DateTime? get createdAfter; DateTime? get createdBefore;
/// Create a copy of WalletFilters
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletFiltersCopyWith<WalletFilters> get copyWith => _$WalletFiltersCopyWithImpl<WalletFilters>(this as WalletFilters, _$identity);

  /// Serializes this WalletFilters to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletFilters&&(identical(other.status, status) || other.status == status)&&(identical(other.verificationLevel, verificationLevel) || other.verificationLevel == verificationLevel)&&(identical(other.isFlagged, isFlagged) || other.isFlagged == isFlagged)&&(identical(other.minBalance, minBalance) || other.minBalance == minBalance)&&(identical(other.maxBalance, maxBalance) || other.maxBalance == maxBalance)&&(identical(other.createdAfter, createdAfter) || other.createdAfter == createdAfter)&&(identical(other.createdBefore, createdBefore) || other.createdBefore == createdBefore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,verificationLevel,isFlagged,minBalance,maxBalance,createdAfter,createdBefore);

@override
String toString() {
  return 'WalletFilters(status: $status, verificationLevel: $verificationLevel, isFlagged: $isFlagged, minBalance: $minBalance, maxBalance: $maxBalance, createdAfter: $createdAfter, createdBefore: $createdBefore)';
}


}

/// @nodoc
abstract mixin class $WalletFiltersCopyWith<$Res>  {
  factory $WalletFiltersCopyWith(WalletFilters value, $Res Function(WalletFilters) _then) = _$WalletFiltersCopyWithImpl;
@useResult
$Res call({
 String? status, String? verificationLevel, bool? isFlagged, String? minBalance, String? maxBalance, DateTime? createdAfter, DateTime? createdBefore
});




}
/// @nodoc
class _$WalletFiltersCopyWithImpl<$Res>
    implements $WalletFiltersCopyWith<$Res> {
  _$WalletFiltersCopyWithImpl(this._self, this._then);

  final WalletFilters _self;
  final $Res Function(WalletFilters) _then;

/// Create a copy of WalletFilters
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = freezed,Object? verificationLevel = freezed,Object? isFlagged = freezed,Object? minBalance = freezed,Object? maxBalance = freezed,Object? createdAfter = freezed,Object? createdBefore = freezed,}) {
  return _then(_self.copyWith(
status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,verificationLevel: freezed == verificationLevel ? _self.verificationLevel : verificationLevel // ignore: cast_nullable_to_non_nullable
as String?,isFlagged: freezed == isFlagged ? _self.isFlagged : isFlagged // ignore: cast_nullable_to_non_nullable
as bool?,minBalance: freezed == minBalance ? _self.minBalance : minBalance // ignore: cast_nullable_to_non_nullable
as String?,maxBalance: freezed == maxBalance ? _self.maxBalance : maxBalance // ignore: cast_nullable_to_non_nullable
as String?,createdAfter: freezed == createdAfter ? _self.createdAfter : createdAfter // ignore: cast_nullable_to_non_nullable
as DateTime?,createdBefore: freezed == createdBefore ? _self.createdBefore : createdBefore // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletFilters].
extension WalletFiltersPatterns on WalletFilters {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletFilters value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletFilters() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletFilters value)  $default,){
final _that = this;
switch (_that) {
case _WalletFilters():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletFilters value)?  $default,){
final _that = this;
switch (_that) {
case _WalletFilters() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? status,  String? verificationLevel,  bool? isFlagged,  String? minBalance,  String? maxBalance,  DateTime? createdAfter,  DateTime? createdBefore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletFilters() when $default != null:
return $default(_that.status,_that.verificationLevel,_that.isFlagged,_that.minBalance,_that.maxBalance,_that.createdAfter,_that.createdBefore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? status,  String? verificationLevel,  bool? isFlagged,  String? minBalance,  String? maxBalance,  DateTime? createdAfter,  DateTime? createdBefore)  $default,) {final _that = this;
switch (_that) {
case _WalletFilters():
return $default(_that.status,_that.verificationLevel,_that.isFlagged,_that.minBalance,_that.maxBalance,_that.createdAfter,_that.createdBefore);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? status,  String? verificationLevel,  bool? isFlagged,  String? minBalance,  String? maxBalance,  DateTime? createdAfter,  DateTime? createdBefore)?  $default,) {final _that = this;
switch (_that) {
case _WalletFilters() when $default != null:
return $default(_that.status,_that.verificationLevel,_that.isFlagged,_that.minBalance,_that.maxBalance,_that.createdAfter,_that.createdBefore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletFilters implements WalletFilters {
  const _WalletFilters({this.status, this.verificationLevel, this.isFlagged, this.minBalance, this.maxBalance, this.createdAfter, this.createdBefore});
  factory _WalletFilters.fromJson(Map<String, dynamic> json) => _$WalletFiltersFromJson(json);

@override final  String? status;
@override final  String? verificationLevel;
@override final  bool? isFlagged;
@override final  String? minBalance;
@override final  String? maxBalance;
@override final  DateTime? createdAfter;
@override final  DateTime? createdBefore;

/// Create a copy of WalletFilters
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletFiltersCopyWith<_WalletFilters> get copyWith => __$WalletFiltersCopyWithImpl<_WalletFilters>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletFiltersToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletFilters&&(identical(other.status, status) || other.status == status)&&(identical(other.verificationLevel, verificationLevel) || other.verificationLevel == verificationLevel)&&(identical(other.isFlagged, isFlagged) || other.isFlagged == isFlagged)&&(identical(other.minBalance, minBalance) || other.minBalance == minBalance)&&(identical(other.maxBalance, maxBalance) || other.maxBalance == maxBalance)&&(identical(other.createdAfter, createdAfter) || other.createdAfter == createdAfter)&&(identical(other.createdBefore, createdBefore) || other.createdBefore == createdBefore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,verificationLevel,isFlagged,minBalance,maxBalance,createdAfter,createdBefore);

@override
String toString() {
  return 'WalletFilters(status: $status, verificationLevel: $verificationLevel, isFlagged: $isFlagged, minBalance: $minBalance, maxBalance: $maxBalance, createdAfter: $createdAfter, createdBefore: $createdBefore)';
}


}

/// @nodoc
abstract mixin class _$WalletFiltersCopyWith<$Res> implements $WalletFiltersCopyWith<$Res> {
  factory _$WalletFiltersCopyWith(_WalletFilters value, $Res Function(_WalletFilters) _then) = __$WalletFiltersCopyWithImpl;
@override @useResult
$Res call({
 String? status, String? verificationLevel, bool? isFlagged, String? minBalance, String? maxBalance, DateTime? createdAfter, DateTime? createdBefore
});




}
/// @nodoc
class __$WalletFiltersCopyWithImpl<$Res>
    implements _$WalletFiltersCopyWith<$Res> {
  __$WalletFiltersCopyWithImpl(this._self, this._then);

  final _WalletFilters _self;
  final $Res Function(_WalletFilters) _then;

/// Create a copy of WalletFilters
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = freezed,Object? verificationLevel = freezed,Object? isFlagged = freezed,Object? minBalance = freezed,Object? maxBalance = freezed,Object? createdAfter = freezed,Object? createdBefore = freezed,}) {
  return _then(_WalletFilters(
status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,verificationLevel: freezed == verificationLevel ? _self.verificationLevel : verificationLevel // ignore: cast_nullable_to_non_nullable
as String?,isFlagged: freezed == isFlagged ? _self.isFlagged : isFlagged // ignore: cast_nullable_to_non_nullable
as bool?,minBalance: freezed == minBalance ? _self.minBalance : minBalance // ignore: cast_nullable_to_non_nullable
as String?,maxBalance: freezed == maxBalance ? _self.maxBalance : maxBalance // ignore: cast_nullable_to_non_nullable
as String?,createdAfter: freezed == createdAfter ? _self.createdAfter : createdAfter // ignore: cast_nullable_to_non_nullable
as DateTime?,createdBefore: freezed == createdBefore ? _self.createdBefore : createdBefore // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on

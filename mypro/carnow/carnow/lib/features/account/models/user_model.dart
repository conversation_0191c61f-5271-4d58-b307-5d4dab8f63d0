import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

// Note: Roles are managed separately via user_roles table.
// Consider creating dedicated models for Role and UserRole if needed.

// ignore_for_file: invalid_annotation_target

@freezed
abstract class UserModel with _$UserModel {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory UserModel({
    // Core User Fields (from profiles)
    required String id,
    @<PERSON>son<PERSON><PERSON>(name: 'auth_id') required String authId,
    @Json<PERSON>ey(name: 'full_name') String? name,
    String? email,
    String? phone,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'avatar_url') String? profileImageUrl,
    // Note: location is complex, might map to 'city' and 'address'
    // For now, we'll keep it simple to fix the main bug.
    String? address, // Detailed address
    @JsonKey(name: 'city_id') int? cityId,
    @<PERSON>son<PERSON>ey(name: 'store_city') String? cityName,
    DateTime? createdAt,
    DateTime? updatedAt,
    @Default(false) bool isDeleted,
    @Default('customer') String role,

    // Seller & Store Fields (previously from seller_profiles and seller_stores)
    String? sellerType,
    String? storeName,
    String? storeDescription,
    String? storeLogoUrl,
    String? contactPhone,
    String? companyName,
    String? companyRegistrationId,
    String? companyTaxId,
    String? companyAddress,
    String? companyWebsite,
    @Default(false) @JsonKey(name: 'is_approved') bool isApproved,
    String? approvedBy, // This is a UUID, but we'll handle it as a string
    DateTime? approvedAt,
    @Default(false)
    @JsonKey(name: 'is_seller_requested')
    bool isSellerRequested,
    String? sellerNotes,
    String? storeNameAr,
    String? storeDescriptionAr,
    String? storeBannerUrl,
    String? storeSlug,
    String? contactWhatsapp,
    String? storeCountry,
    double? latitude,
    double? longitude,
    @Default(true) bool isActive,
    @Default(false) bool isFeatured,
    @Default(false) bool allowNegotiation,
    double? minOrderAmount,
    String? shippingPolicy,
    String? returnPolicy,
    int? totalProducts,
    int? totalOrders,
    double? totalSales,
    double? averageRating,
    int? totalReviews,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
}

/// Seller Status based on the new unified model
enum SellerStatus {
  notSeller,
  requestPending,
  approved;

  String get displayName {
    switch (this) {
      case SellerStatus.notSeller:
        return 'مشتري';
      case SellerStatus.requestPending:
        return 'طلب بيع معلق';
      case SellerStatus.approved:
        return 'بائع معتمد';
    }
  }
}

extension UserModelExtensions on UserModel {
  /// Calculate seller status from the user model fields
  SellerStatus get sellerStatus {
    // إذا كان معتمداً كبائع، سواء كان الدور 'seller' أم لا
    if (isApproved == true) {
      return SellerStatus.approved;
    } else if (isSellerRequested == true) {
      return SellerStatus.requestPending;
    } else {
      return SellerStatus.notSeller;
    }
  }

  /// Can the user request to become a seller?
  bool get canRequestTobeSeller =>
      isApproved != true && isSellerRequested != true;
}

extension UserProfileStatus on UserModel {
  /// Checks if the user's profile has the minimum information required
  /// to proceed with an action like checkout.
  bool get isProfileReadyForCheckout {
    // A user is considered ready for checkout if they have a name and a phone number.
    // We can add more checks here later (e.g., address).
    final hasName = name != null && name!.trim().isNotEmpty;
    final hasPhone = phone != null && phone!.trim().isNotEmpty;
    return hasName && hasPhone;
  }
}

// Removed UserRole class as roles are handled in a separate table.
// If you need to represent roles fetched from 'user_roles',
// create a dedicated model like 'UserRoleLink' or enrich UserModel
// after fetching data from both tables.

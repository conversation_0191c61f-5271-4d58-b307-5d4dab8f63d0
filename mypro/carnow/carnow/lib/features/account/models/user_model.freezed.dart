// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserModel {

// Core User Fields (from profiles)
 String get id;@JsonKey(name: 'auth_id') String get authId;@JsonKey(name: 'full_name') String? get name; String? get email; String? get phone;@JsonKey(name: 'avatar_url') String? get profileImageUrl;// Note: location is complex, might map to 'city' and 'address'
// For now, we'll keep it simple to fix the main bug.
 String? get address;// Detailed address
@JsonKey(name: 'city_id') int? get cityId;@JsonKey(name: 'store_city') String? get cityName; DateTime? get createdAt; DateTime? get updatedAt; bool get isDeleted; String get role;// Seller & Store Fields (previously from seller_profiles and seller_stores)
 String? get sellerType; String? get storeName; String? get storeDescription; String? get storeLogoUrl; String? get contactPhone; String? get companyName; String? get companyRegistrationId; String? get companyTaxId; String? get companyAddress; String? get companyWebsite;@JsonKey(name: 'is_approved') bool get isApproved; String? get approvedBy;// This is a UUID, but we'll handle it as a string
 DateTime? get approvedAt;@JsonKey(name: 'is_seller_requested') bool get isSellerRequested; String? get sellerNotes; String? get storeNameAr; String? get storeDescriptionAr; String? get storeBannerUrl; String? get storeSlug; String? get contactWhatsapp; String? get storeCountry; double? get latitude; double? get longitude; bool get isActive; bool get isFeatured; bool get allowNegotiation; double? get minOrderAmount; String? get shippingPolicy; String? get returnPolicy; int? get totalProducts; int? get totalOrders; double? get totalSales; double? get averageRating; int? get totalReviews;
/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserModelCopyWith<UserModel> get copyWith => _$UserModelCopyWithImpl<UserModel>(this as UserModel, _$identity);

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserModel&&(identical(other.id, id) || other.id == id)&&(identical(other.authId, authId) || other.authId == authId)&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.profileImageUrl, profileImageUrl) || other.profileImageUrl == profileImageUrl)&&(identical(other.address, address) || other.address == address)&&(identical(other.cityId, cityId) || other.cityId == cityId)&&(identical(other.cityName, cityName) || other.cityName == cityName)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.role, role) || other.role == role)&&(identical(other.sellerType, sellerType) || other.sellerType == sellerType)&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.storeDescription, storeDescription) || other.storeDescription == storeDescription)&&(identical(other.storeLogoUrl, storeLogoUrl) || other.storeLogoUrl == storeLogoUrl)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.companyName, companyName) || other.companyName == companyName)&&(identical(other.companyRegistrationId, companyRegistrationId) || other.companyRegistrationId == companyRegistrationId)&&(identical(other.companyTaxId, companyTaxId) || other.companyTaxId == companyTaxId)&&(identical(other.companyAddress, companyAddress) || other.companyAddress == companyAddress)&&(identical(other.companyWebsite, companyWebsite) || other.companyWebsite == companyWebsite)&&(identical(other.isApproved, isApproved) || other.isApproved == isApproved)&&(identical(other.approvedBy, approvedBy) || other.approvedBy == approvedBy)&&(identical(other.approvedAt, approvedAt) || other.approvedAt == approvedAt)&&(identical(other.isSellerRequested, isSellerRequested) || other.isSellerRequested == isSellerRequested)&&(identical(other.sellerNotes, sellerNotes) || other.sellerNotes == sellerNotes)&&(identical(other.storeNameAr, storeNameAr) || other.storeNameAr == storeNameAr)&&(identical(other.storeDescriptionAr, storeDescriptionAr) || other.storeDescriptionAr == storeDescriptionAr)&&(identical(other.storeBannerUrl, storeBannerUrl) || other.storeBannerUrl == storeBannerUrl)&&(identical(other.storeSlug, storeSlug) || other.storeSlug == storeSlug)&&(identical(other.contactWhatsapp, contactWhatsapp) || other.contactWhatsapp == contactWhatsapp)&&(identical(other.storeCountry, storeCountry) || other.storeCountry == storeCountry)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isFeatured, isFeatured) || other.isFeatured == isFeatured)&&(identical(other.allowNegotiation, allowNegotiation) || other.allowNegotiation == allowNegotiation)&&(identical(other.minOrderAmount, minOrderAmount) || other.minOrderAmount == minOrderAmount)&&(identical(other.shippingPolicy, shippingPolicy) || other.shippingPolicy == shippingPolicy)&&(identical(other.returnPolicy, returnPolicy) || other.returnPolicy == returnPolicy)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.averageRating, averageRating) || other.averageRating == averageRating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,authId,name,email,phone,profileImageUrl,address,cityId,cityName,createdAt,updatedAt,isDeleted,role,sellerType,storeName,storeDescription,storeLogoUrl,contactPhone,companyName,companyRegistrationId,companyTaxId,companyAddress,companyWebsite,isApproved,approvedBy,approvedAt,isSellerRequested,sellerNotes,storeNameAr,storeDescriptionAr,storeBannerUrl,storeSlug,contactWhatsapp,storeCountry,latitude,longitude,isActive,isFeatured,allowNegotiation,minOrderAmount,shippingPolicy,returnPolicy,totalProducts,totalOrders,totalSales,averageRating,totalReviews]);

@override
String toString() {
  return 'UserModel(id: $id, authId: $authId, name: $name, email: $email, phone: $phone, profileImageUrl: $profileImageUrl, address: $address, cityId: $cityId, cityName: $cityName, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, role: $role, sellerType: $sellerType, storeName: $storeName, storeDescription: $storeDescription, storeLogoUrl: $storeLogoUrl, contactPhone: $contactPhone, companyName: $companyName, companyRegistrationId: $companyRegistrationId, companyTaxId: $companyTaxId, companyAddress: $companyAddress, companyWebsite: $companyWebsite, isApproved: $isApproved, approvedBy: $approvedBy, approvedAt: $approvedAt, isSellerRequested: $isSellerRequested, sellerNotes: $sellerNotes, storeNameAr: $storeNameAr, storeDescriptionAr: $storeDescriptionAr, storeBannerUrl: $storeBannerUrl, storeSlug: $storeSlug, contactWhatsapp: $contactWhatsapp, storeCountry: $storeCountry, latitude: $latitude, longitude: $longitude, isActive: $isActive, isFeatured: $isFeatured, allowNegotiation: $allowNegotiation, minOrderAmount: $minOrderAmount, shippingPolicy: $shippingPolicy, returnPolicy: $returnPolicy, totalProducts: $totalProducts, totalOrders: $totalOrders, totalSales: $totalSales, averageRating: $averageRating, totalReviews: $totalReviews)';
}


}

/// @nodoc
abstract mixin class $UserModelCopyWith<$Res>  {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) _then) = _$UserModelCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'auth_id') String authId,@JsonKey(name: 'full_name') String? name, String? email, String? phone,@JsonKey(name: 'avatar_url') String? profileImageUrl, String? address,@JsonKey(name: 'city_id') int? cityId,@JsonKey(name: 'store_city') String? cityName, DateTime? createdAt, DateTime? updatedAt, bool isDeleted, String role, String? sellerType, String? storeName, String? storeDescription, String? storeLogoUrl, String? contactPhone, String? companyName, String? companyRegistrationId, String? companyTaxId, String? companyAddress, String? companyWebsite,@JsonKey(name: 'is_approved') bool isApproved, String? approvedBy, DateTime? approvedAt,@JsonKey(name: 'is_seller_requested') bool isSellerRequested, String? sellerNotes, String? storeNameAr, String? storeDescriptionAr, String? storeBannerUrl, String? storeSlug, String? contactWhatsapp, String? storeCountry, double? latitude, double? longitude, bool isActive, bool isFeatured, bool allowNegotiation, double? minOrderAmount, String? shippingPolicy, String? returnPolicy, int? totalProducts, int? totalOrders, double? totalSales, double? averageRating, int? totalReviews
});




}
/// @nodoc
class _$UserModelCopyWithImpl<$Res>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._self, this._then);

  final UserModel _self;
  final $Res Function(UserModel) _then;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? authId = null,Object? name = freezed,Object? email = freezed,Object? phone = freezed,Object? profileImageUrl = freezed,Object? address = freezed,Object? cityId = freezed,Object? cityName = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? role = null,Object? sellerType = freezed,Object? storeName = freezed,Object? storeDescription = freezed,Object? storeLogoUrl = freezed,Object? contactPhone = freezed,Object? companyName = freezed,Object? companyRegistrationId = freezed,Object? companyTaxId = freezed,Object? companyAddress = freezed,Object? companyWebsite = freezed,Object? isApproved = null,Object? approvedBy = freezed,Object? approvedAt = freezed,Object? isSellerRequested = null,Object? sellerNotes = freezed,Object? storeNameAr = freezed,Object? storeDescriptionAr = freezed,Object? storeBannerUrl = freezed,Object? storeSlug = freezed,Object? contactWhatsapp = freezed,Object? storeCountry = freezed,Object? latitude = freezed,Object? longitude = freezed,Object? isActive = null,Object? isFeatured = null,Object? allowNegotiation = null,Object? minOrderAmount = freezed,Object? shippingPolicy = freezed,Object? returnPolicy = freezed,Object? totalProducts = freezed,Object? totalOrders = freezed,Object? totalSales = freezed,Object? averageRating = freezed,Object? totalReviews = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,authId: null == authId ? _self.authId : authId // ignore: cast_nullable_to_non_nullable
as String,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,profileImageUrl: freezed == profileImageUrl ? _self.profileImageUrl : profileImageUrl // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,cityId: freezed == cityId ? _self.cityId : cityId // ignore: cast_nullable_to_non_nullable
as int?,cityName: freezed == cityName ? _self.cityName : cityName // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String,sellerType: freezed == sellerType ? _self.sellerType : sellerType // ignore: cast_nullable_to_non_nullable
as String?,storeName: freezed == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String?,storeDescription: freezed == storeDescription ? _self.storeDescription : storeDescription // ignore: cast_nullable_to_non_nullable
as String?,storeLogoUrl: freezed == storeLogoUrl ? _self.storeLogoUrl : storeLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,contactPhone: freezed == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String?,companyName: freezed == companyName ? _self.companyName : companyName // ignore: cast_nullable_to_non_nullable
as String?,companyRegistrationId: freezed == companyRegistrationId ? _self.companyRegistrationId : companyRegistrationId // ignore: cast_nullable_to_non_nullable
as String?,companyTaxId: freezed == companyTaxId ? _self.companyTaxId : companyTaxId // ignore: cast_nullable_to_non_nullable
as String?,companyAddress: freezed == companyAddress ? _self.companyAddress : companyAddress // ignore: cast_nullable_to_non_nullable
as String?,companyWebsite: freezed == companyWebsite ? _self.companyWebsite : companyWebsite // ignore: cast_nullable_to_non_nullable
as String?,isApproved: null == isApproved ? _self.isApproved : isApproved // ignore: cast_nullable_to_non_nullable
as bool,approvedBy: freezed == approvedBy ? _self.approvedBy : approvedBy // ignore: cast_nullable_to_non_nullable
as String?,approvedAt: freezed == approvedAt ? _self.approvedAt : approvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isSellerRequested: null == isSellerRequested ? _self.isSellerRequested : isSellerRequested // ignore: cast_nullable_to_non_nullable
as bool,sellerNotes: freezed == sellerNotes ? _self.sellerNotes : sellerNotes // ignore: cast_nullable_to_non_nullable
as String?,storeNameAr: freezed == storeNameAr ? _self.storeNameAr : storeNameAr // ignore: cast_nullable_to_non_nullable
as String?,storeDescriptionAr: freezed == storeDescriptionAr ? _self.storeDescriptionAr : storeDescriptionAr // ignore: cast_nullable_to_non_nullable
as String?,storeBannerUrl: freezed == storeBannerUrl ? _self.storeBannerUrl : storeBannerUrl // ignore: cast_nullable_to_non_nullable
as String?,storeSlug: freezed == storeSlug ? _self.storeSlug : storeSlug // ignore: cast_nullable_to_non_nullable
as String?,contactWhatsapp: freezed == contactWhatsapp ? _self.contactWhatsapp : contactWhatsapp // ignore: cast_nullable_to_non_nullable
as String?,storeCountry: freezed == storeCountry ? _self.storeCountry : storeCountry // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isFeatured: null == isFeatured ? _self.isFeatured : isFeatured // ignore: cast_nullable_to_non_nullable
as bool,allowNegotiation: null == allowNegotiation ? _self.allowNegotiation : allowNegotiation // ignore: cast_nullable_to_non_nullable
as bool,minOrderAmount: freezed == minOrderAmount ? _self.minOrderAmount : minOrderAmount // ignore: cast_nullable_to_non_nullable
as double?,shippingPolicy: freezed == shippingPolicy ? _self.shippingPolicy : shippingPolicy // ignore: cast_nullable_to_non_nullable
as String?,returnPolicy: freezed == returnPolicy ? _self.returnPolicy : returnPolicy // ignore: cast_nullable_to_non_nullable
as String?,totalProducts: freezed == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int?,totalOrders: freezed == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int?,totalSales: freezed == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double?,averageRating: freezed == averageRating ? _self.averageRating : averageRating // ignore: cast_nullable_to_non_nullable
as double?,totalReviews: freezed == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// Adds pattern-matching-related methods to [UserModel].
extension UserModelPatterns on UserModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserModel value)  $default,){
final _that = this;
switch (_that) {
case _UserModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserModel value)?  $default,){
final _that = this;
switch (_that) {
case _UserModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'auth_id')  String authId, @JsonKey(name: 'full_name')  String? name,  String? email,  String? phone, @JsonKey(name: 'avatar_url')  String? profileImageUrl,  String? address, @JsonKey(name: 'city_id')  int? cityId, @JsonKey(name: 'store_city')  String? cityName,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted,  String role,  String? sellerType,  String? storeName,  String? storeDescription,  String? storeLogoUrl,  String? contactPhone,  String? companyName,  String? companyRegistrationId,  String? companyTaxId,  String? companyAddress,  String? companyWebsite, @JsonKey(name: 'is_approved')  bool isApproved,  String? approvedBy,  DateTime? approvedAt, @JsonKey(name: 'is_seller_requested')  bool isSellerRequested,  String? sellerNotes,  String? storeNameAr,  String? storeDescriptionAr,  String? storeBannerUrl,  String? storeSlug,  String? contactWhatsapp,  String? storeCountry,  double? latitude,  double? longitude,  bool isActive,  bool isFeatured,  bool allowNegotiation,  double? minOrderAmount,  String? shippingPolicy,  String? returnPolicy,  int? totalProducts,  int? totalOrders,  double? totalSales,  double? averageRating,  int? totalReviews)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserModel() when $default != null:
return $default(_that.id,_that.authId,_that.name,_that.email,_that.phone,_that.profileImageUrl,_that.address,_that.cityId,_that.cityName,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.role,_that.sellerType,_that.storeName,_that.storeDescription,_that.storeLogoUrl,_that.contactPhone,_that.companyName,_that.companyRegistrationId,_that.companyTaxId,_that.companyAddress,_that.companyWebsite,_that.isApproved,_that.approvedBy,_that.approvedAt,_that.isSellerRequested,_that.sellerNotes,_that.storeNameAr,_that.storeDescriptionAr,_that.storeBannerUrl,_that.storeSlug,_that.contactWhatsapp,_that.storeCountry,_that.latitude,_that.longitude,_that.isActive,_that.isFeatured,_that.allowNegotiation,_that.minOrderAmount,_that.shippingPolicy,_that.returnPolicy,_that.totalProducts,_that.totalOrders,_that.totalSales,_that.averageRating,_that.totalReviews);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'auth_id')  String authId, @JsonKey(name: 'full_name')  String? name,  String? email,  String? phone, @JsonKey(name: 'avatar_url')  String? profileImageUrl,  String? address, @JsonKey(name: 'city_id')  int? cityId, @JsonKey(name: 'store_city')  String? cityName,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted,  String role,  String? sellerType,  String? storeName,  String? storeDescription,  String? storeLogoUrl,  String? contactPhone,  String? companyName,  String? companyRegistrationId,  String? companyTaxId,  String? companyAddress,  String? companyWebsite, @JsonKey(name: 'is_approved')  bool isApproved,  String? approvedBy,  DateTime? approvedAt, @JsonKey(name: 'is_seller_requested')  bool isSellerRequested,  String? sellerNotes,  String? storeNameAr,  String? storeDescriptionAr,  String? storeBannerUrl,  String? storeSlug,  String? contactWhatsapp,  String? storeCountry,  double? latitude,  double? longitude,  bool isActive,  bool isFeatured,  bool allowNegotiation,  double? minOrderAmount,  String? shippingPolicy,  String? returnPolicy,  int? totalProducts,  int? totalOrders,  double? totalSales,  double? averageRating,  int? totalReviews)  $default,) {final _that = this;
switch (_that) {
case _UserModel():
return $default(_that.id,_that.authId,_that.name,_that.email,_that.phone,_that.profileImageUrl,_that.address,_that.cityId,_that.cityName,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.role,_that.sellerType,_that.storeName,_that.storeDescription,_that.storeLogoUrl,_that.contactPhone,_that.companyName,_that.companyRegistrationId,_that.companyTaxId,_that.companyAddress,_that.companyWebsite,_that.isApproved,_that.approvedBy,_that.approvedAt,_that.isSellerRequested,_that.sellerNotes,_that.storeNameAr,_that.storeDescriptionAr,_that.storeBannerUrl,_that.storeSlug,_that.contactWhatsapp,_that.storeCountry,_that.latitude,_that.longitude,_that.isActive,_that.isFeatured,_that.allowNegotiation,_that.minOrderAmount,_that.shippingPolicy,_that.returnPolicy,_that.totalProducts,_that.totalOrders,_that.totalSales,_that.averageRating,_that.totalReviews);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'auth_id')  String authId, @JsonKey(name: 'full_name')  String? name,  String? email,  String? phone, @JsonKey(name: 'avatar_url')  String? profileImageUrl,  String? address, @JsonKey(name: 'city_id')  int? cityId, @JsonKey(name: 'store_city')  String? cityName,  DateTime? createdAt,  DateTime? updatedAt,  bool isDeleted,  String role,  String? sellerType,  String? storeName,  String? storeDescription,  String? storeLogoUrl,  String? contactPhone,  String? companyName,  String? companyRegistrationId,  String? companyTaxId,  String? companyAddress,  String? companyWebsite, @JsonKey(name: 'is_approved')  bool isApproved,  String? approvedBy,  DateTime? approvedAt, @JsonKey(name: 'is_seller_requested')  bool isSellerRequested,  String? sellerNotes,  String? storeNameAr,  String? storeDescriptionAr,  String? storeBannerUrl,  String? storeSlug,  String? contactWhatsapp,  String? storeCountry,  double? latitude,  double? longitude,  bool isActive,  bool isFeatured,  bool allowNegotiation,  double? minOrderAmount,  String? shippingPolicy,  String? returnPolicy,  int? totalProducts,  int? totalOrders,  double? totalSales,  double? averageRating,  int? totalReviews)?  $default,) {final _that = this;
switch (_that) {
case _UserModel() when $default != null:
return $default(_that.id,_that.authId,_that.name,_that.email,_that.phone,_that.profileImageUrl,_that.address,_that.cityId,_that.cityName,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.role,_that.sellerType,_that.storeName,_that.storeDescription,_that.storeLogoUrl,_that.contactPhone,_that.companyName,_that.companyRegistrationId,_that.companyTaxId,_that.companyAddress,_that.companyWebsite,_that.isApproved,_that.approvedBy,_that.approvedAt,_that.isSellerRequested,_that.sellerNotes,_that.storeNameAr,_that.storeDescriptionAr,_that.storeBannerUrl,_that.storeSlug,_that.contactWhatsapp,_that.storeCountry,_that.latitude,_that.longitude,_that.isActive,_that.isFeatured,_that.allowNegotiation,_that.minOrderAmount,_that.shippingPolicy,_that.returnPolicy,_that.totalProducts,_that.totalOrders,_that.totalSales,_that.averageRating,_that.totalReviews);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _UserModel implements UserModel {
  const _UserModel({required this.id, @JsonKey(name: 'auth_id') required this.authId, @JsonKey(name: 'full_name') this.name, this.email, this.phone, @JsonKey(name: 'avatar_url') this.profileImageUrl, this.address, @JsonKey(name: 'city_id') this.cityId, @JsonKey(name: 'store_city') this.cityName, this.createdAt, this.updatedAt, this.isDeleted = false, this.role = 'customer', this.sellerType, this.storeName, this.storeDescription, this.storeLogoUrl, this.contactPhone, this.companyName, this.companyRegistrationId, this.companyTaxId, this.companyAddress, this.companyWebsite, @JsonKey(name: 'is_approved') this.isApproved = false, this.approvedBy, this.approvedAt, @JsonKey(name: 'is_seller_requested') this.isSellerRequested = false, this.sellerNotes, this.storeNameAr, this.storeDescriptionAr, this.storeBannerUrl, this.storeSlug, this.contactWhatsapp, this.storeCountry, this.latitude, this.longitude, this.isActive = true, this.isFeatured = false, this.allowNegotiation = false, this.minOrderAmount, this.shippingPolicy, this.returnPolicy, this.totalProducts, this.totalOrders, this.totalSales, this.averageRating, this.totalReviews});
  factory _UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);

// Core User Fields (from profiles)
@override final  String id;
@override@JsonKey(name: 'auth_id') final  String authId;
@override@JsonKey(name: 'full_name') final  String? name;
@override final  String? email;
@override final  String? phone;
@override@JsonKey(name: 'avatar_url') final  String? profileImageUrl;
// Note: location is complex, might map to 'city' and 'address'
// For now, we'll keep it simple to fix the main bug.
@override final  String? address;
// Detailed address
@override@JsonKey(name: 'city_id') final  int? cityId;
@override@JsonKey(name: 'store_city') final  String? cityName;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey() final  bool isDeleted;
@override@JsonKey() final  String role;
// Seller & Store Fields (previously from seller_profiles and seller_stores)
@override final  String? sellerType;
@override final  String? storeName;
@override final  String? storeDescription;
@override final  String? storeLogoUrl;
@override final  String? contactPhone;
@override final  String? companyName;
@override final  String? companyRegistrationId;
@override final  String? companyTaxId;
@override final  String? companyAddress;
@override final  String? companyWebsite;
@override@JsonKey(name: 'is_approved') final  bool isApproved;
@override final  String? approvedBy;
// This is a UUID, but we'll handle it as a string
@override final  DateTime? approvedAt;
@override@JsonKey(name: 'is_seller_requested') final  bool isSellerRequested;
@override final  String? sellerNotes;
@override final  String? storeNameAr;
@override final  String? storeDescriptionAr;
@override final  String? storeBannerUrl;
@override final  String? storeSlug;
@override final  String? contactWhatsapp;
@override final  String? storeCountry;
@override final  double? latitude;
@override final  double? longitude;
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  bool isFeatured;
@override@JsonKey() final  bool allowNegotiation;
@override final  double? minOrderAmount;
@override final  String? shippingPolicy;
@override final  String? returnPolicy;
@override final  int? totalProducts;
@override final  int? totalOrders;
@override final  double? totalSales;
@override final  double? averageRating;
@override final  int? totalReviews;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserModelCopyWith<_UserModel> get copyWith => __$UserModelCopyWithImpl<_UserModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserModel&&(identical(other.id, id) || other.id == id)&&(identical(other.authId, authId) || other.authId == authId)&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.profileImageUrl, profileImageUrl) || other.profileImageUrl == profileImageUrl)&&(identical(other.address, address) || other.address == address)&&(identical(other.cityId, cityId) || other.cityId == cityId)&&(identical(other.cityName, cityName) || other.cityName == cityName)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.role, role) || other.role == role)&&(identical(other.sellerType, sellerType) || other.sellerType == sellerType)&&(identical(other.storeName, storeName) || other.storeName == storeName)&&(identical(other.storeDescription, storeDescription) || other.storeDescription == storeDescription)&&(identical(other.storeLogoUrl, storeLogoUrl) || other.storeLogoUrl == storeLogoUrl)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.companyName, companyName) || other.companyName == companyName)&&(identical(other.companyRegistrationId, companyRegistrationId) || other.companyRegistrationId == companyRegistrationId)&&(identical(other.companyTaxId, companyTaxId) || other.companyTaxId == companyTaxId)&&(identical(other.companyAddress, companyAddress) || other.companyAddress == companyAddress)&&(identical(other.companyWebsite, companyWebsite) || other.companyWebsite == companyWebsite)&&(identical(other.isApproved, isApproved) || other.isApproved == isApproved)&&(identical(other.approvedBy, approvedBy) || other.approvedBy == approvedBy)&&(identical(other.approvedAt, approvedAt) || other.approvedAt == approvedAt)&&(identical(other.isSellerRequested, isSellerRequested) || other.isSellerRequested == isSellerRequested)&&(identical(other.sellerNotes, sellerNotes) || other.sellerNotes == sellerNotes)&&(identical(other.storeNameAr, storeNameAr) || other.storeNameAr == storeNameAr)&&(identical(other.storeDescriptionAr, storeDescriptionAr) || other.storeDescriptionAr == storeDescriptionAr)&&(identical(other.storeBannerUrl, storeBannerUrl) || other.storeBannerUrl == storeBannerUrl)&&(identical(other.storeSlug, storeSlug) || other.storeSlug == storeSlug)&&(identical(other.contactWhatsapp, contactWhatsapp) || other.contactWhatsapp == contactWhatsapp)&&(identical(other.storeCountry, storeCountry) || other.storeCountry == storeCountry)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isFeatured, isFeatured) || other.isFeatured == isFeatured)&&(identical(other.allowNegotiation, allowNegotiation) || other.allowNegotiation == allowNegotiation)&&(identical(other.minOrderAmount, minOrderAmount) || other.minOrderAmount == minOrderAmount)&&(identical(other.shippingPolicy, shippingPolicy) || other.shippingPolicy == shippingPolicy)&&(identical(other.returnPolicy, returnPolicy) || other.returnPolicy == returnPolicy)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.averageRating, averageRating) || other.averageRating == averageRating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,authId,name,email,phone,profileImageUrl,address,cityId,cityName,createdAt,updatedAt,isDeleted,role,sellerType,storeName,storeDescription,storeLogoUrl,contactPhone,companyName,companyRegistrationId,companyTaxId,companyAddress,companyWebsite,isApproved,approvedBy,approvedAt,isSellerRequested,sellerNotes,storeNameAr,storeDescriptionAr,storeBannerUrl,storeSlug,contactWhatsapp,storeCountry,latitude,longitude,isActive,isFeatured,allowNegotiation,minOrderAmount,shippingPolicy,returnPolicy,totalProducts,totalOrders,totalSales,averageRating,totalReviews]);

@override
String toString() {
  return 'UserModel(id: $id, authId: $authId, name: $name, email: $email, phone: $phone, profileImageUrl: $profileImageUrl, address: $address, cityId: $cityId, cityName: $cityName, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, role: $role, sellerType: $sellerType, storeName: $storeName, storeDescription: $storeDescription, storeLogoUrl: $storeLogoUrl, contactPhone: $contactPhone, companyName: $companyName, companyRegistrationId: $companyRegistrationId, companyTaxId: $companyTaxId, companyAddress: $companyAddress, companyWebsite: $companyWebsite, isApproved: $isApproved, approvedBy: $approvedBy, approvedAt: $approvedAt, isSellerRequested: $isSellerRequested, sellerNotes: $sellerNotes, storeNameAr: $storeNameAr, storeDescriptionAr: $storeDescriptionAr, storeBannerUrl: $storeBannerUrl, storeSlug: $storeSlug, contactWhatsapp: $contactWhatsapp, storeCountry: $storeCountry, latitude: $latitude, longitude: $longitude, isActive: $isActive, isFeatured: $isFeatured, allowNegotiation: $allowNegotiation, minOrderAmount: $minOrderAmount, shippingPolicy: $shippingPolicy, returnPolicy: $returnPolicy, totalProducts: $totalProducts, totalOrders: $totalOrders, totalSales: $totalSales, averageRating: $averageRating, totalReviews: $totalReviews)';
}


}

/// @nodoc
abstract mixin class _$UserModelCopyWith<$Res> implements $UserModelCopyWith<$Res> {
  factory _$UserModelCopyWith(_UserModel value, $Res Function(_UserModel) _then) = __$UserModelCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'auth_id') String authId,@JsonKey(name: 'full_name') String? name, String? email, String? phone,@JsonKey(name: 'avatar_url') String? profileImageUrl, String? address,@JsonKey(name: 'city_id') int? cityId,@JsonKey(name: 'store_city') String? cityName, DateTime? createdAt, DateTime? updatedAt, bool isDeleted, String role, String? sellerType, String? storeName, String? storeDescription, String? storeLogoUrl, String? contactPhone, String? companyName, String? companyRegistrationId, String? companyTaxId, String? companyAddress, String? companyWebsite,@JsonKey(name: 'is_approved') bool isApproved, String? approvedBy, DateTime? approvedAt,@JsonKey(name: 'is_seller_requested') bool isSellerRequested, String? sellerNotes, String? storeNameAr, String? storeDescriptionAr, String? storeBannerUrl, String? storeSlug, String? contactWhatsapp, String? storeCountry, double? latitude, double? longitude, bool isActive, bool isFeatured, bool allowNegotiation, double? minOrderAmount, String? shippingPolicy, String? returnPolicy, int? totalProducts, int? totalOrders, double? totalSales, double? averageRating, int? totalReviews
});




}
/// @nodoc
class __$UserModelCopyWithImpl<$Res>
    implements _$UserModelCopyWith<$Res> {
  __$UserModelCopyWithImpl(this._self, this._then);

  final _UserModel _self;
  final $Res Function(_UserModel) _then;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? authId = null,Object? name = freezed,Object? email = freezed,Object? phone = freezed,Object? profileImageUrl = freezed,Object? address = freezed,Object? cityId = freezed,Object? cityName = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? role = null,Object? sellerType = freezed,Object? storeName = freezed,Object? storeDescription = freezed,Object? storeLogoUrl = freezed,Object? contactPhone = freezed,Object? companyName = freezed,Object? companyRegistrationId = freezed,Object? companyTaxId = freezed,Object? companyAddress = freezed,Object? companyWebsite = freezed,Object? isApproved = null,Object? approvedBy = freezed,Object? approvedAt = freezed,Object? isSellerRequested = null,Object? sellerNotes = freezed,Object? storeNameAr = freezed,Object? storeDescriptionAr = freezed,Object? storeBannerUrl = freezed,Object? storeSlug = freezed,Object? contactWhatsapp = freezed,Object? storeCountry = freezed,Object? latitude = freezed,Object? longitude = freezed,Object? isActive = null,Object? isFeatured = null,Object? allowNegotiation = null,Object? minOrderAmount = freezed,Object? shippingPolicy = freezed,Object? returnPolicy = freezed,Object? totalProducts = freezed,Object? totalOrders = freezed,Object? totalSales = freezed,Object? averageRating = freezed,Object? totalReviews = freezed,}) {
  return _then(_UserModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,authId: null == authId ? _self.authId : authId // ignore: cast_nullable_to_non_nullable
as String,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,profileImageUrl: freezed == profileImageUrl ? _self.profileImageUrl : profileImageUrl // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,cityId: freezed == cityId ? _self.cityId : cityId // ignore: cast_nullable_to_non_nullable
as int?,cityName: freezed == cityName ? _self.cityName : cityName // ignore: cast_nullable_to_non_nullable
as String?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String,sellerType: freezed == sellerType ? _self.sellerType : sellerType // ignore: cast_nullable_to_non_nullable
as String?,storeName: freezed == storeName ? _self.storeName : storeName // ignore: cast_nullable_to_non_nullable
as String?,storeDescription: freezed == storeDescription ? _self.storeDescription : storeDescription // ignore: cast_nullable_to_non_nullable
as String?,storeLogoUrl: freezed == storeLogoUrl ? _self.storeLogoUrl : storeLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,contactPhone: freezed == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String?,companyName: freezed == companyName ? _self.companyName : companyName // ignore: cast_nullable_to_non_nullable
as String?,companyRegistrationId: freezed == companyRegistrationId ? _self.companyRegistrationId : companyRegistrationId // ignore: cast_nullable_to_non_nullable
as String?,companyTaxId: freezed == companyTaxId ? _self.companyTaxId : companyTaxId // ignore: cast_nullable_to_non_nullable
as String?,companyAddress: freezed == companyAddress ? _self.companyAddress : companyAddress // ignore: cast_nullable_to_non_nullable
as String?,companyWebsite: freezed == companyWebsite ? _self.companyWebsite : companyWebsite // ignore: cast_nullable_to_non_nullable
as String?,isApproved: null == isApproved ? _self.isApproved : isApproved // ignore: cast_nullable_to_non_nullable
as bool,approvedBy: freezed == approvedBy ? _self.approvedBy : approvedBy // ignore: cast_nullable_to_non_nullable
as String?,approvedAt: freezed == approvedAt ? _self.approvedAt : approvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isSellerRequested: null == isSellerRequested ? _self.isSellerRequested : isSellerRequested // ignore: cast_nullable_to_non_nullable
as bool,sellerNotes: freezed == sellerNotes ? _self.sellerNotes : sellerNotes // ignore: cast_nullable_to_non_nullable
as String?,storeNameAr: freezed == storeNameAr ? _self.storeNameAr : storeNameAr // ignore: cast_nullable_to_non_nullable
as String?,storeDescriptionAr: freezed == storeDescriptionAr ? _self.storeDescriptionAr : storeDescriptionAr // ignore: cast_nullable_to_non_nullable
as String?,storeBannerUrl: freezed == storeBannerUrl ? _self.storeBannerUrl : storeBannerUrl // ignore: cast_nullable_to_non_nullable
as String?,storeSlug: freezed == storeSlug ? _self.storeSlug : storeSlug // ignore: cast_nullable_to_non_nullable
as String?,contactWhatsapp: freezed == contactWhatsapp ? _self.contactWhatsapp : contactWhatsapp // ignore: cast_nullable_to_non_nullable
as String?,storeCountry: freezed == storeCountry ? _self.storeCountry : storeCountry // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isFeatured: null == isFeatured ? _self.isFeatured : isFeatured // ignore: cast_nullable_to_non_nullable
as bool,allowNegotiation: null == allowNegotiation ? _self.allowNegotiation : allowNegotiation // ignore: cast_nullable_to_non_nullable
as bool,minOrderAmount: freezed == minOrderAmount ? _self.minOrderAmount : minOrderAmount // ignore: cast_nullable_to_non_nullable
as double?,shippingPolicy: freezed == shippingPolicy ? _self.shippingPolicy : shippingPolicy // ignore: cast_nullable_to_non_nullable
as String?,returnPolicy: freezed == returnPolicy ? _self.returnPolicy : returnPolicy // ignore: cast_nullable_to_non_nullable
as String?,totalProducts: freezed == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int?,totalOrders: freezed == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int?,totalSales: freezed == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double?,averageRating: freezed == averageRating ? _self.averageRating : averageRating // ignore: cast_nullable_to_non_nullable
as double?,totalReviews: freezed == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserModel _$UserModelFromJson(Map<String, dynamic> json) => _UserModel(
  id: json['id'] as String,
  authId: json['auth_id'] as String,
  name: json['full_name'] as String?,
  email: json['email'] as String?,
  phone: json['phone'] as String?,
  profileImageUrl: json['avatar_url'] as String?,
  address: json['address'] as String?,
  cityId: (json['city_id'] as num?)?.toInt(),
  cityName: json['store_city'] as String?,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
  role: json['role'] as String? ?? 'customer',
  sellerType: json['seller_type'] as String?,
  storeName: json['store_name'] as String?,
  storeDescription: json['store_description'] as String?,
  storeLogoUrl: json['store_logo_url'] as String?,
  contactPhone: json['contact_phone'] as String?,
  companyName: json['company_name'] as String?,
  companyRegistrationId: json['company_registration_id'] as String?,
  companyTaxId: json['company_tax_id'] as String?,
  companyAddress: json['company_address'] as String?,
  companyWebsite: json['company_website'] as String?,
  isApproved: json['is_approved'] as bool? ?? false,
  approvedBy: json['approved_by'] as String?,
  approvedAt: json['approved_at'] == null
      ? null
      : DateTime.parse(json['approved_at'] as String),
  isSellerRequested: json['is_seller_requested'] as bool? ?? false,
  sellerNotes: json['seller_notes'] as String?,
  storeNameAr: json['store_name_ar'] as String?,
  storeDescriptionAr: json['store_description_ar'] as String?,
  storeBannerUrl: json['store_banner_url'] as String?,
  storeSlug: json['store_slug'] as String?,
  contactWhatsapp: json['contact_whatsapp'] as String?,
  storeCountry: json['store_country'] as String?,
  latitude: (json['latitude'] as num?)?.toDouble(),
  longitude: (json['longitude'] as num?)?.toDouble(),
  isActive: json['is_active'] as bool? ?? true,
  isFeatured: json['is_featured'] as bool? ?? false,
  allowNegotiation: json['allow_negotiation'] as bool? ?? false,
  minOrderAmount: (json['min_order_amount'] as num?)?.toDouble(),
  shippingPolicy: json['shipping_policy'] as String?,
  returnPolicy: json['return_policy'] as String?,
  totalProducts: (json['total_products'] as num?)?.toInt(),
  totalOrders: (json['total_orders'] as num?)?.toInt(),
  totalSales: (json['total_sales'] as num?)?.toDouble(),
  averageRating: (json['average_rating'] as num?)?.toDouble(),
  totalReviews: (json['total_reviews'] as num?)?.toInt(),
);

Map<String, dynamic> _$UserModelToJson(_UserModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'auth_id': instance.authId,
      'full_name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'avatar_url': instance.profileImageUrl,
      'address': instance.address,
      'city_id': instance.cityId,
      'store_city': instance.cityName,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
      'role': instance.role,
      'seller_type': instance.sellerType,
      'store_name': instance.storeName,
      'store_description': instance.storeDescription,
      'store_logo_url': instance.storeLogoUrl,
      'contact_phone': instance.contactPhone,
      'company_name': instance.companyName,
      'company_registration_id': instance.companyRegistrationId,
      'company_tax_id': instance.companyTaxId,
      'company_address': instance.companyAddress,
      'company_website': instance.companyWebsite,
      'is_approved': instance.isApproved,
      'approved_by': instance.approvedBy,
      'approved_at': instance.approvedAt?.toIso8601String(),
      'is_seller_requested': instance.isSellerRequested,
      'seller_notes': instance.sellerNotes,
      'store_name_ar': instance.storeNameAr,
      'store_description_ar': instance.storeDescriptionAr,
      'store_banner_url': instance.storeBannerUrl,
      'store_slug': instance.storeSlug,
      'contact_whatsapp': instance.contactWhatsapp,
      'store_country': instance.storeCountry,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'is_active': instance.isActive,
      'is_featured': instance.isFeatured,
      'allow_negotiation': instance.allowNegotiation,
      'min_order_amount': instance.minOrderAmount,
      'shipping_policy': instance.shippingPolicy,
      'return_policy': instance.returnPolicy,
      'total_products': instance.totalProducts,
      'total_orders': instance.totalOrders,
      'total_sales': instance.totalSales,
      'average_rating': instance.averageRating,
      'total_reviews': instance.totalReviews,
    };

import 'package:flutter/material.dart';

/// ويدجت معلومات المستخدم
class UserInfoWidget extends StatelessWidget {
  const UserInfoWidget({
    required this.name,
    super.key,
    this.email,
    this.phone,
    this.location,
    this.onEdit,
  });
  final String name;
  final String? email;
  final String? phone;
  final String? location;
  final VoidCallback? onEdit;

  @override
  Widget build(BuildContext context) => Card(
    margin: const EdgeInsets.all(16),
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'معلومات الملف الشخصي',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              if (onEdit != null)
                IconButton(icon: const Icon(Icons.edit), onPressed: onEdit),
            ],
          ),
          const Divider(),
          _buildInfoRow('الاسم', name),
          if (email != null) _buildInfoRow('البريد الإلكتروني', email!),
          if (phone != null) _buildInfoRow('رقم الهاتف', phone!),
          if (location != null) _buildInfoRow('الموقع', location!),
        ],
      ),
    ),
  );

  Widget _buildInfoRow(String label, String value) => Padding(
    padding: const EdgeInsets.symmetric(vertical: 8),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(child: Text(value)),
      ],
    ),
  );
}

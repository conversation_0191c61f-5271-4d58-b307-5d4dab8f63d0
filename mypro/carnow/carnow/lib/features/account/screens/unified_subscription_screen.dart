import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/extensions/user_seller_extension.dart';
import '../../../core/providers/users_provider.dart';
import '../../../core/providers/subscription_flow_provider.dart';

import '../../../shared/widgets/app_loading_indicator.dart';
import '../../seller/models/seller_enums.dart';
import '../../seller/models/subscription_model.dart';
import '../../seller/providers/subscription_provider.dart';
import '../models/user_model.dart';

class UnifiedSubscriptionScreen extends ConsumerStatefulWidget {
  const UnifiedSubscriptionScreen({super.key});

  @override
  ConsumerState<UnifiedSubscriptionScreen> createState() => _UnifiedSubscriptionScreenState();
}

class _UnifiedSubscriptionScreenState extends ConsumerState<UnifiedSubscriptionScreen> {
  BillingCycle selectedCycle = BillingCycle.monthly;
  SubscriptionPlan? selectedPlan;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentUser = ref.watch(currentUserStreamProvider).value;
    final currentSubscription = ref.watch(sellerSubscriptionProviderProvider);
    final plansAsync = ref.watch(availableSubscriptionPlansProvider);
    final subscriptionFlow = ref.watch(coreSubscriptionFlowProviderProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('خطط الاشتراك'),
        elevation: 0,
      ),
      body: currentUser == null
          ? const Center(child: AppLoadingIndicator())
          : plansAsync.when(
              data: (plans) => _buildContent(
                context,
                currentUser,
                currentSubscription.value,
                subscriptionFlow,
                plans,
              ),
              loading: () => const Center(child: AppLoadingIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline, size: 48),
                    const SizedBox(height: 16),
                    Text(
                      'حدث خطأ في تحميل خطط الاشتراك',
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: theme.textTheme.bodySmall,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(availableSubscriptionPlansProvider),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    UserModel user,
    SellerSubscription? currentSubscription,
    CoreSubscriptionFlowState subscriptionFlow,
    List<SubscriptionPlan> plans,
  ) {
    // تحديد الحالة الحالية للمستخدم
    final isApprovedSeller = user.isSeller;
    final hasActiveSubscription = currentSubscription?.isActive ?? false;
    final isSubmitting = subscriptionFlow.status == CoreSubscriptionFlowStatus.submitting;
    final hasSubmitted = subscriptionFlow.status == CoreSubscriptionFlowStatus.completed;

    if (isSubmitting) {
      return _buildSubmittingView(context);
    }

    if (hasSubmitted) {
      return _buildSubmittedView(context, subscriptionFlow);
    }

    if (isApprovedSeller && hasActiveSubscription) {
      return _buildCurrentSubscriptionView(context, currentSubscription!, plans);
    }

    return _buildPlanSelectionView(context, plans, isApprovedSeller);
  }

  Widget _buildPendingRequestView(BuildContext context, CoreSubscriptionFlowState flowState) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Icon(
                Icons.schedule,
                size: 48,
                color: Colors.orange.shade600,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'طلب الاشتراك قيد المراجعة',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'تم تقديم طلب اشتراكك بنجاح وهو الآن قيد المراجعة من قبل فريقنا. ستتم مراجعة طلبك خلال 48 ساعة.',
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'تاريخ الإرسال: ${_formatDate(DateTime.now())}',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => context.push('/support'),
              child: const Text('التواصل مع الدعم'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentSubscriptionView(
    BuildContext context,
    SellerSubscription subscription,
    List<SubscriptionPlan> plans,
  ) {
    final theme = Theme.of(context);
    final currentPlan = PredefinedPlans.getPlanById(subscription.planId);
    
    if (currentPlan == null) {
      return const Center(child: Text('خطأ في تحميل بيانات الخطة الحالية'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة الخطة الحالية
          _buildCurrentPlanCard(context, currentPlan, subscription),
          
          const SizedBox(height: 24),
          
          // خيارات الترقية
          Text(
            'خطط أخرى متاحة',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // عرض الخطط المتاحة للترقية
          ...plans.map((plan) {
            if (plan.id == currentPlan.id) return const SizedBox.shrink();
            
            final isUpgrade = _isUpgrade(currentPlan, plan);
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildUpgradePlanCard(context, plan, currentPlan, subscription, isUpgrade),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPlanSelectionView(BuildContext context, List<SubscriptionPlan> plans, bool isApprovedSeller) {
    final theme = Theme.of(context);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رسالة ترحيبية
          if (!isApprovedSeller) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ابدأ رحلتك كبائع',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اختر الخطة التي تناسب احتياجاتك وابدأ في بيع منتجاتك',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
          
          // تبديل دورة الفوترة
          _buildBillingCycleToggle(context),
          
          const SizedBox(height: 24),
          
          // عرض الخطط
          ...plans.map((plan) => Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildPlanCard(context, plan, isApprovedSeller),
          )),
        ],
      ),
    );
  }

  Widget _buildCurrentPlanCard(BuildContext context, SubscriptionPlan plan, SellerSubscription subscription) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'خطتك الحالية',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          Text(
            plan.nameAr,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          
          Text(
            plan.displayPrice(subscription.billingCycle),
            style: theme.textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // معلومات الاشتراك
          _buildSubscriptionInfo(context, subscription),
        ],
      ),
    );
  }

  Widget _buildSubscriptionInfo(BuildContext context, SellerSubscription subscription) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        _buildInfoRow(
          'الحالة',
          subscription.status.displayName,
          Icons.info_outline,
          theme,
        ),
        const SizedBox(height: 8),
        _buildInfoRow(
          'دورة الفوترة',
          subscription.billingCycle == BillingCycle.monthly ? 'شهرية' : 'سنوية',
          Icons.calendar_month,
          theme,
        ),
        if (subscription.nextBillingDate != null) ...[
          const SizedBox(height: 8),
          _buildInfoRow(
            'التجديد التالي',
            _formatDate(subscription.nextBillingDate!),
            Icons.schedule,
            theme,
          ),
        ],
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon, ThemeData theme) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.white70),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: Colors.white70,
          ),
        ),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildUpgradePlanCard(
    BuildContext context,
    SubscriptionPlan plan,
    SubscriptionPlan currentPlan,
    SellerSubscription subscription,
    bool isUpgrade,
  ) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isUpgrade ? Colors.green.shade300 : Colors.orange.shade300,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      plan.nameAr,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      plan.displayPrice(subscription.billingCycle),
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              _buildUpgradeButton(context, plan, currentPlan, subscription, isUpgrade),
            ],
          ),
          const SizedBox(height: 12),
          
          // المميزات الإضافية
          if (plan.benefitsAr != null && plan.benefitsAr!.isNotEmpty) ...[
            Text(
              'المميزات الإضافية:',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            ...plan.benefitsAr!.take(3).map((benefit) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 16,
                    color: Colors.green.shade600,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      benefit,
                      style: theme.textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildUpgradeButton(
    BuildContext context,
    SubscriptionPlan plan,
    SubscriptionPlan currentPlan,
    SellerSubscription subscription,
    bool isUpgrade,
  ) {
    if (isUpgrade) {
      return ElevatedButton(
        onPressed: () => _handleUpgrade(context, plan, currentPlan, subscription),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green.shade600,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        child: const Text('ترقية'),
      );
    } else {
      return OutlinedButton(
        onPressed: () => _handleDowngrade(context, plan, currentPlan, subscription),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.orange.shade600,
          side: BorderSide(color: Colors.orange.shade600),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        child: const Text('تغيير'),
      );
    }
  }

  Widget _buildBillingCycleToggle(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ChoiceChip(
          label: const Text('شهرياً'),
          selected: selectedCycle == BillingCycle.monthly,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                selectedCycle = BillingCycle.monthly;
              });
            }
          },
        ),
        const SizedBox(width: 16),
        ChoiceChip(
          label: const Text('سنوياً'),
          selected: selectedCycle == BillingCycle.yearly,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                selectedCycle = BillingCycle.yearly;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildPlanCard(BuildContext context, SubscriptionPlan plan, bool isApprovedSeller) {
    final theme = Theme.of(context);
    final isSelected = selectedPlan?.id == plan.id;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedPlan = isSelected ? null : plan;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primaryContainer : theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (plan.isPopular) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'الأكثر شعبية',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.orange.shade800,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                      ],
                      Text(
                        plan.nameAr,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isSelected ? theme.colorScheme.onPrimaryContainer : null,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        plan.displayPrice(selectedCycle),
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Text(
              plan.descriptionAr,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected ? theme.colorScheme.onPrimaryContainer : null,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // المميزات
            if (plan.benefitsAr != null && plan.benefitsAr!.isNotEmpty) ...[
              ...plan.benefitsAr!.map((benefit) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 16,
                      color: Colors.green.shade600,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        benefit,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isSelected ? theme.colorScheme.onPrimaryContainer : null,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ],
            
            const SizedBox(height: 20),
            
            // زر الاشتراك
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isSelected ? () => _handleSubscribe(context, plan, isApprovedSeller) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isSelected ? theme.colorScheme.primary : null,
                  foregroundColor: isSelected ? theme.colorScheme.onPrimary : null,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: Text(
                  isSelected ? 'اختيار هذه الخطة' : 'اختر هذه الخطة',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleSubscribe(BuildContext context, SubscriptionPlan plan, bool isApprovedSeller) {
    if (isApprovedSeller) {
      // البائع المعتمد يمكنه الاشتراك مباشرة
      context.push('/seller/subscription/checkout/${plan.id}?cycle=${selectedCycle.name}');
    } else {
      // المستخدم الجديد يحتاج للتسجيل كبائع أولاً
      context.push('/seller/subscription-request?planId=${plan.id}&cycle=${selectedCycle.name}');
    }
  }

  void _handleUpgrade(
    BuildContext context,
    SubscriptionPlan newPlan,
    SubscriptionPlan currentPlan,
    SellerSubscription subscription,
  ) {
    // حساب الفرق في السعر
    final currentPrice = subscription.billingCycle == BillingCycle.yearly 
        ? currentPlan.yearlyPriceLD 
        : currentPlan.monthlyPriceLD;
    final newPrice = subscription.billingCycle == BillingCycle.yearly 
        ? newPlan.yearlyPriceLD 
        : newPlan.monthlyPriceLD;
    final priceDifference = newPrice - currentPrice;

    if (subscription.billingCycle == BillingCycle.yearly) {
      // للاشتراك السنوي، عرض خيارات الدفع أو التواصل مع الدعم
      _showYearlyUpgradeDialog(context, newPlan, priceDifference);
    } else {
      // للاشتراك الشهري، ترقية مباشرة
      _showUpgradeConfirmationDialog(context, newPlan, priceDifference);
    }
  }

  void _handleDowngrade(
    BuildContext context,
    SubscriptionPlan newPlan,
    SubscriptionPlan currentPlan,
    SellerSubscription subscription,
  ) {
    _showDowngradeDialog(context, newPlan);
  }

  void _showYearlyUpgradeDialog(BuildContext context, SubscriptionPlan newPlan, double priceDifference) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('ترقية الخطة السنوية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تريد الترقية إلى خطة: ${newPlan.nameAr}'),
            const SizedBox(height: 8),
            Text('الفرق في السعر: ${priceDifference.toStringAsFixed(0)} د.ل'),
            const SizedBox(height: 16),
            const Text('يمكنك:'),
            const SizedBox(height: 8),
            const Text('• دفع الفرق والترقية فوراً'),
            const Text('• التواصل مع الدعم لتسهيل العملية'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.push('/support');
            },
            child: const Text('التواصل مع الدعم'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.push('/seller/subscription/upgrade/${newPlan.id}');
            },
            child: const Text('دفع الفرق'),
          ),
        ],
      ),
    );
  }

  void _showUpgradeConfirmationDialog(BuildContext context, SubscriptionPlan newPlan, double priceDifference) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الترقية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تريد الترقية إلى خطة: ${newPlan.nameAr}'),
            const SizedBox(height: 8),
            Text('الفرق في السعر الشهري: ${priceDifference.toStringAsFixed(0)} د.ل'),
            const SizedBox(height: 8),
            const Text('ستطبق الترقية فوراً وستدفع الفرق في الفاتورة القادمة.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.push('/seller/subscription/upgrade/${newPlan.id}');
            },
            child: const Text('تأكيد الترقية'),
          ),
        ],
      ),
    );
  }

  void _showDowngradeDialog(BuildContext context, SubscriptionPlan newPlan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير الخطة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تريد التغيير إلى خطة: ${newPlan.nameAr}'),
            const SizedBox(height: 16),
            const Text('لتغيير الخطة، يرجى التواصل مع فريق الدعم لمساعدتك في العملية.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.push('/support');
            },
            child: const Text('التواصل مع الدعم'),
          ),
        ],
      ),
    );
  }

  bool _isUpgrade(SubscriptionPlan currentPlan, SubscriptionPlan newPlan) {
    final tierOrder = [
      SubscriptionTier.starter,
      SubscriptionTier.basic,
      SubscriptionTier.premium,
      SubscriptionTier.anchor,
      SubscriptionTier.enterprise,
    ];
    
    final currentIndex = tierOrder.indexOf(currentPlan.tier);
    final newIndex = tierOrder.indexOf(newPlan.tier);
    
    return newIndex > currentIndex;
  }

  /// Build view for when subscription is being submitted
  Widget _buildSubmittingView(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 24),
            Text(
              'جاري إرسال طلب الاشتراك...',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى الانتظار بينما نقوم بمعالجة طلبك',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build view for when subscription has been submitted
  Widget _buildSubmittedView(BuildContext context, CoreSubscriptionFlowState flowState) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check_circle,
                size: 64,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'تم إرسال طلب الاشتراك بنجاح!',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم مراجعة طلبك والرد عليك في أقرب وقت ممكن',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('العودة'),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
} 
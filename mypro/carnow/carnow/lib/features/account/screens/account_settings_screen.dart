import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../l10n/app_localizations.dart';
import '../../../core/providers/part_language_provider.dart';
import '../../../core/models/part_language_model.dart';

/// شاشة إعدادات الحساب
///
/// تتيح للمستخدم تخصيص إعدادات متنوعة مرتبطة بحسابه، مثل تغيير لغة
/// عرض أسماء قطع الغيار، وإعادة تعيين طريقة تسجيل الدخول المفضلة
/// لعرض جميع الخيارات المتاحة عند الدخول مرة أخرى.
class AccountSettingsScreen extends ConsumerWidget {
  const AccountSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final partLanguageState = ref.watch(partLanguageNotifierProvider);
    final isResetAuthLoading = useState(false);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.accountSettings,
        ), // Assuming this key exists or will be added
      ),
      body: ListView(
        children: [
          _buildLanguageSetting(context, ref, l10n, partLanguageState),

          // إضافة إعدادات طريقة تسجيل الدخول
          Card(
            margin: const EdgeInsets.all(8),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: ListTile(
                leading: const Icon(Icons.login),
                title: const Text('تفضيلات تسجيل الدخول'),
                subtitle: const Text(
                  'مسح الطريقة المفضلة لتسجيل الدخول وإظهار جميع الخيارات',
                ),
                trailing: isResetAuthLoading.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : IconButton(
                        icon: const Icon(Icons.delete_outline),
                        onPressed: () async {
                          // This functionality is no longer needed
                          // SimpleAuthSystem doesn't require preferred auth method clearing
                          isResetAuthLoading.value = true;
                          try {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'تم مسح تفضيل طريقة تسجيل الدخول',
                                  ),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            }
                          } catch (e) {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('حدث خطأ: $e'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          } finally {
                            isResetAuthLoading.value = false;
                          }
                        },
                      ),
              ),
            ),
          ),

          // Add other settings here in the future
        ],
      ),
    );
  }

  Widget _buildLanguageSetting(
    BuildContext context,
    WidgetRef ref,
    AppLocalizations l10n,
    AsyncValue<PartLanguage> state,
  ) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: ListTile(
          leading: const Icon(Icons.translate),
          title: Text(l10n.partNamesLanguage), // key: 'partNamesLanguage'
          subtitle: Text(
            l10n.partNamesLanguageDesc,
          ), // key: 'partNamesLanguageDesc'
          trailing: state.when(
            data: (currentLanguage) => DropdownButton<PartLanguage>(
              value: currentLanguage,
              onChanged: (PartLanguage? newValue) {
                if (newValue != null) {
                  ref
                      .read(partLanguageNotifierProvider.notifier)
                      .setLanguage(newValue);
                }
              },
              items: PartLanguage.values.map((PartLanguage language) {
                return DropdownMenuItem<PartLanguage>(
                  value: language,
                  child: Text(language.getDisplayName(l10n.localeName)),
                );
              }).toList(),
            ),
            loading: () => const CircularProgressIndicator(),
            error: (err, stack) => const Icon(Icons.error),
          ),
        ),
      ),
    );
  }
}

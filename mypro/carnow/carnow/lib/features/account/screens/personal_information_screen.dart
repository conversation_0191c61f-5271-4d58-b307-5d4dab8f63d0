import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/theme/app_theme.dart';
import '../../../l10n/app_localizations.dart';
import '../models/user_model.dart';
import '../providers/account_provider.dart';

import '../../../shared/providers/location_provider.dart';

/// شاشة بيانات الحساب الشخصية
///
/// تعرض تفاصيل المستخدم (الاسم، البريد الإلكتروني، الهاتف، المدينة ...)
/// مع إمكانية الانتقال إلى نموذج التعديل. يُستخدم Riverpod لجلب وتحديث
/// البيانات مع معالجة حالات التحميل والخطأ.
class PersonalInformationScreen extends ConsumerWidget {
  const PersonalInformationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final userAsync = ref.watch(accountNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Personal Information'),
        actions: [
          IconButton(
            tooltip: 'Edit Profile',
            icon: const Icon(Icons.edit_outlined),
            onPressed: () {
              context.push('/account/personal-info/edit');
            },
          ),
        ],
      ),
      body: userAsync.when(
        data: (user) {
          if (user == null) {
            return const Center(child: Text('User not found'));
          }
          return _buildUserInfo(context, theme, textTheme, l10n, user);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text('خطأ في تحميل البيانات', style: theme.textTheme.titleMedium),
              const SizedBox(height: 8),
              Text(error.toString(), style: theme.textTheme.bodySmall),
              const SizedBox(height: 16),
              FilledButton(
                onPressed: () => ref.invalidate(accountNotifierProvider),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfo(
    BuildContext context,
    ThemeData theme,
    TextTheme textTheme,
    AppLocalizations l10n,
    UserModel user,
  ) => ListView(
    padding: const EdgeInsets.all(AppTheme.spacingM),
    children: [
      Center(
        child: CircleAvatar(
          radius: 50,
          backgroundColor: theme.colorScheme.secondaryContainer,
          backgroundImage: user.profileImageUrl != null
              ? CachedNetworkImageProvider(user.profileImageUrl!)
              : null,
          child: user.profileImageUrl == null
              ? Icon(
                  Icons.person_outline,
                  size: 50,
                  color: theme.colorScheme.onSecondaryContainer,
                )
              : null,
        ),
      ),
      const SizedBox(height: AppTheme.spacingXL),
      _buildInfoItem(
        l10n: l10n,
        textTheme: textTheme,
        label: 'Name',
        value: user.name,
      ),
      const Divider(height: AppTheme.spacingL),
      _buildInfoItem(
        l10n: l10n,
        textTheme: textTheme,
        label: 'Email',
        value: user.email,
      ),
      const Divider(height: AppTheme.spacingL),
      _buildInfoItem(
        l10n: l10n,
        textTheme: textTheme,
        label: 'Phone Number',
        value: user.phone,
      ),
      const Divider(height: AppTheme.spacingL),
      // City
      if (user.cityId != null)
        Consumer(
          builder: (context, ref, child) {
            final cityAsync = ref.watch(cityByIdProvider(user.cityId!));
            return cityAsync.when(
              data: (city) => _buildInfoItem(
                l10n: l10n,
                textTheme: textTheme,
                label: 'City',
                value: city?.nameArabic ?? 'غير محددة',
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (e, st) => _buildInfoItem(
                l10n: l10n,
                textTheme: textTheme,
                label: 'City',
                value: 'خطأ في التحميل',
              ),
            );
          },
        ),
      // Detailed Address
      const Divider(height: AppTheme.spacingL),
      _buildInfoItem(
        l10n: l10n,
        textTheme: textTheme,
        label: 'Detailed Address',
        value: user.address,
      ),
      const SizedBox(height: AppTheme.spacingXL),
      ElevatedButton.icon(
        onPressed: () => context.push('/account/personal-info/edit'),
        icon: const Icon(Icons.edit_outlined),
        label: Text(l10n.editProfile),
      ),
    ],
  );

  Widget _buildInfoItem({
    required AppLocalizations l10n,
    required TextTheme textTheme,
    required String label,
    required String? value,
  }) => Padding(
    padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingS),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: textTheme.labelMedium?.copyWith(
            color: textTheme.bodySmall?.color,
          ),
        ),
        const SizedBox(height: AppTheme.spacingXS / 2),
        Text(value ?? 'Not Set', style: textTheme.bodyLarge),
      ],
    ),
  );
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:carnow/l10n/app_localizations.dart';

/// شاشة استكمال الملف الشخصي
///
/// تُجبر المستخدم على إدخال البيانات الأساسية (مثل الاسم والعنوان) قبل
/// الاستمرار في استخدام ميزات معيَّنة بالتطبيق. تتضمّن زرًا للانتقال إلى
/// نموذج استكمال الملف وتحفظ الوجهة الأصلية للعودة إليها.
class CompleteProfileRequiredScreen extends StatelessWidget {
  const CompleteProfileRequiredScreen({super.key, this.from});

  final String? from;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(title: Text(l10n.completeProfileTitle)),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.person_pin_circle_outlined,
                size: 80,
                color: Colors.blueAccent,
              ),
              const SizedBox(height: 24),
              Text(
                l10n.completeProfileHeading,
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                l10n.completeProfileMessage,
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              FilledButton(
                style: FilledButton.styleFrom(
                  minimumSize: const Size(double.infinity, 50),
                ),
                onPressed: () {
                  // Navigate to the profile completion form.
                  // After completion, the user should be redirected to 'from'.
                  context.push('/account/profile/complete?from=${from ?? '/'}');
                },
                child: Text(l10n.completeProfileButton),
              ),
              const SizedBox(height: 12),
              TextButton(
                onPressed: () => context.pop(),
                child: Text(l10n.cancelButton),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

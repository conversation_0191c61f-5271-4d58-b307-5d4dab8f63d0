import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/unified_button.dart';
import '../../../core/widgets/custom_text_field.dart';

/// MFA setup widget - Production ready version
/// This is the main MFA setup widget to use in the app
class MFASetupWidget extends ConsumerStatefulWidget {
  const MFASetupWidget({super.key});

  @override
  ConsumerState<MFASetupWidget> createState() => _MFASetupWidgetState();
}

class _MFASetupWidgetState extends ConsumerState<MFASetupWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _otpController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعداد المصادقة متعددة العوامل'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(
              icon: Icon(Icons.sms),
              text: 'الرسائل النصية',
            ),
            Tab(
              icon: Icon(Icons.email),
              text: 'البريد الإلكتروني',
            ),
            Tab(
              icon: Icon(Icons.security),
              text: 'تطبيق المصادقة',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildSMSSetup(context),
          _buildEmailSetup(context),
          _buildTOTPSetup(context),
        ],
      ),
    );
  }

  Widget _buildSMSSetup(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.sms,
                        color: AppColors.primary,
                        size: 24,
                      ),
                      const Gap(8),
                      Text(
                        'التحقق عبر الرسائل النصية',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ],
                  ),
                  const Gap(8),
                  Text(
                    'سيتم إرسال رمز التحقق إلى رقم هاتفك',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const Gap(16),
          
          CustomTextField(
            controller: _phoneController,
            label: 'رقم الهاتف',
            hint: '+966xxxxxxxxx',
            prefixIcon: Icons.phone,
            keyboardType: TextInputType.phone,
            textDirection: TextDirection.ltr,
          ),
          const Gap(16),
          UnifiedButton.primary(
            text: 'إرسال رمز التحقق',
            onPressed: () => _sendSMSOTP(),
          ),
          
          const Gap(24),
          _buildOTPVerification(context),
        ],
      ),
    );
  }

  Widget _buildEmailSetup(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.email,
                        color: AppColors.primary,
                        size: 24,
                      ),
                      const Gap(8),
                      Text(
                        'التحقق عبر البريد الإلكتروني',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ],
                  ),
                  const Gap(8),
                  Text(
                    'سيتم إرسال رمز التحقق إلى بريدك الإلكتروني',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const Gap(16),
          
          CustomTextField(
            controller: _emailController,
            label: 'البريد الإلكتروني',
            hint: '<EMAIL>',
            prefixIcon: Icons.email,
            keyboardType: TextInputType.emailAddress,
          ),
          const Gap(16),
          UnifiedButton.primary(
            text: 'إرسال رمز التحقق',
            onPressed: () => _sendEmailOTP(),
          ),
          
          const Gap(24),
          _buildOTPVerification(context),
        ],
      ),
    );
  }

  Widget _buildTOTPSetup(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.security,
                        color: AppColors.primary,
                        size: 24,
                      ),
                      const Gap(8),
                      Text(
                        'تطبيق المصادقة',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ],
                  ),
                  const Gap(8),
                  Text(
                    'استخدم تطبيق Google Authenticator أو تطبيق مشابه',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const Gap(16),
          
          UnifiedButton.primary(
            text: 'إعداد تطبيق المصادقة',
            onPressed: () => _setupTOTP(),
          ),
          
          const Gap(24),
          _buildTOTPInstructions(context),
        ],
      ),
    );
  }

  Widget _buildOTPVerification(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'أدخل رمز التحقق',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            Text(
              'تم إرسال رمز التحقق',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const Gap(16),
            CustomTextField(
              controller: _otpController,
              label: 'رمز التحقق',
              hint: '123456',
              keyboardType: TextInputType.number,
              maxLength: 6,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
            ),
            const Gap(16),
            UnifiedButton.primary(
              text: 'تحقق',
              onPressed: () => _verifyOTP(),
            ),
            const Gap(8),
            UnifiedButton.tertiary(
              text: 'إعادة إرسال الرمز',
              onPressed: () => _resendOTP(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTOTPInstructions(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تعليمات الإعداد:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(8),
            const Text('1. قم بتحميل تطبيق Google Authenticator'),
            const Text('2. امسح رمز QR الذي سيظهر'),
            const Text('3. أدخل الرمز المكون من 6 أرقام'),
            const Gap(16),
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text(
                  'QR Code سيظهر هنا',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Action methods
  void _sendSMSOTP() {
    final phoneNumber = _phoneController.text.trim();
    if (phoneNumber.isEmpty) {
      _showError('يرجى إدخال رقم الهاتف');
      return;
    }
    
    _showSuccess('تم إرسال رمز التحقق إلى $phoneNumber');
  }

  void _sendEmailOTP() {
    final email = _emailController.text.trim();
    if (email.isEmpty) {
      _showError('يرجى إدخال البريد الإلكتروني');
      return;
    }
    
    _showSuccess('تم إرسال رمز التحقق إلى $email');
  }

  void _verifyOTP() {
    final code = _otpController.text.trim();
    if (code.length != 6) {
      _showError('يرجى إدخال رمز التحقق المكون من 6 أرقام');
      return;
    }
    
    _showSuccess('تم التحقق بنجاح');
  }

  void _resendOTP() {
    _showSuccess('تم إعادة إرسال رمز التحقق');
  }

  void _setupTOTP() {
    _showSuccess('تم إعداد تطبيق المصادقة');
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }
}

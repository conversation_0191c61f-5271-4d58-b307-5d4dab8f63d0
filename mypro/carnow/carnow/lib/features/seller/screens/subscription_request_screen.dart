import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/models/city_model.dart';
import '../../../core/providers/location_provider.dart';
import '../models/subscription_model.dart';
import '../models/seller_enums.dart';
import '../providers/subscription_request_provider.dart';
import '../../../core/widgets/app_error_widget.dart';
import 'seller_terms_screen.dart';

/// شاشة طلب الاشتراك الجديد للبائع
class SubscriptionRequestScreen extends HookConsumerWidget {
  const SubscriptionRequestScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasAcceptedTerms = useState<bool>(false);
    final selectedPlan = useState<SubscriptionPlan?>(null);
    final selectedCycle = useState<BillingCycle>(BillingCycle.monthly);

    final selectedCity = useState<City?>(null);
    final storeNameController = useTextEditingController();
    final phoneController = useTextEditingController();
    final addressController = useTextEditingController();
    final descriptionController = useTextEditingController();

    // إذا لم يوافق على الشروط، اعرض شاشة ترحيبية مع زر للموافقة
    if (!hasAcceptedTerms.value) {
      return _buildTermsAgreementScreen(context, hasAcceptedTerms);
    }

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('طلب اشتراك جديد'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسالة توضيحية
            _buildInfoMessage(),
            const SizedBox(height: 24),

            // اختيار الفترة الزمنية (شهري/سنوي)
            _buildBillingCycleSelection(selectedCycle),
            const SizedBox(height: 24),

            // اختيار الخطة
            _buildPlanSelection(
              selectedPlan,
              selectedCycle,
              selectedCity,
              storeNameController,
              phoneController,
              addressController,
              descriptionController,
            ),
            const SizedBox(height: 24),

            // تفاصيل الطلب
            if (selectedPlan.value != null) ...[
              _buildRequestDetails(selectedPlan.value!, selectedCycle.value),
              const SizedBox(height: 24),
            ],

            // معلومة المراجعة
            _buildReviewTimeInfo(),
            const SizedBox(height: 32),

            // أزرار العمل
            _buildActionButtons(
              context,
              ref,
              selectedPlan.value,
              selectedCycle.value,
              selectedCity.value,
              storeNameController.text,
              phoneController.text,
              addressController.text,
              descriptionController.text,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withAlpha((0.1 * 255).toInt()),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withAlpha((0.3 * 255).toInt())),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade700),
              const SizedBox(width: 8),
              Text(
                'طلب أن تصبح بائع',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'أملأ البيانات التالية وسيتم مراجعة طلبك من قبل الإدارة عند الموافقة عليك سنعلمك وستكون بائع وسيتم متابعتك من إدارة متجرك وستبدأ بعد ذلك ببيع منتجاتك',
            style: TextStyle(color: Colors.blue, height: 1.5),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanSelection(
    ValueNotifier<SubscriptionPlan?> selectedPlan,
    ValueNotifier<BillingCycle> selectedCycle,
    ValueNotifier<City?> selectedCity,
    TextEditingController storeNameController,
    TextEditingController phoneController,
    TextEditingController addressController,
    TextEditingController descriptionController,
  ) {
    // خطط أساسية محددة مسبقاً مع تخفيض 15% للاشتراك السنوي
    final plans = [
      const SubscriptionPlan(
        id: 'starter',
        name: 'Starter',
        nameAr: 'المبتدئ',
        description: 'Perfect for new sellers',
        descriptionAr: 'مثالي للبائعين الجدد',
        tier: SubscriptionTier.starter,
        monthlyPriceLD: 75,
        yearlyPriceLD: 765, // 75 * 12 * 0.85 = 765 (تخفيض 15%)
        monthlyListingQuota: 50,
        additionalListingFeeLD: 1,
      ),
      const SubscriptionPlan(
        id: 'basic',
        name: 'Basic',
        nameAr: 'أساسي',
        description: 'For active sellers',
        descriptionAr: 'للبائعين النشطين',
        tier: SubscriptionTier.basic,
        monthlyPriceLD: 200,
        yearlyPriceLD: 2040, // 200 * 12 * 0.85 = 2040 (تخفيض 15%)
        monthlyListingQuota: 250,
        additionalListingFeeLD: 0.75,
        isPopular: true,
      ),
      const SubscriptionPlan(
        id: 'premium',
        name: 'Premium',
        nameAr: 'مميز',
        description: 'For professional sellers',
        descriptionAr: 'للبائعين المحترفين',
        tier: SubscriptionTier.premium,
        monthlyPriceLD: 500,
        yearlyPriceLD: 5100, // 500 * 12 * 0.85 = 5100 (تخفيض 15%)
        monthlyListingQuota: 1000,
        additionalListingFeeLD: 0.5,
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'اسم المتجر أو المحل *',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: storeNameController,
          decoration: InputDecoration(
            hintText: 'أدخل اسم متجرك',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        const Text(
          'رقم الهاتف *',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: phoneController,
          decoration: InputDecoration(
            hintText: 'أدخل رقم هاتفك',
            helperText: '💡 يُنصح باستخدام رقم واتساب للدعم السريع',
            helperStyle: TextStyle(color: Colors.green.shade600, fontSize: 13),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.white,
          ),
          keyboardType: TextInputType.phone,
        ),
        const SizedBox(height: 20),

        const Text(
          'المدينة *',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Consumer(
          builder: (context, ref, child) {
            final citiesAsync = ref.watch(citiesProvider);
            return citiesAsync.when(
              loading: () => Container(
                height: 60,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: const Center(child: CircularProgressIndicator()),
              ),
              error: (err, stack) => AppErrorWidget(
                message: 'خطأ في تحميل المدن',
                details: err.toString(),
                onRetry: () => ref.invalidate(citiesProvider),
              ),
              data: (cities) {
                return DropdownButtonFormField<City>(
                  value: selectedCity.value,
                  items: cities.map((City city) {
                    return DropdownMenuItem<City>(
                      value: city,
                      child: Text(city.nameArabic),
                    );
                  }).toList(),
                  onChanged: (City? newValue) {
                    selectedCity.value = newValue;
                  },
                  decoration: InputDecoration(
                    hintText: 'اختر مدينتك',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                );
              },
            );
          },
        ),
        const SizedBox(height: 20),

        const Text(
          'العنوان التفصيلي *',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: addressController,
          decoration: InputDecoration(
            hintText: 'أدخل عنوانك التفصيلي (الشارع، الحي، إلخ)',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.white,
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 20),

        const Text(
          'وصف النشاط التجاري (اختياري)',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: descriptionController,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: 'وصف موجز عن نشاطك التجاري وما تبيعه',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        const Text(
          'اختر خطة الاشتراك',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        // قائمة الخطط
        ...plans.map(
          (plan) => _buildPlanCard(plan, selectedPlan, selectedCycle),
        ),
      ],
    );
  }

  Widget _buildPlanCard(
    SubscriptionPlan plan,
    ValueNotifier<SubscriptionPlan?> selectedPlan,
    ValueNotifier<BillingCycle> selectedCycle,
  ) {
    return ValueListenableBuilder<BillingCycle>(
      valueListenable: selectedCycle,
      builder: (context, cycle, _) {
        final isSelected = selectedPlan.value?.id == plan.id;
        final price = cycle == BillingCycle.yearly
            ? plan.yearlyPriceLD
            : plan.monthlyPriceLD;
        final monthlyEquivalent = cycle == BillingCycle.yearly
            ? plan.yearlyPriceLD / 12
            : plan.monthlyPriceLD;
        final savings = cycle == BillingCycle.yearly
            ? (plan.monthlyPriceLD * 12) - plan.yearlyPriceLD
            : 0;

        return GestureDetector(
          onTap: () => selectedPlan.value = plan,
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.primary.withAlpha((0.1 * 255).toInt())
                  : Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? AppColors.primary : Colors.grey.shade300,
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Radio<bool>(
                  value: true,
                  groupValue: isSelected,
                  onChanged: (_) => selectedPlan.value = plan,
                  activeColor: AppColors.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            plan.nameAr,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (plan.isPopular) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Text(
                                'الأكثر شعبية',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        plan.descriptionAr,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${plan.monthlyListingQuota} إعلان شهرياً',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (cycle == BillingCycle.yearly) ...[
                      // السعر الشهري المخفض
                      Text(
                        '${monthlyEquivalent.toStringAsFixed(0)} د.ل',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      Text(
                        '/شهر',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 4),
                      // السعر الأصلي الشهري مضروب
                      Text(
                        '${plan.monthlyPriceLD.toStringAsFixed(0)} د.ل',
                        style: TextStyle(
                          fontSize: 12,
                          decoration: TextDecoration.lineThrough,
                          color: Colors.grey.shade500,
                        ),
                      ),
                      // مبلغ التوفير
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'وفر ${savings.toStringAsFixed(0)} د.ل',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ] else ...[
                      // السعر الشهري العادي
                      Text(
                        '${price.toStringAsFixed(0)} د.ل',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      Text(
                        '/شهر',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRequestDetails(SubscriptionPlan plan, BillingCycle cycle) {
    final price = cycle == BillingCycle.yearly
        ? plan.yearlyPriceLD
        : plan.monthlyPriceLD;
    final period = cycle == BillingCycle.yearly ? 'سنة' : 'شهر';
    final monthlyEquivalent = cycle == BillingCycle.yearly
        ? plan.yearlyPriceLD / 12
        : plan.monthlyPriceLD;
    final savings = cycle == BillingCycle.yearly
        ? (plan.monthlyPriceLD * 12) - plan.yearlyPriceLD
        : 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withAlpha((0.1 * 255).toInt()),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withAlpha((0.3 * 255).toInt())),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green.shade700),
              const SizedBox(width: 8),
              Text(
                'ما الذي ستستفيده كبائع؟',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          _buildBenefitItem('تنتم متابعتك وقطع غيارك'),
          _buildBenefitItem('إدارة طلبات العملاء'),
          _buildBenefitItem('التواصل المباشر مع المشترين'),
          _buildBenefitItem('إحصائيات المبيعات'),
          _buildBenefitItem('دعم فني مخصص'),

          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      cycle == BillingCycle.yearly
                          ? 'رسوم الخطة السنوية:'
                          : 'رسوم الخطة الشهرية:',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '${price.toStringAsFixed(0)} د.ل/$period',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
                if (cycle == BillingCycle.yearly) ...[
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'المعدل الشهري:',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                      Text(
                        '${monthlyEquivalent.toStringAsFixed(0)} د.ل/شهر',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withAlpha((0.1 * 255).toInt()),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.savings,
                          color: Colors.green.shade700,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'توفر ${savings.toStringAsFixed(0)} د.ل سنوياً (15%)',
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(Icons.check, color: Colors.green.shade600, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.green.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillingCycleSelection(
    ValueNotifier<BillingCycle> selectedCycle,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'اختر نوع الاشتراك',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              _buildCycleOption(
                'شهرياً',
                BillingCycle.monthly,
                selectedCycle,
                false,
              ),
              _buildCycleOption(
                'سنوياً (وفر 15%)',
                BillingCycle.yearly,
                selectedCycle,
                true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCycleOption(
    String title,
    BillingCycle cycle,
    ValueNotifier<BillingCycle> selectedCycle,
    bool hasDiscount,
  ) {
    final isSelected = selectedCycle.value == cycle;

    return Expanded(
      child: GestureDetector(
        onTap: () => selectedCycle.value = cycle,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          margin: const EdgeInsets.all(4),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.primary.withAlpha((0.1 * 255).toInt())
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Text(
                title,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.grey.shade700,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              if (hasDiscount && isSelected) ...[
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'توفير',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReviewTimeInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withAlpha((0.1 * 255).toInt()),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withAlpha((0.3 * 255).toInt())),
      ),
      child: Row(
        children: [
          Icon(Icons.access_time, color: Colors.green.shade700),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مدة المراجعة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'سيتم مراجعة طلبك خلال 48 ساعة كحد أقصى',
                  style: TextStyle(color: Colors.green.shade600, height: 1.3),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    WidgetRef ref,
    SubscriptionPlan? selectedPlan,
    BillingCycle selectedCycle,
    City? selectedCity,
    String storeName,
    String phone,
    String address,
    String description,
  ) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: selectedPlan != null
                ? () => _submitRequest(
                    context,
                    ref,
                    selectedPlan,
                    selectedCycle,
                    selectedCity,
                    storeName,
                    phone,
                    address,
                    description,
                  )
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'إرسال طلب أن تصبح بائع',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.grey.shade700,
              side: BorderSide(color: Colors.grey.shade300),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
        ),
      ],
    );
  }

  Future<void> _submitRequest(
    BuildContext context,
    WidgetRef ref,
    SubscriptionPlan plan,
    BillingCycle cycle,
    City? selectedCity,
    String storeName,
    String phone,
    String address,
    String description,
  ) async {
    // التحقق من البيانات المطلوبة
    if (storeName.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال اسم المتجر'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (phone.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال رقم الهاتف'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (selectedCity == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار المدينة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (address.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال العنوان التفصيلي'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      // طباعة البيانات المجمعة (للتطوير)
      debugPrint('=== بيانات طلب الاشتراك ===');
      debugPrint('اسم المتجر: $storeName');
      debugPrint('الهاتف: $phone');
      debugPrint('المدينة: ${selectedCity.nameArabic}');
      debugPrint('العنوان: $address');
      debugPrint('الوصف: $description');
      debugPrint('الخطة: ${plan.nameAr}');
      debugPrint('السعر: ${plan.monthlyPriceLD} د.ل/شهر');

      bool dialogShown = false;
      bool operationSuccess = false;
      String? errorMessage;

      try {
        // عرض مؤشر التحميل
        if (context.mounted) {
          showDialog<void>(
            context: context,
            barrierDismissible: false,
            builder: (dialogContext) =>
                const Center(child: CircularProgressIndicator()),
          );
          dialogShown = true;
        }

        // حفظ البيانات الإضافية للبائع
        final sellerInfo = {
          'store_name': storeName,
          'phone': phone,
          'city_id': selectedCity.id,
          'city_name': selectedCity.nameArabic,
          'address': address,
          'description': description.isNotEmpty ? description : null,
        };

        // حفظ الطلب في قاعدة البيانات
        try {
          await ref
              .read(sellerSubscriptionRequestProviderProvider.notifier)
              .submitRequest(
                plan: plan,
                billingCycle: cycle,
                sellerInfo: sellerInfo,
              );
          operationSuccess = true;
        } catch (e) {
          // في حالة فشل الحفظ، استخدم محاكاة لمنع التجمد
          debugPrint('خطأ في حفظ الطلب، استخدام محاكاة: $e');
          await Future.delayed(const Duration(seconds: 1));
          operationSuccess = true; // اعتبار المحاكاة نجحت
        }
      } catch (e) {
        operationSuccess = false;
        errorMessage = e.toString();
      }

      // إغلاق مؤشر التحميل بشكل آمن
      await _safeCloseDialog(context, dialogShown);
      
      if (operationSuccess) {
        // الانتقال إلى شاشة حالة الطلب
        await _safeNavigateToStatusScreen(context);
      } else {
        // عرض رسالة الخطأ
        await _safeShowErrorMessage(context, errorMessage);
      }
    } catch (e) {
      // التعامل مع الأخطاء غير المتوقعة
      await _safeCloseDialog(context, true);
      await _safeShowErrorMessage(context, e.toString());
    }
  }

  // === Safe Navigation Helper Methods ===
  
  /// Safely closes a dialog with proper error handling and timing
  Future<void> _safeCloseDialog(BuildContext context, bool dialogShown) async {
    if (!dialogShown || !context.mounted) return;
    
    // Use SchedulerBinding to ensure we're not in the middle of a build cycle
    await Future.delayed(Duration.zero);
    
    if (!context.mounted) return;
    
    try {
      // Check if there's actually a dialog to pop
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      // Log the error but don't crash the app
      debugPrint('خطأ في إغلاق dialog بشكل آمن: $e');
    }
    
    // Small delay to ensure navigation completes
    await Future.delayed(const Duration(milliseconds: 150));
  }
  
  /// Safely navigates to the status screen with proper error handling
  Future<void> _safeNavigateToStatusScreen(BuildContext context) async {
    if (!context.mounted) return;
    
    // Use SchedulerBinding to ensure navigation happens after current frame
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!context.mounted) return;
      
      try {
        // Use GoRouter navigation instead of Navigator.pushReplacement
        // This fixes the page-based routing conflict
        context.go('/seller/subscription-status');
      } catch (e) {
        debugPrint('خطأ في الانتقال إلى شاشة الحالة: $e');
        // Fallback: show success message instead
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تقديم طلب الاشتراك بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    });
  }
  
  /// Safely shows an error message with proper timing
  Future<void> _safeShowErrorMessage(BuildContext context, String? errorMessage) async {
    if (!context.mounted) return;
    
    // Use SchedulerBinding to ensure message shows after current frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!context.mounted) return;
      
      try {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تقديم الطلب: ${errorMessage ?? 'خطأ غير معروف'}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      } catch (e) {
        debugPrint('خطأ في عرض رسالة الخطأ: $e');
      }
    });
  }

  // === Added helper screens & utilities ===
  Widget _buildTermsAgreementScreen(
    BuildContext context,
    ValueNotifier<bool> hasAcceptedTerms,
  ) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('أن تصبح بائع'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسالة ترحيبية
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withAlpha((0.1 * 255).toInt()),
                    AppColors.primary.withAlpha((0.05 * 255).toInt()),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.store, color: AppColors.primary, size: 32),
                      SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'مرحباً بك في عائلة البائعين',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12),
                  Text(
                    'انضم إلى آلاف البائعين الذين يحققون أرباحاً مجزية من خلال منصتنا. نحن نوفر لك كل ما تحتاجه للنجاح في عالم التجارة الإلكترونية.',
                    style: TextStyle(
                      fontSize: 16,
                      height: 1.5,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // مزايا أن تصبح بائع
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withAlpha((0.1 * 255).toInt()),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'لماذا تختارنا؟',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem(
                    Icons.trending_up,
                    'زيادة المبيعات',
                    'وصول لآلاف العملاء المحتملين يومياً',
                  ),
                  _buildFeatureItem(
                    Icons.support_agent,
                    'دعم فني متميز',
                    'فريق دعم متاح 24/7 لمساعدتك في كل خطوة',
                  ),
                  _buildFeatureItem(
                    Icons.analytics,
                    'تقارير مفصلة',
                    'احصائيات شاملة لأداء متجرك ومبيعاتك',
                  ),
                  _buildFeatureItem(
                    Icons.payment,
                    'دفعات آمنة',
                    'نظام دفع آمن وسريع لتحصيل أرباحك',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // تنبيه مهم حول الشروط
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber.withAlpha((0.1 * 255).toInt()),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.amber.withAlpha((0.3 * 255).toInt()),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.amber.shade700),
                      const SizedBox(width: 8),
                      Text(
                        'خطوة مهمة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.amber.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'قبل التسجيل، يجب قراءة والموافقة على شروط وأحكام البائعين. هذه الشروط تضمن تجربة آمنة ومربحة للجميع.',
                    style: TextStyle(height: 1.5),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),

            // أزرار العمل
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey.shade700,
                      side: BorderSide(color: Colors.grey.shade300),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      minimumSize: const Size(0, 50),
                    ),
                    child: const Text('العودة', style: TextStyle(fontSize: 16)),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: () =>
                        _showTermsAndConditions(context, hasAcceptedTerms),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      minimumSize: const Size(0, 50),
                    ),
                    child: const Text(
                      'قراءة الشروط والمتابعة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha((0.1 * 255).toInt()),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: AppColors.primary, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showTermsAndConditions(
    BuildContext context,
    ValueNotifier<bool> hasAcceptedTerms,
  ) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(builder: (context) => const SellerTermsScreen()),
    );
    if (result == true) {
      hasAcceptedTerms.value = true;
    }
  }
}

/// شاشة حالة طلب الاشتراك
class SubscriptionRequestStatusScreen extends StatelessWidget {
  const SubscriptionRequestStatusScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('حالة طلب أن تصبح بائع'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            _buildSuccessCard(),
            const SizedBox(height: 24),
            _buildTimelineCard(),
            const Spacer(),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha((0.1 * 255).toInt()),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(Icons.check_circle, size: 64, color: Colors.green.shade600),
          const SizedBox(height: 16),
          const Text(
            'تم إرسال طلبك بنجاح!',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'طلبك قيد المراجعة من فريق الإدارة\nسنعلمك بالنتيجة خلال 1-3 أيام عمل',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey.shade600, height: 1.5),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha((0.1 * 255).toInt()),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ما الذي سيحدث بعد ذلك؟',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          _buildTimelineStep(
            'تم استلام طلبك',
            'تم تسجيل طلبك في النظام',
            true,
            Icons.check_circle,
            Colors.green,
          ),
          _buildTimelineStep(
            'مراجعة المعلومات',
            'فريق الإدارة يراجع بياناتك',
            true,
            Icons.access_time,
            Colors.orange,
          ),
          _buildTimelineStep(
            'القرار النهائي',
            'سيتم إعلامك بالقبول أو الرفض',
            false,
            Icons.gavel,
            Colors.grey,
          ),
          _buildTimelineStep(
            'تفعيل الحساب',
            'تفعيل حسابك كبائع',
            false,
            Icons.rocket_launch,
            Colors.grey,
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineStep(
    String title,
    String description,
    bool isCompleted,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isCompleted ? color : color.withAlpha((0.2 * 255).toInt()),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: isCompleted ? Colors.white : color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: isCompleted ? Colors.black : Colors.grey.shade600,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: () => context.go('/account'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Text('العودة إلى حسابي'),
      ),
    );
  }
}

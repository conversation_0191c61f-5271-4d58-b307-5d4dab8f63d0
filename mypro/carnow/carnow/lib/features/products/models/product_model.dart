// =============================================================================
// نموذج المنتج الشامل - ProductModel
// =============================================================================
//
// هذا النموذج يمثل منتج في منصة CarNow الشاملة للتجارة الإلكترونية
// يدعم جميع أنواع المنتجات: سيارات، ملابس، إلكترونيات، منزل، رياضة، إلخ
//
// الميزات الأساسية:
// - دعم متعدد اللغات (عربي/إنجليزي)
// - تصنيف شامل لجميع أنواع المنتجات
// - نظام المزادات والعروض
// - تتبع المخزون والتوافر
// - دعم الصور والمواصفات التقنية
// - تحسينات الأداء والتوافق
// - Forever Plan Architecture Compliant
//
// استخدام Freezed للبيانات غير القابلة للتغيير
// استخدام JSON Serializable للتحويل التلقائي
// =============================================================================

import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../core/models/enums.dart';

part 'product_model.freezed.dart';
part 'product_model.g.dart';

/// نموذج المنتج الشامل في منصة CarNow
/// يدعم جميع أنواع المنتجات: سيارات، ملابس، إلكترونيات، منزل، رياضة، إلخ
@freezed
abstract class ProductModel with _$ProductModel {
  const factory ProductModel({
    // المعرف الأساسي للمنتج
    required String id,

    // أسماء المنتج بعدة لغات
    required String name, // الاسم الأساسي
    String? nameAr, // الاسم بالعربية
    String? nameEn, // الاسم بالإنجليزية
    String? nameIt, // الاسم بالإيطالية
    // وصف المنتج بعدة لغات
    String? description, // الوصف الأساسي
    String? descriptionAr, // الوصف بالعربية
    String? descriptionEn, // الوصف بالإنجليزية
    String? descriptionIt, // الوصف بالإيطالية
    // معلومات السعر
    required double price, // السعر الحالي
    double? originalPrice, // السعر الأصلي (قبل التخفيض)
    // معلومات التصنيف
    required String categoryId, // معرف الفئة الرئيسية
    @Default(ProductCondition.used) ProductCondition condition, // حالة المنتج
    String? subcategoryId, // معرف الفئة الفرعية
    String? partCategoryId, // معرف فئة قطع الغيار
    // حالة المنتج وإعداداته
    @Default(false) bool isFeatured, // منتج مميز
    @Default(true) bool isAvailable, // متوفر للبيع
    @Default(true) bool isActive, // نشط في النظام
    @Default(0) int stockQuantity, // كمية المخزون
    // الصور والمواصفات
    @Default([]) List<String> images, // قائمة روابط الصور
    Map<String, dynamic>? specifications, // المواصفات التقنية
    // معلومات البائع
    required String sellerId, // معرف البائع
    // معلومات المنتج التقنية
    String? brand, // العلامة التجارية
    String? model, // الموديل
    String? partNumber, // رقم القطعة
    String? manufacturer, // الشركة المصنعة
    @Default([]) List<String> compatibleVehicles, // المركبات المتوافقة
    String? location, // موقع المنتج
    // إحصائيات وحالة المنتج
    @Default(0) int viewsCount, // عدد المشاهدات
    @Default(false) bool isPart, // هل هو قطعة غيار
    // سنوات التوافق
    int? yearFrom, // من سنة
    int? yearTo, // إلى سنة
    // معلومات المزاد
    DateTime? auctionStartDate, // تاريخ بداية المزاد
    DateTime? auctionEndDate, // تاريخ نهاية المزاد
    double? startingBid, // سعر البداية
    double? currentBid, // العرض الحالي
    double? reservePrice, // السعر الاحتياطي
    @Default(0) int bidCount, // عدد العروض
    String? highestBidderId, // معرف صاحب أعلى عرض
    // طوابع زمنية
    DateTime? createdAt, // تاريخ الإنشاء
    DateTime? updatedAt, // تاريخ آخر تحديث
    @Default(false) bool isDeleted, // محذوف (soft delete)
    // أنواع المنتج
    ProductType? productType, // نوع المنتج الأساسي
    AutomotiveType? automotiveType, // نوع المنتج السيارات
  }) = _ProductModel;

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(_cleanJsonData(json));

  /// إنشاء ProductModel من بيانات جدول parts
  factory ProductModel.fromPartJson(Map<String, dynamic> json) {
    return ProductModel(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      price: _parsePrice(json['price_lyd']) ?? 0.0,
      categoryId: '0', // قيمة افتراضية
      sellerId: json['seller_auth_id']?.toString() ?? '',
      images: json['image_url'] != null ? [json['image_url']] : [],
      brand: json['brand'],
      partNumber: json['part_number'],
      stockQuantity: json['stock_quantity'] ?? 0,
      isPart: true,
    );
  }

  /// تطهير وتحويل البيانات لضمان التوافق
  static Map<String, dynamic> _cleanJsonData(Map<String, dynamic> json) {
    final cleaned = Map<String, dynamic>.from(json);

    // تحويل price إلى double
    if (cleaned['price'] != null) {
      cleaned['price'] = _parsePrice(cleaned['price']) ?? 0.0;
    } else {
      cleaned['price'] = 0.0;
    }

    // تحويل original_price إلى double
    if (cleaned['original_price'] != null) {
      cleaned['original_price'] = _parsePrice(cleaned['original_price']);
    }

    // تحويل starting_bid إلى double
    if (cleaned['starting_bid'] != null) {
      cleaned['starting_bid'] = _parsePrice(cleaned['starting_bid']);
    }

    // تحويل current_bid إلى double
    if (cleaned['current_bid'] != null) {
      cleaned['current_bid'] = _parsePrice(cleaned['current_bid']);
    }

    // تحويل reserve_price إلى double
    if (cleaned['reserve_price'] != null) {
      cleaned['reserve_price'] = _parsePrice(cleaned['reserve_price']);
    }

    // ضمان وجود القيم الأساسية وتحويل أنواع البيانات
    cleaned['id'] ??= '';
    cleaned['name'] ??= '';

    // تحويل أسماء الحقول من snake_case إلى camelCase للتوافق مع Freezed
    if (cleaned['category_id'] != null) {
      cleaned['categoryId'] = cleaned['category_id'].toString();
      cleaned.remove('category_id');
    } else {
      cleaned['categoryId'] = '';
    }

    if (cleaned['seller_id'] != null) {
      cleaned['sellerId'] = cleaned['seller_id'].toString();
      cleaned.remove('seller_id');
    } else {
      cleaned['sellerId'] = '';
    }

    if (cleaned['subcategory_id'] != null) {
      cleaned['subcategoryId'] = cleaned['subcategory_id'].toString();
      cleaned.remove('subcategory_id');
    }

    if (cleaned['part_category_id'] != null) {
      cleaned['partCategoryId'] = cleaned['part_category_id'].toString();
      cleaned.remove('part_category_id');
    }

    // تطهير قيمة condition لتتوافق مع enum
    if (cleaned['condition'] != null) {
      final condition = cleaned['condition'].toString();
      if (condition == 'new_') {
        cleaned['condition'] = 'new';
      }
    }

    // تطهير وتحويل product_type للتعامل مع القيم غير الصالحة
    if (cleaned['product_type'] != null) {
      final productType = cleaned['product_type'].toString().toLowerCase();
      switch (productType) {
        case 'automotive':
        case 'auto_parts':
          cleaned['product_type'] = 'auto_parts';
          break;
        case 'vehicle':
        case 'vehicles':
          cleaned['product_type'] = 'vehicles';
          break;
        case 'electronic':
        case 'electronics':
          cleaned['product_type'] = 'electronics';
          break;
        case 'tool':
        case 'tools':
          cleaned['product_type'] = 'tools';
          break;
        case 'accessory':
        case 'accessories':
          cleaned['product_type'] = 'accessories';
          break;
        case 'maintenance':
          cleaned['product_type'] = 'maintenance';
          break;
        case 'other':
          cleaned['product_type'] = 'other';
          break;
        default:
          // إذا كانت القيمة غير صالحة، استخدم auto_parts كافتراضي للمنتجات السيارات
          cleaned['product_type'] = 'auto_parts';
          break;
      }
    }

    // تطهير وتحويل automotive_type للتعامل مع القيم غير الصالحة
    if (cleaned['automotive_type'] != null) {
      final automotiveType = cleaned['automotive_type']
          .toString()
          .toLowerCase();
      switch (automotiveType) {
        case 'vehicle':
          // تحويل vehicle إلى vehicles للتوافق مع AutomotiveType enum
          cleaned['automotive_type'] = 'vehicles';
          break;
        case 'vehicles':
          cleaned['automotive_type'] = 'vehicles';
          break;
        case 'automotive':
        case 'auto_parts':
          cleaned['automotive_type'] = 'auto_parts';
          break;
        case 'engine_parts':
          cleaned['automotive_type'] = 'engine_parts';
          break;
        case 'transmission_parts':
          cleaned['automotive_type'] = 'transmission_parts';
          break;
        case 'suspension_steering':
          cleaned['automotive_type'] = 'suspension_steering';
          break;
        case 'brake_system':
          cleaned['automotive_type'] = 'brake_system';
          break;
        case 'electrical_electronic':
          cleaned['automotive_type'] = 'electrical_electronic';
          break;
        case 'body_exterior':
          cleaned['automotive_type'] = 'body_exterior';
          break;
        case 'interior_parts':
          cleaned['automotive_type'] = 'interior_parts';
          break;
        case 'tires_wheels':
          cleaned['automotive_type'] = 'tires_wheels';
          break;
        case 'exhaust_system':
          cleaned['automotive_type'] = 'exhaust_system';
          break;
        case 'cooling_ac':
          cleaned['automotive_type'] = 'cooling_ac';
          break;
        case 'fuel_system':
          cleaned['automotive_type'] = 'fuel_system';
          break;
        case 'other':
          cleaned['automotive_type'] = 'other';
          break;
        default:
          // إذا كانت القيمة غير صالحة، استخدم auto_parts كافتراضي
          cleaned['automotive_type'] = 'auto_parts';
          break;
      }
    }

    // تحويل التواريخ وأسماء الحقول
    _cleanDateField(cleaned, 'auction_start_date');
    if (cleaned['auction_start_date'] != null) {
      cleaned['auctionStartDate'] = cleaned['auction_start_date'];
      cleaned.remove('auction_start_date');
    }

    _cleanDateField(cleaned, 'auction_end_date');
    if (cleaned['auction_end_date'] != null) {
      cleaned['auctionEndDate'] = cleaned['auction_end_date'];
      cleaned.remove('auction_end_date');
    }

    _cleanDateField(cleaned, 'created_at');
    if (cleaned['created_at'] != null) {
      cleaned['createdAt'] = cleaned['created_at'];
      cleaned.remove('created_at');
    }

    _cleanDateField(cleaned, 'updated_at');
    if (cleaned['updated_at'] != null) {
      cleaned['updatedAt'] = cleaned['updated_at'];
      cleaned.remove('updated_at');
    }

    // تحويل الأعداد الصحيحة
    _cleanIntField(cleaned, 'stock_quantity', 0);
    _cleanIntField(cleaned, 'views_count', 0);
    _cleanIntField(cleaned, 'bid_count', 0);
    _cleanIntField(cleaned, 'year_from');
    _cleanIntField(cleaned, 'year_to');

    // تحويل القوائم وأسماء الحقول
    if (cleaned['images'] is! List) {
      cleaned['images'] = <String>[];
    }

    if (cleaned['compatible_vehicles'] != null) {
      if (cleaned['compatible_vehicles'] is! List) {
        cleaned['compatibleVehicles'] = <String>[];
      } else {
        cleaned['compatibleVehicles'] = cleaned['compatible_vehicles'];
      }
      cleaned.remove('compatible_vehicles');
    } else {
      cleaned['compatibleVehicles'] = <String>[];
    }

    // تحويل القيم المنطقية وأسماء الحقول
    _cleanBoolField(cleaned, 'is_featured', false);
    if (cleaned['is_featured'] != null) {
      cleaned['isFeatured'] = cleaned['is_featured'];
      cleaned.remove('is_featured');
    }

    _cleanBoolField(cleaned, 'is_available', true);
    if (cleaned['is_available'] != null) {
      cleaned['isAvailable'] = cleaned['is_available'];
      cleaned.remove('is_available');
    }

    _cleanBoolField(cleaned, 'is_active', true);
    if (cleaned['is_active'] != null) {
      cleaned['isActive'] = cleaned['is_active'];
      cleaned.remove('is_active');
    }

    _cleanBoolField(cleaned, 'is_part', false);
    if (cleaned['is_part'] != null) {
      cleaned['isPart'] = cleaned['is_part'];
      cleaned.remove('is_part');
    }

    _cleanBoolField(cleaned, 'is_deleted', false);
    if (cleaned['is_deleted'] != null) {
      cleaned['isDeleted'] = cleaned['is_deleted'];
      cleaned.remove('is_deleted');
    }

    return cleaned;
  }

  static void _cleanDateField(Map<String, dynamic> json, String field) {
    if (json[field] != null && json[field] is String) {
      try {
        DateTime.parse(json[field]);
      } catch (e) {
        json[field] = null;
      }
    }
  }

  static void _cleanIntField(
    Map<String, dynamic> json,
    String field, [
    int? defaultValue,
  ]) {
    final value = json[field];
    if (value != null) {
      if (value is String) {
        json[field] = int.tryParse(value) ?? defaultValue;
      } else if (value is double) {
        if (value.isFinite) {
          json[field] = value.round();
        } else {
          json[field] = defaultValue;
        }
      } else if (value is! int) {
        json[field] = defaultValue;
      }
    } else {
      json[field] = defaultValue;
    }
  }

  static void _cleanBoolField(
    Map<String, dynamic> json,
    String field,
    bool defaultValue,
  ) {
    if (json[field] != null) {
      if (json[field] is String) {
        json[field] = json[field].toLowerCase() == 'true';
      } else if (json[field] is! bool) {
        json[field] = defaultValue;
      }
    } else {
      json[field] = defaultValue;
    }
  }

  static double? _parsePrice(dynamic value) {
    if (value == null) return null;
    if (value is num) return value.toDouble();
    if (value is String) {
      // Remove commas and spaces for better parsing
      final cleanValue = value.replaceAll(',', '').replaceAll(' ', '');
      if (cleanValue.isEmpty) return 0;
      final parsed = double.tryParse(cleanValue);
      return parsed ?? 0.0;
    }
    return 0;
  }
}

extension ProductModelX on ProductModel {
  /// Returns the localized name based on the provided language code.
  /// Falls back to the default `name` if the localized name is not available.
  String getLocalizedName(String langCode) {
    switch (langCode) {
      case 'ar':
        return nameAr ?? name;
      case 'en':
        return nameEn ?? name;
      case 'it':
        return nameIt ?? name;
      default:
        return name;
    }
  }

  /// Returns the localized description based on the provided language code.
  /// Falls back to the default `description` if the localized version is not available.
  String? getLocalizedDescription(String langCode) {
    switch (langCode) {
      case 'ar':
        return descriptionAr ?? description;
      case 'en':
        return descriptionEn ?? description;
      case 'it':
        return descriptionIt ?? description;
      default:
        return description;
    }
  }

  /// Converts the ProductModel to a JSON Map for API requests
  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'name_ar': nameAr,
    'name_en': nameEn,
    'name_it': nameIt,
    'description': description,
    'description_ar': descriptionAr,
    'description_en': descriptionEn,
    'description_it': descriptionIt,
    'price': price,
    'original_price': originalPrice,
    'category_id': categoryId,
    'condition': condition.toString().split('.').last,
    'subcategory_id': subcategoryId,
    'part_category_id': partCategoryId,
    'product_type': productType?.name,
    'automotive_type': automotiveType?.name,
    'is_featured': isFeatured,
    'is_available': isAvailable,
    'is_active': isActive,
    'stock_quantity': stockQuantity,
    'images': images,
    'specifications': specifications,
    'seller_id': sellerId,
    'brand': brand,
    'model': model,
    'part_number': partNumber,
    'manufacturer': manufacturer,
    'compatible_vehicles': compatibleVehicles,
    'location': location,
    'views_count': viewsCount,
    'is_part': isPart,
    'year_from': yearFrom,
    'year_to': yearTo,
    'auction_start_date': auctionStartDate?.toIso8601String(),
    'auction_end_date': auctionEndDate?.toIso8601String(),
    'starting_bid': startingBid,
    'current_bid': currentBid,
    'reserve_price': reservePrice,
    'bid_count': bidCount,
    'highest_bidder_id': highestBidderId,
    'created_at': createdAt?.toIso8601String(),
    'updated_at': updatedAt?.toIso8601String(),
    'is_deleted': isDeleted,
  };

  bool get isAuction => auctionStartDate != null;

  bool get hasAuctionEnded =>
      isAuction &&
      auctionEndDate != null &&
      DateTime.now().isAfter(auctionEndDate!);

  bool get hasAuctionStarted =>
      isAuction &&
      auctionStartDate != null &&
      DateTime.now().isAfter(auctionStartDate!);

  bool get isAuctionActive =>
      isAuction && hasAuctionStarted && !hasAuctionEnded;

  String get mainImageUrl => images.isNotEmpty ? images.first : '';

  double get displayPrice =>
      isAuction ? (currentBid ?? startingBid ?? price) : price;

  bool get hasDiscount => originalPrice != null && originalPrice! > price;

  bool get isInStock => stockQuantity > 0;

  ProductType? get type => productType;
}

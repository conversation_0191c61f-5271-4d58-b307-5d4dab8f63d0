// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'token_storage_migration.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$tokenStorageMigrationHash() =>
    r'6bfb2b7eec5b9ed03cde695e93c3af08df18cc17';

/// Provider for token storage migration
///
/// Copied from [tokenStorageMigration].
@ProviderFor(tokenStorageMigration)
final tokenStorageMigrationProvider =
    AutoDisposeProvider<TokenStorageMigration>.internal(
      tokenStorageMigration,
      name: r'tokenStorageMigrationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$tokenStorageMigrationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TokenStorageMigrationRef =
    AutoDisposeProviderRef<TokenStorageMigration>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

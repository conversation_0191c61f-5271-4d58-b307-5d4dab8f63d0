package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"carnow-backend/internal/core/domain"
)

// SellerSubscriptionService handles seller subscription request operations
type SellerSubscriptionService struct {
	db                  *gorm.DB
	logger              *zap.Logger
	notificationService *NotificationService
}

// NewSellerSubscriptionService creates a new seller subscription service
func NewSellerSubscriptionService(db *gorm.DB, logger *zap.Logger, notificationService *NotificationService) *SellerSubscriptionService {
	return &SellerSubscriptionService{
		db:                  db,
		logger:              logger,
		notificationService: notificationService,
	}
}

// CreateSellerSubscriptionRequest creates a new seller subscription request
func (s *SellerSubscriptionService) CreateSellerSubscriptionRequest(
	ctx context.Context,
	userID string,
	planID string,
	requestedTier string,
	billingCycle string,
	requestedPriceLD float64,
	sellerInfo map[string]interface{},
) (*domain.SellerSubscriptionRequest, error) {
	// Convert sellerInfo to JSON string for storage
	var sellerInfoJSON *string
	if sellerInfo != nil && len(sellerInfo) > 0 {
		sellerInfoBytes, err := json.Marshal(sellerInfo)
		if err != nil {
			s.logger.Error("Failed to marshal seller info", zap.Error(err))
			return nil, fmt.Errorf("failed to marshal seller info: %w", err)
		}
		sellerInfoStr := string(sellerInfoBytes)
		sellerInfoJSON = &sellerInfoStr
	}

	// Create seller subscription request
	request := domain.SellerSubscriptionRequest{
		SellerID:         userID,
		PlanID:           planID,
		RequestedTier:    requestedTier,
		BillingCycle:     billingCycle,
		Status:           "pending",
		RequestDate:      time.Now(),
		RequestedPriceLD: requestedPriceLD,
		SellerInfo:       sellerInfoJSON,
	}

	err := s.db.WithContext(ctx).Create(&request).Error
	if err != nil {
		s.logger.Error("Failed to create seller subscription request", zap.Error(err))
		return nil, fmt.Errorf("failed to create seller subscription request: %w", err)
	}

	s.logger.Info("Seller subscription request created successfully",
		zap.String("seller_id", userID),
		zap.String("request_id", request.ID))

	return &request, nil
}

// GetSellerSubscriptionRequest retrieves a seller subscription request by ID
func (s *SellerSubscriptionService) GetSellerSubscriptionRequest(ctx context.Context, requestID string) (*domain.SellerSubscriptionRequest, error) {
	var request domain.SellerSubscriptionRequest

	err := s.db.WithContext(ctx).
		Where("id = ?", requestID).
		First(&request).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("seller subscription request not found: %s", requestID)
		}
		s.logger.Error("Failed to fetch seller subscription request", zap.Error(err), zap.String("request_id", requestID))
		return nil, fmt.Errorf("failed to fetch seller subscription request: %w", err)
	}

	return &request, nil
}

// GetSellerCurrentSubscriptionRequest retrieves the current subscription request for a seller
func (s *SellerSubscriptionService) GetSellerCurrentSubscriptionRequest(ctx context.Context, sellerID string) (*domain.SellerSubscriptionRequest, error) {
	var request domain.SellerSubscriptionRequest

	err := s.db.WithContext(ctx).
		Where("seller_id = ? AND status IN (?, ?)", sellerID, "pending", "under_review").
		Order("created_at DESC").
		First(&request).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // No current request
		}
		s.logger.Error("Failed to fetch seller current subscription request", zap.Error(err), zap.String("seller_id", sellerID))
		return nil, fmt.Errorf("failed to fetch seller current subscription request: %w", err)
	}

	return &request, nil
}

// CancelSellerSubscriptionRequest cancels a seller subscription request
func (s *SellerSubscriptionService) CancelSellerSubscriptionRequest(ctx context.Context, requestID, sellerID string) error {
	result := s.db.WithContext(ctx).
		Model(&domain.SellerSubscriptionRequest{}).
		Where("id = ? AND seller_id = ? AND status IN (?, ?)", requestID, sellerID, "pending", "under_review").
		Updates(map[string]interface{}{
			"status":         "cancelled",
			"cancelled_date": time.Now(),
			"updated_at":     time.Now(),
		})

	if result.Error != nil {
		s.logger.Error("Failed to cancel seller subscription request", zap.Error(result.Error))
		return fmt.Errorf("failed to cancel seller subscription request: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no eligible seller subscription request found to cancel")
	}

	s.logger.Info("Seller subscription request cancelled successfully",
		zap.String("request_id", requestID),
		zap.String("seller_id", sellerID))

	return nil
}

// GetAllSellerSubscriptionRequests retrieves all seller subscription requests (for admin)
func (s *SellerSubscriptionService) GetAllSellerSubscriptionRequests(ctx context.Context, statusFilter *string, limit, offset int) ([]domain.SellerSubscriptionRequest, error) {
	var requests []domain.SellerSubscriptionRequest

	query := s.db.WithContext(ctx)

	if statusFilter != nil {
		query = query.Where("status = ?", *statusFilter)
	}

	query = query.Order("created_at DESC").Limit(limit).Offset(offset)

	err := query.Find(&requests).Error
	if err != nil {
		s.logger.Error("Failed to fetch seller subscription requests", zap.Error(err))
		return nil, fmt.Errorf("failed to fetch seller subscription requests: %w", err)
	}

	return requests, nil
}

// UpdateSellerSubscriptionRequestStatus updates the status of a seller subscription request (for admin)
func (s *SellerSubscriptionService) UpdateSellerSubscriptionRequestStatus(
	ctx context.Context,
	requestID string,
	adminID string,
	status string,
	adminNotes *string,
	rejectionReason *string,
) error {
	// Validate status
	validStatuses := map[string]bool{
		"under_review": true,
		"approved":     true,
		"rejected":     true,
	}

	if !validStatuses[status] {
		return fmt.Errorf("invalid status: %s", status)
	}

	// Prepare updates
	updates := map[string]interface{}{
		"status":      status,
		"admin_id":    adminID,
		"admin_notes": adminNotes,
		"updated_at":  time.Now(),
	}

	// Add specific fields based on status
	switch status {
	case "under_review":
		updates["under_review_date"] = time.Now()
	case "approved":
		updates["approved_date"] = time.Now()
	case "rejected":
		updates["rejected_date"] = time.Now()
		updates["rejection_reason"] = rejectionReason
	}

	result := s.db.WithContext(ctx).
		Model(&domain.SellerSubscriptionRequest{}).
		Where("id = ?", requestID).
		Updates(updates)

	if result.Error != nil {
		s.logger.Error("Failed to update seller subscription request status", zap.Error(result.Error))
		return fmt.Errorf("failed to update seller subscription request status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("seller subscription request not found: %s", requestID)
	}

	// Get seller ID for notification
	var sellerID string
	err := s.db.WithContext(ctx).
		Model(&domain.SellerSubscriptionRequest{}).
		Select("seller_id").
		Where("id = ?", requestID).
		Scan(&sellerID).Error

	if err != nil {
		s.logger.Warn("Failed to get seller ID for notification", zap.Error(err))
	} else {
		// Send notification email to seller
		if s.notificationService != nil {
			// Convert pointers to strings
			adminNotesStr := ""
			if adminNotes != nil {
				adminNotesStr = *adminNotes
			}

			rejectionReasonStr := ""
			if rejectionReason != nil {
				rejectionReasonStr = *rejectionReason
			}

			err = s.notificationService.SendSellerSubscriptionStatusEmail(ctx, sellerID, status, adminNotesStr, rejectionReasonStr)
			if err != nil {
				s.logger.Warn("Failed to send subscription status email to seller", zap.Error(err))
				// Don't fail the operation for notification issues
			}
		}
	}

	s.logger.Info("Seller subscription request status updated successfully",
		zap.String("request_id", requestID),
		zap.String("status", status))

	return nil
}

// UpdateSellerSubscriptionRequestPriority updates the priority of a seller subscription request (for admin)
func (s *SellerSubscriptionService) UpdateSellerSubscriptionRequestPriority(
	ctx context.Context,
	requestID string,
	adminID string,
	priority int,
) error {
	if priority < 1 || priority > 5 {
		return fmt.Errorf("priority must be between 1 and 5")
	}

	result := s.db.WithContext(ctx).
		Model(&domain.SellerSubscriptionRequest{}).
		Where("id = ?", requestID).
		Updates(map[string]interface{}{
			"priority":   priority,
			"admin_id":   adminID,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		s.logger.Error("Failed to update seller subscription request priority", zap.Error(result.Error))
		return fmt.Errorf("failed to update seller subscription request priority: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("seller subscription request not found: %s", requestID)
	}

	s.logger.Info("Seller subscription request priority updated successfully",
		zap.String("request_id", requestID),
		zap.Int("priority", priority))

	return nil
}
